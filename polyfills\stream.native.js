/**
 * Stream polyfill for React Native
 * 
 * This polyfill provides a minimal implementation of Node.js stream module
 * to avoid bundling errors in React Native.
 */

// Mock EventEmitter for stream compatibility
class MockEventEmitter {
  constructor() {
    this._events = {};
  }
  
  on(event, listener) {
    if (!this._events[event]) {
      this._events[event] = [];
    }
    this._events[event].push(listener);
    return this;
  }
  
  emit(event, ...args) {
    if (this._events[event]) {
      this._events[event].forEach(listener => {
        try {
          listener.apply(this, args);
        } catch (error) {
          console.warn('Error in event listener:', error);
        }
      });
    }
    return this;
  }
  
  removeListener(event, listener) {
    if (this._events[event]) {
      this._events[event] = this._events[event].filter(l => l !== listener);
    }
    return this;
  }
  
  removeAllListeners(event) {
    if (event) {
      delete this._events[event];
    } else {
      this._events = {};
    }
    return this;
  }
}

// Mock Readable stream
class MockReadable extends MockEventEmitter {
  constructor(options = {}) {
    super();
    this.readable = true;
    this.destroyed = false;
    this._readableState = {
      objectMode: false,
      highWaterMark: 16384,
      buffer: [],
      length: 0,
      pipes: null,
      pipesCount: 0,
      flowing: null,
      ended: false,
      endEmitted: false,
      reading: false,
      sync: true,
      needReadable: false,
      emittedReadable: false,
      readableListening: false,
      resumeScheduled: false,
      destroyed: false,
      defaultEncoding: 'utf8',
      awaitDrain: 0,
      readingMore: false,
      decoder: null,
      encoding: null
    };
  }
  
  read() {
    return null;
  }
  
  pipe() {
    return this;
  }
  
  unpipe() {
    return this;
  }
  
  destroy() {
    this.destroyed = true;
    this.emit('close');
    return this;
  }
}

// Mock Writable stream
class MockWritable extends MockEventEmitter {
  constructor(options = {}) {
    super();
    this.writable = true;
    this.destroyed = false;
  }
  
  write(chunk, encoding, callback) {
    if (typeof encoding === 'function') {
      callback = encoding;
      encoding = 'utf8';
    }
    
    if (callback) {
      setTimeout(callback, 0);
    }
    
    return true;
  }
  
  end(chunk, encoding, callback) {
    if (typeof chunk === 'function') {
      callback = chunk;
      chunk = null;
    } else if (typeof encoding === 'function') {
      callback = encoding;
      encoding = 'utf8';
    }
    
    if (chunk) {
      this.write(chunk, encoding);
    }
    
    this.writable = false;
    this.emit('finish');
    
    if (callback) {
      setTimeout(callback, 0);
    }
    
    return this;
  }
  
  destroy() {
    this.destroyed = true;
    this.emit('close');
    return this;
  }
}

// Mock Duplex stream
class MockDuplex extends MockReadable {
  constructor(options = {}) {
    super(options);
    this.writable = true;
  }
  
  write(chunk, encoding, callback) {
    return MockWritable.prototype.write.call(this, chunk, encoding, callback);
  }
  
  end(chunk, encoding, callback) {
    return MockWritable.prototype.end.call(this, chunk, encoding, callback);
  }
}

// Mock Transform stream
class MockTransform extends MockDuplex {
  constructor(options = {}) {
    super(options);
  }
  
  _transform(chunk, encoding, callback) {
    callback(null, chunk);
  }
}

// Export stream classes
module.exports = {
  Readable: MockReadable,
  Writable: MockWritable,
  Duplex: MockDuplex,
  Transform: MockTransform,
  PassThrough: MockTransform,
  Stream: MockEventEmitter,
  EventEmitter: MockEventEmitter,
};

// For CommonJS compatibility
module.exports.default = module.exports;
