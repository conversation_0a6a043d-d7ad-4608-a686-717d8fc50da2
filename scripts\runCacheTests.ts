/**
 * Test Runner Script for Advanced Cache System
 * 
 * Executes comprehensive tests for the Advanced Map Cache System
 * and integrates results with the existing test framework.
 */

import { exportCacheTestResults, validateCacheSystem } from '../tests/cache/AdvancedCacheTestSuite';

/**
 * Run cache system tests and update results
 */
export const runAdvancedCacheTests = async (): Promise<any> => {
  console.log('🧪 Starting Advanced Cache System Tests...');
  
  try {
    const testResults = await exportCacheTestResults();
    const testData = JSON.parse(testResults);
    
    console.log('✅ Cache System Tests Completed');
    console.log(`📊 Results: ${testData.summary.totalPassed}/${testData.summary.totalTests} tests passed (${testData.summary.overallSuccessRate}%)`);
    
    return testData;
  } catch (error) {
    console.error('❌ Cache System Tests Failed:', error);
    throw error;
  }
};

/**
 * Validate cache system integration
 */
export const validateCacheIntegration = async (): Promise<boolean> => {
  console.log('🔍 Validating Cache System Integration...');
  
  try {
    const isValid = await validateCacheSystem();
    
    if (isValid) {
      console.log('✅ Cache System Integration Validated');
    } else {
      console.log('❌ Cache System Integration Failed');
    }
    
    return isValid;
  } catch (error) {
    console.error('❌ Cache System Validation Error:', error);
    return false;
  }
};
