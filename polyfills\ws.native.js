/**
 * WebSocket polyfill for React Native
 *
 * This polyfill replaces the Node.js 'ws' package with React Native's built-in WebSocket
 * to avoid Node.js-specific dependencies like 'stream' module.
 */

// Use React Native's built-in WebSocket
const NativeWebSocket = global.WebSocket;

class WebSocketPolyfill extends NativeWebSocket {
  constructor(url, protocols, options = {}) {
    // Handle different parameter combinations
    if (typeof protocols === 'object' && !Array.isArray(protocols)) {
      options = protocols;
      protocols = undefined;
    }

    // React Native WebSocket doesn't support the options parameter
    super(url, protocols);

    // Store options for compatibility
    this._options = options;

    // Add compatibility properties
    this.readyState = this.CONNECTING;
    this._bufferedAmount = 0;

    // Bind event handlers to maintain compatibility
    this.addEventListener('open', () => {
      this.readyState = this.OPEN;
    });

    this.addEventListener('close', () => {
      this.readyState = this.CLOSED;
    });

    this.addEventListener('error', () => {
      this.readyState = this.CLOSED;
    });
  }

  // Add bufferedAmount property for compatibility
  get bufferedAmount() {
    return this._bufferedAmount || 0;
  }
  
  // Add ping/pong methods for compatibility (no-op in React Native)
  ping(data, mask, callback) {
    if (typeof data === 'function') {
      callback = data;
      data = undefined;
    }
    if (typeof mask === 'function') {
      callback = mask;
      mask = undefined;
    }
    if (callback) {
      setTimeout(callback, 0);
    }
  }
  
  pong(data, mask, callback) {
    if (typeof data === 'function') {
      callback = data;
      data = undefined;
    }
    if (typeof mask === 'function') {
      callback = mask;
      mask = undefined;
    }
    if (callback) {
      setTimeout(callback, 0);
    }
  }
  
  // Add terminate method for compatibility
  terminate() {
    this.close();
  }
}

// Copy static properties
WebSocketPolyfill.CONNECTING = NativeWebSocket.CONNECTING || 0;
WebSocketPolyfill.OPEN = NativeWebSocket.OPEN || 1;
WebSocketPolyfill.CLOSING = NativeWebSocket.CLOSING || 2;
WebSocketPolyfill.CLOSED = NativeWebSocket.CLOSED || 3;

// Fallback for environments without WebSocket
if (!NativeWebSocket) {
  console.warn('WebSocket not available, using mock implementation');

  // Mock WebSocket for environments that don't support it
  class MockWebSocket {
    constructor(url, protocols, options = {}) {
      this.url = url;
      this.readyState = 3; // CLOSED
      this._options = options;

      // Emit error after a short delay
      setTimeout(() => {
        if (this.onerror) {
          this.onerror(new Error('WebSocket not supported'));
        }
      }, 0);
    }

    send() {
      throw new Error('WebSocket not supported');
    }

    close() {
      // No-op
    }

    addEventListener() {
      // No-op
    }

    removeEventListener() {
      // No-op
    }

    ping() {
      // No-op
    }

    pong() {
      // No-op
    }

    terminate() {
      // No-op
    }
  }

  MockWebSocket.CONNECTING = 0;
  MockWebSocket.OPEN = 1;
  MockWebSocket.CLOSING = 2;
  MockWebSocket.CLOSED = 3;

  module.exports = MockWebSocket;
  module.exports.default = MockWebSocket;
  module.exports.WebSocket = MockWebSocket;
} else {
  // Export as both default and named export for compatibility
  module.exports = WebSocketPolyfill;
  module.exports.default = WebSocketPolyfill;
  module.exports.WebSocket = WebSocketPolyfill;
}

// For ES6 imports
if (typeof exports === 'object' && typeof module !== 'undefined') {
  // Already handled above
} else if (typeof define === 'function' && define.amd) {
  define(function() { return module.exports; });
} else if (NativeWebSocket) {
  global.WebSocket = WebSocketPolyfill;
}
