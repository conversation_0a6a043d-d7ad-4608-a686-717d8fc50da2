#!/bin/bash

# AsyncStorage Fix Verification Script
# Check if all fixes are properly applied

echo "🔍 Verifying AsyncStorage Fix"
echo "============================="

# Color codes
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

checks_passed=0
total_checks=0

check_file() {
    total_checks=$((total_checks + 1))
    if [ -f "$1" ]; then
        echo -e "${GREEN}✅ $1 exists${NC}"
        checks_passed=$((checks_passed + 1))
    else
        echo -e "${RED}❌ $1 missing${NC}"
    fi
}

check_content() {
    total_checks=$((total_checks + 1))
    if [ -f "$2" ] && grep -q "$1" "$2" 2>/dev/null; then
        echo -e "${GREEN}✅ $3${NC}"
        checks_passed=$((checks_passed + 1))
    else
        echo -e "${RED}❌ $3${NC}"
    fi
}

echo "📁 File Structure Check"
echo "======================="

# Check if storage utility exists
check_file "lib/storage.ts"

# Check if components exist
check_file "components/map/MapScreen.tsx"
check_file "components/map/EnhancedMapIntegration.tsx"
check_file "components/map/ProfessionalMapUI.tsx"

echo ""
echo "🔧 Component Integration Check"
echo "=============================="

# Check MapScreen uses EnhancedMapIntegration
check_content "EnhancedMapIntegration" "components/map/MapScreen.tsx" "MapScreen uses EnhancedMapIntegration"

# Check EnhancedMapIntegration uses storage utility
check_content "StorageUtils" "components/map/EnhancedMapIntegration.tsx" "EnhancedMapIntegration uses StorageUtils"

# Check MapIntegration uses storage utility
check_content "StorageUtils" "components/map/MapIntegration.tsx" "MapIntegration uses StorageUtils"

echo ""
echo "❌ AsyncStorage Import Check"
echo "============================"

# Check that AsyncStorage is NOT directly imported
if grep -r "AsyncStorage from.*async-storage" components/map/ 2>/dev/null; then
    echo -e "${RED}❌ Found direct AsyncStorage imports that need to be fixed${NC}"
else
    echo -e "${GREEN}✅ No direct AsyncStorage imports found${NC}"
    checks_passed=$((checks_passed + 1))
fi
total_checks=$((total_checks + 1))

echo ""
echo "📦 Package Check"
echo "================"

# Check if AsyncStorage package is installed
if [ -d "node_modules/@react-native-async-storage" ]; then
    echo -e "${GREEN}✅ AsyncStorage package installed${NC}"
    checks_passed=$((checks_passed + 1))
else
    echo -e "${RED}❌ AsyncStorage package not installed${NC}"
fi
total_checks=$((total_checks + 1))

echo ""
echo "📊 VERIFICATION SUMMARY"
echo "======================"

success_rate=$((checks_passed * 100 / total_checks))

echo "Checks Passed: $checks_passed/$total_checks"
echo "Success Rate: $success_rate%"

if [ $success_rate -ge 90 ]; then
    echo -e "${GREEN}🎉 EXCELLENT! All fixes are properly applied${NC}"
    echo -e "${GREEN}✨ Your AsyncStorage error should be resolved${NC}"
    echo ""
    echo "🚀 Next Steps:"
    echo "   1. Run: npm run web"
    echo "   2. Navigate to: http://localhost:8081/map"
    echo "   3. Enjoy your professional map interface!"
elif [ $success_rate -ge 70 ]; then
    echo -e "${YELLOW}⚠️  GOOD! Most fixes applied, minor issues remain${NC}"
    echo ""
    echo "🔧 Recommendations:"
    echo "   1. Review failed checks above"
    echo "   2. Run the complete fix script"
    echo "   3. Clear Metro cache and restart"
else
    echo -e "${RED}❌ NEEDS ATTENTION! Several fixes missing${NC}"
    echo ""
    echo "🚨 Required Actions:"
    echo "   1. Run: complete-fix-asyncstorage.bat"
    echo "   2. Ensure all files are properly created"
    echo "   3. Clear caches and restart development server"
fi

echo ""
echo "🔗 Available Fix Scripts:"
echo "   • complete-fix-asyncstorage.bat (Windows)"
echo "   • complete-fix-asyncstorage.sh (Linux/Mac)"
