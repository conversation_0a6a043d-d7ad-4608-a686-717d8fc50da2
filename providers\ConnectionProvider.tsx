import { createContext, useState, useEffect, ReactNode } from 'react';
import NetInfo from '@react-native-community/netinfo';
import { Platform } from 'react-native';

type ConnectionContextType = {
  isConnected: boolean;
  isConnecting: boolean;
  connectionType: string | null;
};

export const ConnectionContext = createContext<ConnectionContextType | null>(null);

export function ConnectionProvider({ children }: { children: ReactNode }) {
  const [isConnected, setIsConnected] = useState(true);
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionType, setConnectionType] = useState<string | null>(null);
  
  useEffect(() => {
    // For web platform, use navigator.onLine
    if (Platform.OS === 'web') {
      const handleOnline = () => setIsConnected(true);
      const handleOffline = () => setIsConnected(false);
      
      window.addEventListener('online', handleOnline);
      window.addEventListener('offline', handleOffline);
      
      setIsConnected(navigator.onLine);
      
      return () => {
        window.removeEventListener('online', handleOnline);
        window.removeEventListener('offline', handleOffline);
      };
    } 
    // For native platforms, use NetInfo
    else {
      const unsubscribe = NetInfo.addEventListener(state => {
        setIsConnected(state.isConnected ?? false);
        setConnectionType(state.type);
        setIsConnecting(state.isConnectionExpensive ?? false);
      });
      
      return () => {
        unsubscribe();
      };
    }
  }, []);
  
  return (
    <ConnectionContext.Provider value={{ isConnected, isConnecting, connectionType }}>
      {children}
    </ConnectionContext.Provider>
  );
}