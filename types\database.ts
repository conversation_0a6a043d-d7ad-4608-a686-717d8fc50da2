// Supabase Database Types for FieldSync Pro
export interface Database {
  public: {
    Tables: {
      user_profiles: {
        Row: {
          id: string;
          email: string;
          full_name: string | null;
          avatar_url: string | null;
          role: 'admin' | 'manager' | 'collector' | 'viewer';
          organization: string | null;
          created_at: string;
          updated_at: string;
          last_seen: string | null;
          is_active: boolean;
          preferences: Json | null;
        };
        Insert: {
          id: string;
          email: string;
          full_name?: string | null;
          avatar_url?: string | null;
          role?: 'admin' | 'manager' | 'collector' | 'viewer';
          organization?: string | null;
          created_at?: string;
          updated_at?: string;
          last_seen?: string | null;
          is_active?: boolean;
          preferences?: Json | null;
        };
        Update: {
          id?: string;
          email?: string;
          full_name?: string | null;
          avatar_url?: string | null;
          role?: 'admin' | 'manager' | 'collector' | 'viewer';
          organization?: string | null;
          created_at?: string;
          updated_at?: string;
          last_seen?: string | null;
          is_active?: boolean;
          preferences?: Json | null;
        };
      };
      teams: {
        Row: {
          id: string;
          name: string;
          description: string | null;
          created_by: string;
          created_at: string;
          updated_at: string;
          is_active: boolean;
          organization: string | null;
        };
        Insert: {
          id?: string;
          name: string;
          description?: string | null;
          created_by: string;
          created_at?: string;
          updated_at?: string;
          is_active?: boolean;
          organization?: string | null;
        };
        Update: {
          id?: string;
          name?: string;
          description?: string | null;
          created_by?: string;
          created_at?: string;
          updated_at?: string;
          is_active?: boolean;
          organization?: string | null;
        };
      };
      team_members: {
        Row: {
          id: string;
          team_id: string;
          user_id: string;
          role: 'lead' | 'member';
          joined_at: string;
          is_active: boolean;
        };
        Insert: {
          id?: string;
          team_id: string;
          user_id: string;
          role?: 'lead' | 'member';
          joined_at?: string;
          is_active?: boolean;
        };
        Update: {
          id?: string;
          team_id?: string;
          user_id?: string;
          role?: 'lead' | 'member';
          joined_at?: string;
          is_active?: boolean;
        };
      };
      projects: {
        Row: {
          id: string;
          name: string;
          description: string | null;
          status: 'draft' | 'active' | 'completed' | 'archived';
          created_by: string;
          organization: string | null;
          created_at: string;
          updated_at: string;
          start_date: string | null;
          end_date: string | null;
          region: Json | null; // GeoJSON for project boundary
          settings: Json | null;
          metadata: Json | null;
        };
        Insert: {
          id?: string;
          name: string;
          description?: string | null;
          status?: 'draft' | 'active' | 'completed' | 'archived';
          created_by: string;
          organization?: string | null;
          created_at?: string;
          updated_at?: string;
          start_date?: string | null;
          end_date?: string | null;
          region?: Json | null;
          settings?: Json | null;
          metadata?: Json | null;
        };
        Update: {
          id?: string;
          name?: string;
          description?: string | null;
          status?: 'draft' | 'active' | 'completed' | 'archived';
          created_by?: string;
          organization?: string | null;
          created_at?: string;
          updated_at?: string;
          start_date?: string | null;
          end_date?: string | null;
          region?: Json | null;
          settings?: Json | null;
          metadata?: Json | null;
        };
      };
      project_teams: {
        Row: {
          id: string;
          project_id: string;
          team_id: string;
          assigned_at: string;
          assigned_by: string;
          is_active: boolean;
        };
        Insert: {
          id?: string;
          project_id: string;
          team_id: string;
          assigned_at?: string;
          assigned_by: string;
          is_active?: boolean;
        };
        Update: {
          id?: string;
          project_id?: string;
          team_id?: string;
          assigned_at?: string;
          assigned_by?: string;
          is_active?: boolean;
        };
      };
      forms: {
        Row: {
          id: string;
          project_id: string;
          name: string;
          description: string | null;
          version: number;
          status: 'draft' | 'published' | 'archived';
          schema: Json; // Form field definitions
          created_by: string;
          created_at: string;
          updated_at: string;
          published_at: string | null;
          settings: Json | null;
        };
        Insert: {
          id?: string;
          project_id: string;
          name: string;
          description?: string | null;
          version?: number;
          status?: 'draft' | 'published' | 'archived';
          schema: Json;
          created_by: string;
          created_at?: string;
          updated_at?: string;
          published_at?: string | null;
          settings?: Json | null;
        };
        Update: {
          id?: string;
          project_id?: string;
          name?: string;
          description?: string | null;
          version?: number;
          status?: 'draft' | 'published' | 'archived';
          schema?: Json;
          created_by?: string;
          created_at?: string;
          updated_at?: string;
          published_at?: string | null;
          settings?: Json | null;
        };
      };
      submissions: {
        Row: {
          id: string;
          form_id: string;
          project_id: string;
          user_id: string;
          status: 'draft' | 'completed' | 'synced' | 'rejected';
          data: Json; // Form submission data
          location: Json | null; // GPS coordinates and metadata
          started_at: string;
          completed_at: string | null;
          synced_at: string | null;
          device_info: Json | null;
          metadata: Json | null;
        };
        Insert: {
          id?: string;
          form_id: string;
          project_id: string;
          user_id: string;
          status?: 'draft' | 'completed' | 'synced' | 'rejected';
          data: Json;
          location?: Json | null;
          started_at?: string;
          completed_at?: string | null;
          synced_at?: string | null;
          device_info?: Json | null;
          metadata?: Json | null;
        };
        Update: {
          id?: string;
          form_id?: string;
          project_id?: string;
          user_id?: string;
          status?: 'draft' | 'completed' | 'synced' | 'rejected';
          data?: Json;
          location?: Json | null;
          started_at?: string;
          completed_at?: string | null;
          synced_at?: string | null;
          device_info?: Json | null;
          metadata?: Json | null;
        };
      };
      media_attachments: {
        Row: {
          id: string;
          submission_id: string;
          field_id: string;
          file_name: string;
          file_type: string;
          file_size: number;
          storage_path: string;
          thumbnail_path: string | null;
          upload_status: 'pending' | 'uploading' | 'completed' | 'failed';
          created_at: string;
          metadata: Json | null;
        };
        Insert: {
          id?: string;
          submission_id: string;
          field_id: string;
          file_name: string;
          file_type: string;
          file_size: number;
          storage_path: string;
          thumbnail_path?: string | null;
          upload_status?: 'pending' | 'uploading' | 'completed' | 'failed';
          created_at?: string;
          metadata?: Json | null;
        };
        Update: {
          id?: string;
          submission_id?: string;
          field_id?: string;
          file_name?: string;
          file_type?: string;
          file_size?: number;
          storage_path?: string;
          thumbnail_path?: string | null;
          upload_status?: 'pending' | 'uploading' | 'completed' | 'failed';
          created_at?: string;
          metadata?: Json | null;
        };
      };
    };
    Views: {
      project_stats: {
        Row: {
          project_id: string;
          total_forms: number;
          total_submissions: number;
          completed_submissions: number;
          team_count: number;
          completion_rate: number;
        };
      };
      team_performance: {
        Row: {
          team_id: string;
          team_name: string;
          member_count: number;
          total_submissions: number;
          avg_completion_time: number;
          last_activity: string;
        };
      };
    };
    Functions: {
      get_project_analytics: {
        Args: {
          project_id: string;
          date_from?: string;
          date_to?: string;
        };
        Returns: {
          submissions_by_date: Json;
          completion_rates: Json;
          team_performance: Json;
          geographic_distribution: Json;
        };
      };
      search_projects: {
        Args: {
          search_term: string;
          user_id: string;
          limit?: number;
        };
        Returns: {
          id: string;
          name: string;
          description: string;
          status: string;
          created_at: string;
          match_score: number;
        }[];
      };
    };
    Enums: {
      user_role: 'admin' | 'manager' | 'collector' | 'viewer';
      project_status: 'draft' | 'active' | 'completed' | 'archived';
      submission_status: 'draft' | 'completed' | 'synced' | 'rejected';
      form_status: 'draft' | 'published' | 'archived';
      upload_status: 'pending' | 'uploading' | 'completed' | 'failed';
    };
  };
}

// Helper types
export type Json = string | number | boolean | null | { [key: string]: Json | undefined } | Json[];

export type UserProfile = Database['public']['Tables']['user_profiles']['Row'];
export type Team = Database['public']['Tables']['teams']['Row'];
export type TeamMember = Database['public']['Tables']['team_members']['Row'];
export type Project = Database['public']['Tables']['projects']['Row'];
export type ProjectTeam = Database['public']['Tables']['project_teams']['Row'];
export type Form = Database['public']['Tables']['forms']['Row'];
export type Submission = Database['public']['Tables']['submissions']['Row'];
export type MediaAttachment = Database['public']['Tables']['media_attachments']['Row'];

// Insert types
export type UserProfileInsert = Database['public']['Tables']['user_profiles']['Insert'];
export type TeamInsert = Database['public']['Tables']['teams']['Insert'];
export type TeamMemberInsert = Database['public']['Tables']['team_members']['Insert'];
export type ProjectInsert = Database['public']['Tables']['projects']['Insert'];
export type ProjectTeamInsert = Database['public']['Tables']['project_teams']['Insert'];
export type FormInsert = Database['public']['Tables']['forms']['Insert'];
export type SubmissionInsert = Database['public']['Tables']['submissions']['Insert'];
export type MediaAttachmentInsert = Database['public']['Tables']['media_attachments']['Insert'];

// Update types
export type UserProfileUpdate = Database['public']['Tables']['user_profiles']['Update'];
export type TeamUpdate = Database['public']['Tables']['teams']['Update'];
export type TeamMemberUpdate = Database['public']['Tables']['team_members']['Update'];
export type ProjectUpdate = Database['public']['Tables']['projects']['Update'];
export type ProjectTeamUpdate = Database['public']['Tables']['project_teams']['Update'];
export type FormUpdate = Database['public']['Tables']['forms']['Update'];
export type SubmissionUpdate = Database['public']['Tables']['submissions']['Update'];
export type MediaAttachmentUpdate = Database['public']['Tables']['media_attachments']['Update'];

// View types
export type ProjectStats = Database['public']['Views']['project_stats']['Row'];
export type TeamPerformance = Database['public']['Views']['team_performance']['Row'];

// Combined types for UI
export interface ProjectWithStats extends Project {
  stats?: ProjectStats;
  teams?: Team[];
  forms?: Form[];
}

export interface TeamWithMembers extends Team {
  members?: (TeamMember & { user_profile?: UserProfile })[];
  member_count?: number;
}

export interface SubmissionWithDetails extends Submission {
  form?: Form;
  project?: Project;
  user_profile?: UserProfile;
  media_attachments?: MediaAttachment[];
}

// Real-time subscription types
export interface RealtimePayload<T = any> {
  schema: string;
  table: string;
  commit_timestamp: string;
  eventType: 'INSERT' | 'UPDATE' | 'DELETE';
  new: T;
  old: T;
  errors: any[];
}

// API Response types
export interface ApiResponse<T = any> {
  data: T | null;
  error: string | null;
  count?: number;
}

export interface PaginatedResponse<T = any> extends ApiResponse<T[]> {
  page: number;
  pageSize: number;
  totalPages: number;
  totalCount: number;
}
