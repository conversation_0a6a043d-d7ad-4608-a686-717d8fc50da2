/**
 * Update Service
 * 
 * Handles Expo Updates gracefully, preventing runtime errors
 * when updates are disabled or unavailable.
 */

import { Platform } from 'react-native';

// Type definitions for update service
interface UpdateInfo {
  isUpdateAvailable: boolean;
  isUpdatePending: boolean;
  isChecking: boolean;
  lastCheckTime: Date | null;
  error: string | null;
}

interface UpdateService {
  checkForUpdates: () => Promise<boolean>;
  downloadUpdate: () => Promise<boolean>;
  reloadApp: () => Promise<void>;
  getUpdateInfo: () => UpdateInfo;
  isUpdatesEnabled: () => boolean;
}

class UpdateServiceImpl implements UpdateService {
  private updateInfo: UpdateInfo = {
    isUpdateAvailable: false,
    isUpdatePending: false,
    isChecking: false,
    lastCheckTime: null,
    error: null,
  };

  private isDevMode = process.env.EXPO_PUBLIC_DEV_MODE === 'true';
  private updatesDisabled = process.env.EXPO_PUBLIC_DISABLE_UPDATES === 'true';

  constructor() {
    // Initialize update service
    this.initializeUpdateService();
  }

  private async initializeUpdateService() {
    try {
      // Only initialize updates in production and when enabled
      if (this.isUpdatesEnabled()) {
        // Dynamically import Updates to avoid bundling issues
        const Updates = await this.getUpdatesModule();
        if (Updates) {
          // Set up update listeners if available
          this.setupUpdateListeners(Updates);
        }
      }
    } catch (error) {
      console.warn('Update service initialization failed:', error);
      this.updateInfo.error = 'Update service unavailable';
    }
  }

  private async getUpdatesModule() {
    try {
      // Dynamically import expo-updates to avoid errors when disabled
      const Updates = await import('expo-updates');
      return Updates.default || Updates;
    } catch (error) {
      // This is expected when expo-updates is not installed or disabled
      console.log('expo-updates module not available (this is normal in development)');
      return null;
    }
  }

  private setupUpdateListeners(Updates: any) {
    try {
      // Set up update event listeners if the module supports them
      if (Updates.addListener) {
        Updates.addListener((event: any) => {
          if (event.type === 'updateAvailable') {
            this.updateInfo.isUpdateAvailable = true;
          } else if (event.type === 'updateDownloaded') {
            this.updateInfo.isUpdatePending = true;
          } else if (event.type === 'error') {
            this.updateInfo.error = event.message;
          }
        });
      }
    } catch (error) {
      console.warn('Failed to set up update listeners:', error);
    }
  }

  public isUpdatesEnabled(): boolean {
    // Updates are disabled in development or when explicitly disabled
    if (this.isDevMode || this.updatesDisabled) {
      return false;
    }

    // Updates are not supported on web
    if (Platform.OS === 'web') {
      return false;
    }

    return true;
  }

  public async checkForUpdates(): Promise<boolean> {
    if (!this.isUpdatesEnabled()) {
      console.log('Updates disabled - skipping update check');
      return false;
    }

    try {
      this.updateInfo.isChecking = true;
      this.updateInfo.error = null;

      const Updates = await this.getUpdatesModule();
      if (!Updates) {
        throw new Error('Updates module not available');
      }

      const update = await Updates.checkForUpdateAsync();
      this.updateInfo.isUpdateAvailable = update.isAvailable;
      this.updateInfo.lastCheckTime = new Date();

      return update.isAvailable;
    } catch (error) {
      console.warn('Failed to check for updates:', error);
      this.updateInfo.error = error instanceof Error ? error.message : 'Update check failed';
      return false;
    } finally {
      this.updateInfo.isChecking = false;
    }
  }

  public async downloadUpdate(): Promise<boolean> {
    if (!this.isUpdatesEnabled() || !this.updateInfo.isUpdateAvailable) {
      return false;
    }

    try {
      const Updates = await this.getUpdatesModule();
      if (!Updates) {
        throw new Error('Updates module not available');
      }

      await Updates.fetchUpdateAsync();
      this.updateInfo.isUpdatePending = true;
      return true;
    } catch (error) {
      console.warn('Failed to download update:', error);
      this.updateInfo.error = error instanceof Error ? error.message : 'Update download failed';
      return false;
    }
  }

  public async reloadApp(): Promise<void> {
    if (!this.isUpdatesEnabled()) {
      console.log('Updates disabled - cannot reload with update');
      return;
    }

    try {
      const Updates = await this.getUpdatesModule();
      if (!Updates) {
        throw new Error('Updates module not available');
      }

      await Updates.reloadAsync();
    } catch (error) {
      console.warn('Failed to reload app:', error);
      this.updateInfo.error = error instanceof Error ? error.message : 'App reload failed';
    }
  }

  public getUpdateInfo(): UpdateInfo {
    return { ...this.updateInfo };
  }
}

// Export singleton instance
export const updateService = new UpdateServiceImpl();

// Export hook for React components
export function useUpdateService() {
  return {
    checkForUpdates: updateService.checkForUpdates.bind(updateService),
    downloadUpdate: updateService.downloadUpdate.bind(updateService),
    reloadApp: updateService.reloadApp.bind(updateService),
    getUpdateInfo: updateService.getUpdateInfo.bind(updateService),
    isUpdatesEnabled: updateService.isUpdatesEnabled.bind(updateService),
  };
}

// Export types
export type { UpdateInfo, UpdateService };
