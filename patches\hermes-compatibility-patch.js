/**
 * Hermes Compatibility Patch
 * Fixes "Value is undefined, expected an Object" errors in Hermes engine
 * by providing polyfills and safe object handling
 */

import { Platform } from 'react-native';

// Check if we're running on Hermes
const isHermes = () => {
  return typeof HermesInternal === 'object' && HermesInternal !== null;
};

// Safe object creation and validation
const createSafeObject = (obj) => {
  if (obj === null || obj === undefined) {
    return {};
  }
  
  if (typeof obj !== 'object') {
    console.warn('⚠️ Expected object, got:', typeof obj);
    return {};
  }
  
  return obj;
};

// Safe property access
const safeGet = (obj, path, defaultValue = undefined) => {
  try {
    if (!obj || typeof obj !== 'object') {
      return defaultValue;
    }
    
    const keys = Array.isArray(path) ? path : path.split('.');
    let result = obj;
    
    for (const key of keys) {
      if (result === null || result === undefined) {
        return defaultValue;
      }
      result = result[key];
    }
    
    return result !== undefined ? result : defaultValue;
  } catch (error) {
    console.warn('⚠️ Safe property access failed:', error);
    return defaultValue;
  }
};

// Safe property setting
const safeSet = (obj, path, value) => {
  try {
    if (!obj || typeof obj !== 'object') {
      return false;
    }
    
    const keys = Array.isArray(path) ? path : path.split('.');
    const lastKey = keys.pop();
    let current = obj;
    
    for (const key of keys) {
      if (current[key] === null || current[key] === undefined) {
        current[key] = {};
      }
      current = current[key];
    }
    
    current[lastKey] = value;
    return true;
  } catch (error) {
    console.warn('⚠️ Safe property setting failed:', error);
    return false;
  }
};

// Object validation utilities
const validateObject = (obj, schema = {}) => {
  try {
    if (obj === null || obj === undefined) {
      return { valid: false, errors: ['Object is null or undefined'] };
    }
    
    if (typeof obj !== 'object') {
      return { valid: false, errors: [`Expected object, got ${typeof obj}`] };
    }
    
    const errors = [];
    
    // Check required properties
    for (const [key, config] of Object.entries(schema)) {
      if (config.required && !(key in obj)) {
        errors.push(`Missing required property: ${key}`);
      }
      
      if (key in obj && config.type && typeof obj[key] !== config.type) {
        errors.push(`Property ${key} should be ${config.type}, got ${typeof obj[key]}`);
      }
    }
    
    return { valid: errors.length === 0, errors };
  } catch (error) {
    return { valid: false, errors: [`Validation error: ${error.message}`] };
  }
};

// Hermes-specific polyfills
const applyHermesPolyfills = () => {
  if (!isHermes()) {
    return;
  }
  
  console.log('🔧 Applying Hermes compatibility patches...');
  
  // Polyfill for Object.fromEntries if missing
  if (!Object.fromEntries) {
    Object.fromEntries = function(entries) {
      const obj = {};
      for (const [key, value] of entries) {
        obj[key] = value;
      }
      return obj;
    };
  }
  
  // Polyfill for Array.prototype.flatMap if missing
  if (!Array.prototype.flatMap) {
    Array.prototype.flatMap = function(callback, thisArg) {
      return this.map(callback, thisArg).flat();
    };
  }
  
  // Polyfill for String.prototype.matchAll if missing
  if (!String.prototype.matchAll) {
    String.prototype.matchAll = function(regexp) {
      const matches = [];
      let match;
      const globalRegexp = new RegExp(regexp.source, regexp.flags + (regexp.global ? '' : 'g'));
      
      while ((match = globalRegexp.exec(this)) !== null) {
        matches.push(match);
        if (!regexp.global) break;
      }
      
      return matches[Symbol.iterator] ? matches : matches.values();
    };
  }
  
  console.log('✅ Hermes compatibility patches applied');
};

// Error handling for Hermes
const handleHermesError = (error, context = 'Unknown') => {
  console.error(`🚨 Hermes Error in ${context}:`, error);
  
  // Common Hermes error patterns
  if (error.message && error.message.includes('Value is undefined, expected an Object')) {
    console.warn('⚠️ Hermes object validation error - providing fallback');
    return {};
  }
  
  if (error.message && error.message.includes('Cannot read property')) {
    console.warn('⚠️ Hermes property access error - providing fallback');
    return null;
  }
  
  // Re-throw if we can't handle it
  throw error;
};

// Safe component wrapper for Hermes
const withHermesSafety = (Component) => {
  return function HermesSafeComponent(props) {
    try {
      // Validate props object
      const safeProps = createSafeObject(props);
      
      // Render component with safe props
      return React.createElement(Component, safeProps);
    } catch (error) {
      console.error('🚨 Component render error in Hermes:', error);
      
      // Return error fallback
      return React.createElement('div', {
        style: {
          padding: '20px',
          backgroundColor: '#ffebee',
          border: '1px solid #f44336',
          borderRadius: '4px',
          color: '#c62828',
        }
      }, `Component Error: ${error.message}`);
    }
  };
};

// Initialize Hermes compatibility
const initializeHermesCompatibility = () => {
  if (Platform.OS !== 'web' && isHermes()) {
    console.log('🔧 Initializing Hermes compatibility...');
    
    try {
      applyHermesPolyfills();
      
      // Set up global error handler for Hermes
      const originalErrorHandler = global.ErrorUtils?.getGlobalHandler();
      
      global.ErrorUtils?.setGlobalHandler((error, isFatal) => {
        try {
          handleHermesError(error, 'Global');
        } catch (handledError) {
          // If our handler fails, fall back to original
          if (originalErrorHandler) {
            originalErrorHandler(handledError, isFatal);
          }
        }
      });
      
      console.log('✅ Hermes compatibility initialized');
    } catch (error) {
      console.error('❌ Failed to initialize Hermes compatibility:', error);
    }
  }
};

// Auto-initialize when module is imported
if (Platform.OS !== 'web') {
  setTimeout(initializeHermesCompatibility, 0);
}

export {
  isHermes,
  createSafeObject,
  safeGet,
  safeSet,
  validateObject,
  handleHermesError,
  withHermesSafety,
  initializeHermesCompatibility,
};

export default {
  isHermes,
  createSafeObject,
  safeGet,
  safeSet,
  validateObject,
  handleHermesError,
  withHermesSafety,
  initializeHermesCompatibility,
};
