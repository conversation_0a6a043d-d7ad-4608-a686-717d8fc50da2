// Web polyfill for react-native-maps
import React from 'react';
import { View } from 'react-native';

// Mock MapView component for web
const MapView = React.forwardRef(({ children, ...props }, ref) => {
  return (
    <View {...props} ref={ref}>
      {children}
    </View>
  );
});

// Mock components
const Marker = ({ children, ...props }) => <View {...props}>{children}</View>;
const Polyline = (props) => <View {...props} />;
const Polygon = (props) => <View {...props} />;
const Circle = (props) => <View {...props} />;
const Heatmap = (props) => <View {...props} />;
const Callout = (props) => <View {...props} />;

// Mock constants
export const PROVIDER_GOOGLE = 'google';
export const PROVIDER_DEFAULT = 'default';

// Mock types (these will be ignored in JS but help with TS)
export const LatLng = {};
export const Region = {};
export const MapPressEvent = {};

export default MapView;
export { <PERSON><PERSON>, Polyline, Polygon, Circle, Heatmap, Callout };
