import React from 'react';
import { Platform } from 'react-native';

// Platform-specific imports
const PhotoCaptureNative = React.lazy(() => import('./PhotoCapture.native'));
const PhotoCaptureWeb = React.lazy(() => import('./PhotoCapture.web'));

interface PhotoCaptureProps {
  value?: string;
  onChange: (uri: string | null) => void;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
}

export default function PhotoCapture(props: PhotoCaptureProps) {
  const PhotoCaptureComponent = Platform.OS === 'web' ? PhotoCaptureWeb : PhotoCaptureNative;
  
  return (
    <React.Suspense fallback={null}>
      <PhotoCaptureComponent {...props} />
    </React.Suspense>
  );
}
