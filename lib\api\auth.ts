import { supabase, handleSupabaseError, TABLES } from '../supabase';
import { BaseApi } from './base';
import { 
  UserProfile, 
  UserProfileInsert, 
  UserProfileUpdate,
  ApiResponse 
} from '@/types/database';

export interface AuthCredentials {
  email: string;
  password: string;
}

export interface SignUpData extends AuthCredentials {
  fullName?: string;
  role?: 'admin' | 'manager' | 'collector' | 'viewer';
  organization?: string;
}

export interface AuthSession {
  user: any;
  session: any;
}

export interface PasswordResetRequest {
  email: string;
}

export interface PasswordUpdateRequest {
  currentPassword: string;
  newPassword: string;
}

/**
 * Authentication API Service
 * Handles user authentication, registration, and profile management
 */
export class AuthApi extends BaseApi {
  constructor() {
    super(TABLES.USER_PROFILES);
  }

  /**
   * Sign in with email and password
   */
  async signIn(credentials: AuthCredentials): Promise<ApiResponse<AuthSession>> {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email: credentials.email,
        password: credentials.password,
      });

      if (error) {
        handleSupabaseError(error, 'sign in');
      }

      // Get user profile
      let userProfile = null;
      if (data.user) {
        const profileResponse = await this.getProfile(data.user.id);
        userProfile = profileResponse.data;
      }

      return {
        data: {
          user: { ...data.user, profile: userProfile },
          session: data.session
        },
        error: null
      };
    } catch (error) {
      console.error('Sign in failed:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Sign in failed'
      };
    }
  }

  /**
   * Sign up with email and password
   */
  async signUp(signUpData: SignUpData): Promise<ApiResponse<AuthSession>> {
    try {
      const { data, error } = await supabase.auth.signUp({
        email: signUpData.email,
        password: signUpData.password,
        options: {
          data: {
            full_name: signUpData.fullName,
            role: signUpData.role || 'collector',
            organization: signUpData.organization,
          }
        }
      });

      if (error) {
        handleSupabaseError(error, 'sign up');
      }

      return {
        data: {
          user: data.user,
          session: data.session
        },
        error: null
      };
    } catch (error) {
      console.error('Sign up failed:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Sign up failed'
      };
    }
  }

  /**
   * Sign out current user
   */
  async signOut(): Promise<ApiResponse<null>> {
    try {
      // Update last_seen timestamp before signing out
      const userId = await this.getCurrentUserId();
      if (userId) {
        await this.updateLastSeen(userId);
      }

      const { error } = await supabase.auth.signOut();

      if (error) {
        handleSupabaseError(error, 'sign out');
      }

      return {
        data: null,
        error: null
      };
    } catch (error) {
      console.error('Sign out failed:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Sign out failed'
      };
    }
  }

  /**
   * Get current session
   */
  async getSession(): Promise<ApiResponse<AuthSession>> {
    try {
      const { data, error } = await supabase.auth.getSession();

      if (error) {
        handleSupabaseError(error, 'get session');
      }

      // Get user profile if session exists
      let userProfile = null;
      if (data.session?.user) {
        const profileResponse = await this.getProfile(data.session.user.id);
        userProfile = profileResponse.data;
      }

      return {
        data: {
          user: data.session?.user ? { ...data.session.user, profile: userProfile } : null,
          session: data.session
        },
        error: null
      };
    } catch (error) {
      console.error('Get session failed:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to get session'
      };
    }
  }

  /**
   * Reset password
   */
  async resetPassword(request: PasswordResetRequest): Promise<ApiResponse<null>> {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(request.email, {
        redirectTo: `${window.location.origin}/reset-password`,
      });

      if (error) {
        handleSupabaseError(error, 'reset password');
      }

      return {
        data: null,
        error: null
      };
    } catch (error) {
      console.error('Reset password failed:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Password reset failed'
      };
    }
  }

  /**
   * Update password
   */
  async updatePassword(request: PasswordUpdateRequest): Promise<ApiResponse<null>> {
    try {
      const { error } = await supabase.auth.updateUser({
        password: request.newPassword
      });

      if (error) {
        handleSupabaseError(error, 'update password');
      }

      return {
        data: null,
        error: null
      };
    } catch (error) {
      console.error('Update password failed:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Password update failed'
      };
    }
  }

  /**
   * Get user profile by ID
   */
  async getProfile(userId: string): Promise<ApiResponse<UserProfile>> {
    return this.handleResponse<UserProfile>(
      supabase
        .from(TABLES.USER_PROFILES)
        .select('*')
        .eq('id', userId)
        .single(),
      'get user profile'
    );
  }

  /**
   * Update user profile
   */
  async updateProfile(
    userId: string, 
    updates: UserProfileUpdate
  ): Promise<ApiResponse<UserProfile>> {
    return this.handleResponse<UserProfile>(
      supabase
        .from(TABLES.USER_PROFILES)
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)
        .select()
        .single(),
      'update user profile'
    );
  }

  /**
   * Create user profile (usually called automatically via trigger)
   */
  async createProfile(profile: UserProfileInsert): Promise<ApiResponse<UserProfile>> {
    return this.handleResponse<UserProfile>(
      supabase
        .from(TABLES.USER_PROFILES)
        .insert(profile)
        .select()
        .single(),
      'create user profile'
    );
  }

  /**
   * Update last seen timestamp
   */
  async updateLastSeen(userId: string): Promise<ApiResponse<UserProfile>> {
    return this.handleResponse<UserProfile>(
      supabase
        .from(TABLES.USER_PROFILES)
        .update({ 
          last_seen: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)
        .select()
        .single(),
      'update last seen'
    );
  }

  /**
   * Get all users (admin only)
   */
  async getAllUsers(
    page: number = 1,
    pageSize: number = 20,
    filters: {
      role?: string;
      organization?: string;
      isActive?: boolean;
      search?: string;
    } = {}
  ) {
    // Check admin permission
    const isAdmin = await this.checkUserRole(['admin']);
    if (!isAdmin) {
      return {
        data: null,
        error: 'Unauthorized: Admin access required',
        page,
        pageSize,
        totalPages: 0,
        totalCount: 0
      };
    }

    const from = (page - 1) * pageSize;
    const to = from + pageSize - 1;

    let query = supabase
      .from(TABLES.USER_PROFILES)
      .select('*', { count: 'exact' })
      .range(from, to)
      .order('created_at', { ascending: false });

    // Apply filters
    if (filters.role) {
      query = query.eq('role', filters.role);
    }
    if (filters.organization) {
      query = query.eq('organization', filters.organization);
    }
    if (filters.isActive !== undefined) {
      query = query.eq('is_active', filters.isActive);
    }
    if (filters.search) {
      query = query.or(`full_name.ilike.%${filters.search}%,email.ilike.%${filters.search}%`);
    }

    return this.handlePaginatedResponse<UserProfile>(
      query,
      'get all users',
      page,
      pageSize
    );
  }

  /**
   * Deactivate user (admin only)
   */
  async deactivateUser(userId: string): Promise<ApiResponse<UserProfile>> {
    const isAdmin = await this.checkUserRole(['admin']);
    if (!isAdmin) {
      return {
        data: null,
        error: 'Unauthorized: Admin access required'
      };
    }

    return this.handleResponse<UserProfile>(
      supabase
        .from(TABLES.USER_PROFILES)
        .update({ 
          is_active: false,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)
        .select()
        .single(),
      'deactivate user'
    );
  }

  /**
   * Update user role (admin only)
   */
  async updateUserRole(
    userId: string, 
    role: 'admin' | 'manager' | 'collector' | 'viewer'
  ): Promise<ApiResponse<UserProfile>> {
    const isAdmin = await this.checkUserRole(['admin']);
    if (!isAdmin) {
      return {
        data: null,
        error: 'Unauthorized: Admin access required'
      };
    }

    return this.handleResponse<UserProfile>(
      supabase
        .from(TABLES.USER_PROFILES)
        .update({ 
          role,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)
        .select()
        .single(),
      'update user role'
    );
  }

  /**
   * Listen to auth state changes
   */
  onAuthStateChange(callback: (event: string, session: any) => void) {
    return supabase.auth.onAuthStateChange(callback);
  }

  /**
   * Get current user ID (convenience method)
   */
  async getCurrentUser(): Promise<string | null> {
    return this.getCurrentUserId();
  }

  /**
   * Check if user is authenticated
   */
  async isAuthenticated(): Promise<boolean> {
    const { data } = await supabase.auth.getSession();
    return !!data.session;
  }

  /**
   * Refresh session
   */
  async refreshSession(): Promise<ApiResponse<AuthSession>> {
    try {
      const { data, error } = await supabase.auth.refreshSession();

      if (error) {
        handleSupabaseError(error, 'refresh session');
      }

      return {
        data: {
          user: data.user,
          session: data.session
        },
        error: null
      };
    } catch (error) {
      console.error('Refresh session failed:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Session refresh failed'
      };
    }
  }
}

// Export singleton instance
export const authApi = new AuthApi();
export default authApi;
