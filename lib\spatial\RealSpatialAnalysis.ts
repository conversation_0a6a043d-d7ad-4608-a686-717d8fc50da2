/**
 * Real Spatial Analysis Implementation using Turf.js
 * Provides actual geospatial analysis capabilities for the GIS application
 */

import * as turf from '@turf/turf';
import { GIS<PERSON><PERSON>ure, MapLayer, AnalysisResult } from '@/types/gis';

export interface SpatialAnalysisOptions {
  distance?: number;
  units?: 'meters' | 'kilometers' | 'miles' | 'feet';
  steps?: number;
  dissolve?: boolean;
  preserveAttributes?: boolean;
  tolerance?: number;
  operation?: 'intersects' | 'contains' | 'within' | 'touches' | 'crosses';
  joinType?: 'one_to_one' | 'one_to_many';
  keepAllTargets?: boolean;
  searchRadius?: number;
  maxResults?: number;
  includeAngles?: boolean;
}

/**
 * Convert GISFeature to GeoJSON Feature for Turf.js
 */
function gisFeatureToGeoJSON(feature: GISFeature): turf.Feature {
  let geometry: any;
  
  switch (feature.type) {
    case 'point':
      geometry = {
        type: 'Point',
        coordinates: feature.coordinates.length > 0 ? [feature.coordinates[0].longitude, feature.coordinates[0].latitude] : [0, 0]
      };
      break;
    case 'line':
      geometry = {
        type: 'LineString',
        coordinates: feature.coordinates.map(coord => [coord.longitude, coord.latitude])
      };
      break;
    case 'polygon':
      // Ensure polygon is closed
      const coords = feature.coordinates.map(coord => [coord.longitude, coord.latitude]);
      if (coords.length > 0 && (coords[0][0] !== coords[coords.length - 1][0] || coords[0][1] !== coords[coords.length - 1][1])) {
        coords.push(coords[0]);
      }
      geometry = {
        type: 'Polygon',
        coordinates: [coords]
      };
      break;
    default:
      throw new Error(`Unsupported geometry type: ${feature.type}`);
  }

  return {
    type: 'Feature',
    geometry,
    properties: feature.properties || {}
  };
}

/**
 * Convert GeoJSON Feature back to GISFeature
 */
function geoJSONToGISFeature(feature: turf.Feature, originalId?: string): GISFeature {
  const geometry = feature.geometry;
  let coordinates: Array<{ latitude: number; longitude: number }> = [];
  let type: GISFeature['type'] = 'point';

  switch (geometry.type) {
    case 'Point':
      coordinates = [{ longitude: geometry.coordinates[0], latitude: geometry.coordinates[1] }];
      type = 'point';
      break;
    case 'LineString':
      coordinates = geometry.coordinates.map((coord: number[]) => ({
        longitude: coord[0],
        latitude: coord[1]
      }));
      type = 'line';
      break;
    case 'Polygon':
      // Take the exterior ring
      coordinates = geometry.coordinates[0].map((coord: number[]) => ({
        longitude: coord[0],
        latitude: coord[1]
      }));
      type = 'polygon';
      break;
    default:
      throw new Error(`Unsupported GeoJSON geometry type: ${geometry.type}`);
  }

  return {
    id: originalId || `feature_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    type,
    coordinates,
    properties: feature.properties || {},
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    style: {
      strokeColor: '#3B82F6',
      strokeWidth: 2,
      fillColor: '#3B82F620',
    }
  };
}

/**
 * Extract features from a layer
 */
function extractFeaturesFromLayer(layer: MapLayer): GISFeature[] {
  if (!layer.data) {
    return [];
  }

  // Handle GeoJSON data
  if (layer.data.type === 'FeatureCollection') {
    return layer.data.features.map((feature: any, index: number) => {
      const gisFeature = geoJSONToGISFeature(feature, `${layer.id}_feature_${index}`);
      return gisFeature;
    });
  }

  // Handle direct features array
  if (Array.isArray(layer.data)) {
    return layer.data;
  }

  return [];
}

/**
 * Real Buffer Analysis using Turf.js
 */
export async function performBufferAnalysis(
  inputLayer: MapLayer,
  options: SpatialAnalysisOptions
): Promise<AnalysisResult> {
  const startTime = Date.now();

  try {
    const { distance = 100, units = 'meters', steps = 64, dissolve = false } = options;
    
    const inputFeatures = extractFeaturesFromLayer(inputLayer);
    if (inputFeatures.length === 0) {
      throw new Error('No features found in input layer');
    }

    const bufferedFeatures: GISFeature[] = [];

    for (const feature of inputFeatures) {
      try {
        const geoJSONFeature = gisFeatureToGeoJSON(feature);
        
        // Create buffer using Turf.js
        const buffered = turf.buffer(geoJSONFeature, distance, {
          units: units as any,
          steps
        });

        if (buffered) {
          const bufferedGISFeature = geoJSONToGISFeature(buffered, `${feature.id}_buffer`);
          bufferedGISFeature.properties = {
            ...feature.properties,
            originalId: feature.id,
            bufferDistance: distance,
            bufferUnits: units,
            area: turf.area(buffered),
            perimeter: turf.length(buffered, { units: 'meters' })
          };
          
          bufferedFeatures.push(bufferedGISFeature);
        }
      } catch (featureError) {
        console.warn(`Failed to buffer feature ${feature.id}:`, featureError);
      }
    }

    // Apply dissolve if requested
    let finalFeatures = bufferedFeatures;
    if (dissolve && bufferedFeatures.length > 1) {
      try {
        const geoJSONFeatures = bufferedFeatures.map(f => gisFeatureToGeoJSON(f));
        const featureCollection = turf.featureCollection(geoJSONFeatures);
        const dissolved = turf.dissolve(featureCollection);
        
        if (dissolved && dissolved.features.length > 0) {
          finalFeatures = dissolved.features.map((f, index) => 
            geoJSONToGISFeature(f, `dissolved_buffer_${index}`)
          );
        }
      } catch (dissolveError) {
        console.warn('Failed to dissolve buffers, returning individual buffers:', dissolveError);
      }
    }

    const endTime = Date.now();
    const executionTime = endTime - startTime;

    return {
      id: `buffer_${Date.now()}`,
      type: 'buffer',
      name: `Buffer Analysis (${distance} ${units})`,
      status: 'completed',
      progress: 100,
      parameters: options,
      result: {
        features: finalFeatures,
        summary: {
          inputFeatureCount: inputFeatures.length,
          outputFeatureCount: finalFeatures.length,
          totalArea: finalFeatures.reduce((sum, f) => sum + (f.properties.area || 0), 0),
          averageArea: finalFeatures.length > 0 ? 
            finalFeatures.reduce((sum, f) => sum + (f.properties.area || 0), 0) / finalFeatures.length : 0
        }
      },
      startTime: new Date(startTime).toISOString(),
      endTime: new Date(endTime).toISOString(),
      executionTime
    };

  } catch (error) {
    const endTime = Date.now();
    return {
      id: `buffer_${Date.now()}`,
      type: 'buffer',
      name: 'Buffer Analysis',
      status: 'failed',
      progress: 0,
      parameters: options,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      startTime: new Date(startTime).toISOString(),
      endTime: new Date(endTime).toISOString(),
      executionTime: endTime - startTime
    };
  }
}

/**
 * Real Clip Analysis using Turf.js
 */
export async function performClipAnalysis(
  inputLayer: MapLayer,
  clipLayer: MapLayer,
  options: SpatialAnalysisOptions
): Promise<AnalysisResult> {
  const startTime = Date.now();

  try {
    const { preserveAttributes = true } = options;
    
    const inputFeatures = extractFeaturesFromLayer(inputLayer);
    const clipFeatures = extractFeaturesFromLayer(clipLayer);
    
    if (inputFeatures.length === 0) {
      throw new Error('No features found in input layer');
    }
    if (clipFeatures.length === 0) {
      throw new Error('No features found in clip layer');
    }

    const clippedFeatures: GISFeature[] = [];

    // Use the first clip feature as the clipping boundary
    const clipBoundary = gisFeatureToGeoJSON(clipFeatures[0]);

    for (const feature of inputFeatures) {
      try {
        const geoJSONFeature = gisFeatureToGeoJSON(feature);
        
        // Perform intersection (clip) using Turf.js
        const intersection = turf.intersect(turf.featureCollection([geoJSONFeature]), turf.featureCollection([clipBoundary]));

        if (intersection && intersection.features.length > 0) {
          for (const intersectedFeature of intersection.features) {
            const clippedGISFeature = geoJSONToGISFeature(intersectedFeature, `${feature.id}_clipped`);
            clippedGISFeature.properties = preserveAttributes ? {
              ...feature.properties,
              clippedFrom: feature.id,
              originalArea: feature.type === 'polygon' ? turf.area(geoJSONFeature) : undefined,
              clippedArea: turf.area(intersectedFeature)
            } : {
              clippedFrom: feature.id,
              clippedArea: turf.area(intersectedFeature)
            };
            
            clippedFeatures.push(clippedGISFeature);
          }
        }
      } catch (featureError) {
        console.warn(`Failed to clip feature ${feature.id}:`, featureError);
      }
    }

    const endTime = Date.now();
    const executionTime = endTime - startTime;

    return {
      id: `clip_${Date.now()}`,
      type: 'clip',
      name: 'Clip Analysis',
      status: 'completed',
      progress: 100,
      parameters: options,
      result: {
        features: clippedFeatures,
        summary: {
          inputFeatureCount: inputFeatures.length,
          outputFeatureCount: clippedFeatures.length,
          totalClippedArea: clippedFeatures.reduce((sum, f) => sum + (f.properties.clippedArea || 0), 0)
        }
      },
      startTime: new Date(startTime).toISOString(),
      endTime: new Date(endTime).toISOString(),
      executionTime
    };

  } catch (error) {
    const endTime = Date.now();
    return {
      id: `clip_${Date.now()}`,
      type: 'clip',
      name: 'Clip Analysis',
      status: 'failed',
      progress: 0,
      parameters: options,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      startTime: new Date(startTime).toISOString(),
      endTime: new Date(endTime).toISOString(),
      executionTime: endTime - startTime
    };
  }
}

/**
 * Real Intersect Analysis using Turf.js
 */
export async function performIntersectAnalysis(
  inputLayer: MapLayer,
  overlayLayer: MapLayer,
  options: SpatialAnalysisOptions
): Promise<AnalysisResult> {
  const startTime = Date.now();

  try {
    const inputFeatures = extractFeaturesFromLayer(inputLayer);
    const overlayFeatures = extractFeaturesFromLayer(overlayLayer);

    if (inputFeatures.length === 0) {
      throw new Error('No features found in input layer');
    }
    if (overlayFeatures.length === 0) {
      throw new Error('No features found in overlay layer');
    }

    const intersectedFeatures: GISFeature[] = [];

    for (const inputFeature of inputFeatures) {
      for (const overlayFeature of overlayFeatures) {
        try {
          const inputGeoJSON = gisFeatureToGeoJSON(inputFeature);
          const overlayGeoJSON = gisFeatureToGeoJSON(overlayFeature);

          // Perform intersection using Turf.js
          const intersection = turf.intersect(
            turf.featureCollection([inputGeoJSON]),
            turf.featureCollection([overlayGeoJSON])
          );

          if (intersection && intersection.features.length > 0) {
            for (const intersectedFeature of intersection.features) {
              const intersectedGISFeature = geoJSONToGISFeature(
                intersectedFeature,
                `${inputFeature.id}_${overlayFeature.id}_intersect`
              );
              intersectedGISFeature.properties = {
                ...inputFeature.properties,
                ...overlayFeature.properties,
                inputFeatureId: inputFeature.id,
                overlayFeatureId: overlayFeature.id,
                intersectionArea: turf.area(intersectedFeature)
              };

              intersectedFeatures.push(intersectedGISFeature);
            }
          }
        } catch (featureError) {
          console.warn(`Failed to intersect features ${inputFeature.id} and ${overlayFeature.id}:`, featureError);
        }
      }
    }

    const endTime = Date.now();
    const executionTime = endTime - startTime;

    return {
      id: `intersect_${Date.now()}`,
      type: 'intersect',
      name: 'Intersect Analysis',
      status: 'completed',
      progress: 100,
      parameters: options,
      result: {
        features: intersectedFeatures,
        summary: {
          inputFeatureCount: inputFeatures.length,
          overlayFeatureCount: overlayFeatures.length,
          outputFeatureCount: intersectedFeatures.length,
          totalIntersectionArea: intersectedFeatures.reduce((sum, f) => sum + (f.properties.intersectionArea || 0), 0)
        }
      },
      startTime: new Date(startTime).toISOString(),
      endTime: new Date(endTime).toISOString(),
      executionTime
    };

  } catch (error) {
    const endTime = Date.now();
    return {
      id: `intersect_${Date.now()}`,
      type: 'intersect',
      name: 'Intersect Analysis',
      status: 'failed',
      progress: 0,
      parameters: options,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      startTime: new Date(startTime).toISOString(),
      endTime: new Date(endTime).toISOString(),
      executionTime: endTime - startTime
    };
  }
}

/**
 * Real Proximity Analysis using Turf.js
 */
export async function performProximityAnalysis(
  sourceLayer: MapLayer,
  targetLayer: MapLayer,
  options: SpatialAnalysisOptions
): Promise<AnalysisResult> {
  const startTime = Date.now();

  try {
    const { searchRadius = 1000, units = 'meters', maxResults = 1, includeAngles = false } = options;

    const sourceFeatures = extractFeaturesFromLayer(sourceLayer);
    const targetFeatures = extractFeaturesFromLayer(targetLayer);

    if (sourceFeatures.length === 0) {
      throw new Error('No features found in source layer');
    }
    if (targetFeatures.length === 0) {
      throw new Error('No features found in target layer');
    }

    const proximityResults: GISFeature[] = [];

    for (const sourceFeature of sourceFeatures) {
      try {
        const sourceGeoJSON = gisFeatureToGeoJSON(sourceFeature);
        const sourcePoint = turf.centroid(sourceGeoJSON);

        const nearbyFeatures: Array<{
          feature: GISFeature;
          distance: number;
          bearing?: number;
        }> = [];

        for (const targetFeature of targetFeatures) {
          const targetGeoJSON = gisFeatureToGeoJSON(targetFeature);
          const targetPoint = turf.centroid(targetGeoJSON);

          const distance = turf.distance(sourcePoint, targetPoint, { units: units as any });

          if (distance <= searchRadius) {
            const bearing = includeAngles ? turf.bearing(sourcePoint, targetPoint) : undefined;
            nearbyFeatures.push({
              feature: targetFeature,
              distance,
              bearing
            });
          }
        }

        // Sort by distance and take only maxResults
        nearbyFeatures.sort((a, b) => a.distance - b.distance);
        const closestFeatures = nearbyFeatures.slice(0, maxResults);

        // Create result feature for each nearby feature
        for (let i = 0; i < closestFeatures.length; i++) {
          const nearby = closestFeatures[i];
          const resultFeature: GISFeature = {
            ...sourceFeature,
            id: `${sourceFeature.id}_proximity_${nearby.feature.id}`,
            properties: {
              ...sourceFeature.properties,
              nearestFeatureId: nearby.feature.id,
              nearestFeatureName: nearby.feature.properties.name || `Feature ${nearby.feature.id}`,
              distance: nearby.distance,
              distanceUnits: units,
              rank: i + 1,
              ...(nearby.bearing !== undefined && { bearing: nearby.bearing })
            }
          };

          proximityResults.push(resultFeature);
        }
      } catch (featureError) {
        console.warn(`Failed to analyze proximity for feature ${sourceFeature.id}:`, featureError);
      }
    }

    const endTime = Date.now();
    const executionTime = endTime - startTime;

    return {
      id: `proximity_${Date.now()}`,
      type: 'proximity',
      name: 'Proximity Analysis',
      status: 'completed',
      progress: 100,
      parameters: options,
      result: {
        features: proximityResults,
        summary: {
          sourceFeatureCount: sourceFeatures.length,
          targetFeatureCount: targetFeatures.length,
          proximityPairsFound: proximityResults.length,
          averageDistance: proximityResults.length > 0 ?
            proximityResults.reduce((sum, f) => sum + f.properties.distance, 0) / proximityResults.length : 0,
          searchRadius,
          searchUnits: units
        }
      },
      startTime: new Date(startTime).toISOString(),
      endTime: new Date(endTime).toISOString(),
      executionTime
    };

  } catch (error) {
    const endTime = Date.now();
    return {
      id: `proximity_${Date.now()}`,
      type: 'proximity',
      name: 'Proximity Analysis',
      status: 'failed',
      progress: 0,
      parameters: options,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      startTime: new Date(startTime).toISOString(),
      endTime: new Date(endTime).toISOString(),
      executionTime: endTime - startTime
    };
  }
}

/**
 * Main function to execute spatial analysis based on type
 */
export async function executeSpatialAnalysis(
  analysisType: string,
  layers: MapLayer[],
  options: SpatialAnalysisOptions
): Promise<AnalysisResult> {
  switch (analysisType) {
    case 'buffer':
      if (layers.length < 1) {
        throw new Error('Buffer analysis requires at least 1 input layer');
      }
      return performBufferAnalysis(layers[0], options);

    case 'clip':
      if (layers.length < 2) {
        throw new Error('Clip analysis requires 2 input layers (input and clip boundary)');
      }
      return performClipAnalysis(layers[0], layers[1], options);

    case 'intersect':
      if (layers.length < 2) {
        throw new Error('Intersect analysis requires 2 input layers');
      }
      return performIntersectAnalysis(layers[0], layers[1], options);

    case 'proximity':
      if (layers.length < 2) {
        throw new Error('Proximity analysis requires 2 input layers (source and target)');
      }
      return performProximityAnalysis(layers[0], layers[1], options);

    default:
      throw new Error(`Unsupported analysis type: ${analysisType}`);
  }
}
