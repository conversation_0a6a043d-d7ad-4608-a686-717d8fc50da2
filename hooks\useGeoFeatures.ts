import { useState, useEffect, useCallback } from 'react';
import { GeoFeature } from '@/types';
import { Platform, Alert } from 'react-native';

interface UseGeoFeaturesReturn {
  geoFeatures: GeoFeature[];
  loading: boolean;
  error: string | null;
  createFeature: (feature: Omit<GeoFeature, 'id'>) => Promise<GeoFeature>;
  updateFeature: (id: string, updates: Partial<GeoFeature>) => Promise<void>;
  deleteFeature: (id: string) => Promise<void>;
  syncFeatures: () => Promise<void>;
  clearFeatures: () => Promise<void>;
}

const STORAGE_KEY = 'fieldsync_geofeatures';

export function useGeoFeatures(projectId?: string): UseGeoFeaturesReturn {
  const [geoFeatures, setGeoFeatures] = useState<GeoFeature[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load features from storage on mount
  useEffect(() => {
    loadFeatures();
  }, [projectId]);

  const loadFeatures = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      let features: GeoFeature[] = [];
      
      if (Platform.OS === 'web') {
        const stored = localStorage.getItem(STORAGE_KEY);
        if (stored) {
          const allFeatures = JSON.parse(stored);
          features = projectId 
            ? allFeatures.filter((f: GeoFeature) => f.projectId === projectId)
            : allFeatures;
        }
      } else {
        // For React Native, you would use AsyncStorage or SQLite
        // For now, we'll use an in-memory store
        features = [];
      }
      
      setGeoFeatures(features);
    } catch (err) {
      setError('Failed to load geo features');
      console.error('Error loading geo features:', err);
    } finally {
      setLoading(false);
    }
  }, [projectId]);

  const saveFeatures = useCallback(async (features: GeoFeature[]) => {
    try {
      if (Platform.OS === 'web') {
        // Load all features, update/add the ones for this project, save all
        const stored = localStorage.getItem(STORAGE_KEY);
        let allFeatures: GeoFeature[] = stored ? JSON.parse(stored) : [];
        
        if (projectId) {
          // Remove existing features for this project
          allFeatures = allFeatures.filter(f => f.projectId !== projectId);
          // Add updated features for this project
          allFeatures.push(...features);
        } else {
          allFeatures = features;
        }
        
        localStorage.setItem(STORAGE_KEY, JSON.stringify(allFeatures));
      }
    } catch (err) {
      console.error('Error saving geo features:', err);
      throw new Error('Failed to save geo features');
    }
  }, [projectId]);

  const createFeature = useCallback(async (featureData: Omit<GeoFeature, 'id'>): Promise<GeoFeature> => {
    const newFeature: GeoFeature = {
      ...featureData,
      id: `feature_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      createdAt: Date.now(),
      syncStatus: 'local',
    };

    const updatedFeatures = [...geoFeatures, newFeature];
    setGeoFeatures(updatedFeatures);
    
    try {
      await saveFeatures(updatedFeatures);
      return newFeature;
    } catch (err) {
      // Rollback on error
      setGeoFeatures(geoFeatures);
      throw err;
    }
  }, [geoFeatures, saveFeatures]);

  const updateFeature = useCallback(async (id: string, updates: Partial<GeoFeature>) => {
    const featureIndex = geoFeatures.findIndex(f => f.id === id);
    if (featureIndex === -1) {
      throw new Error('Feature not found');
    }

    const updatedFeature = {
      ...geoFeatures[featureIndex],
      ...updates,
      syncStatus: 'local' as const, // Mark as needing sync
    };

    const updatedFeatures = [...geoFeatures];
    updatedFeatures[featureIndex] = updatedFeature;
    setGeoFeatures(updatedFeatures);

    try {
      await saveFeatures(updatedFeatures);
    } catch (err) {
      // Rollback on error
      setGeoFeatures(geoFeatures);
      throw err;
    }
  }, [geoFeatures, saveFeatures]);

  const deleteFeature = useCallback(async (id: string) => {
    const updatedFeatures = geoFeatures.filter(f => f.id !== id);
    setGeoFeatures(updatedFeatures);

    try {
      await saveFeatures(updatedFeatures);
    } catch (err) {
      // Rollback on error
      setGeoFeatures(geoFeatures);
      throw err;
    }
  }, [geoFeatures, saveFeatures]);

  const syncFeatures = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      // In a real app, this would sync with your backend
      const localFeatures = geoFeatures.filter(f => f.syncStatus === 'local');
      
      if (localFeatures.length === 0) {
        Alert.alert('Sync Complete', 'All features are already synced.');
        return;
      }

      // Simulate sync process
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Mark features as synced
      const syncedFeatures = geoFeatures.map(feature => ({
        ...feature,
        syncStatus: 'synced' as const,
      }));

      setGeoFeatures(syncedFeatures);
      await saveFeatures(syncedFeatures);

      Alert.alert('Sync Complete', `Successfully synced ${localFeatures.length} feature(s).`);
    } catch (err) {
      setError('Failed to sync features');
      Alert.alert('Sync Failed', 'Unable to sync features. Please try again.');
      console.error('Sync error:', err);
    } finally {
      setLoading(false);
    }
  }, [geoFeatures, saveFeatures]);

  const clearFeatures = useCallback(async () => {
    Alert.alert(
      'Clear All Features',
      'Are you sure you want to delete all geo features? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear All',
          style: 'destructive',
          onPress: async () => {
            setGeoFeatures([]);
            try {
              if (projectId) {
                // Only clear features for this project
                const stored = localStorage.getItem(STORAGE_KEY);
                if (stored) {
                  const allFeatures = JSON.parse(stored);
                  const otherFeatures = allFeatures.filter((f: GeoFeature) => f.projectId !== projectId);
                  localStorage.setItem(STORAGE_KEY, JSON.stringify(otherFeatures));
                }
              } else {
                // Clear all features
                await saveFeatures([]);
              }
            } catch (err) {
              console.error('Error clearing features:', err);
            }
          },
        },
      ]
    );
  }, [projectId, saveFeatures]);

  return {
    geoFeatures,
    loading,
    error,
    createFeature,
    updateFeature,
    deleteFeature,
    syncFeatures,
    clearFeatures,
  };
}
