import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Platform,
  Modal,
  Dimensions,
} from 'react-native';
import { useTheme } from '@/hooks/useTheme';
import { 
  Video as VideoIcon, 
  Camera as CameraIcon, 
  Play, 
  Pause, 
  Square, 
  RotateCcw, 
  XCircle,
  Download,
  Trash2,
  Volume2,
  VolumeX,
} from 'lucide-react-native';
import { Camera } from 'expo-camera';
import { Video } from 'expo-av';
import * as FileSystem from 'expo-file-system';

interface VideoRecorderProps {
  value?: string;
  onChange: (uri: string, metadata?: any) => void;
  placeholder?: string;
  required?: boolean;
  error?: string;
  disabled?: boolean;
  maxDuration?: number; // in seconds
  enablePlayback?: boolean;
  allowRetake?: boolean;
}

export default function VideoRecorder({
  value = '',
  onChange,
  placeholder = 'Record or select video',
  required = false,
  error,
  disabled = false,
  maxDuration = 60, // 1 minute default
  enablePlayback = true,
  allowRetake = true,
}: VideoRecorderProps) {
  const { theme } = useTheme();
  const [hasCameraPermission, setHasCameraPermission] = useState<boolean | null>(null);
  const [hasAudioPermission, setHasAudioPermission] = useState<boolean | null>(null);
  const [showRecorder, setShowRecorder] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [recordingDuration, setRecordingDuration] = useState(0);
  const [videoMetadata, setVideoMetadata] = useState<{
    duration: number;
    size: number;
    timestamp: number;
  } | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [playbackPosition, setPlaybackPosition] = useState(0);
  const [playbackDuration, setPlaybackDuration] = useState(0);
  const [isMuted, setIsMuted] = useState(false);

  const cameraRef = useRef<Camera>(null);
  const videoRef = useRef<Video>(null);
  const recordingTimer = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    requestPermissions();
    return () => {
      if (recordingTimer.current) {
        clearInterval(recordingTimer.current);
      }
    };
  }, []);

  const requestPermissions = async () => {
    const cameraResult = await Camera.requestCameraPermissionsAsync();
    const audioResult = await Camera.requestMicrophonePermissionsAsync();
    
    setHasCameraPermission(cameraResult.status === 'granted');
    setHasAudioPermission(audioResult.status === 'granted');
  };

  const handleRecordPress = async () => {
    if (!hasCameraPermission || !hasAudioPermission) {
      Alert.alert(
        'Permissions Required',
        'Camera and microphone permissions are needed to record video'
      );
      return;
    }
    
    setShowRecorder(true);
  };

  const startRecording = async () => {
    if (!cameraRef.current) return;

    try {
      const video = await cameraRef.current.recordAsync({
        quality: Camera.Constants.VideoQuality['720p'],
        maxDuration: maxDuration,
        mute: false,
      });

      setIsRecording(true);
      setRecordingDuration(0);
      
      // Start timer
      recordingTimer.current = setInterval(() => {
        setRecordingDuration(prev => {
          const newDuration = prev + 1;
          if (newDuration >= maxDuration) {
            stopRecording();
          }
          return newDuration;
        });
      }, 1000);

    } catch (error) {
      Alert.alert('Error', 'Failed to start recording');
    }
  };

  const stopRecording = async () => {
    if (!cameraRef.current) return;

    try {
      cameraRef.current.stopRecording();
      setIsRecording(false);
      
      if (recordingTimer.current) {
        clearInterval(recordingTimer.current);
        recordingTimer.current = null;
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to stop recording');
    }
  };

  const handleRecordingComplete = async (video: any) => {
    try {
      // Get file info
      const fileInfo = await FileSystem.getInfoAsync(video.uri);
      
      const metadata = {
        duration: recordingDuration,
        size: fileInfo.size || 0,
        timestamp: Date.now(),
      };

      setVideoMetadata(metadata);
      onChange(video.uri, metadata);
      setShowRecorder(false);
      setRecordingDuration(0);
      
      Alert.alert('Success', 'Video recorded successfully!');
    } catch (error) {
      Alert.alert('Error', 'Failed to process recorded video');
    }
  };

  const handlePlayPause = async () => {
    if (!videoRef.current) return;

    try {
      if (isPlaying) {
        await videoRef.current.pauseAsync();
      } else {
        await videoRef.current.playAsync();
      }
      setIsPlaying(!isPlaying);
    } catch (error) {
      console.error('Playback error:', error);
    }
  };

  const handlePlaybackStatusUpdate = (status: any) => {
    if (status.isLoaded) {
      setPlaybackPosition(status.positionMillis || 0);
      setPlaybackDuration(status.durationMillis || 0);
      
      if (status.didJustFinish) {
        setIsPlaying(false);
        setPlaybackPosition(0);
      }
    }
  };

  const handleRetake = () => {
    Alert.alert(
      'Retake Video',
      'Are you sure you want to retake this video? The current recording will be lost.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Retake',
          style: 'destructive',
          onPress: () => {
            onChange('');
            setVideoMetadata(null);
            setPlaybackPosition(0);
            setPlaybackDuration(0);
            setIsPlaying(false);
          },
        },
      ]
    );
  };

  const handleDelete = () => {
    Alert.alert(
      'Delete Video',
      'Are you sure you want to delete this video?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            onChange('');
            setVideoMetadata(null);
            setPlaybackPosition(0);
            setPlaybackDuration(0);
            setIsPlaying(false);
          },
        },
      ]
    );
  };

  const formatDuration = (milliseconds: number) => {
    const totalSeconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  const renderRecorderModal = () => (
    <Modal
      visible={showRecorder}
      animationType="slide"
      presentationStyle="fullScreen"
    >
      <View style={styles.recorderContainer}>
        <Camera
          ref={cameraRef}
          style={styles.camera}
          facing="back"
          flashMode="off"
          onRecordingStatusUpdate={(status) => {
            if (status.isRecording === false && status.uri) {
              handleRecordingComplete(status);
            }
          }}
        >
          <View style={styles.recorderOverlay}>
            {/* Header */}
            <View style={styles.recorderHeader}>
              <TouchableOpacity
                style={[styles.headerButton, { backgroundColor: theme.colors.error }]}
                onPress={() => {
                  if (isRecording) {
                    stopRecording();
                  }
                  setShowRecorder(false);
                }}
              >
                <XCircle size={24} color="white" />
              </TouchableOpacity>
              
              <View style={styles.recordingInfo}>
                {isRecording && (
                  <>
                    <View style={styles.recordingIndicator} />
                    <Text style={styles.recordingText}>
                      {formatDuration(recordingDuration * 1000)} / {formatDuration(maxDuration * 1000)}
                    </Text>
                  </>
                )}
              </View>
              
              <View style={styles.headerButton} />
            </View>

            {/* Footer Controls */}
            <View style={styles.recorderFooter}>
              <View style={styles.recordingControls}>
                <TouchableOpacity
                  style={[
                    styles.recordButton,
                    {
                      backgroundColor: isRecording ? theme.colors.error : theme.colors.primary,
                    }
                  ]}
                  onPress={isRecording ? stopRecording : startRecording}
                >
                  {isRecording ? (
                    <Square size={32} color="white" />
                  ) : (
                    <VideoIcon size={32} color="white" />
                  )}
                </TouchableOpacity>
              </View>
              
              <Text style={styles.recordingInstructions}>
                {isRecording 
                  ? 'Tap to stop recording' 
                  : 'Tap to start recording'
                }
              </Text>
            </View>
          </View>
        </Camera>
      </View>
    </Modal>
  );

  const renderVideoPlayer = () => {
    if (!value) return null;

    return (
      <View style={[styles.playerContainer, { backgroundColor: theme.colors.card }]}>
        <Video
          ref={videoRef}
          source={{ uri: value }}
          style={styles.videoPlayer}
          useNativeControls={false}
          resizeMode="contain"
          isLooping={false}
          isMuted={isMuted}
          onPlaybackStatusUpdate={handlePlaybackStatusUpdate}
        />
        
        <View style={styles.playerOverlay}>
          <TouchableOpacity
            style={[styles.playButton, { backgroundColor: theme.colors.primary }]}
            onPress={handlePlayPause}
          >
            {isPlaying ? (
              <Pause size={24} color="white" />
            ) : (
              <Play size={24} color="white" />
            )}
          </TouchableOpacity>
        </View>

        <View style={styles.playerControls}>
          <TouchableOpacity
            style={[styles.controlButton, { backgroundColor: theme.colors.muted }]}
            onPress={() => setIsMuted(!isMuted)}
          >
            {isMuted ? (
              <VolumeX size={16} color="white" />
            ) : (
              <Volume2 size={16} color="white" />
            )}
          </TouchableOpacity>

          <View style={styles.progressInfo}>
            <Text style={[styles.progressText, { color: theme.colors.text }]}>
              {formatDuration(playbackPosition)} / {formatDuration(playbackDuration)}
            </Text>
          </View>

          {allowRetake && (
            <TouchableOpacity
              style={[styles.controlButton, { backgroundColor: theme.colors.warning }]}
              onPress={handleRetake}
            >
              <RotateCcw size={16} color="white" />
            </TouchableOpacity>
          )}

          <TouchableOpacity
            style={[styles.controlButton, { backgroundColor: theme.colors.error }]}
            onPress={handleDelete}
          >
            <Trash2 size={16} color="white" />
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  const renderVideoInfo = () => {
    if (!value || !videoMetadata) return null;

    return (
      <View style={styles.videoInfo}>
        <Text style={[styles.infoText, { color: theme.colors.muted }]}>
          Duration: {formatDuration(videoMetadata.duration * 1000)} • 
          Size: {formatFileSize(videoMetadata.size)} • 
          Recorded: {new Date(videoMetadata.timestamp).toLocaleString()}
        </Text>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {value ? (
        <>
          {enablePlayback && renderVideoPlayer()}
          {renderVideoInfo()}
        </>
      ) : (
        <TouchableOpacity
          style={[
            styles.recordArea,
            {
              borderColor: error ? theme.colors.error : theme.colors.border,
              backgroundColor: disabled ? theme.colors.muted + '20' : theme.colors.card,
            }
          ]}
          onPress={handleRecordPress}
          disabled={disabled}
        >
          <VideoIcon size={32} color={theme.colors.muted} />
          <Text style={[styles.recordText, { color: theme.colors.text }]}>
            {placeholder}
          </Text>
          <Text style={[styles.recordHint, { color: theme.colors.muted }]}>
            Tap to record (max {maxDuration}s)
          </Text>
        </TouchableOpacity>
      )}

      {error && (
        <Text style={[styles.errorText, { color: theme.colors.error }]}>
          {error}
        </Text>
      )}

      {required && !value && (
        <Text style={[styles.requiredText, { color: theme.colors.muted }]}>
          * Required field
        </Text>
      )}

      {renderRecorderModal()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  recordArea: {
    borderWidth: 2,
    borderStyle: 'dashed',
    borderRadius: 12,
    paddingVertical: 32,
    paddingHorizontal: 16,
    alignItems: 'center',
    gap: 8,
  },
  recordText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    textAlign: 'center',
  },
  recordHint: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
  },
  playerContainer: {
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: 8,
  },
  videoPlayer: {
    width: '100%',
    height: 200,
  },
  playerOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 44,
    justifyContent: 'center',
    alignItems: 'center',
  },
  playButton: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
  },
  playerControls: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    gap: 8,
  },
  controlButton: {
    padding: 8,
    borderRadius: 6,
  },
  progressInfo: {
    flex: 1,
    alignItems: 'center',
  },
  progressText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
  },
  videoInfo: {
    marginTop: 4,
  },
  infoText: {
    fontSize: 11,
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
  },
  errorText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    marginTop: 4,
  },
  requiredText: {
    fontSize: 11,
    fontFamily: 'Inter-Regular',
    marginTop: 4,
  },
  recorderContainer: {
    flex: 1,
  },
  camera: {
    flex: 1,
  },
  recorderOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.2)',
    justifyContent: 'space-between',
  },
  recorderHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
    paddingBottom: 16,
  },
  headerButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  recordingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  recordingIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: 'red',
  },
  recordingText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
  },
  recorderFooter: {
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingBottom: Platform.OS === 'ios' ? 40 : 20,
    gap: 16,
  },
  recordingControls: {
    alignItems: 'center',
  },
  recordButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  recordingInstructions: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: 'white',
    textAlign: 'center',
  },
});
