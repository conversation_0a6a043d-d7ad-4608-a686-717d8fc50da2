/**
 * Supabase configuration for React Native platforms
 * 
 * This configuration disables real-time features on mobile platforms
 * to avoid WebSocket-related Node.js dependencies.
 */

import { createClient } from '@supabase/supabase-js';
import { Database } from '@/types/database';

// Supabase configuration
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL || 'https://dxfzgqjqmqnbycgiriam.supabase.co';
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.pL-nM39Esfx8RFbM6jsX7Vr4WH0AynfnUwAq3RwMc_E';

// Create Supabase client with real-time completely disabled for mobile
const supabaseClient = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: undefined, // We'll handle storage in AuthProvider
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
  global: {
    headers: {
      'X-Client-Info': 'fieldsyncpro-mobile',
    },
  },
});

// Override realtime methods to prevent any WebSocket usage
const originalChannel = supabaseClient.channel.bind(supabaseClient);
supabaseClient.channel = function(name: string, opts?: any) {
  console.warn('Real-time channels not available on mobile platform');
  return {
    on: () => ({ subscribe: () => null }),
    subscribe: () => null,
    unsubscribe: () => Promise.resolve(),
    send: () => Promise.resolve(),
  } as any;
};

const originalRemoveChannel = supabaseClient.removeChannel.bind(supabaseClient);
supabaseClient.removeChannel = function(channel: any) {
  console.warn('Real-time channels not available on mobile platform');
  return Promise.resolve();
};

export const supabase = supabaseClient;

// Configuration checker
export const isSupabaseConfigured = (): boolean => {
  return !!(supabaseUrl && supabaseAnonKey && 
    supabaseUrl !== 'your-supabase-url' && 
    supabaseAnonKey !== 'your-supabase-anon-key');
};

// Development mode checker
export const isDevelopmentMode = (): boolean => {
  return !isSupabaseConfigured();
};

// Real-time availability checker (always false on mobile)
export const isRealtimeAvailable = (): boolean => {
  return false;
};

// Export types
export type { Database } from '@/types/database';
