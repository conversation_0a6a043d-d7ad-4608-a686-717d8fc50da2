import React, { useRef, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Dimensions,
  PanResponder,
  Platform,
} from 'react-native';
import { useTheme } from '@/hooks/useTheme';
import { Edit3, Trash2, RotateCcw, Check } from 'lucide-react-native';
import Svg, { Path } from 'react-native-svg';
import * as FileSystem from 'expo-file-system';

interface SignatureCaptureProps {
  value?: string; // Base64 string or URI of signature
  onChange: (signature: string | null) => void;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  strokeColor?: string;
  strokeWidth?: number;
}

export default function SignatureCapture({
  value,
  onChange,
  placeholder = 'Add signature',
  required = false,
  disabled = false,
  strokeColor = '#000000',
  strokeWidth = 2,
}: SignatureCaptureProps) {
  const { theme } = useTheme();
  const [isDrawing, setIsDrawing] = useState(false);
  const [paths, setPaths] = useState<string[]>([]);
  const [currentPath, setCurrentPath] = useState('');
  const [showSignaturePad, setShowSignaturePad] = useState(false);
  
  const screenWidth = Dimensions.get('window').width;
  const padWidth = screenWidth - 32;
  const padHeight = 200;

  const panResponder = PanResponder.create({
    onStartShouldSetPanResponder: () => true,
    onMoveShouldSetPanResponder: () => true,

    onPanResponderGrant: (event) => {
      if (disabled) return;
      
      const { locationX, locationY } = event.nativeEvent;
      const newPath = `M${locationX},${locationY}`;
      setCurrentPath(newPath);
      setIsDrawing(true);
    },

    onPanResponderMove: (event) => {
      if (!isDrawing || disabled) return;
      
      const { locationX, locationY } = event.nativeEvent;
      setCurrentPath(prev => `${prev} L${locationX},${locationY}`);
    },

    onPanResponderRelease: () => {
      if (!isDrawing || disabled) return;
      
      setPaths(prev => [...prev, currentPath]);
      setCurrentPath('');
      setIsDrawing(false);
    },
  });

  const clearSignature = () => {
    setPaths([]);
    setCurrentPath('');
    setIsDrawing(false);
  };

  const saveSignature = async () => {
    if (paths.length === 0 && !currentPath) {
      Alert.alert('No Signature', 'Please draw a signature before saving.');
      return;
    }

    try {
      // Create SVG string
      const allPaths = currentPath ? [...paths, currentPath] : paths;
      const svgString = `
        <svg width="${padWidth}" height="${padHeight}" xmlns="http://www.w3.org/2000/svg">
          ${allPaths.map(path => `<path d="${path}" stroke="${strokeColor}" stroke-width="${strokeWidth}" fill="none" stroke-linecap="round" stroke-linejoin="round" />`).join('')}
        </svg>
      `;

      // Convert to base64 for storage
      const base64Signature = `data:image/svg+xml;base64,${Buffer.from(svgString).toString('base64')}`;
      
      onChange(base64Signature);
      setShowSignaturePad(false);
    } catch (error) {
      Alert.alert('Error', 'Failed to save signature');
      console.error('Signature save error:', error);
    }
  };

  const removeSignature = () => {
    Alert.alert(
      'Remove Signature',
      'Are you sure you want to remove this signature?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: () => onChange(null),
        },
      ]
    );
  };

  const SignaturePadModal = () => (
    <View style={[styles.modal, { backgroundColor: theme.colors.background + 'F0' }]}>
      <View style={[styles.modalContent, { backgroundColor: theme.colors.card }]}>
        <View style={[styles.modalHeader, { borderBottomColor: theme.colors.border }]}>
          <Text style={[styles.modalTitle, { color: theme.colors.text }]}>
            Draw Your Signature
          </Text>
          <TouchableOpacity
            onPress={() => setShowSignaturePad(false)}
            style={styles.closeButton}
          >
            <Text style={[styles.closeText, { color: theme.colors.muted }]}>Cancel</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.signaturePadContainer}>
          <View
            style={[
              styles.signaturePad,
              { 
                backgroundColor: theme.colors.background,
                borderColor: theme.colors.border,
                width: padWidth,
                height: padHeight,
              }
            ]}
            {...panResponder.panHandlers}
          >
            <Svg width={padWidth} height={padHeight} style={StyleSheet.absoluteFillObject}>
              {paths.map((path, index) => (
                <Path
                  key={index}
                  d={path}
                  stroke={strokeColor}
                  strokeWidth={strokeWidth}
                  fill="none"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              ))}
              {currentPath && (
                <Path
                  d={currentPath}
                  stroke={strokeColor}
                  strokeWidth={strokeWidth}
                  fill="none"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              )}
            </Svg>
            
            {paths.length === 0 && !currentPath && (
              <View style={styles.placeholderOverlay}>
                <Text style={[styles.placeholderText, { color: theme.colors.muted }]}>
                  Draw your signature here
                </Text>
              </View>
            )}
          </View>
        </View>

        <View style={styles.signatureControls}>
          <TouchableOpacity
            style={[styles.controlButton, { backgroundColor: theme.colors.muted }]}
            onPress={clearSignature}
          >
            <Trash2 size={16} color="white" />
            <Text style={styles.controlButtonText}>Clear</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.controlButton, { backgroundColor: theme.colors.primary }]}
            onPress={saveSignature}
          >
            <Check size={16} color="white" />
            <Text style={styles.controlButtonText}>Save Signature</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );

  if (value) {
    return (
      <View style={[styles.container, { borderColor: theme.colors.border }]}>
        <View style={styles.signatureDisplay}>
          <Text style={[styles.signatureLabel, { color: theme.colors.text }]}>
            Signature Captured
          </Text>
          
          {/* Display signature preview */}
          <View style={[styles.signaturePreview, { backgroundColor: theme.colors.background, borderColor: theme.colors.border }]}>
            <Edit3 size={24} color={theme.colors.primary} />
            <Text style={[styles.previewText, { color: theme.colors.muted }]}>
              Signature saved
            </Text>
          </View>
        </View>

        <View style={styles.signatureActions}>
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: theme.colors.secondary }]}
            onPress={() => setShowSignaturePad(true)}
            disabled={disabled}
          >
            <RotateCcw size={16} color="white" />
            <Text style={styles.actionButtonText}>Replace</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: theme.colors.error }]}
            onPress={removeSignature}
            disabled={disabled}
          >
            <Trash2 size={16} color="white" />
            <Text style={styles.actionButtonText}>Remove</Text>
          </TouchableOpacity>
        </View>

        {showSignaturePad && <SignaturePadModal />}
      </View>
    );
  }

  return (
    <>
      <TouchableOpacity
        style={[
          styles.captureButton,
          {
            backgroundColor: theme.colors.background,
            borderColor: theme.colors.border,
            opacity: disabled ? 0.5 : 1,
          },
        ]}
        onPress={() => setShowSignaturePad(true)}
        disabled={disabled}
      >
        <Edit3 size={32} color={theme.colors.muted} />
        <Text style={[styles.captureText, { color: theme.colors.muted }]}>
          {placeholder}
        </Text>
        {required && (
          <Text style={[styles.requiredText, { color: theme.colors.error }]}>
            Required
          </Text>
        )}
      </TouchableOpacity>

      {showSignaturePad && <SignaturePadModal />}
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 8,
    borderWidth: 1,
    padding: 16,
    gap: 12,
  },
  signatureDisplay: {
    gap: 8,
  },
  signatureLabel: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
  },
  signaturePreview: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
    borderRadius: 8,
    borderWidth: 1,
    gap: 8,
  },
  previewText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
  },
  signatureActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    borderRadius: 6,
    gap: 4,
  },
  actionButtonText: {
    color: 'white',
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
  },
  captureButton: {
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderStyle: 'dashed',
    borderRadius: 8,
    paddingVertical: 40,
    gap: 8,
  },
  captureText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
  },
  requiredText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },
  modal: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  modalContent: {
    margin: 16,
    borderRadius: 12,
    maxWidth: 400,
    width: '100%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 8,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
  },
  closeButton: {
    padding: 4,
  },
  closeText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
  },
  signaturePadContainer: {
    padding: 16,
    alignItems: 'center',
  },
  signaturePad: {
    borderWidth: 1,
    borderRadius: 8,
    position: 'relative',
  },
  placeholderOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    pointerEvents: 'none',
  },
  placeholderText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
  },
  signatureControls: {
    flexDirection: 'row',
    padding: 16,
    gap: 12,
  },
  controlButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    gap: 6,
  },
  controlButtonText: {
    color: 'white',
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
  },
});
