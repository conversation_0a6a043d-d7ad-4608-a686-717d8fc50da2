import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  KeyboardAvoidingView,
  Platform,
  BackHandler,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '@/hooks/useTheme';
import { FormSchema, FormPage, FormSection, FormQuestion, Submission } from '@/types';
import FormFieldRenderer from './FormFieldRenderer';
import {
  ChevronLeft,
  ChevronRight,
  Save,
  CheckCircle,
  AlertCircle,
  FileText,
  MapPin,
  Clock,
  User,
  Layers,
} from 'lucide-react-native';

interface EnhancedFormRendererProps {
  schema: FormSchema;
  submission?: Partial<Submission>;
  onSave: (data: Record<string, any>, isComplete: boolean) => void;
  onCancel: () => void;
  disabled?: boolean;
  autoSave?: boolean;
  autoSaveInterval?: number; // in seconds
  showProgress?: boolean;
  allowBackNavigation?: boolean;
}

export default function EnhancedFormRenderer({
  schema,
  submission,
  onSave,
  onCancel,
  disabled = false,
  autoSave = true,
  autoSaveInterval = 30,
  showProgress = true,
  allowBackNavigation = true,
}: EnhancedFormRendererProps) {
  const { theme } = useTheme();
  const [currentPage, setCurrentPage] = useState(0);
  const [formData, setFormData] = useState<Record<string, any>>(submission?.data || {});
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [lastSaved, setLastSaved] = useState<number | null>(submission?.completedAt || null);
  
  const autoSaveTimer = useRef<NodeJS.Timeout | null>(null);
  const scrollViewRef = useRef<ScrollView>(null);

  useEffect(() => {
    if (autoSave && !disabled) {
      startAutoSave();
    }
    
    return () => {
      if (autoSaveTimer.current) {
        clearInterval(autoSaveTimer.current);
      }
    };
  }, [autoSave, disabled]);

  useEffect(() => {
    const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      if (currentPage > 0 && allowBackNavigation) {
        goToPreviousPage();
        return true;
      }
      return false;
    });

    return () => backHandler.remove();
  }, [currentPage, allowBackNavigation]);

  const startAutoSave = () => {
    if (autoSaveTimer.current) {
      clearInterval(autoSaveTimer.current);
    }
    
    autoSaveTimer.current = setInterval(() => {
      if (Object.keys(formData).length > 0) {
        handleSave(false); // Save as draft
      }
    }, autoSaveInterval * 1000);
  };

  const validateForm = (pageIndex?: number) => {
    const errors: Record<string, string> = {};
    
    try {
      if (!schema || !schema.pages || !Array.isArray(schema.pages)) {
        console.warn('validateForm: Invalid schema');
        return false;
      }

      const pagesToValidate = pageIndex !== undefined ? [schema.pages[pageIndex]] : schema.pages;
      
      if (!pagesToValidate || pagesToValidate.length === 0) {
        return true; // No pages to validate
      }
      
      pagesToValidate.forEach(page => {
        if (!page || !page.sections || !Array.isArray(page.sections)) return;
        
        page.sections.forEach(section => {
          if (!section || !section.questions || !Array.isArray(section.questions)) return;
          
          section.questions.forEach(question => {
            if (!question || !question.id || !question.type) return;
            
            const value = formData[question.id];
            
            // Required validation
            if (question.required && (!value || value === '' || (Array.isArray(value) && value.length === 0))) {
              errors[question.id] = `${question.label || 'This field'} is required`;
              return;
            }
            
            // Skip other validations if field is empty
            if (!value && !question.required) return;
            
            // Type-specific validation
            if (question.validation && Array.isArray(question.validation)) {
              for (const rule of question.validation) {
                if (!rule || !rule.type) continue;
                
                switch (rule.type) {
                  case 'min':
                    if (question.type === 'text' && value?.length < rule.value) {
                      errors[question.id] = rule.message || `Minimum ${rule.value} characters required`;
                    } else if (question.type === 'number' && parseFloat(value) < rule.value) {
                      errors[question.id] = rule.message || `Minimum value is ${rule.value}`;
                    }
                    break;
                  case 'max':
                    if (question.type === 'text' && value?.length > rule.value) {
                      errors[question.id] = rule.message || `Maximum ${rule.value} characters allowed`;
                    } else if (question.type === 'number' && parseFloat(value) > rule.value) {
                      errors[question.id] = rule.message || `Maximum value is ${rule.value}`;
                    }
                    break;
                  case 'pattern':
                    try {
                      const regex = new RegExp(rule.value);
                      if (!regex.test(value)) {
                        errors[question.id] = rule.message || 'Invalid format';
                      }
                    } catch (regexError) {
                      console.warn('Invalid regex pattern:', rule.value);
                    }
                    break;
                }
              }
            }
          });
        });
      });
    } catch (error) {
      console.error('Validation error:', error);
      return false;
    }
    
    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleFieldChange = (questionId: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [questionId]: value,
    }));
    
    // Clear validation error for this field
    if (validationErrors[questionId]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[questionId];
        return newErrors;
      });
    }
  };

  const goToNextPage = () => {
    try {
      if (!schema || !schema.pages || !Array.isArray(schema.pages)) {
        console.error('Invalid schema or pages');
        Alert.alert('Error', 'Form configuration is invalid');
        return;
      }

      if (currentPage >= schema.pages.length - 1) {
        console.warn('Already on last page');
        return;
      }
      
      if (!schema.pages[currentPage]) {
        console.error('Current page not found');
        Alert.alert('Error', 'Current page configuration is missing');
        return;
      }
      
      if (validateForm(currentPage)) {
        const nextPage = currentPage + 1;
        if (nextPage < schema.pages.length && schema.pages[nextPage]) {
          setCurrentPage(nextPage);
          scrollViewRef.current?.scrollTo({ y: 0, animated: true });
        } else {
          console.error('Next page not found or invalid');
          Alert.alert('Error', 'Cannot navigate to next page');
        }
      } else {
        Alert.alert('Validation Error', 'Please fix the errors before continuing to the next page.');
      }
    } catch (error) {
      console.error('Navigation error:', error);
      Alert.alert('Error', 'Failed to navigate to next page. Please try again.');
    }
  };

  const goToPreviousPage = () => {
    try {
      if (currentPage > 0) {
        setCurrentPage(prev => Math.max(0, prev - 1));
        scrollViewRef.current?.scrollTo({ y: 0, animated: true });
      }
    } catch (error) {
      console.error('Back navigation error:', error);
    }
  };

  const handleSave = async (isComplete: boolean = false) => {
    if (isComplete && !validateForm()) {
      Alert.alert('Validation Error', 'Please fix all errors before submitting the form.');
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      await onSave(formData, isComplete);
      setLastSaved(Date.now());
      
      if (isComplete) {
        Alert.alert('Success', 'Form submitted successfully!');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to save form. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const getCompletionPercentage = () => {
    try {
      let totalQuestions = 0;
      let answeredQuestions = 0;
      
      if (!schema || !schema.pages || !Array.isArray(schema.pages)) {
        return 0;
      }
      
      schema.pages.forEach(page => {
        if (!page || !page.sections || !Array.isArray(page.sections)) return;
        
        page.sections.forEach(section => {
          if (!section || !section.questions || !Array.isArray(section.questions)) return;
          
          section.questions.forEach(question => {
            if (!question || !question.id || !question.type) return;
            
            totalQuestions++;
            const value = formData[question.id];
            
            if (value !== undefined && value !== '' && value !== null) {
              if (Array.isArray(value) && value.length > 0) {
                answeredQuestions++;
              } else if (!Array.isArray(value)) {
                answeredQuestions++;
              }
            }
          });
        });
      });
      
      return totalQuestions > 0 ? Math.round((answeredQuestions / totalQuestions) * 100) : 0;
    } catch (error) {
      console.error('Error calculating completion percentage:', error);
      return 0;
    }
  };

  const renderProgressBar = () => {
    if (!showProgress || !schema || !schema.pages || !Array.isArray(schema.pages)) return null;
    
    const completionPercentage = getCompletionPercentage();
    
    return (
      <View style={[styles.progressContainer, { backgroundColor: theme.colors.card }]}>
        <View style={styles.progressInfo}>
          <Text style={[styles.progressText, { color: theme.colors.text }]}>
            Page {currentPage + 1} of {schema.pages.length}
          </Text>
          <Text style={[styles.completionText, { color: theme.colors.muted }]}>
            {completionPercentage}% completed
          </Text>
        </View>
        
        <View style={[styles.progressBarTrack, { backgroundColor: theme.colors.border }]}>
          <View
            style={[
              styles.progressBarFill,
              {
                width: `${completionPercentage}%`,
                backgroundColor: theme.colors.primary,
              }
            ]}
          />
        </View>
        
        <View style={styles.pageIndicators}>
          {schema.pages.map((_, index) => (
            <View
              key={index}
              style={[
                styles.pageIndicator,
                {
                  backgroundColor: index === currentPage 
                    ? theme.colors.primary 
                    : index < currentPage 
                      ? theme.colors.success 
                      : theme.colors.border,
                }
              ]}
            />
          ))}
        </View>
      </View>
    );
  };

  const renderPageHeader = () => {
    const page = schema.pages[currentPage];
    if (!page) return null;
    
    return (
      <View style={styles.pageHeader}>
        <Text style={[styles.pageTitle, { color: theme.colors.text }]}>
          {page.title}
        </Text>
        {page.description && (
          <Text style={[styles.pageDescription, { color: theme.colors.muted }]}>
            {page.description}
          </Text>
        )}
      </View>
    );
  };

  const renderSection = (section: FormSection) => {
    if (!section || !section.id || !section.questions) {
      console.warn('EnhancedFormRenderer: Invalid section', section);
      return null;
    }

    return (
      <View key={section.id} style={[styles.sectionContainer, { backgroundColor: theme.colors.card }]}>
        <View style={styles.sectionHeader}>
          <Layers size={20} color={theme.colors.primary} />
          <View style={styles.sectionInfo}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
              {section.title || 'Untitled Section'}
            </Text>
            {section.description && (
              <Text style={[styles.sectionDescription, { color: theme.colors.muted }]}>
                {section.description}
              </Text>
            )}
          </View>
        </View>
        
        <View style={styles.questionsContainer}>
          {section.questions.filter(q => q && q.id).map(question => (
            <FormFieldRenderer
              key={question.id}
              question={question}
              value={formData[question.id]}
              onChange={(value) => handleFieldChange(question.id, value)}
              disabled={disabled}
              showValidation={true}
            />
          ))}
        </View>
      </View>
    );
  };

  const renderNavigationButtons = () => (
    <View style={[styles.navigationContainer, { backgroundColor: theme.colors.card }]}>
      <View style={styles.navigationLeft}>
        {currentPage > 0 && allowBackNavigation && (
          <TouchableOpacity
            style={[styles.navButton, styles.backButton, { backgroundColor: theme.colors.muted }]}
            onPress={goToPreviousPage}
            disabled={disabled}
          >
            <ChevronLeft size={20} color="white" />
            <Text style={styles.navButtonText}>Previous</Text>
          </TouchableOpacity>
        )}
      </View>
      
      <View style={styles.saveInfo}>
        {lastSaved && (
          <Text style={[styles.lastSavedText, { color: theme.colors.muted }]}>
            <Clock size={12} color={theme.colors.muted} />
            {' '}Last saved: {new Date(lastSaved).toLocaleTimeString()}
          </Text>
        )}
      </View>
      
      <View style={styles.navigationRight}>
        {currentPage < schema.pages.length - 1 ? (
          <TouchableOpacity
            style={[styles.navButton, styles.nextButton, { backgroundColor: theme.colors.primary }]}
            onPress={goToNextPage}
            disabled={disabled}
          >
            <Text style={styles.navButtonText}>Next</Text>
            <ChevronRight size={20} color="white" />
          </TouchableOpacity>
        ) : (
          <TouchableOpacity
            style={[styles.navButton, styles.submitButton, { backgroundColor: theme.colors.success }]}
            onPress={() => handleSave(true)}
            disabled={disabled || isSubmitting}
          >
            <CheckCircle size={20} color="white" />
            <Text style={styles.navButtonText}>
              {isSubmitting ? 'Submitting...' : 'Submit'}
            </Text>
          </TouchableOpacity>
        )}
        
        {!disabled && (
          <TouchableOpacity
            style={[styles.saveDraftButton, { backgroundColor: theme.colors.secondary }]}
            onPress={() => handleSave(false)}
            disabled={isSubmitting}
          >
            <Save size={16} color="white" />
          </TouchableOpacity>
        )}
      </View>
    </View>
  );

  const currentPageData = schema?.pages?.[currentPage];
  
  if (!currentPageData || !schema || !schema.pages || schema.pages.length === 0) {
    return (
      <View style={[styles.container, styles.centered, { backgroundColor: theme.colors.background }]}>
        <AlertCircle size={48} color={theme.colors.error} />
        <Text style={[styles.errorText, { color: theme.colors.text }]}>
          {!schema ? 'Form schema is missing' : 
           !schema.pages || schema.pages.length === 0 ? 'Form has no pages' :
           'Page not found'}
        </Text>
        <TouchableOpacity
          style={[styles.navButton, { backgroundColor: theme.colors.muted }]}
          onPress={onCancel}
        >
          <Text style={styles.navButtonText}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {renderProgressBar()}
      
      <KeyboardAvoidingView
        style={styles.content}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          ref={scrollViewRef}
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {renderPageHeader()}
          
          {currentPageData.sections && Array.isArray(currentPageData.sections) && currentPageData.sections.length > 0 ? (
            currentPageData.sections.filter(section => section && section.id).map(renderSection)
          ) : (
            <View style={[styles.emptyPageContainer, { backgroundColor: theme.colors.card }]}>
              <FileText size={48} color={theme.colors.muted} />
              <Text style={[styles.emptyPageText, { color: theme.colors.text }]}>
                This page has no content yet
              </Text>
            </View>
          )}
        </ScrollView>
        
        {renderNavigationButtons()}
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  errorText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
  },
  progressContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  progressInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  progressText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
  },
  completionText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
  },
  progressBarTrack: {
    height: 4,
    borderRadius: 2,
    marginBottom: 8,
  },
  progressBarFill: {
    height: '100%',
    borderRadius: 2,
  },
  pageIndicators: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 4,
  },
  pageIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  content: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 20,
  },
  pageHeader: {
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  pageTitle: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    marginBottom: 8,
  },
  pageDescription: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    lineHeight: 22,
  },
  sectionContainer: {
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    overflow: 'hidden',
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    gap: 12,
  },
  sectionInfo: {
    flex: 1,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 4,
  },
  sectionDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    lineHeight: 20,
  },
  questionsContainer: {
    padding: 16,
  },
  emptyPageContainer: {
    margin: 16,
    padding: 32,
    borderRadius: 12,
    alignItems: 'center',
    gap: 12,
  },
  emptyPageText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
  },
  navigationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  navigationLeft: {
    flex: 1,
  },
  saveInfo: {
    flex: 2,
    alignItems: 'center',
  },
  lastSavedText: {
    fontSize: 11,
    fontFamily: 'Inter-Regular',
    flexDirection: 'row',
    alignItems: 'center',
  },
  navigationRight: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 8,
  },
  navButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
    gap: 6,
  },
  backButton: {},
  nextButton: {},
  submitButton: {},
  navButtonText: {
    color: 'white',
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
  },
  saveDraftButton: {
    padding: 10,
    borderRadius: 8,
  },
});
