import { View, StyleSheet } from 'react-native';
import { useTheme } from '@/hooks/useTheme';

type DashboardSummaryProps = {
  children: React.ReactNode;
};

export default function DashboardSummary({ children }: DashboardSummaryProps) {
  const { theme } = useTheme();
  
  return (
    <View style={[styles.container, { backgroundColor: theme.colors.card, borderColor: theme.colors.border }]}>
      {children}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderRadius: 12,
    borderWidth: 1,
    padding: 16,
    marginBottom: 16,
  },
});