@echo off
echo ===============================================
echo Testing Professional Map UI Improvements
echo ===============================================
echo.

REM Check Node.js version
echo Checking Node.js version...
node --version
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed
    exit /b 1
)
echo.

REM Check npm version
echo Checking npm version...
npm --version
if %errorlevel% neq 0 (
    echo ERROR: npm is not installed
    exit /b 1
)
echo.

REM Navigate to project directory
cd /d "D:\devprojects\FieldSyncPro"
if %errorlevel% neq 0 (
    echo ERROR: Could not navigate to project directory
    exit /b 1
)
echo Current directory: %CD%
echo.

REM Check if node_modules exists
if not exist "node_modules" (
    echo Installing dependencies...
    npm install
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install dependencies
        exit /b 1
    )
)

REM Clear Metro cache
echo Clearing Metro cache...
if exist "%TEMP%\metro-cache" (
    rmdir /s /q "%TEMP%\metro-cache"
)
echo Metro cache cleared
echo.

REM Clear Expo cache
echo Clearing Expo cache...
npx expo start --clear
echo.

REM Display success message
echo ===============================================
echo Professional Map UI is ready!
echo ===============================================
echo.
echo The map interface has been updated with:
echo - No overlapping UI elements
echo - Professional toolbar at top
echo - Side panels that slide from bottom
echo - Map controls on right side
echo - Animated search bar
echo - Status indicators
echo - Clean z-index hierarchy
echo.
echo To test:
echo 1. Navigate to the map screen
echo 2. Verify toolbar positioning
echo 3. Test drawing tools panel
echo 4. Check spatial analysis panel
echo 5. Confirm no UI overlapping
echo.
echo Press any key to continue...
pause >nul
