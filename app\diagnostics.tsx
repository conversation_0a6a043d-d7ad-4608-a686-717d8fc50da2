import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, ScrollView, Platform } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '@/hooks/useTheme';
import { useAuth } from '@/hooks/useAuth';
import { useSettings } from '@/hooks/useSettings';
import { DatabaseContext } from '@/providers/DatabaseProvider';
import { useContext } from 'react';

interface DiagnosticItem {
  name: string;
  status: 'success' | 'error' | 'warning';
  message: string;
}

export default function DiagnosticsScreen() {
  const { theme } = useTheme();
  const { user } = useAuth();
  const { settings } = useSettings();
  const { db, webStorage, initialized, error } = useContext(DatabaseContext);
  const [diagnostics, setDiagnostics] = useState<DiagnosticItem[]>([]);

  useEffect(() => {
    const runDiagnostics = () => {
      const results: DiagnosticItem[] = [];

      // Platform check
      results.push({
        name: 'Platform',
        status: 'success',
        message: `Running on ${Platform.OS}`
      });

      // Database check
      if (Platform.OS === 'web') {
        results.push({
          name: 'Web Storage',
          status: webStorage ? 'success' : 'error',
          message: webStorage ? 'Web storage available' : 'Web storage not available'
        });
      } else {
        results.push({
          name: 'SQLite Database',
          status: db ? 'success' : 'error',
          message: db ? 'Database connected' : 'Database not connected'
        });
      }

      // Database initialization
      results.push({
        name: 'Database Initialized',
        status: initialized ? 'success' : 'warning',
        message: initialized ? 'Database is initialized' : 'Database not yet initialized'
      });

      // Database errors
      if (error) {
        results.push({
          name: 'Database Error',
          status: 'error',
          message: error.message
        });
      }

      // Auth check
      results.push({
        name: 'Authentication',
        status: user ? 'success' : 'warning',
        message: user ? `Logged in as ${user.email}` : 'Not logged in'
      });

      // Settings check
      results.push({
        name: 'Settings',
        status: settings ? 'success' : 'error',
        message: settings ? 'Settings loaded' : 'Settings not loaded'
      });

      // Theme check
      results.push({
        name: 'Theme',
        status: theme ? 'success' : 'error',
        message: theme ? `Theme loaded (${theme.dark ? 'dark' : 'light'})` : 'Theme not loaded'
      });

      setDiagnostics(results);
    };

    runDiagnostics();
  }, [theme, user, settings, db, webStorage, initialized, error]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return theme.colors.success;
      case 'error':
        return theme.colors.error;
      case 'warning':
        return theme.colors.warning;
      default:
        return theme.colors.text;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return '✅';
      case 'error':
        return '❌';
      case 'warning':
        return '⚠️';
      default:
        return '❓';
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: theme.colors.text }]}>System Diagnostics</Text>
        <Text style={[styles.subtitle, { color: theme.colors.muted }]}>
          Debug information for FieldSync Pro
        </Text>
      </View>

      <ScrollView style={styles.content}>
        {diagnostics.map((item, index) => (
          <View key={index} style={[styles.diagnosticItem, { backgroundColor: theme.colors.card }]}>
            <View style={styles.itemHeader}>
              <Text style={styles.icon}>{getStatusIcon(item.status)}</Text>
              <Text style={[styles.itemName, { color: theme.colors.text }]}>{item.name}</Text>
            </View>
            <Text style={[styles.itemMessage, { color: getStatusColor(item.status) }]}>
              {item.message}
            </Text>
          </View>
        ))}

        <View style={[styles.systemInfo, { backgroundColor: theme.colors.card }]}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>System Information</Text>
          <Text style={[styles.infoText, { color: theme.colors.muted }]}>
            Platform: {Platform.OS}
          </Text>
          <Text style={[styles.infoText, { color: theme.colors.muted }]}>
            Version: {Platform.Version}
          </Text>
          {__DEV__ && (
            <Text style={[styles.infoText, { color: theme.colors.warning }]}>
              Development Mode: Enabled
            </Text>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 16,
    paddingTop: 8,
    paddingBottom: 12,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  subtitle: {
    fontSize: 14,
    marginTop: 4,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  diagnosticItem: {
    padding: 16,
    marginBottom: 8,
    borderRadius: 8,
  },
  itemHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  icon: {
    fontSize: 16,
    marginRight: 8,
  },
  itemName: {
    fontSize: 16,
    fontWeight: '600',
  },
  itemMessage: {
    fontSize: 14,
    marginLeft: 24,
  },
  systemInfo: {
    padding: 16,
    marginTop: 16,
    marginBottom: 32,
    borderRadius: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    marginBottom: 4,
  },
});
