# FieldSync Pro - Quick Test Script (PowerShell)
# This script helps verify that all major issues have been resolved

Write-Host "🔍 FieldSync Pro - Issue Verification Script" -ForegroundColor Cyan
Write-Host "==============================================" -ForegroundColor Cyan

# Check if backup files were removed
Write-Host ""
Write-Host "📁 Checking for backup files..." -ForegroundColor Yellow
$backupFiles = Get-ChildItem -Path "./app" -Recurse -Name "*backup*", "*temp*" -ErrorAction SilentlyContinue
if ($backupFiles.Count -eq 0) {
    Write-Host "✅ No backup files found" -ForegroundColor Green
} else {
    Write-Host "⚠️  Backup files still exist:" -ForegroundColor Yellow
    $backupFiles | ForEach-Object { Write-Host "   $_" }
}

# Check for proper imports in key files
Write-Host ""
Write-Host "📦 Checking imports in key files..." -ForegroundColor Yellow

# Check DatabaseProvider
if (Test-Path "./providers/DatabaseProvider.tsx") {
    $content = Get-Content "./providers/DatabaseProvider.tsx" -Raw
    if ($content -match "Platform") {
        Write-Host "✅ DatabaseProvider has Platform import" -ForegroundColor Green
    } else {
        Write-Host "❌ DatabaseProvider missing Platform import" -ForegroundColor Red
    }
} else {
    Write-Host "❌ DatabaseProvider file not found" -ForegroundColor Red
}

# Check AuthProvider
if (Test-Path "./providers/AuthProvider.tsx") {
    $content = Get-Content "./providers/AuthProvider.tsx" -Raw
    if ($content -match "Platform") {
        Write-Host "✅ AuthProvider has Platform import" -ForegroundColor Green
    } else {
        Write-Host "❌ AuthProvider missing Platform import" -ForegroundColor Red
    }
} else {
    Write-Host "❌ AuthProvider file not found" -ForegroundColor Red
}

# Check for font family issues
Write-Host ""
Write-Host "🔤 Checking for font family references..." -ForegroundColor Yellow
$fontIssues = Select-String -Path "./components/*" -Pattern "fontFamily.*Inter" -Recurse -ErrorAction SilentlyContinue
if ($fontIssues.Count -eq 0) {
    Write-Host "✅ No problematic font family references found" -ForegroundColor Green
} else {
    Write-Host "⚠️  Font family references still exist:" -ForegroundColor Yellow
    $fontIssues | ForEach-Object { Write-Host "   $($_.Filename):$($_.LineNumber)" }
}

# Check tab layout for icons
Write-Host ""
Write-Host "🎨 Checking tab layout configuration..." -ForegroundColor Yellow
if (Test-Path "./app/(tabs)/_layout.tsx") {
    $content = Get-Content "./app/(tabs)/_layout.tsx" -Raw
    if ($content -match "tabBarIcon.*color.*size") {
        Write-Host "✅ Tab layout has proper icon configuration" -ForegroundColor Green
    } else {
        Write-Host "❌ Tab layout missing proper icon configuration" -ForegroundColor Red
    }
} else {
    Write-Host "❌ Tab layout file not found" -ForegroundColor Red
}

# Check settings hook for database context
Write-Host ""
Write-Host "⚙️  Checking settings hook integration..." -ForegroundColor Yellow
if (Test-Path "./hooks/useSettings.ts") {
    $content = Get-Content "./hooks/useSettings.ts" -Raw
    if ($content -match "DatabaseContext") {
        Write-Host "✅ Settings hook has database integration" -ForegroundColor Green
    } else {
        Write-Host "❌ Settings hook missing database integration" -ForegroundColor Red
    }
} else {
    Write-Host "❌ Settings hook file not found" -ForegroundColor Red
}

# Check for proper error handling
Write-Host ""
Write-Host "🛡️  Checking error handling patterns..." -ForegroundColor Yellow
$tryCatchCount = (Select-String -Path "./providers/*", "./hooks/*" -Pattern "try \{" -ErrorAction SilentlyContinue).Count
if ($tryCatchCount -gt 5) {
    Write-Host "✅ Good error handling coverage ($tryCatchCount try-catch blocks found)" -ForegroundColor Green
} else {
    Write-Host "⚠️  Limited error handling coverage ($tryCatchCount try-catch blocks found)" -ForegroundColor Yellow
}

# Check package.json for required dependencies
Write-Host ""
Write-Host "📋 Checking required dependencies..." -ForegroundColor Yellow
$requiredDeps = @("expo-sqlite", "expo-secure-store", "lucide-react-native", "zustand")
$missingDeps = @()

if (Test-Path "./package.json") {
    $packageContent = Get-Content "./package.json" -Raw
    foreach ($dep in $requiredDeps) {
        if ($packageContent -match "`"$dep`"") {
            Write-Host "✅ $dep is installed" -ForegroundColor Green
        } else {
            Write-Host "❌ $dep is missing" -ForegroundColor Red
            $missingDeps += $dep
        }
    }
} else {
    Write-Host "❌ package.json not found" -ForegroundColor Red
}

# Summary
Write-Host ""
Write-Host "📊 SUMMARY" -ForegroundColor Cyan
Write-Host "==========" -ForegroundColor Cyan
if ($missingDeps.Count -eq 0 -and $backupFiles.Count -eq 0) {
    Write-Host "🎉 All major issues appear to be resolved!" -ForegroundColor Green
    Write-Host "   Ready for testing with: npm run dev" -ForegroundColor Green
} else {
    Write-Host "⚠️  Some issues may still need attention:" -ForegroundColor Yellow
    if ($missingDeps.Count -gt 0) {
        Write-Host "   - Missing dependencies: $($missingDeps -join ', ')" -ForegroundColor Yellow
    }
    if ($backupFiles.Count -gt 0) {
        Write-Host "   - Backup files still present" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "🚀 Next steps:" -ForegroundColor Cyan
Write-Host "   1. Run: npm run dev" -ForegroundColor White
Write-Host "   2. Navigate to Settings tab" -ForegroundColor White
Write-Host "   3. Check console for errors" -ForegroundColor White
Write-Host "   4. Visit /diagnostics for detailed status" -ForegroundColor White
Write-Host "   5. Test theme toggle and settings persistence" -ForegroundColor White
