// Main map component exports with enhanced functionality
export { default as MapScreen } from './MapScreen';
export { default as EnhancedMapIntegration } from './EnhancedMapIntegration';
export { default as EnhancedMapIntegrationUpdated } from './EnhancedMapIntegrationUpdated';
export { default as ProfessionalMapUI } from './ProfessionalMapUI';
export { default as ProfessionalMapUIEnhanced } from './ProfessionalMapUIEnhanced';
export { default as EnhancedMapWithFixes } from './EnhancedMapWithFixes';
export { default as MapErrorBoundary } from './MapErrorBoundary';
export { default as MapToolbar } from './MapToolbar';
export { default as MapLayersPanel } from './MapLayersPanel';
export { default as SpatialToolkit } from './SpatialToolkit';
export { GISMapViewer } from './GISMapViewer';

// Advanced Map Components
export { LayerManagerModal } from './LayerManagerModal';
export { SpatialAnalysisModal } from './SpatialAnalysisModal';
export { MapSettingsModal } from './MapSettingsModal';
export { StoryBuilderModal } from './StoryBuilderModal';
export { BookmarkManagerModal } from './BookmarkManagerModal';

// Utility Components
export { CoordinateDisplay } from './CoordinateDisplay';
export { MeasurementDisplay } from './MeasurementDisplay';

// Spatial Analysis
export * from './spatial/SpatialAnalysisEngine';

// Platform-specific exports
export { default as EnhancedLeafletMap } from './EnhancedLeafletMap.web';
export { default as EnhancedLeafletMapFixed } from './EnhancedLeafletMapFixed.web';

// Type exports
export type { MapRef } from './EnhancedMapWithFixes';

// Spatial analysis exports
export * from './spatial/SpatialAnalysis';
export * from './spatial/AdvancedSpatialAnalysis';

// Default export - use the enhanced version
export { default } from './MapScreen';
