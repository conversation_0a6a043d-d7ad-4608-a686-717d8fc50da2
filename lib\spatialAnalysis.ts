import { Coordinate, calculateDistance, calculatePolygonArea, calculateCentroid } from '@/lib/measurements';

export interface SpatialAnalysisOptions {
  distance?: number;
  units?: 'meters' | 'kilometers' | 'miles' | 'feet';
  steps?: number;
  tolerance?: number;
  selectedOnly?: boolean;
  includeProperties?: boolean;
}

export interface SpatialAnalysisResult {
  id: string;
  type: string;
  geometry: any;
  properties: any;
  metadata: {
    originalFeatures: string[];
    algorithm: string;
    parameters: SpatialAnalysisOptions;
    executionTime: number;
    timestamp: string;
  };
}

export interface BufferAnalysisResult extends SpatialAnalysisResult {
  type: 'buffer';
  properties: {
    originalFeatureId: string;
    bufferDistance: number;
    bufferUnits: string;
    area: number;
    perimeter: number;
  };
}

export interface IntersectionAnalysisResult extends SpatialAnalysisResult {
  type: 'intersection';
  properties: {
    feature1Id: string;
    feature2Id: string;
    intersectionArea?: number;
    intersectionLength?: number;
    overlapPercentage: number;
  };
}

export interface UnionAnalysisResult extends SpatialAnalysisResult {
  type: 'union';
  properties: {
    mergedFeatures: string[];
    totalArea: number;
    totalPerimeter: number;
    featureCount: number;
  };
}

export interface NearestNeighborResult {
  targetFeatureId: string;
  nearestFeatureId: string;
  distance: number;
  bearing: number;
  nearestPoint: Coordinate;
}

export interface ClusterAnalysisResult {
  clusterId: string;
  features: string[];
  centroid: Coordinate;
  radius: number;
  density: number;
}

export interface VoronoiAnalysisResult extends SpatialAnalysisResult {
  type: 'voronoi';
  properties: {
    seedFeatureId: string;
    area: number;
    neighbors: string[];
  };
}

/**
 * Enhanced Spatial Analysis Toolkit
 * Provides comprehensive spatial analysis capabilities using computational geometry
 */
export class EnhancedSpatialAnalysis {
  private features: Map<string, any> = new Map();
  private analysisHistory: SpatialAnalysisResult[] = [];

  constructor(features: any[] = []) {
    this.loadFeatures(features);
  }

  /**
   * Load features for analysis
   */
  loadFeatures(features: any[]): void {
    this.features.clear();
    features.forEach(feature => {
      this.features.set(feature.id, feature);
    });
  }

  /**
   * Add a single feature
   */
  addFeature(feature: any): void {
    this.features.set(feature.id, feature);
  }

  /**
   * Remove a feature
   */
  removeFeature(featureId: string): void {
    this.features.delete(featureId);
  }

  /**
   * Get analysis history
   */
  getAnalysisHistory(): SpatialAnalysisResult[] {
    return [...this.analysisHistory];
  }

  /**
   * Clear analysis history
   */
  clearAnalysisHistory(): void {
    this.analysisHistory = [];
  }

  /**
   * Create buffer around features
   */
  async createBuffer(featureIds: string[], options: SpatialAnalysisOptions = {}): Promise<BufferAnalysisResult[]> {
    const startTime = performance.now();
    const distance = options.distance || 100;
    const units = options.units || 'meters';
    const steps = options.steps || 32;
    
    const results: BufferAnalysisResult[] = [];
    
    for (const featureId of featureIds) {
      const feature = this.features.get(featureId);
      if (!feature) continue;
      
      const bufferedGeometry = this.generateBufferGeometry(feature, distance, steps);
      if (!bufferedGeometry) continue;
      
      const area = calculatePolygonArea(this.coordinatesToPoints(bufferedGeometry.coordinates[0]));
      const perimeter = this.calculatePolygonPerimeter(bufferedGeometry.coordinates[0]);
      
      const result: BufferAnalysisResult = {
        id: `buffer_${featureId}_${Date.now()}`,
        type: 'buffer',
        geometry: bufferedGeometry,
        properties: {
          originalFeatureId: featureId,
          bufferDistance: distance,
          bufferUnits: units,
          area,
          perimeter,
        },
        metadata: {
          originalFeatures: [featureId],
          algorithm: 'buffer',
          parameters: options,
          executionTime: performance.now() - startTime,
          timestamp: new Date().toISOString(),
        },
      };
      
      results.push(result);
      this.analysisHistory.push(result);
    }
    
    return results;
  }

  /**
   * Find intersections between features
   */
  async findIntersections(featureIds?: string[], options: SpatialAnalysisOptions = {}): Promise<IntersectionAnalysisResult[]> {
    const startTime = performance.now();
    const targetFeatures = featureIds ? 
      featureIds.map(id => this.features.get(id)).filter(Boolean) :
      Array.from(this.features.values());
    
    const results: IntersectionAnalysisResult[] = [];
    
    for (let i = 0; i < targetFeatures.length - 1; i++) {
      for (let j = i + 1; j < targetFeatures.length; j++) {
        const feature1 = targetFeatures[i];
        const feature2 = targetFeatures[j];
        
        const intersection = this.calculateIntersection(feature1, feature2);
        if (intersection) {
          const result: IntersectionAnalysisResult = {
            id: `intersection_${feature1.id}_${feature2.id}_${Date.now()}`,
            type: 'intersection',
            geometry: intersection.geometry,
            properties: {
              feature1Id: feature1.id,
              feature2Id: feature2.id,
              intersectionArea: intersection.area,
              intersectionLength: intersection.length,
              overlapPercentage: intersection.overlapPercentage,
            },
            metadata: {
              originalFeatures: [feature1.id, feature2.id],
              algorithm: 'intersection',
              parameters: options,
              executionTime: performance.now() - startTime,
              timestamp: new Date().toISOString(),
            },
          };
          
          results.push(result);
          this.analysisHistory.push(result);
        }
      }
    }
    
    return results;
  }

  /**
   * Create union of multiple features
   */
  async createUnion(featureIds: string[], options: SpatialAnalysisOptions = {}): Promise<UnionAnalysisResult | null> {
    const startTime = performance.now();
    const features = featureIds.map(id => this.features.get(id)).filter(Boolean);
    
    if (features.length < 2) return null;
    
    const unionGeometry = this.calculateUnion(features);
    if (!unionGeometry) return null;
    
    const area = calculatePolygonArea(this.coordinatesToPoints(unionGeometry.coordinates[0]));
    const perimeter = this.calculatePolygonPerimeter(unionGeometry.coordinates[0]);
    
    const result: UnionAnalysisResult = {
      id: `union_${featureIds.join('_')}_${Date.now()}`,
      type: 'union',
      geometry: unionGeometry,
      properties: {
        mergedFeatures: featureIds,
        totalArea: area,
        totalPerimeter: perimeter,
        featureCount: features.length,
      },
      metadata: {
        originalFeatures: featureIds,
        algorithm: 'union',
        parameters: options,
        executionTime: performance.now() - startTime,
        timestamp: new Date().toISOString(),
      },
    };
    
    this.analysisHistory.push(result);
    return result;
  }

  /**
   * Find nearest neighbors for each feature
   */
  async findNearestNeighbors(featureIds?: string[], options: SpatialAnalysisOptions = {}): Promise<NearestNeighborResult[]> {
    const targetFeatures = featureIds ? 
      featureIds.map(id => this.features.get(id)).filter(Boolean) :
      Array.from(this.features.values());
    
    const results: NearestNeighborResult[] = [];
    
    for (const feature of targetFeatures) {
      const centroid = this.getFeatureCentroid(feature);
      let nearestDistance = Infinity;
      let nearestFeature: any = null;
      
      for (const [id, otherFeature] of this.features) {
        if (id === feature.id) continue;
        
        const otherCentroid = this.getFeatureCentroid(otherFeature);
        const distance = calculateDistance(centroid, otherCentroid);
        
        if (distance < nearestDistance) {
          nearestDistance = distance;
          nearestFeature = otherFeature;
        }
      }
      
      if (nearestFeature) {
        const nearestCentroid = this.getFeatureCentroid(nearestFeature);
        const bearing = this.calculateBearing(centroid, nearestCentroid);
        
        results.push({
          targetFeatureId: feature.id,
          nearestFeatureId: nearestFeature.id,
          distance: nearestDistance,
          bearing,
          nearestPoint: nearestCentroid,
        });
      }
    }
    
    return results;
  }

  /**
   * Perform cluster analysis using k-means clustering
   */
  async performClusterAnalysis(k: number, options: SpatialAnalysisOptions = {}): Promise<ClusterAnalysisResult[]> {
    const points = Array.from(this.features.values())
      .filter(feature => feature.type === 'point')
      .map(feature => ({
        id: feature.id,
        coordinate: this.getFeatureCentroid(feature),
      }));
    
    if (points.length < k) {
      throw new Error(`Not enough points for ${k} clusters. Have ${points.length} points.`);
    }
    
    const clusters = this.kMeansClustering(points, k);
    
    return clusters.map((cluster, index) => ({
      clusterId: `cluster_${index}`,
      features: cluster.points.map(p => p.id),
      centroid: cluster.centroid,
      radius: cluster.radius,
      density: cluster.points.length / (Math.PI * cluster.radius * cluster.radius),
    }));
  }

  /**
   * Generate Voronoi diagram
   */
  async generateVoronoiDiagram(featureIds?: string[], options: SpatialAnalysisOptions = {}): Promise<VoronoiAnalysisResult[]> {
    const startTime = performance.now();
    const features = featureIds ? 
      featureIds.map(id => this.features.get(id)).filter(Boolean) :
      Array.from(this.features.values()).filter(f => f.type === 'point');
    
    if (features.length < 3) {
      throw new Error('Voronoi diagram requires at least 3 points');
    }
    
    // Simplified Voronoi implementation - in production use a proper library
    const voronoiCells = this.calculateVoronoiCells(features);
    
    return voronoiCells.map(cell => ({
      id: `voronoi_${cell.seedId}_${Date.now()}`,
      type: 'voronoi',
      geometry: {
        type: 'Polygon',
        coordinates: [cell.polygon],
      },
      properties: {
        seedFeatureId: cell.seedId,
        area: cell.area,
        neighbors: cell.neighbors,
      },
      metadata: {
        originalFeatures: [cell.seedId],
        algorithm: 'voronoi',
        parameters: options,
        executionTime: performance.now() - startTime,
        timestamp: new Date().toISOString(),
      },
    }));
  }

  /**
   * Calculate feature density within specified radius
   */
  async calculateDensity(centerFeatureId: string, radius: number, options: SpatialAnalysisOptions = {}): Promise<number> {
    const centerFeature = this.features.get(centerFeatureId);
    if (!centerFeature) return 0;
    
    const centerPoint = this.getFeatureCentroid(centerFeature);
    let count = 0;
    
    for (const [id, feature] of this.features) {
      if (id === centerFeatureId) continue;
      
      const featurePoint = this.getFeatureCentroid(feature);
      const distance = calculateDistance(centerPoint, featurePoint);
      
      if (distance <= radius) {
        count++;
      }
    }
    
    const area = Math.PI * radius * radius;
    return count / area; // features per square meter
  }

  /**
   * Find features within specified distance
   */
  async findFeaturesWithinDistance(centerFeatureId: string, distance: number): Promise<string[]> {
    const centerFeature = this.features.get(centerFeatureId);
    if (!centerFeature) return [];
    
    const centerPoint = this.getFeatureCentroid(centerFeature);
    const nearbyFeatures: string[] = [];
    
    for (const [id, feature] of this.features) {
      if (id === centerFeatureId) continue;
      
      const featurePoint = this.getFeatureCentroid(feature);
      const dist = calculateDistance(centerPoint, featurePoint);
      
      if (dist <= distance) {
        nearbyFeatures.push(id);
      }
    }
    
    return nearbyFeatures;
  }

  // Private helper methods

  private generateBufferGeometry(feature: any, distance: number, steps: number): any {
    const centroid = this.getFeatureCentroid(feature);
    const points: number[][] = [];
    
    for (let i = 0; i < steps; i++) {
      const angle = (i * 2 * Math.PI) / steps;
      const x = centroid.longitude + (distance / 111000) * Math.cos(angle) / Math.cos(centroid.latitude * Math.PI / 180);
      const y = centroid.latitude + (distance / 111000) * Math.sin(angle);
      points.push([x, y]);
    }
    
    // Close the polygon
    points.push(points[0]);
    
    return {
      type: 'Polygon',
      coordinates: [points],
    };
  }

  private calculateIntersection(feature1: any, feature2: any): any {
    // Simplified intersection calculation
    // In production, use a proper computational geometry library
    const centroid1 = this.getFeatureCentroid(feature1);
    const centroid2 = this.getFeatureCentroid(feature2);
    
    // For demonstration, create a simple intersection between two circular areas
    const distance = calculateDistance(centroid1, centroid2);
    const radius1 = this.estimateFeatureRadius(feature1);
    const radius2 = this.estimateFeatureRadius(feature2);
    
    if (distance >= radius1 + radius2) {
      return null; // No intersection
    }
    
    // Calculate intersection area (lens shape)
    const area1 = Math.PI * radius1 * radius1;
    const area2 = Math.PI * radius2 * radius2;
    const overlapArea = this.calculateCircleIntersectionArea(radius1, radius2, distance);
    
    const overlapPercentage = (overlapArea / Math.min(area1, area2)) * 100;
    
    return {
      geometry: {
        type: 'Polygon',
        coordinates: [this.generateIntersectionPolygon(centroid1, centroid2, radius1, radius2, distance)],
      },
      area: overlapArea,
      overlapPercentage,
    };
  }

  private calculateUnion(features: any[]): any {
    // Simplified union calculation
    // In production, use a proper computational geometry library
    const allPoints: Coordinate[] = [];
    
    features.forEach(feature => {
      const coords = this.getFeatureCoordinates(feature);
      allPoints.push(...coords);
    });
    
    // Calculate convex hull as simplified union
    const hull = this.convexHull(allPoints);
    
    return {
      type: 'Polygon',
      coordinates: [hull.map(p => [p.longitude, p.latitude])],
    };
  }

  private kMeansClustering(points: any[], k: number): any[] {
    // Initialize centroids randomly
    let centroids = points.slice(0, k).map(p => ({ ...p.coordinate }));
    let clusters: any[] = [];
    let iterations = 0;
    const maxIterations = 100;
    
    while (iterations < maxIterations) {
      // Assign points to clusters
      clusters = Array.from({ length: k }, () => ({ points: [], centroid: null, radius: 0 }));
      
      points.forEach(point => {
        let minDistance = Infinity;
        let clusterIndex = 0;
        
        centroids.forEach((centroid, index) => {
          const distance = calculateDistance(point.coordinate, centroid);
          if (distance < minDistance) {
            minDistance = distance;
            clusterIndex = index;
          }
        });
        
        clusters[clusterIndex].points.push(point);
      });
      
      // Update centroids
      const newCentroids = clusters.map(cluster => {
        if (cluster.points.length === 0) return centroids[0]; // fallback
        
        const avgLat = cluster.points.reduce((sum, p) => sum + p.coordinate.latitude, 0) / cluster.points.length;
        const avgLng = cluster.points.reduce((sum, p) => sum + p.coordinate.longitude, 0) / cluster.points.length;
        
        return { latitude: avgLat, longitude: avgLng };
      });
      
      // Check for convergence
      const converged = centroids.every((centroid, index) => {
        const distance = calculateDistance(centroid, newCentroids[index]);
        return distance < 0.001; // 1 meter threshold
      });
      
      centroids = newCentroids;
      iterations++;
      
      if (converged) break;
    }
    
    // Calculate cluster radii
    clusters.forEach((cluster, index) => {
      cluster.centroid = centroids[index];
      if (cluster.points.length > 0) {
        cluster.radius = Math.max(
          ...cluster.points.map(p => calculateDistance(p.coordinate, cluster.centroid))
        );
      }
    });
    
    return clusters.filter(cluster => cluster.points.length > 0);
  }

  private calculateVoronoiCells(features: any[]): any[] {
    // Simplified Voronoi calculation
    // In production, use a proper Voronoi library like d3-delaunay
    const cells: any[] = [];
    
    features.forEach(feature => {
      const seedPoint = this.getFeatureCentroid(feature);
      
      // Generate a simplified cell polygon
      const cellPolygon = this.generateVoronoiCell(seedPoint, features);
      const area = calculatePolygonArea(this.coordinatesToPoints(cellPolygon));
      
      cells.push({
        seedId: feature.id,
        polygon: cellPolygon,
        area,
        neighbors: [], // Would need proper Voronoi algorithm to find neighbors
      });
    });
    
    return cells;
  }

  private generateVoronoiCell(seedPoint: Coordinate, allFeatures: any[]): number[][] {
    // Very simplified Voronoi cell generation
    // Creates a square around the seed point
    const size = 0.01; // roughly 1km
    
    return [
      [seedPoint.longitude - size, seedPoint.latitude - size],
      [seedPoint.longitude + size, seedPoint.latitude - size],
      [seedPoint.longitude + size, seedPoint.latitude + size],
      [seedPoint.longitude - size, seedPoint.latitude + size],
      [seedPoint.longitude - size, seedPoint.latitude - size],
    ];
  }

  private getFeatureCentroid(feature: any): Coordinate {
    switch (feature.type) {
      case 'point':
        return {
          latitude: feature.coordinates[1],
          longitude: feature.coordinates[0],
        };
      case 'polygon':
      case 'rectangle':
        const coords = feature.coordinates[0] || feature.coordinates;
        return calculateCentroid(this.coordinatesToPoints(coords));
      case 'line':
        const lineCoords = this.coordinatesToPoints(feature.coordinates);
        const midIndex = Math.floor(lineCoords.length / 2);
        return lineCoords[midIndex];
      case 'circle':
        return {
          latitude: feature.coordinates[1],
          longitude: feature.coordinates[0],
        };
      default:
        return { latitude: 0, longitude: 0 };
    }
  }

  private getFeatureCoordinates(feature: any): Coordinate[] {
    switch (feature.type) {
      case 'point':
        return [{
          latitude: feature.coordinates[1],
          longitude: feature.coordinates[0],
        }];
      case 'line':
        return this.coordinatesToPoints(feature.coordinates);
      case 'polygon':
      case 'rectangle':
        return this.coordinatesToPoints(feature.coordinates[0] || feature.coordinates);
      case 'circle':
        // Return points around the circle perimeter
        const center = { latitude: feature.coordinates[1], longitude: feature.coordinates[0] };
        const radius = feature.properties?.radius || 100;
        return this.generateCirclePoints(center, radius, 16);
      default:
        return [];
    }
  }

  private coordinatesToPoints(coordinates: number[][]): Coordinate[] {
    return coordinates.map(([lng, lat]) => ({ latitude: lat, longitude: lng }));
  }

  private generateCirclePoints(center: Coordinate, radius: number, steps: number): Coordinate[] {
    const points: Coordinate[] = [];
    for (let i = 0; i < steps; i++) {
      const angle = (i * 2 * Math.PI) / steps;
      const lat = center.latitude + (radius / 111000) * Math.sin(angle);
      const lng = center.longitude + (radius / 111000) * Math.cos(angle) / Math.cos(center.latitude * Math.PI / 180);
      points.push({ latitude: lat, longitude: lng });
    }
    return points;
  }

  private estimateFeatureRadius(feature: any): number {
    switch (feature.type) {
      case 'point':
        return 50; // Default 50m radius
      case 'circle':
        return feature.properties?.radius || 100;
      case 'polygon':
      case 'rectangle':
        const coords = this.coordinatesToPoints(feature.coordinates[0] || feature.coordinates);
        const area = calculatePolygonArea(coords);
        return Math.sqrt(area / Math.PI); // Equivalent circle radius
      case 'line':
        const lineCoords = this.coordinatesToPoints(feature.coordinates);
        const length = this.calculateLineLength(lineCoords);
        return length / 2; // Half the line length
      default:
        return 100;
    }
  }

  private calculatePolygonPerimeter(coordinates: number[][]): number {
    const points = this.coordinatesToPoints(coordinates);
    let perimeter = 0;
    
    for (let i = 0; i < points.length - 1; i++) {
      perimeter += calculateDistance(points[i], points[i + 1]);
    }
    
    return perimeter;
  }

  private calculateLineLength(points: Coordinate[]): number {
    let length = 0;
    for (let i = 0; i < points.length - 1; i++) {
      length += calculateDistance(points[i], points[i + 1]);
    }
    return length;
  }

  private calculateBearing(from: Coordinate, to: Coordinate): number {
    const φ1 = from.latitude * Math.PI / 180;
    const φ2 = to.latitude * Math.PI / 180;
    const Δλ = (to.longitude - from.longitude) * Math.PI / 180;
    
    const y = Math.sin(Δλ) * Math.cos(φ2);
    const x = Math.cos(φ1) * Math.sin(φ2) - Math.sin(φ1) * Math.cos(φ2) * Math.cos(Δλ);
    
    const bearing = Math.atan2(y, x) * 180 / Math.PI;
    return (bearing + 360) % 360;
  }

  private calculateCircleIntersectionArea(r1: number, r2: number, distance: number): number {
    if (distance >= r1 + r2) return 0;
    if (distance <= Math.abs(r1 - r2)) return Math.PI * Math.min(r1, r2) ** 2;
    
    const area1 = r1 * r1 * Math.acos((distance * distance + r1 * r1 - r2 * r2) / (2 * distance * r1));
    const area2 = r2 * r2 * Math.acos((distance * distance + r2 * r2 - r1 * r1) / (2 * distance * r2));
    const area3 = 0.5 * Math.sqrt((-distance + r1 + r2) * (distance + r1 - r2) * (distance - r1 + r2) * (distance + r1 + r2));
    
    return area1 + area2 - area3;
  }

  private generateIntersectionPolygon(center1: Coordinate, center2: Coordinate, r1: number, r2: number, distance: number): number[][] {
    // Simplified intersection polygon generation
    const midLat = (center1.latitude + center2.latitude) / 2;
    const midLng = (center1.longitude + center2.longitude) / 2;
    const size = Math.min(r1, r2) / 111000; // Convert to degrees
    
    return [
      [midLng - size, midLat - size],
      [midLng + size, midLat - size],
      [midLng + size, midLat + size],
      [midLng - size, midLat + size],
      [midLng - size, midLat - size],
    ];
  }

  private convexHull(points: Coordinate[]): Coordinate[] {
    // Graham scan algorithm for convex hull
    if (points.length < 3) return points;
    
    // Find the bottom-most point
    let start = 0;
    for (let i = 1; i < points.length; i++) {
      if (points[i].latitude < points[start].latitude || 
          (points[i].latitude === points[start].latitude && points[i].longitude < points[start].longitude)) {
        start = i;
      }
    }
    
    // Sort points by polar angle with respect to start point
    const sorted = points.slice();
    const startPoint = sorted[start];
    sorted.splice(start, 1);
    
    sorted.sort((a, b) => {
      const angleA = Math.atan2(a.latitude - startPoint.latitude, a.longitude - startPoint.longitude);
      const angleB = Math.atan2(b.latitude - startPoint.latitude, b.longitude - startPoint.longitude);
      return angleA - angleB;
    });
    
    // Build convex hull
    const hull = [startPoint, sorted[0]];
    
    for (let i = 1; i < sorted.length; i++) {
      while (hull.length > 1 && this.crossProduct(hull[hull.length - 2], hull[hull.length - 1], sorted[i]) <= 0) {
        hull.pop();
      }
      hull.push(sorted[i]);
    }
    
    return hull;
  }

  private crossProduct(a: Coordinate, b: Coordinate, c: Coordinate): number {
    return (b.longitude - a.longitude) * (c.latitude - a.latitude) - 
           (b.latitude - a.latitude) * (c.longitude - a.longitude);
  }
}

export default EnhancedSpatialAnalysis;
