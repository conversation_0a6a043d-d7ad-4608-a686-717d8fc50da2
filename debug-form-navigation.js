#!/usr/bin/env node

/**
 * Debug Script for Form Navigation Issues
 * 
 * This script helps diagnose form navigation problems by:
 * 1. Checking the form schema structure
 * 2. Validating question types
 * 3. Testing navigation logic
 */

const path = require('path');
const fs = require('fs');

// Load the demo form schema from the app
const appFilePath = path.join(__dirname, 'app', 'enhanced-form-demo.tsx');

console.log('🔍 Debugging Form Navigation Issues...\n');

// Function to validate form schema structure
function validateFormSchema(schema) {
  const errors = [];
  
  if (!schema) {
    errors.push('Schema is undefined or null');
    return errors;
  }
  
  if (!schema.pages || !Array.isArray(schema.pages)) {
    errors.push('Schema.pages is missing or not an array');
    return errors;
  }
  
  if (schema.pages.length === 0) {
    errors.push('Schema has no pages');
    return errors;
  }
  
  // Validate each page
  schema.pages.forEach((page, pageIndex) => {
    if (!page) {
      errors.push(`Page ${pageIndex} is null or undefined`);
      return;
    }
    
    if (!page.id) {
      errors.push(`Page ${pageIndex} missing id`);
    }
    
    if (!page.sections || !Array.isArray(page.sections)) {
      errors.push(`Page ${pageIndex} (${page.id}) missing sections array`);
      return;
    }
    
    // Validate each section
    page.sections.forEach((section, sectionIndex) => {
      if (!section) {
        errors.push(`Page ${pageIndex}, Section ${sectionIndex} is null or undefined`);
        return;
      }
      
      if (!section.id) {
        errors.push(`Page ${pageIndex}, Section ${sectionIndex} missing id`);
      }
      
      if (!section.questions || !Array.isArray(section.questions)) {
        errors.push(`Page ${pageIndex}, Section ${sectionIndex} (${section.id}) missing questions array`);
        return;
      }
      
      // Validate each question
      section.questions.forEach((question, questionIndex) => {
        if (!question) {
          errors.push(`Page ${pageIndex}, Section ${sectionIndex}, Question ${questionIndex} is null or undefined`);
          return;
        }
        
        if (!question.id) {
          errors.push(`Page ${pageIndex}, Section ${sectionIndex}, Question ${questionIndex} missing id`);
        }
        
        if (!question.type) {
          errors.push(`Page ${pageIndex}, Section ${sectionIndex}, Question ${questionIndex} (${question.id}) missing type`);
        }
        
        // Check for invalid type case (Type vs type)
        if (question.Type) {
          errors.push(`Page ${pageIndex}, Section ${sectionIndex}, Question ${questionIndex} (${question.id}) uses capital 'Type' instead of lowercase 'type'`);
        }
      });
    });
  });
  
  return errors;
}

// Function to check for common navigation issues
function checkNavigationLogic() {
  const issues = [];
  
  // Check for undefined question access patterns
  const formRendererPath = path.join(__dirname, 'components', 'forms', 'FormFieldRenderer.tsx');
  const enhancedRendererPath = path.join(__dirname, 'components', 'forms', 'EnhancedFormRenderer.tsx');
  
  try {
    if (fs.existsSync(formRendererPath)) {
      const content = fs.readFileSync(formRendererPath, 'utf8');
      
      // Check for unsafe question property access
      if (content.includes('question.type') && !content.includes('question && question.type')) {
        issues.push('FormFieldRenderer may access question.type without null checking');
      }
      
      if (content.includes('.Type')) {
        issues.push('FormFieldRenderer contains references to .Type (should be .type)');
      }
    }
    
    if (fs.existsSync(enhancedRendererPath)) {
      const content = fs.readFileSync(enhancedRendererPath, 'utf8');
      
      if (content.includes('schema.pages[') && !content.includes('schema?.pages')) {
        issues.push('EnhancedFormRenderer may access schema.pages without null checking');
      }
    }
  } catch (error) {
    issues.push(`Error checking files: ${error.message}`);
  }
  
  return issues;
}

// Mock form schema for testing (extracted from enhanced-form-demo.tsx)
const testSchema = {
  pages: [
    {
      id: 'page1',
      title: 'Contact Information',
      description: 'Please provide your contact details',
      order: 0,
      sections: [
        {
          id: 'section1',
          title: 'Basic Information',
          description: 'Your personal details',
          order: 0,
          questions: [
            {
              id: 'name',
              type: 'text',
              label: 'Full Name',
              required: true,
              placeholder: 'Enter your full name',
              validation: [
                { type: 'min', value: 2, message: 'Name must be at least 2 characters' }
              ]
            },
            {
              id: 'phone',
              type: 'phone',
              label: 'Phone Number',
              required: true,
              placeholder: 'Enter phone number',
              properties: {
                enableValidation: true
              }
            }
          ]
        }
      ]
    },
    {
      id: 'page2',
      title: 'Media Collection',
      description: 'Capture photos, videos, and audio',
      order: 1,
      sections: [
        {
          id: 'section2',
          title: 'Photo Documentation',
          description: 'Take multiple photos for documentation',
          order: 0,
          questions: [
            {
              id: 'photos',
              type: 'photo',
              label: 'Project Photos',
              required: false,
              placeholder: 'Add up to 5 photos',
              properties: {
                maxPhotos: 5,
                allowCamera: true,
                allowGallery: true,
                captureLocation: true
              }
            }
          ]
        }
      ]
    }
  ],
  logicRules: []
};

console.log('📋 Validating form schema structure...');
const schemaErrors = validateFormSchema(testSchema);

if (schemaErrors.length === 0) {
  console.log('✅ Form schema structure is valid');
} else {
  console.log('❌ Form schema validation errors:');
  schemaErrors.forEach(error => console.log(`   - ${error}`));
}

console.log('\n🔧 Checking navigation logic...');
const navigationIssues = checkNavigationLogic();

if (navigationIssues.length === 0) {
  console.log('✅ Navigation logic appears to be safe');
} else {
  console.log('❌ Potential navigation issues:');
  navigationIssues.forEach(issue => console.log(`   - ${issue}`));
}

console.log('\n🏃‍♂️ Testing navigation scenarios...');

// Test navigation scenarios
function testNavigationScenarios() {
  const scenarios = [];
  
  // Scenario 1: Normal navigation
  let currentPage = 0;
  const totalPages = testSchema.pages.length;
  
  // Test next page navigation
  if (currentPage < totalPages - 1) {
    currentPage++;
    scenarios.push(`✅ Navigate to page ${currentPage + 1}: SUCCESS`);
  }
  
  // Test previous page navigation
  if (currentPage > 0) {
    currentPage--;
    scenarios.push(`✅ Navigate back to page ${currentPage + 1}: SUCCESS`);
  }
  
  // Scenario 2: Edge cases
  currentPage = totalPages - 1;
  if (currentPage >= totalPages - 1) {
    scenarios.push(`✅ On last page (${currentPage + 1}), next navigation blocked: CORRECT`);
  }
  
  currentPage = 0;
  if (currentPage <= 0) {
    scenarios.push(`✅ On first page (${currentPage + 1}), previous navigation blocked: CORRECT`);
  }
  
  return scenarios;
}

const scenarios = testNavigationScenarios();
scenarios.forEach(scenario => console.log(`   ${scenario}`));

console.log('\n📝 Summary of Fixes Applied:');
console.log('   ✅ Fixed Camera.Constants.Type to "back" in VideoRecorder');
console.log('   ✅ Added null checking for question objects in FormFieldRenderer');
console.log('   ✅ Enhanced error handling in EnhancedFormRenderer navigation');
console.log('   ✅ Improved schema validation throughout form components');
console.log('   ✅ Added filtering for invalid questions/sections');

console.log('\n🚀 Next Steps:');
console.log('   1. Restart the development server: npm start or yarn start');
console.log('   2. Navigate to the enhanced form demo');
console.log('   3. Test form navigation by clicking "Try Demo Form"');
console.log('   4. Test navigation between pages using Next/Previous buttons');
console.log('   5. Monitor browser console for any remaining errors');

console.log('\n✨ Form navigation debugging complete!');
