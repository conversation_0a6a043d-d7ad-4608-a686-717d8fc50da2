/**
 * Simple GIS Map Viewer Component
 * Provides basic map functionality for development and testing
 */

import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  PanResponder,
  Dimensions,
} from 'react-native';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

interface MapRegion {
  latitude: number;
  longitude: number;
  latitudeDelta: number;
  longitudeDelta: number;
}

interface GISMapViewerProps {
  region?: MapRegion;
  initialRegion?: MapRegion;
  onRegionChange?: (region: MapRegion) => void;
  onFeatureCreate?: (feature: any) => void;
  onFeatureUpdate?: (feature: any) => void;
  onFeatureDelete?: (featureId: string) => void;
  onFeatureCreated?: (feature: any) => void;
  onMeasurementComplete?: (measurement: any) => void;
  layers?: any[];
  activeDrawingTool?: string | null;
  activeMeasurementTool?: string | null;
  baseMapType?: string;
  style?: any;
}

export interface MapRef {
  zoomIn: () => void;
  zoomOut: () => void;
  exportGeoJSON: () => Promise<void>;
  exportShapefile: () => Promise<void>;
  importLayer: (file: File) => Promise<void>;
  runAnalysis: (type: string, options?: any) => Promise<any>;
  centerOnFeature: (featureId: string) => void;
  selectFeature: (featureId: string | null) => void;
  startMeasurement: () => void;
  stopMeasurement: () => void;
}

export const GISMapViewer = forwardRef<MapRef, GISMapViewerProps>(({
  region: propRegion,
  initialRegion = {
    latitude: 37.7749,
    longitude: -122.4194,
    latitudeDelta: 0.05,
    longitudeDelta: 0.05,
  },
  onRegionChange,
  onFeatureCreate,
  onFeatureUpdate,
  onFeatureDelete,
  onFeatureCreated,
  onMeasurementComplete,
  layers = [],
  activeDrawingTool,
  activeMeasurementTool,
  baseMapType = 'standard',
  style,
}, ref) => {
  const [region, setRegion] = useState<MapRegion>(propRegion || initialRegion);
  const [features, setFeatures] = useState<any[]>([]);
  const [selectedFeature, setSelectedFeature] = useState<string | null>(null);
  const [isMeasuring, setIsMeasuring] = useState(false);

  // Update region when prop changes
  useEffect(() => {
    if (propRegion) {
      setRegion(propRegion);
    }
  }, [propRegion]);

  // Update measuring state based on active tool
  useEffect(() => {
    setIsMeasuring(!!activeMeasurementTool);
  }, [activeMeasurementTool]);

  // Expose methods through ref
  useImperativeHandle(ref, () => ({
    zoomIn: () => {
      const newRegion = {
        ...region,
        latitudeDelta: region.latitudeDelta * 0.7,
        longitudeDelta: region.longitudeDelta * 0.7,
      };
      setRegion(newRegion);
      onRegionChange?.(newRegion);
    },
    
    zoomOut: () => {
      const newRegion = {
        ...region,
        latitudeDelta: region.latitudeDelta * 1.4,
        longitudeDelta: region.longitudeDelta * 1.4,
      };
      setRegion(newRegion);
      onRegionChange?.(newRegion);
    },
    
    exportGeoJSON: async () => {
      console.log('Exporting GeoJSON...');
      // Mock export functionality
    },
    
    exportShapefile: async () => {
      console.log('Exporting Shapefile...');
      // Mock export functionality
    },
    
    importLayer: async (file: File) => {
      console.log('Importing layer:', file);
      // Mock import functionality
    },
    
    runAnalysis: async (type: string, options?: any) => {
      console.log('Running analysis:', type, options);
      // Mock analysis functionality
      return { result: 'mock_analysis_result' };
    },
    
    centerOnFeature: (featureId: string) => {
      console.log('Centering on feature:', featureId);
      // Mock center functionality
    },
    
    selectFeature: (featureId: string | null) => {
      setSelectedFeature(featureId);
      console.log('Selected feature:', featureId);
    },
    
    startMeasurement: () => {
      setIsMeasuring(true);
      console.log('Started measurement');
    },
    
    stopMeasurement: () => {
      setIsMeasuring(false);
      console.log('Stopped measurement');
    },
  }));

  // Pan responder for map interaction
  const panResponder = PanResponder.create({
    onStartShouldSetPanResponder: () => true,
    onMoveShouldSetPanResponder: () => true,
    
    onPanResponderGrant: (evt) => {
      // Handle touch start
      const { locationX, locationY } = evt.nativeEvent;
      console.log('Touch started at:', locationX, locationY);
    },
    
    onPanResponderMove: (evt, gestureState) => {
      // Handle pan movement
      const { dx, dy } = gestureState;
      
      if (Math.abs(dx) > 10 || Math.abs(dy) > 10) {
        const deltaLat = dy * region.latitudeDelta / SCREEN_HEIGHT;
        const deltaLng = -dx * region.longitudeDelta / SCREEN_WIDTH;
        
        const newRegion = {
          ...region,
          latitude: region.latitude + deltaLat,
          longitude: region.longitude + deltaLng,
        };
        
        setRegion(newRegion);
        onRegionChange?.(newRegion);
      }
    },
    
    onPanResponderRelease: (evt, gestureState) => {
      // Handle touch end
      const { dx, dy } = gestureState;
      
      if (Math.abs(dx) < 5 && Math.abs(dy) < 5) {
        // This was a tap, not a pan
        handleMapTap(evt);
      }
    },
  });

  const handleMapTap = (evt: any) => {
    const { locationX, locationY } = evt.nativeEvent;
    
    // Convert screen coordinates to lat/lng
    const lat = region.latitude + ((SCREEN_HEIGHT / 2 - locationY) / SCREEN_HEIGHT) * region.latitudeDelta;
    const lng = region.longitude + ((locationX - SCREEN_WIDTH / 2) / SCREEN_WIDTH) * region.longitudeDelta;
    
    console.log('Map tapped at:', lat, lng);
    
    // Create a new feature if measuring
    if (isMeasuring) {
      const newFeature = {
        id: `feature_${Date.now()}`,
        type: 'Feature',
        properties: {
          name: `Point ${features.length + 1}`,
          timestamp: new Date().toISOString(),
        },
        geometry: {
          type: 'Point',
          coordinates: [lng, lat],
        },
      };
      
      setFeatures(prev => [...prev, newFeature]);
      onFeatureCreate?.(newFeature);
      onFeatureCreated?.(newFeature);

      // If measuring, create a measurement result
      if (activeMeasurementTool) {
        const measurement = {
          id: `measurement_${Date.now()}`,
          type: activeMeasurementTool,
          value: Math.random() * 100, // Mock measurement value
          unit: activeMeasurementTool === 'distance' ? 'm' : activeMeasurementTool === 'area' ? 'm²' : '°',
          coordinates: [lng, lat],
          timestamp: new Date().toISOString(),
        };
        onMeasurementComplete?.(measurement);
      }
    }
  };

  return (
    <View style={[styles.container, style]} {...panResponder.panHandlers}>
      {/* Map Background */}
      <View style={styles.mapBackground}>
        {/* Grid Pattern */}
        <View style={styles.gridContainer}>
          {Array.from({ length: 20 }, (_, i) => (
            <View key={`h-${i}`} style={[styles.gridLineH, { top: (i * SCREEN_HEIGHT) / 20 }]} />
          ))}
          {Array.from({ length: 20 }, (_, i) => (
            <View key={`v-${i}`} style={[styles.gridLineV, { left: (i * SCREEN_WIDTH) / 20 }]} />
          ))}
        </View>
        
        {/* Map Info */}
        <View style={styles.mapInfo}>
          <Text style={styles.mapInfoText}>
            📍 {region.latitude.toFixed(4)}, {region.longitude.toFixed(4)}
          </Text>
          <Text style={styles.mapInfoSubtext}>
            Zoom: {(1 / region.latitudeDelta).toFixed(1)}x
          </Text>
        </View>
        
        {/* Features */}
        {features.map((feature) => (
          <View
            key={feature.id}
            style={[
              styles.feature,
              {
                left: SCREEN_WIDTH / 2 + ((feature.geometry.coordinates[0] - region.longitude) / region.longitudeDelta) * SCREEN_WIDTH,
                top: SCREEN_HEIGHT / 2 - ((feature.geometry.coordinates[1] - region.latitude) / region.latitudeDelta) * SCREEN_HEIGHT,
                backgroundColor: selectedFeature === feature.id ? '#FF6B6B' : '#007AFF',
              }
            ]}
          >
            <Text style={styles.featureText}>📍</Text>
          </View>
        ))}
        
        {/* Measurement Mode Indicator */}
        {isMeasuring && (
          <View style={styles.measurementIndicator}>
            <Text style={styles.measurementText}>📏 Measurement Mode</Text>
            <Text style={styles.measurementSubtext}>Tap to add points</Text>
          </View>
        )}
        
        {/* Feature Count */}
        {features.length > 0 && (
          <View style={styles.featureCounter}>
            <Text style={styles.featureCounterText}>
              Features: {features.length}
            </Text>
          </View>
        )}
      </View>
      
      {/* Map Controls */}
      <View style={styles.controls}>
        <TouchableOpacity
          style={styles.controlButton}
          onPress={() => {
            const newRegion = {
              ...region,
              latitudeDelta: region.latitudeDelta * 0.7,
              longitudeDelta: region.longitudeDelta * 0.7,
            };
            setRegion(newRegion);
            onRegionChange?.(newRegion);
          }}
        >
          <Text style={styles.controlButtonText}>+</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.controlButton}
          onPress={() => {
            const newRegion = {
              ...region,
              latitudeDelta: region.latitudeDelta * 1.4,
              longitudeDelta: region.longitudeDelta * 1.4,
            };
            setRegion(newRegion);
            onRegionChange?.(newRegion);
          }}
        >
          <Text style={styles.controlButtonText}>-</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.controlButton, { backgroundColor: isMeasuring ? '#FF6B6B' : '#007AFF' }]}
          onPress={() => {
            setIsMeasuring(!isMeasuring);
            console.log(isMeasuring ? 'Stopped measurement' : 'Started measurement');
          }}
        >
          <Text style={styles.controlButtonText}>📏</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.controlButton}
          onPress={() => {
            setFeatures([]);
            setSelectedFeature(null);
            console.log('Cleared all features');
          }}
        >
          <Text style={styles.controlButtonText}>🗑️</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#E8F4FD',
  },
  mapBackground: {
    flex: 1,
    position: 'relative',
  },
  gridContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  gridLineH: {
    position: 'absolute',
    left: 0,
    right: 0,
    height: 1,
    backgroundColor: '#D0E7F5',
  },
  gridLineV: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    width: 1,
    backgroundColor: '#D0E7F5',
  },
  mapInfo: {
    position: 'absolute',
    top: 20,
    left: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    padding: 10,
    borderRadius: 8,
  },
  mapInfoText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
  },
  mapInfoSubtext: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  feature: {
    position: 'absolute',
    width: 20,
    height: 20,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    transform: [{ translateX: -10 }, { translateY: -10 }],
  },
  featureText: {
    fontSize: 12,
    color: 'white',
  },
  measurementIndicator: {
    position: 'absolute',
    top: 20,
    right: 20,
    backgroundColor: 'rgba(255, 107, 107, 0.9)',
    padding: 10,
    borderRadius: 8,
  },
  measurementText: {
    fontSize: 14,
    fontWeight: '600',
    color: 'white',
  },
  measurementSubtext: {
    fontSize: 12,
    color: 'white',
    opacity: 0.8,
    marginTop: 2,
  },
  featureCounter: {
    position: 'absolute',
    bottom: 80,
    left: 20,
    backgroundColor: 'rgba(0, 122, 255, 0.9)',
    padding: 8,
    borderRadius: 6,
  },
  featureCounterText: {
    fontSize: 12,
    color: 'white',
    fontWeight: '500',
  },
  controls: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    gap: 10,
  },
  controlButton: {
    width: 44,
    height: 44,
    backgroundColor: '#007AFF',
    borderRadius: 22,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 4,
  },
  controlButtonText: {
    fontSize: 18,
    color: 'white',
    fontWeight: 'bold',
  },
});

export default GISMapViewer;
