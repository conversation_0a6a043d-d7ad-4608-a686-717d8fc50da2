# Map UI Test Plan - Verify No Overlapping

## Pre-Test Setup
1. Run `fix-map-ui-complete.bat` to clear all caches
2. Wait for Expo to start completely
3. Have both Android and Web ready for testing

## Android Testing Checklist

### ✓ Initial Load
- [ ] App loads without IOException error
- [ ] Map screen displays correctly
- [ ] No "Failed to download remote update" error

### ✓ <PERSON>u <PERSON>
- [ ] Blue circular button visible at bottom-left
- [ ] <PERSON><PERSON> is above map (not hidden)
- [ ] Clicking opens toolbar smoothly

### ✓ Bottom Toolbar
- [ ] Slides up from bottom when menu clicked
- [ ] Contains: Layers, Draw, Measure, Analyze, Features
- [ ] No overlap with map
- [ ] Scrollable if screen is small

### ✓ Drawing Tools
- [ ] Click "Draw" opens panel from bottom
- [ ] Panel shows Point, Line, Polygon options
- [ ] Selecting tool closes panel
- [ ] Status badge appears at top showing mode
- [ ] Can tap map to draw
- [ ] Drawing actually works

### ✓ Right-Side Controls
- [ ] Zoom buttons visible and clickable
- [ ] Location button above zoom
- [ ] No overlap with other UI elements
- [ ] Proper spacing between buttons

### ✓ Status Badges
- [ ] Appear at top-left
- [ ] Show active modes (Drawing, Measuring, Offline)
- [ ] Don't overlap with map or other UI

## Web Testing Checklist

### ✓ Initial Load
- [ ] Map loads without errors
- [ ] No "OVERLAP" text visible
- [ ] UI elements properly positioned

### ✓ Bottom Toolbar
- [ ] Fixed at bottom of screen
- [ ] White background with shadow
- [ ] Tools: Point, Line, Polygon, Measure
- [ ] No overlap with map

### ✓ Drawing Panel
- [ ] Opens above toolbar (not overlapping)
- [ ] Proper spacing from edges
- [ ] Can select drawing tools
- [ ] Panel has rounded corners and shadow

### ✓ Spatial Analysis
- [ ] Opens in correct position
- [ ] No overlap with toolbar
- [ ] Accessible and functional

### ✓ Zoom Controls
- [ ] Positioned at bottom-right
- [ ] Above toolbar height
- [ ] Clickable and functional

## Expected Results

### Android
- Menu button → Toolbar slides up → Select tool → Draw on map
- All UI elements visible and accessible
- No overlapping components
- Smooth animations

### Web
- Toolbar fixed at bottom
- Panels open above toolbar
- Zoom controls don't overlap
- All tools functional

## Common Issues & Solutions

| Issue | Solution |
|-------|----------|
| IOException on Android | Updates disabled in app.json |
| Toolbar overlaps map | Fixed positioning with z-index |
| Drawing tools overlap zoom | Proper spacing and positioning |
| Can't draw on map | Event handling fixed |
| UI elements hidden | Z-index hierarchy corrected |

## Final Verification
1. Take screenshot showing clean UI
2. Test all drawing modes
3. Verify no overlapping in any state
4. Confirm smooth user experience

---

✅ **SUCCESS CRITERIA**: All checkboxes marked, no overlapping UI elements, smooth functionality
