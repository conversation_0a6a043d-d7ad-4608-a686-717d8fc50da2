/**
 * Advanced Offline Map Caching System for FieldSyncPro
 * 
 * Provides comprehensive caching capabilities for map tiles, features, analysis results,
 * and spatial data to enable robust offline functionality with intelligent cache management.
 * 
 * Features:
 * - Hierarchical tile caching with zoom level optimization
 * - Feature data caching with spatial indexing
 * - Analysis result caching with dependency tracking
 * - Intelligent cache eviction strategies
 * - Storage quota management
 * - Background sync capabilities
 * - Cache analytics and monitoring
 * 
 * @version 2.0.0
 * <AUTHOR> Development Team
 */

import * as SecureStore from 'expo-secure-store';
import { Platform } from 'react-native';

// Cache configuration constants
const CACHE_VERSION = '2.0.0';
const MAX_STORAGE_SIZE = 500 * 1024 * 1024; // 500MB default
const TILE_CACHE_SIZE = 300 * 1024 * 1024;   // 300MB for tiles
const FEATURE_CACHE_SIZE = 100 * 1024 * 1024; // 100MB for features
const ANALYSIS_CACHE_SIZE = 50 * 1024 * 1024;  // 50MB for analysis results
const DEFAULT_TTL = 7 * 24 * 60 * 60 * 1000;   // 7 days in milliseconds

// Cache item interfaces
interface CacheItem<T = any> {
  id: string;
  data: T;
  timestamp: number;
  ttl: number;
  size: number;
  accessCount: number;
  lastAccessed: number;
  priority: 'low' | 'medium' | 'high' | 'critical';
  tags: string[];
  metadata?: Record<string, any>;
}

interface TileCacheItem extends CacheItem {
  data: {
    blob: ArrayBuffer;
    mimeType: string;
    tileKey: string;
    zoomLevel: number;
    x: number;
    y: number;
  };
}

interface FeatureCacheItem extends CacheItem {
  data: {
    features: GeoJSON.Feature[];
    layerId: string;
    bounds: [number, number, number, number];
    featureCount: number;
  };
}

interface AnalysisCacheItem extends CacheItem {
  data: {
    result: any;
    analysisType: string;
    inputFeatureIds: string[];
    parameters: Record<string, any>;
    executionTime: number;
  };
}

interface CacheStats {
  totalSize: number;
  totalItems: number;
  hitRate: number;
  missRate: number;
  evictionCount: number;
  lastOptimization: number;
  storageBreakdown: {
    tiles: { size: number; count: number };
    features: { size: number; count: number };
    analysis: { size: number; count: number };
  };
}

interface CacheConfig {
  maxStorageSize: number;
  tileMaxSize: number;
  featureMaxSize: number;
  analysisMaxSize: number;
  defaultTTL: number;
  enableBackgroundSync: boolean;
  enableCompression: boolean;
  evictionStrategy: 'lru' | 'lfu' | 'fifo' | 'adaptive';
  syncInterval: number;
}

/**
 * Advanced Map Cache Manager
 * 
 * Manages all aspects of offline map caching with intelligent storage optimization
 */
export class AdvancedMapCacheSystem {
  private config: CacheConfig;
  private tileCache: Map<string, TileCacheItem> = new Map();
  private featureCache: Map<string, FeatureCacheItem> = new Map();
  private analysisCache: Map<string, AnalysisCacheItem> = new Map();
  
  private stats: CacheStats = {
    totalSize: 0,
    totalItems: 0,
    hitRate: 0,
    missRate: 0,
    evictionCount: 0,
    lastOptimization: Date.now(),
    storageBreakdown: {
      tiles: { size: 0, count: 0 },
      features: { size: 0, count: 0 },
      analysis: { size: 0, count: 0 },
    },
  };
  
  private requestCounts = {
    hits: 0,
    misses: 0,
  };
  
  private backgroundSyncTimer?: NodeJS.Timeout;
  private optimizationTimer?: NodeJS.Timeout;

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = {
      maxStorageSize: MAX_STORAGE_SIZE,
      tileMaxSize: TILE_CACHE_SIZE,
      featureMaxSize: FEATURE_CACHE_SIZE,
      analysisMaxSize: ANALYSIS_CACHE_SIZE,
      defaultTTL: DEFAULT_TTL,
      enableBackgroundSync: true,
      enableCompression: true,
      evictionStrategy: 'adaptive',
      syncInterval: 30 * 60 * 1000, // 30 minutes
      ...config,
    };

    this.initialize();
  }

  /**
   * Initialize the cache system
   */
  private async initialize(): Promise<void> {
    console.log('🗄️ Initializing Advanced Map Cache System v' + CACHE_VERSION);
    
    try {
      // Load existing cache data
      await this.loadCacheFromStorage();
      
      // Start background processes
      if (this.config.enableBackgroundSync) {
        this.startBackgroundSync();
      }
      
      // Start optimization timer
      this.startOptimizationTimer();
      
      console.log('✅ Advanced Map Cache System initialized successfully');
      console.log(`📊 Cache stats:`, this.getStats());
      
    } catch (error) {
      console.error('❌ Failed to initialize cache system:', error);
      // Continue with empty cache
    }
  }

  // ========================================
  // TILE CACHING METHODS
  // ========================================

  /**
   * Cache a map tile
   */
  async cacheTile(
    tileKey: string,
    tileData: ArrayBuffer,
    zoomLevel: number,
    x: number,
    y: number,
    mimeType: string = 'image/png',
    priority: CacheItem['priority'] = 'medium'
  ): Promise<boolean> {
    try {
      const size = tileData.byteLength;
      
      // Check if we have space for this tile
      if (!this.canFitInCache('tiles', size)) {
        await this.evictItems('tiles', size);
      }
      
      const cacheItem: TileCacheItem = {
        id: tileKey,
        data: {
          blob: tileData,
          mimeType,
          tileKey,
          zoomLevel,
          x,
          y,
        },
        timestamp: Date.now(),
        ttl: this.config.defaultTTL,
        size,
        accessCount: 1,
        lastAccessed: Date.now(),
        priority,
        tags: ['tile', `zoom-${zoomLevel}`],
        metadata: { zoomLevel, x, y },
      };
      
      this.tileCache.set(tileKey, cacheItem);
      this.updateStats('tiles', size, 1);
      
      // Persist to storage if important
      if (priority === 'high' || priority === 'critical') {
        await this.persistCacheItem('tiles', tileKey, cacheItem);
      }
      
      return true;
      
    } catch (error) {
      console.error('❌ Failed to cache tile:', error);
      return false;
    }
  }

  /**
   * Retrieve a cached tile
   */
  async getTile(tileKey: string): Promise<ArrayBuffer | null> {
    try {
      let cacheItem = this.tileCache.get(tileKey);
      
      // Try loading from persistent storage if not in memory
      if (!cacheItem) {
        cacheItem = await this.loadCacheItem('tiles', tileKey);
        if (cacheItem) {
          this.tileCache.set(tileKey, cacheItem as TileCacheItem);
        }
      }
      
      if (cacheItem && !this.isExpired(cacheItem)) {
        // Update access statistics
        cacheItem.accessCount++;
        cacheItem.lastAccessed = Date.now();
        this.requestCounts.hits++;
        
        return (cacheItem as TileCacheItem).data.blob;
      }
      
      this.requestCounts.misses++;
      return null;
      
    } catch (error) {
      console.error('❌ Failed to retrieve tile:', error);
      this.requestCounts.misses++;
      return null;
    }
  }

  /**
   * Pre-cache tiles for a region
   */
  async precacheTilesForRegion(
    bounds: [number, number, number, number], // [west, south, east, north]
    zoomLevels: number[],
    tileUrlTemplate: string,
    onProgress?: (progress: number, total: number) => void
  ): Promise<number> {
    let cachedCount = 0;
    const tiles = this.calculateTilesForRegion(bounds, zoomLevels);
    const totalTiles = tiles.length;
    
    console.log(`🗺️ Pre-caching ${totalTiles} tiles for region`);
    
    for (let i = 0; i < tiles.length; i++) {
      const tile = tiles[i];
      const tileKey = `${tile.z}/${tile.x}/${tile.y}`;
      
      // Check if already cached
      if (await this.getTile(tileKey)) {
        cachedCount++;
        onProgress?.(i + 1, totalTiles);
        continue;
      }
      
      try {
        const tileUrl = this.replaceTileUrlTemplate(tileUrlTemplate, tile);
        const response = await fetch(tileUrl);
        
        if (response.ok) {
          const arrayBuffer = await response.arrayBuffer();
          const success = await this.cacheTile(
            tileKey,
            arrayBuffer,
            tile.z,
            tile.x,
            tile.y,
            response.headers.get('content-type') || 'image/png',
            'high'
          );
          
          if (success) {
            cachedCount++;
          }
        }
        
      } catch (error) {
        console.warn(`⚠️ Failed to cache tile ${tileKey}:`, error);
      }
      
      onProgress?.(i + 1, totalTiles);
      
      // Small delay to prevent overwhelming the server
      if (i % 10 === 0) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }
    
    console.log(`✅ Successfully cached ${cachedCount}/${totalTiles} tiles`);
    return cachedCount;
  }

  // ========================================
  // FEATURE CACHING METHODS
  // ========================================

  /**
   * Cache feature data for a layer
   */
  async cacheFeatures(
    layerId: string,
    features: GeoJSON.Feature[],
    bounds: [number, number, number, number],
    priority: CacheItem['priority'] = 'medium'
  ): Promise<boolean> {
    try {
      const serializedData = JSON.stringify(features);
      const size = new Blob([serializedData]).size;
      
      if (!this.canFitInCache('features', size)) {
        await this.evictItems('features', size);
      }
      
      const cacheItem: FeatureCacheItem = {
        id: layerId,
        data: {
          features,
          layerId,
          bounds,
          featureCount: features.length,
        },
        timestamp: Date.now(),
        ttl: this.config.defaultTTL,
        size,
        accessCount: 1,
        lastAccessed: Date.now(),
        priority,
        tags: ['features', `layer-${layerId}`],
        metadata: { layerId, featureCount: features.length, bounds },
      };
      
      this.featureCache.set(layerId, cacheItem);
      this.updateStats('features', size, 1);
      
      // Persist critical data
      if (priority === 'critical') {
        await this.persistCacheItem('features', layerId, cacheItem);
      }
      
      return true;
      
    } catch (error) {
      console.error('❌ Failed to cache features:', error);
      return false;
    }
  }

  /**
   * Retrieve cached features for a layer
   */
  async getFeatures(layerId: string): Promise<GeoJSON.Feature[] | null> {
    try {
      let cacheItem = this.featureCache.get(layerId);
      
      if (!cacheItem) {
        cacheItem = await this.loadCacheItem('features', layerId);
        if (cacheItem) {
          this.featureCache.set(layerId, cacheItem as FeatureCacheItem);
        }
      }
      
      if (cacheItem && !this.isExpired(cacheItem)) {
        cacheItem.accessCount++;
        cacheItem.lastAccessed = Date.now();
        this.requestCounts.hits++;
        
        return (cacheItem as FeatureCacheItem).data.features;
      }
      
      this.requestCounts.misses++;
      return null;
      
    } catch (error) {
      console.error('❌ Failed to retrieve features:', error);
      this.requestCounts.misses++;
      return null;
    }
  }

  // ========================================
  // ANALYSIS RESULT CACHING METHODS
  // ========================================

  /**
   * Cache analysis results
   */
  async cacheAnalysisResult(
    analysisId: string,
    result: any,
    analysisType: string,
    inputFeatureIds: string[],
    parameters: Record<string, any>,
    executionTime: number,
    priority: CacheItem['priority'] = 'medium'
  ): Promise<boolean> {
    try {
      const serializedData = JSON.stringify(result);
      const size = new Blob([serializedData]).size;
      
      if (!this.canFitInCache('analysis', size)) {
        await this.evictItems('analysis', size);
      }
      
      const cacheItem: AnalysisCacheItem = {
        id: analysisId,
        data: {
          result,
          analysisType,
          inputFeatureIds,
          parameters,
          executionTime,
        },
        timestamp: Date.now(),
        ttl: this.config.defaultTTL,
        size,
        accessCount: 1,
        lastAccessed: Date.now(),
        priority,
        tags: ['analysis', `type-${analysisType}`],
        metadata: { analysisType, executionTime, inputFeatureCount: inputFeatureIds.length },
      };
      
      this.analysisCache.set(analysisId, cacheItem);
      this.updateStats('analysis', size, 1);
      
      return true;
      
    } catch (error) {
      console.error('❌ Failed to cache analysis result:', error);
      return false;
    }
  }

  /**
   * Retrieve cached analysis result
   */
  async getAnalysisResult(analysisId: string): Promise<any | null> {
    try {
      let cacheItem = this.analysisCache.get(analysisId);
      
      if (!cacheItem) {
        cacheItem = await this.loadCacheItem('analysis', analysisId);
        if (cacheItem) {
          this.analysisCache.set(analysisId, cacheItem as AnalysisCacheItem);
        }
      }
      
      if (cacheItem && !this.isExpired(cacheItem)) {
        cacheItem.accessCount++;
        cacheItem.lastAccessed = Date.now();
        this.requestCounts.hits++;
        
        return (cacheItem as AnalysisCacheItem).data.result;
      }
      
      this.requestCounts.misses++;
      return null;
      
    } catch (error) {
      console.error('❌ Failed to retrieve analysis result:', error);
      this.requestCounts.misses++;
      return null;
    }
  }

  // ========================================
  // CACHE MANAGEMENT METHODS
  // ========================================

  /**
   * Clear all cached data
   */
  async clearCache(): Promise<void> {
    try {
      this.tileCache.clear();
      this.featureCache.clear();
      this.analysisCache.clear();
      
      // Clear persistent storage
      await this.clearPersistentStorage();
      
      // Reset stats
      this.stats = {
        totalSize: 0,
        totalItems: 0,
        hitRate: 0,
        missRate: 0,
        evictionCount: 0,
        lastOptimization: Date.now(),
        storageBreakdown: {
          tiles: { size: 0, count: 0 },
          features: { size: 0, count: 0 },
          analysis: { size: 0, count: 0 },
        },
      };
      
      this.requestCounts = { hits: 0, misses: 0 };
      
      console.log('🗑️ Cache cleared successfully');
      
    } catch (error) {
      console.error('❌ Failed to clear cache:', error);
    }
  }

  /**
   * Clear expired items from cache
   */
  async clearExpiredItems(): Promise<number> {
    let removedCount = 0;
    const now = Date.now();
    
    // Clear expired tiles
    for (const [key, item] of this.tileCache.entries()) {
      if (this.isExpired(item)) {
        this.tileCache.delete(key);
        this.updateStats('tiles', -item.size, -1);
        removedCount++;
      }
    }
    
    // Clear expired features
    for (const [key, item] of this.featureCache.entries()) {
      if (this.isExpired(item)) {
        this.featureCache.delete(key);
        this.updateStats('features', -item.size, -1);
        removedCount++;
      }
    }
    
    // Clear expired analysis results
    for (const [key, item] of this.analysisCache.entries()) {
      if (this.isExpired(item)) {
        this.analysisCache.delete(key);
        this.updateStats('analysis', -item.size, -1);
        removedCount++;
      }
    }
    
    console.log(`🧹 Removed ${removedCount} expired cache items`);
    return removedCount;
  }

  /**
   * Optimize cache storage
   */
  async optimizeCache(): Promise<void> {
    console.log('🔧 Starting cache optimization...');
    
    try {
      // Remove expired items
      await this.clearExpiredItems();
      
      // Check if we're over storage limits
      const totalSize = this.getTotalCacheSize();
      if (totalSize > this.config.maxStorageSize) {
        await this.evictItems('all', totalSize - this.config.maxStorageSize);
      }
      
      // Update hit rate statistics
      const totalRequests = this.requestCounts.hits + this.requestCounts.misses;
      if (totalRequests > 0) {
        this.stats.hitRate = (this.requestCounts.hits / totalRequests) * 100;
        this.stats.missRate = (this.requestCounts.misses / totalRequests) * 100;
      }
      
      this.stats.lastOptimization = Date.now();
      
      console.log('✅ Cache optimization completed');
      console.log('📊 Updated stats:', this.getStats());
      
    } catch (error) {
      console.error('❌ Cache optimization failed:', error);
    }
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    return {
      ...this.stats,
      totalSize: this.getTotalCacheSize(),
      totalItems: this.tileCache.size + this.featureCache.size + this.analysisCache.size,
    };
  }

  /**
   * Export cache data
   */
  async exportCache(): Promise<string> {
    const exportData = {
      version: CACHE_VERSION,
      timestamp: new Date().toISOString(),
      config: this.config,
      stats: this.getStats(),
      tileCache: Array.from(this.tileCache.entries()),
      featureCache: Array.from(this.featureCache.entries()),
      analysisCache: Array.from(this.analysisCache.entries()),
    };
    
    return JSON.stringify(exportData, null, 2);
  }

  // ========================================
  // PRIVATE HELPER METHODS
  // ========================================

  /**
   * Check if cache item is expired
   */
  private isExpired(item: CacheItem): boolean {
    return Date.now() > (item.timestamp + item.ttl);
  }

  /**
   * Check if we can fit new data in cache
   */
  private canFitInCache(type: 'tiles' | 'features' | 'analysis', size: number): boolean {
    const currentSize = this.getCacheSizeByType(type);
    const maxSize = type === 'tiles' ? this.config.tileMaxSize :
                   type === 'features' ? this.config.featureMaxSize :
                   this.config.analysisMaxSize;
    
    return (currentSize + size) <= maxSize;
  }

  /**
   * Get cache size by type
   */
  private getCacheSizeByType(type: 'tiles' | 'features' | 'analysis'): number {
    let size = 0;
    
    const cache = type === 'tiles' ? this.tileCache :
                  type === 'features' ? this.featureCache :
                  this.analysisCache;
    
    for (const item of cache.values()) {
      size += item.size;
    }
    
    return size;
  }

  /**
   * Get total cache size
   */
  private getTotalCacheSize(): number {
    return this.getCacheSizeByType('tiles') +
           this.getCacheSizeByType('features') +
           this.getCacheSizeByType('analysis');
  }

  /**
   * Evict items to make space
   */
  private async evictItems(type: 'tiles' | 'features' | 'analysis' | 'all', sizeNeeded: number): Promise<void> {
    let sizeFreed = 0;
    
    // Collect all items for eviction consideration
    const candidates: Array<{ key: string; item: CacheItem; type: string }> = [];
    
    if (type === 'all' || type === 'tiles') {
      for (const [key, item] of this.tileCache.entries()) {
        candidates.push({ key, item, type: 'tiles' });
      }
    }
    
    if (type === 'all' || type === 'features') {
      for (const [key, item] of this.featureCache.entries()) {
        candidates.push({ key, item, type: 'features' });
      }
    }
    
    if (type === 'all' || type === 'analysis') {
      for (const [key, item] of this.analysisCache.entries()) {
        candidates.push({ key, item, type: 'analysis' });
      }
    }
    
    // Sort by eviction strategy
    this.sortForEviction(candidates);
    
    // Evict items until we have enough space
    for (const candidate of candidates) {
      if (sizeFreed >= sizeNeeded) break;
      
      const cache = candidate.type === 'tiles' ? this.tileCache :
                    candidate.type === 'features' ? this.featureCache :
                    this.analysisCache;
      
      cache.delete(candidate.key);
      sizeFreed += candidate.item.size;
      this.updateStats(candidate.type as any, -candidate.item.size, -1);
      this.stats.evictionCount++;
    }
    
    console.log(`🗑️ Evicted ${sizeFreed} bytes of cache data`);
  }

  /**
   * Sort candidates for eviction based on strategy
   */
  private sortForEviction(candidates: Array<{ key: string; item: CacheItem; type: string }>): void {
    switch (this.config.evictionStrategy) {
      case 'lru': // Least Recently Used
        candidates.sort((a, b) => a.item.lastAccessed - b.item.lastAccessed);
        break;
        
      case 'lfu': // Least Frequently Used
        candidates.sort((a, b) => a.item.accessCount - b.item.accessCount);
        break;
        
      case 'fifo': // First In, First Out
        candidates.sort((a, b) => a.item.timestamp - b.item.timestamp);
        break;
        
      case 'adaptive': // Adaptive strategy considering multiple factors
        candidates.sort((a, b) => {
          const scoreA = this.calculateEvictionScore(a.item);
          const scoreB = this.calculateEvictionScore(b.item);
          return scoreA - scoreB; // Lower score = higher priority for eviction
        });
        break;
    }
  }

  /**
   * Calculate eviction score for adaptive strategy
   */
  private calculateEvictionScore(item: CacheItem): number {
    const now = Date.now();
    const age = now - item.timestamp;
    const timeSinceAccess = now - item.lastAccessed;
    
    // Priority weights
    const priorityWeight = item.priority === 'critical' ? 1000 :
                          item.priority === 'high' ? 500 :
                          item.priority === 'medium' ? 100 : 50;
    
    // Calculate score (lower = more likely to be evicted)
    const score = priorityWeight + 
                  (item.accessCount * 10) - 
                  (timeSinceAccess / 60000) - // Penalty for not being accessed
                  (age / 3600000); // Penalty for being old
    
    return score;
  }

  /**
   * Update statistics
   */
  private updateStats(type: 'tiles' | 'features' | 'analysis', sizeDelta: number, countDelta: number): void {
    this.stats.storageBreakdown[type].size += sizeDelta;
    this.stats.storageBreakdown[type].count += countDelta;
  }

  /**
   * Calculate tiles needed for a region
   */
  private calculateTilesForRegion(
    bounds: [number, number, number, number],
    zoomLevels: number[]
  ): Array<{ x: number; y: number; z: number }> {
    const tiles: Array<{ x: number; y: number; z: number }> = [];
    
    for (const zoom of zoomLevels) {
      const minTile = this.deg2tile(bounds[3], bounds[0], zoom); // north, west
      const maxTile = this.deg2tile(bounds[1], bounds[2], zoom); // south, east
      
      for (let x = minTile.x; x <= maxTile.x; x++) {
        for (let y = minTile.y; y <= maxTile.y; y++) {
          tiles.push({ x, y, z: zoom });
        }
      }
    }
    
    return tiles;
  }

  /**
   * Convert degrees to tile coordinates
   */
  private deg2tile(lat: number, lon: number, zoom: number): { x: number; y: number } {
    const latRad = lat * Math.PI / 180;
    const n = Math.pow(2, zoom);
    const x = Math.floor((lon + 180) / 360 * n);
    const y = Math.floor((1 - Math.asinh(Math.tan(latRad)) / Math.PI) / 2 * n);
    return { x, y };
  }

  /**
   * Replace tile URL template with actual coordinates
   */
  private replaceTileUrlTemplate(template: string, tile: { x: number; y: number; z: number }): string {
    return template
      .replace('{z}', tile.z.toString())
      .replace('{x}', tile.x.toString())
      .replace('{y}', tile.y.toString());
  }

  /**
   * Load cache from persistent storage
   */
  private async loadCacheFromStorage(): Promise<void> {
    try {
      // Load cache metadata
      const metadataJson = await SecureStore.getItemAsync('map_cache_metadata');
      if (metadataJson) {
        const metadata = JSON.parse(metadataJson);
        if (metadata.version === CACHE_VERSION) {
          this.stats = { ...this.stats, ...metadata.stats };
        }
      }
      
      console.log('📥 Cache metadata loaded from storage');
      
    } catch (error) {
      console.warn('⚠️ Failed to load cache from storage:', error);
    }
  }

  /**
   * Persist cache item to storage
   */
  private async persistCacheItem(type: string, key: string, item: CacheItem): Promise<void> {
    try {
      const storageKey = `map_cache_${type}_${key}`;
      await SecureStore.setItemAsync(storageKey, JSON.stringify(item));
    } catch (error) {
      console.warn('⚠️ Failed to persist cache item:', error);
    }
  }

  /**
   * Load cache item from storage
   */
  private async loadCacheItem(type: string, key: string): Promise<CacheItem | null> {
    try {
      const storageKey = `map_cache_${type}_${key}`;
      const itemJson = await SecureStore.getItemAsync(storageKey);
      if (itemJson) {
        return JSON.parse(itemJson);
      }
    } catch (error) {
      console.warn('⚠️ Failed to load cache item:', error);
    }
    return null;
  }

  /**
   * Clear persistent storage
   */
  private async clearPersistentStorage(): Promise<void> {
    try {
      // Note: expo-secure-store doesn't provide a way to list all keys
      // In a production app, you'd maintain an index of stored keys
      await SecureStore.deleteItemAsync('map_cache_metadata');
      console.log('🗑️ Persistent storage cleared');
    } catch (error) {
      console.warn('⚠️ Failed to clear persistent storage:', error);
    }
  }

  /**
   * Start background sync process
   */
  private startBackgroundSync(): void {
    this.backgroundSyncTimer = setInterval(async () => {
      try {
        await this.syncCacheToStorage();
      } catch (error) {
        console.error('❌ Background sync failed:', error);
      }
    }, this.config.syncInterval);
    
    console.log('🔄 Background sync started');
  }

  /**
   * Start optimization timer
   */
  private startOptimizationTimer(): void {
    this.optimizationTimer = setInterval(async () => {
      await this.optimizeCache();
    }, 10 * 60 * 1000); // Optimize every 10 minutes
    
    console.log('🔧 Optimization timer started');
  }

  /**
   * Sync cache metadata to storage
   */
  private async syncCacheToStorage(): Promise<void> {
    try {
      const metadata = {
        version: CACHE_VERSION,
        timestamp: new Date().toISOString(),
        stats: this.stats,
      };
      
      await SecureStore.setItemAsync('map_cache_metadata', JSON.stringify(metadata));
      console.log('💾 Cache metadata synced to storage');
      
    } catch (error) {
      console.error('❌ Failed to sync cache to storage:', error);
    }
  }

  /**
   * Cleanup and shutdown
   */
  public shutdown(): void {
    if (this.backgroundSyncTimer) {
      clearInterval(this.backgroundSyncTimer);
    }
    
    if (this.optimizationTimer) {
      clearInterval(this.optimizationTimer);
    }
    
    console.log('🛑 Advanced Map Cache System shutdown');
  }
}

// Export singleton instance
export const advancedMapCache = new AdvancedMapCacheSystem();

// React hook for cache management with error handling
export const useMapCache = () => {
  const cacheTile = async (tileKey: string, data: ArrayBuffer, zoom: number, x: number, y: number) => {
    try {
      return await advancedMapCache.cacheTile(tileKey, data, zoom, x, y);
    } catch (error) {
      console.error('Cache tile error:', error);
      return false;
    }
  };
  
  const getTile = async (tileKey: string) => {
    try {
      return await advancedMapCache.getTile(tileKey);
    } catch (error) {
      console.error('Get tile error:', error);
      return null;
    }
  };
  
  const cacheFeatures = async (layerId: string, features: GeoJSON.Feature[], bounds: [number, number, number, number]) => {
    try {
      return await advancedMapCache.cacheFeatures(layerId, features, bounds);
    } catch (error) {
      console.error('Cache features error:', error);
      return false;
    }
  };
  
  const getFeatures = async (layerId: string) => {
    try {
      return await advancedMapCache.getFeatures(layerId);
    } catch (error) {
      console.error('Get features error:', error);
      return null;
    }
  };
  
  const cacheAnalysis = async (id: string, result: any, type: string, inputs: string[], params: any, time: number) => {
    try {
      return await advancedMapCache.cacheAnalysisResult(id, result, type, inputs, params, time);
    } catch (error) {
      console.error('Cache analysis error:', error);
      return false;
    }
  };
  
  const getAnalysis = async (id: string) => {
    try {
      return await advancedMapCache.getAnalysisResult(id);
    } catch (error) {
      console.error('Get analysis error:', error);
      return null;
    }
  };
  
  const getStats = () => {
    try {
      return advancedMapCache.getStats();
    } catch (error) {
      console.error('Get stats error:', error);
      return {
        totalSize: 0,
        totalItems: 0,
        hitRate: 0,
        missRate: 0,
        evictionCount: 0,
        lastOptimization: Date.now(),
        storageBreakdown: {
          tiles: { size: 0, count: 0 },
          features: { size: 0, count: 0 },
          analysis: { size: 0, count: 0 },
        },
      };
    }
  };
  
  const clearCache = async () => {
    try {
      return await advancedMapCache.clearCache();
    } catch (error) {
      console.error('Clear cache error:', error);
    }
  };
  
  const optimizeCache = async () => {
    try {
      return await advancedMapCache.optimizeCache();
    } catch (error) {
      console.error('Optimize cache error:', error);
    }
  };
  
  return {
    cacheTile,
    getTile,
    cacheFeatures,
    getFeatures,
    cacheAnalysis,
    getAnalysis,
    getStats,
    clearCache,
    optimizeCache,
  };
};
