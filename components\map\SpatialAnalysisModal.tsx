import React, { useState, useCallback, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Switch,
  Alert,
  Platform,
  ActivityIndicator,
} from 'react-native';
import { useTheme } from '@/hooks/useTheme';
import {
  X,
  Play,
  Pause,
  RotateCcw,
  Settings,
  Info,
  CheckCircle,
  AlertCircle,
  Clock,
  Database,
  Target,
  Circle,
  Scissors,
  Merge,
  Shuffle,
  Navigation,
  Grid,
  Hexagon,
  BarChart3,
  Zap,
  MapPin,
  ArrowRight,
  Download,
  Trash2,
} from 'lucide-react-native';

interface AnalysisParameter {
  key: string;
  label: string;
  type: 'number' | 'select' | 'boolean' | 'layer' | 'distance';
  value: any;
  options?: string[];
  unit?: string;
  min?: number;
  max?: number;
  required?: boolean;
  description?: string;
}

interface AnalysisResult {
  id: string;
  type: string;
  name: string;
  status: 'running' | 'completed' | 'failed';
  progress: number;
  parameters: Record<string, any>;
  result?: any;
  error?: string;
  startTime: string;
  endTime?: string;
  outputLayers?: string[];
}

interface SpatialAnalysisModalProps {
  visible: boolean;
  onClose: () => void;
  availableLayers: Array<{ id: string; name: string; type: string; geometryType?: string }>;
  onAnalysisStart: (type: string, parameters: Record<string, any>) => Promise<AnalysisResult>;
  analysisHistory: AnalysisResult[];
  onResultExport: (resultId: string) => void;
  onResultDelete: (resultId: string) => void;
}

const ANALYSIS_TOOLS = {
  'buffer': {
    name: 'Buffer Analysis',
    description: 'Create buffer zones around features',
    icon: Circle,
    category: 'proximity',
    parameters: [
      { key: 'inputLayer', label: 'Input Layer', type: 'layer', required: true },
      { key: 'distance', label: 'Buffer Distance', type: 'distance', value: 100, unit: 'm', min: 1, max: 10000, required: true },
      { key: 'units', label: 'Distance Units', type: 'select', value: 'meters', options: ['meters', 'kilometers', 'feet', 'miles'], required: true },
      { key: 'dissolve', label: 'Dissolve Overlapping', type: 'boolean', value: false, description: 'Merge overlapping buffer areas' },
      { key: 'segments', label: 'Buffer Segments', type: 'number', value: 16, min: 4, max: 64, description: 'Number of segments for curved areas' },
    ],
  },
  'clip': {
    name: 'Clip Analysis',
    description: 'Extract features within a boundary',
    icon: Scissors,
    category: 'overlay',
    parameters: [
      { key: 'inputLayer', label: 'Input Layer', type: 'layer', required: true },
      { key: 'clipLayer', label: 'Clip Boundary', type: 'layer', required: true },
      { key: 'preserveAttributes', label: 'Preserve Attributes', type: 'boolean', value: true },
    ],
  },
  'dissolve': {
    name: 'Dissolve Features',
    description: 'Merge features based on attributes',
    icon: Merge,
    category: 'generalization',
    parameters: [
      { key: 'inputLayer', label: 'Input Layer', type: 'layer', required: true },
      { key: 'dissolveField', label: 'Dissolve Field', type: 'select', options: [], description: 'Field to group features by' },
      { key: 'statisticsFields', label: 'Statistics Fields', type: 'select', options: [], description: 'Fields to calculate statistics for' },
      { key: 'multipart', label: 'Create Multipart Features', type: 'boolean', value: true },
    ],
  },
  'intersect': {
    name: 'Intersect Analysis',
    description: 'Find overlapping areas between layers',
    icon: Shuffle,
    category: 'overlay',
    parameters: [
      { key: 'inputLayer', label: 'Input Layer', type: 'layer', required: true },
      { key: 'overlayLayer', label: 'Overlay Layer', type: 'layer', required: true },
      { key: 'outputType', label: 'Output Type', type: 'select', value: 'input', options: ['input', 'overlay', 'intersection'] },
      { key: 'toleranceEnabled', label: 'Use Tolerance', type: 'boolean', value: false },
      { key: 'tolerance', label: 'Tolerance', type: 'distance', value: 0, unit: 'm', min: 0, max: 1000 },
    ],
  },
  'union': {
    name: 'Union Analysis',
    description: 'Combine features from multiple layers',
    icon: Merge,
    category: 'overlay',
    parameters: [
      { key: 'inputLayers', label: 'Input Layers', type: 'layer', required: true, description: 'Select multiple layers to union' },
      { key: 'gapTolerance', label: 'Gap Tolerance', type: 'distance', value: 0, unit: 'm', min: 0, max: 100 },
      { key: 'preserveAttributes', label: 'Preserve All Attributes', type: 'boolean', value: true },
    ],
  },
  'spatial-join': {
    name: 'Spatial Join',
    description: 'Join attributes based on spatial relationship',
    icon: Target,
    category: 'analysis',
    parameters: [
      { key: 'targetLayer', label: 'Target Layer', type: 'layer', required: true },
      { key: 'joinLayer', label: 'Join Layer', type: 'layer', required: true },
      { key: 'spatialRelation', label: 'Spatial Relationship', type: 'select', value: 'intersects', options: ['intersects', 'contains', 'within', 'touches', 'crosses'] },
      { key: 'joinOperation', label: 'Join Operation', type: 'select', value: 'one_to_one', options: ['one_to_one', 'one_to_many'] },
      { key: 'keepAll', label: 'Keep All Target Features', type: 'boolean', value: true },
    ],
  },
  'proximity': {
    name: 'Proximity Analysis',
    description: 'Find nearest features and distances',
    icon: Target,
    category: 'proximity',
    parameters: [
      { key: 'inputLayer', label: 'Input Layer', type: 'layer', required: true },
      { key: 'nearLayer', label: 'Near Layer', type: 'layer', required: true },
      { key: 'searchRadius', label: 'Search Radius', type: 'distance', value: 1000, unit: 'm', min: 1, max: 50000 },
      { key: 'maxResults', label: 'Max Results per Feature', type: 'number', value: 1, min: 1, max: 100 },
      { key: 'includeAngles', label: 'Include Bearing Angles', type: 'boolean', value: false },
    ],
  },
  'isochrone': {
    name: 'Isochrone Analysis',
    description: 'Create time/distance accessibility areas',
    icon: Navigation,
    category: 'network',
    parameters: [
      { key: 'startPoints', label: 'Start Points', type: 'layer', required: true },
      { key: 'analysisType', label: 'Analysis Type', type: 'select', value: 'time', options: ['time', 'distance'] },
      { key: 'cutoffs', label: 'Cutoff Values', type: 'number', value: '5,10,15', description: 'Comma-separated values (e.g., 5,10,15)' },
      { key: 'units', label: 'Units', type: 'select', value: 'minutes', options: ['minutes', 'hours', 'meters', 'kilometers'] },
      { key: 'travelMode', label: 'Travel Mode', type: 'select', value: 'driving', options: ['driving', 'walking', 'cycling', 'transit'] },
    ],
  },
  'grid-create': {
    name: 'Create Grid',
    description: 'Generate regular grid for analysis',
    icon: Grid,
    category: 'generation',
    parameters: [
      { key: 'extent', label: 'Grid Extent', type: 'select', value: 'current_view', options: ['current_view', 'layer_extent', 'custom'] },
      { key: 'extentLayer', label: 'Extent Layer', type: 'layer', description: 'Layer to use for grid extent' },
      { key: 'gridType', label: 'Grid Type', type: 'select', value: 'square', options: ['square', 'rectangle', 'hexagon', 'triangle'] },
      { key: 'cellSize', label: 'Cell Size', type: 'distance', value: 1000, unit: 'm', min: 1, max: 100000, required: true },
      { key: 'rotation', label: 'Rotation Angle', type: 'number', value: 0, min: 0, max: 360, unit: '°' },
    ],
  },
  'hexbin': {
    name: 'Hexagonal Binning',
    description: 'Aggregate points into hexagonal bins',
    icon: Hexagon,
    category: 'aggregation',
    parameters: [
      { key: 'inputLayer', label: 'Point Layer', type: 'layer', required: true },
      { key: 'hexSize', label: 'Hexagon Size', type: 'distance', value: 1000, unit: 'm', min: 10, max: 50000, required: true },
      { key: 'aggregateField', label: 'Aggregate Field', type: 'select', options: [], description: 'Numeric field to aggregate' },
      { key: 'aggregateMethod', label: 'Aggregate Method', type: 'select', value: 'count', options: ['count', 'sum', 'mean', 'min', 'max', 'std'] },
      { key: 'extent', label: 'Analysis Extent', type: 'select', value: 'layer_extent', options: ['layer_extent', 'current_view'] },
    ],
  },
};

export const SpatialAnalysisModal: React.FC<SpatialAnalysisModalProps> = ({
  visible,
  onClose,
  availableLayers,
  onAnalysisStart,
  analysisHistory,
  onResultExport,
  onResultDelete,
}) => {
  const { theme } = useTheme();
  const [activeTab, setActiveTab] = useState<'tools' | 'history' | 'results'>('tools');
  const [selectedTool, setSelectedTool] = useState<string | null>(null);
  const [parameters, setParameters] = useState<Record<string, any>>({});
  const [isRunning, setIsRunning] = useState(false);
  const [progress, setProgress] = useState(0);

  const selectedToolConfig = selectedTool ? ANALYSIS_TOOLS[selectedTool as keyof typeof ANALYSIS_TOOLS] : null;

  useEffect(() => {
    if (selectedToolConfig) {
      const defaultParams: Record<string, any> = {};
      selectedToolConfig.parameters.forEach(param => {
        if (param.value !== undefined) {
          defaultParams[param.key] = param.value;
        } else if (param.type === 'layer' && availableLayers.length > 0) {
          // Auto-select first available layer for layer parameters
          defaultParams[param.key] = availableLayers[0].id;
        } else if (param.type === 'select' && param.options && param.options.length > 0) {
          // Auto-select first option for select parameters
          defaultParams[param.key] = param.options[0];
        } else if (param.type === 'boolean') {
          // Default boolean to false
          defaultParams[param.key] = false;
        }
      });
      setParameters(defaultParams);
    }
  }, [selectedTool, selectedToolConfig, availableLayers]);

  const handleParameterChange = useCallback((key: string, value: any) => {
    setParameters(prev => ({ ...prev, [key]: value }));
  }, []);

  const handleRunAnalysis = useCallback(async () => {
    if (!selectedTool || !selectedToolConfig) {
      Alert.alert('Error', 'Please select an analysis tool first.');
      return;
    }

    // Validate required parameters
    const missingParams = selectedToolConfig.parameters
      .filter(param => param.required && (!parameters[param.key] || parameters[param.key] === ''))
      .map(param => param.label);

    if (missingParams.length > 0) {
      Alert.alert(
        'Missing Parameters',
        `Please provide values for: ${missingParams.join(', ')}`,
        [{ text: 'OK' }]
      );
      return;
    }

    // Special validation for layer parameters
    const layerParams = selectedToolConfig.parameters.filter(param => param.type === 'layer');
    for (const param of layerParams) {
      if (param.required && !parameters[param.key]) {
        Alert.alert(
          'Missing Input Layer',
          `Please select a layer for: ${param.label}`,
          [{ text: 'OK' }]
        );
        return;
      }
    }

    setIsRunning(true);
    setProgress(0);

    try {
      console.log('Starting real spatial analysis:', selectedTool, 'with parameters:', parameters);

      // Start the real analysis
      const result = await onAnalysisStart(selectedTool, parameters);

      // Monitor progress (the real analysis will update progress)
      const progressMonitor = setInterval(() => {
        setProgress(prev => {
          if (prev >= 100) {
            clearInterval(progressMonitor);
            setIsRunning(false);
            setActiveTab('results');
            Alert.alert(
              'Analysis Complete',
              `${selectedToolConfig.name} completed successfully!\n\nCheck the map for results and the Results tab for details.`
            );
            return 100;
          }
          return Math.min(prev + 5, 95); // Gradually increase to 95%, let real analysis finish at 100%
        });
      }, 100);

      // The actual analysis completion will be handled by the parent component

    } catch (error) {
      console.error('Analysis error:', error);
      setIsRunning(false);
      setProgress(0);
      Alert.alert(
        'Analysis Failed',
        `An error occurred during analysis: ${error instanceof Error ? error.message : 'Unknown error'}\n\nPlease check your input parameters and try again.`
      );
    }
  }, [selectedTool, selectedToolConfig, parameters, onAnalysisStart]);

  const renderToolsTab = () => (
    <View style={styles.tabContent}>
      {!selectedTool ? (
        <ScrollView style={styles.toolsList}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Spatial Analysis Tools
          </Text>
          
          {Object.entries(ANALYSIS_TOOLS).map(([key, tool]) => (
            <TouchableOpacity
              key={key}
              style={[styles.toolItem, { backgroundColor: theme.colors.background, borderColor: theme.colors.border }]}
              onPress={() => setSelectedTool(key)}
            >
              <View style={styles.toolHeader}>
                <tool.icon size={24} color={theme.colors.primary} />
                <View style={styles.toolInfo}>
                  <Text style={[styles.toolName, { color: theme.colors.text }]}>
                    {tool.name}
                  </Text>
                  <Text style={[styles.toolDescription, { color: theme.colors.muted }]}>
                    {tool.description}
                  </Text>
                  <Text style={[styles.toolCategory, { color: theme.colors.primary }]}>
                    {tool.category.toUpperCase()}
                  </Text>
                </View>
                <ArrowRight size={20} color={theme.colors.muted} />
              </View>
            </TouchableOpacity>
          ))}
        </ScrollView>
      ) : (
        <View style={styles.toolConfiguration}>
          {/* Tool Header */}
          <View style={[styles.configHeader, { borderBottomColor: theme.colors.border }]}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => setSelectedTool(null)}
            >
              <ArrowRight size={20} color={theme.colors.text} style={{ transform: [{ rotate: '180deg' }] }} />
            </TouchableOpacity>
            <View style={styles.toolHeaderInfo}>
              <selectedToolConfig.icon size={24} color={theme.colors.primary} />
              <Text style={[styles.configTitle, { color: theme.colors.text }]}>
                {selectedToolConfig.name}
              </Text>
            </View>
          </View>

          {/* Parameters */}
          <ScrollView style={styles.parametersContainer}>
            {selectedToolConfig.parameters.map(param => (
              <View key={param.key} style={styles.parameterItem}>
                <Text style={[styles.parameterLabel, { color: theme.colors.text }]}>
                  {param.label}
                  {param.required && <Text style={{ color: theme.colors.destructive }}> *</Text>}
                </Text>
                
                {param.description && (
                  <Text style={[styles.parameterDescription, { color: theme.colors.muted }]}>
                    {param.description}
                  </Text>
                )}

                {renderParameterInput(param)}
              </View>
            ))}
          </ScrollView>

          {/* Run Button */}
          <View style={[styles.runSection, { borderTopColor: theme.colors.border }]}>
            {isRunning && (
              <View style={styles.progressContainer}>
                <ActivityIndicator size="small" color={theme.colors.primary} />
                <Text style={[styles.progressText, { color: theme.colors.text }]}>
                  Running analysis... {progress}%
                </Text>
              </View>
            )}
            
            <TouchableOpacity
              style={[
                styles.runButton,
                {
                  backgroundColor: isRunning ? theme.colors.muted : theme.colors.primary,
                }
              ]}
              onPress={handleRunAnalysis}
              disabled={isRunning}
            >
              {isRunning ? (
                <Pause size={20} color="white" />
              ) : (
                <Play size={20} color="white" />
              )}
              <Text style={[styles.runButtonText, { color: 'white' }]}>
                {isRunning ? 'Running...' : 'Run Analysis'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      )}
    </View>
  );

  const renderParameterInput = (param: AnalysisParameter) => {
    const value = parameters[param.key];

    switch (param.type) {
      case 'number':
      case 'distance':
        return (
          <View style={styles.numberInputContainer}>
            <TextInput
              style={[styles.numberInput, { backgroundColor: theme.colors.background, color: theme.colors.text }]}
              value={value?.toString() || ''}
              onChangeText={(text) => handleParameterChange(param.key, parseFloat(text) || 0)}
              placeholder={param.min ? `Min: ${param.min}` : '0'}
              placeholderTextColor={theme.colors.muted}
              keyboardType="numeric"
            />
            {param.unit && (
              <Text style={[styles.unitText, { color: theme.colors.muted }]}>
                {param.unit}
              </Text>
            )}
          </View>
        );

      case 'select':
        return (
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.selectContainer}>
            {(param.options || []).map(option => (
              <TouchableOpacity
                key={option}
                style={[
                  styles.selectOption,
                  {
                    backgroundColor: value === option ? theme.colors.primary : theme.colors.background,
                    borderColor: theme.colors.border,
                  }
                ]}
                onPress={() => handleParameterChange(param.key, option)}
              >
                <Text style={[
                  styles.selectOptionText,
                  { color: value === option ? 'white' : theme.colors.text }
                ]}>
                  {option}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        );

      case 'boolean':
        return (
          <View style={styles.booleanContainer}>
            <Switch
              value={value || false}
              onValueChange={(newValue) => handleParameterChange(param.key, newValue)}
              trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
              thumbColor={Platform.OS === 'ios' ? undefined : 'white'}
            />
            <Text style={[styles.booleanText, { color: theme.colors.text }]}>
              {value ? 'Enabled' : 'Disabled'}
            </Text>
          </View>
        );

      case 'layer':
        return (
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.layerContainer}>
            {availableLayers.map(layer => (
              <TouchableOpacity
                key={layer.id}
                style={[
                  styles.layerOption,
                  {
                    backgroundColor: value === layer.id ? theme.colors.primary : theme.colors.background,
                    borderColor: theme.colors.border,
                  }
                ]}
                onPress={() => handleParameterChange(param.key, layer.id)}
              >
                <Database size={16} color={value === layer.id ? 'white' : theme.colors.text} />
                <Text style={[
                  styles.layerOptionText,
                  { color: value === layer.id ? 'white' : theme.colors.text }
                ]}>
                  {layer.name}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        );

      default:
        return (
          <TextInput
            style={[styles.textInput, { backgroundColor: theme.colors.background, color: theme.colors.text }]}
            value={value?.toString() || ''}
            onChangeText={(text) => handleParameterChange(param.key, text)}
            placeholder="Enter value..."
            placeholderTextColor={theme.colors.muted}
          />
        );
    }
  };

  const renderHistoryTab = () => (
    <ScrollView style={styles.tabContent}>
      <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
        Analysis History
      </Text>
      
      {analysisHistory.length === 0 ? (
        <View style={styles.emptyState}>
          <Clock size={48} color={theme.colors.muted} />
          <Text style={[styles.emptyStateText, { color: theme.colors.muted }]}>
            No analysis history yet
          </Text>
        </View>
      ) : (
        analysisHistory.map(result => (
          <View key={result.id} style={[styles.historyItem, { backgroundColor: theme.colors.background, borderColor: theme.colors.border }]}>
            <View style={styles.historyHeader}>
              <View style={styles.historyInfo}>
                <Text style={[styles.historyName, { color: theme.colors.text }]}>
                  {result.name}
                </Text>
                <Text style={[styles.historyTime, { color: theme.colors.muted }]}>
                  {new Date(result.startTime).toLocaleString()}
                </Text>
              </View>
              
              <View style={styles.historyStatus}>
                {result.status === 'completed' && <CheckCircle size={16} color={theme.colors.success} />}
                {result.status === 'failed' && <AlertCircle size={16} color={theme.colors.destructive} />}
                {result.status === 'running' && <ActivityIndicator size="small" color={theme.colors.primary} />}
              </View>
            </View>
            
            {result.status === 'running' && (
              <View style={styles.progressBar}>
                <View
                  style={[
                    styles.progressFill,
                    {
                      backgroundColor: theme.colors.primary,
                      width: `${result.progress}%`,
                    }
                  ]}
                />
              </View>
            )}
          </View>
        ))
      )}
    </ScrollView>
  );

  const renderResultsTab = () => (
    <ScrollView style={styles.tabContent}>
      <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
        Analysis Results
      </Text>
      
      {analysisHistory.filter(r => r.status === 'completed').length === 0 ? (
        <View style={styles.emptyState}>
          <BarChart3 size={48} color={theme.colors.muted} />
          <Text style={[styles.emptyStateText, { color: theme.colors.muted }]}>
            No completed analyses yet
          </Text>
        </View>
      ) : (
        analysisHistory
          .filter(result => result.status === 'completed')
          .map(result => (
            <View key={result.id} style={[styles.resultItem, { backgroundColor: theme.colors.background, borderColor: theme.colors.border }]}>
              <View style={styles.resultHeader}>
                <View style={styles.resultInfo}>
                  <Text style={[styles.resultName, { color: theme.colors.text }]}>
                    {result.name}
                  </Text>
                  <Text style={[styles.resultTime, { color: theme.colors.muted }]}>
                    Completed: {result.endTime ? new Date(result.endTime).toLocaleString() : 'Unknown'}
                  </Text>
                  {result.outputLayers && (
                    <Text style={[styles.resultLayers, { color: theme.colors.primary }]}>
                      {result.outputLayers.length} output layer(s) created
                    </Text>
                  )}
                </View>
                
                <View style={styles.resultActions}>
                  <TouchableOpacity
                    style={[styles.actionButton, { backgroundColor: theme.colors.primary }]}
                    onPress={() => onResultExport(result.id)}
                  >
                    <Download size={16} color="white" />
                  </TouchableOpacity>
                  
                  <TouchableOpacity
                    style={[styles.actionButton, { backgroundColor: theme.colors.destructive }]}
                    onPress={() => onResultDelete(result.id)}
                  >
                    <Trash2 size={16} color="white" />
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          ))
      )}
    </ScrollView>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={[styles.modalContainer, { backgroundColor: theme.colors.background }]}>
        {/* Header */}
        <View style={[styles.modalHeader, { backgroundColor: theme.colors.card, borderBottomColor: theme.colors.border }]}>
          <Text style={[styles.modalTitle, { color: theme.colors.text }]}>Spatial Analysis</Text>
          <TouchableOpacity onPress={onClose}>
            <X size={24} color={theme.colors.text} />
          </TouchableOpacity>
        </View>

        {/* Tabs */}
        <View style={[styles.tabsContainer, { backgroundColor: theme.colors.card, borderBottomColor: theme.colors.border }]}>
          {[
            { key: 'tools', icon: Zap, label: 'Tools' },
            { key: 'history', icon: Clock, label: 'History' },
            { key: 'results', icon: BarChart3, label: 'Results' },
          ].map(tab => (
            <TouchableOpacity
              key={tab.key}
              style={[
                styles.tab,
                { backgroundColor: activeTab === tab.key ? theme.colors.primary : 'transparent' }
              ]}
              onPress={() => setActiveTab(tab.key as any)}
            >
              <tab.icon
                size={18}
                color={activeTab === tab.key ? 'white' : theme.colors.text}
              />
              <Text style={[
                styles.tabText,
                { color: activeTab === tab.key ? 'white' : theme.colors.text }
              ]}>
                {tab.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Content */}
        {activeTab === 'tools' && renderToolsTab()}
        {activeTab === 'history' && renderHistoryTab()}
        {activeTab === 'results' && renderResultsTab()}
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
  },
  tabsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    borderBottomWidth: 1,
  },
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginRight: 8,
    borderRadius: 8,
    gap: 6,
  },
  tabText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
  },
  tabContent: {
    flex: 1,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    padding: 16,
  },
  toolsList: {
    flex: 1,
    paddingHorizontal: 16,
  },
  toolItem: {
    marginBottom: 12,
    borderRadius: 8,
    borderWidth: 1,
    padding: 16,
  },
  toolHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  toolInfo: {
    flex: 1,
  },
  toolName: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 4,
  },
  toolDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    marginBottom: 6,
  },
  toolCategory: {
    fontSize: 11,
    fontFamily: 'Inter-SemiBold',
    textTransform: 'uppercase',
  },
  toolConfiguration: {
    flex: 1,
  },
  configHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    gap: 12,
  },
  backButton: {
    padding: 4,
  },
  toolHeaderInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  configTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
  },
  parametersContainer: {
    flex: 1,
    paddingHorizontal: 16,
  },
  parameterItem: {
    marginVertical: 12,
  },
  parameterLabel: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 4,
  },
  parameterDescription: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    marginBottom: 8,
  },
  numberInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  numberInput: {
    flex: 1,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    fontSize: 14,
    fontFamily: 'Inter-Regular',
  },
  unitText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
  },
  selectContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  selectOption: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    borderWidth: 1,
  },
  selectOptionText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },
  booleanContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  booleanText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
  },
  layerContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  layerOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    borderWidth: 1,
    gap: 6,
  },
  layerOptionText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },
  textInput: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    fontSize: 14,
    fontFamily: 'Inter-Regular',
  },
  runSection: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
    gap: 8,
  },
  progressText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
  },
  runButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  runButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
  },
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 48,
    gap: 16,
  },
  emptyStateText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
  },
  historyItem: {
    marginHorizontal: 16,
    marginBottom: 12,
    borderRadius: 8,
    borderWidth: 1,
    padding: 16,
  },
  historyHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  historyInfo: {
    flex: 1,
  },
  historyName: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 4,
  },
  historyTime: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
  },
  historyStatus: {
    padding: 4,
  },
  progressBar: {
    height: 4,
    backgroundColor: '#E5E7EB',
    borderRadius: 2,
    marginTop: 8,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
  resultItem: {
    marginHorizontal: 16,
    marginBottom: 12,
    borderRadius: 8,
    borderWidth: 1,
    padding: 16,
  },
  resultHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
  },
  resultInfo: {
    flex: 1,
  },
  resultName: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 4,
  },
  resultTime: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    marginBottom: 4,
  },
  resultLayers: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },
  resultActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
});
