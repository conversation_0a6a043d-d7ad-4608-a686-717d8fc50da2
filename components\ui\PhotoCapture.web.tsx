import React, { useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Image,
} from 'react-native';
import { useTheme } from '@/hooks/useTheme';
import { Camera, Trash2, RotateCcw } from 'lucide-react-native';

interface PhotoCaptureProps {
  value?: string; // URI of captured photo
  onChange: (uri: string | null) => void;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
}

export default function PhotoCapture({
  value,
  onChange,
  placeholder = 'Take photo',
  required = false,
  disabled = false,
}: PhotoCaptureProps) {
  const { theme } = useTheme();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (event: Event) => {
    const target = event.target as HTMLInputElement;
    const file = target.files?.[0];
    
    if (file) {
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const result = e.target?.result as string;
          onChange(result);
        };
        reader.readAsDataURL(file);
      } else {
        Alert.alert('Error', 'Please select a valid image file');
      }
    }
  };

  const selectPhoto = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const removePhoto = () => {
    Alert.alert(
      'Remove Photo',
      'Are you sure you want to remove this photo?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: () => onChange(null),
        },
      ]
    );
  };

  if (value) {
    return (
      <View style={[styles.container, { borderColor: theme.colors.border }]}>
        <Image source={{ uri: value }} style={styles.photo} resizeMode="cover" />
        
        <View style={[styles.overlay, { backgroundColor: theme.colors.background + 'CC' }]}>
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: theme.colors.secondary }]}
            onPress={selectPhoto}
            disabled={disabled}
          >
            <RotateCcw size={16} color="white" />
            <Text style={styles.actionButtonText}>Replace</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: theme.colors.error }]}
            onPress={removePhoto}
            disabled={disabled}
          >
            <Trash2 size={16} color="white" />
            <Text style={styles.actionButtonText}>Remove</Text>
          </TouchableOpacity>
        </View>

        {/* Hidden file input for web */}
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          style={{ display: 'none' }}
          onChange={handleFileSelect}
        />
      </View>
    );
  }

  return (
    <>
      <TouchableOpacity
        style={[
          styles.captureButton,
          {
            backgroundColor: theme.colors.background,
            borderColor: theme.colors.border,
            opacity: disabled ? 0.5 : 1,
          },
        ]}
        onPress={selectPhoto}
        disabled={disabled}
      >
        <Camera size={32} color={theme.colors.muted} />
        <Text style={[styles.captureText, { color: theme.colors.muted }]}>
          {placeholder}
        </Text>
        {required && (
          <Text style={[styles.requiredText, { color: theme.colors.error }]}>
            Required
          </Text>
        )}
      </TouchableOpacity>

      {/* Hidden file input for web */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        style={{ display: 'none' }}
        onChange={handleFileSelect}
      />
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    borderRadius: 8,
    borderWidth: 2,
    overflow: 'hidden',
    aspectRatio: 4 / 3,
  },
  photo: {
    width: '100%',
    height: '100%',
  },
  overlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 8,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    gap: 4,
  },
  actionButtonText: {
    color: 'white',
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
  },
  captureButton: {
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderStyle: 'dashed',
    borderRadius: 8,
    paddingVertical: 40,
    gap: 8,
  },
  captureText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
  },
  requiredText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },
});
