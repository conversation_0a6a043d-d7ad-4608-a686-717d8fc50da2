export interface User {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'manager' | 'collector' | 'viewer';
  teamId?: string;
  lastSyncTimestamp?: number;
}

export interface Team {
  id: string;
  name: string;
  description?: string;
  members: string[]; // User IDs
}

export interface Project {
  id: string;
  name: string;
  description?: string;
  createdAt: number;
  updatedAt: number;
  createdBy: string; // User ID
  teams: string[]; // Team IDs
  status: 'draft' | 'active' | 'completed' | 'archived';
  forms: string[]; // Form IDs
  region?: {
    latitude: number;
    longitude: number;
    latitudeDelta: number;
    longitudeDelta: number;
  };
}

export interface Form {
  id: string;
  projectId: string;
  name: string;
  description?: string;
  version: number;
  createdAt: number;
  updatedAt: number;
  createdBy: string; // User ID
  status: 'draft' | 'published' | 'archived';
  schema: FormSchema;
}

export interface FormSchema {
  pages: FormPage[];
  logicRules?: LogicRule[];
}

export interface FormPage {
  id: string;
  title: string;
  description?: string;
  order: number;
  sections: FormSection[];
}

export interface FormSection {
  id: string;
  title: string;
  description?: string;
  order: number;
  questions: FormQuestion[];
}

export type QuestionType = 
  | 'text' 
  | 'number' 
  | 'select' 
  | 'multiselect' 
  | 'date' 
  | 'time' 
  | 'datetime'
  | 'location'
  | 'photo'
  | 'audio'
  | 'video'
  | 'signature'
  | 'drawing'
  | 'barcode'
  | 'qr_scan'
  | 'phone';

export interface FormQuestion {
  id: string;
  type: QuestionType;
  label: string;
  required: boolean;
  description?: string;
  placeholder?: string;
  defaultValue?: any;
  validation?: ValidationRule[];
  options?: Array<{ label: string; value: string }>;
  properties?: Record<string, any>;
}

export interface ValidationRule {
  type: 'required' | 'min' | 'max' | 'pattern' | 'custom';
  value?: any;
  message: string;
}

export interface LogicRule {
  id: string;
  condition: {
    questionId: string;
    operator: 'equals' | 'notEquals' | 'contains' | 'greaterThan' | 'lessThan';
    value: any;
  };
  action: {
    type: 'show' | 'hide' | 'enable' | 'disable' | 'require' | 'setValue';
    targets: string[]; // Question IDs
    value?: any;
  };
}

export interface Submission {
  id: string;
  formId: string;
  projectId: string;
  userId: string;
  status: 'draft' | 'completed' | 'synced' | 'error';
  startedAt: number;
  completedAt?: number;
  syncedAt?: number;
  data: Record<string, any>;
  location?: {
    latitude: number;
    longitude: number;
    accuracy?: number;
    altitude?: number;
    timestamp: number;
  };
  media?: Array<{
    id: string;
    questionId: string;
    type: 'photo' | 'audio' | 'video' | 'signature' | 'drawing';
    uri: string;
    localUri?: string;
    synced: boolean;
    timestamp: number;
  }>;
}

export interface GeoFeature {
  id: string;
  projectId: string;
  submissionId?: string;
  type: 'point' | 'line' | 'polygon';
  geometry: {
    type: 'Point' | 'LineString' | 'Polygon';
    coordinates: number[] | number[][] | number[][][];
  };
  properties: Record<string, any>;
  createdAt: number;
  createdBy: string; // User ID
  syncStatus: 'local' | 'syncing' | 'synced' | 'error';
}

export interface SyncStatus {
  lastSync: number;
  pendingSubmissions: number;
  pendingMedia: number;
  pendingGeoFeatures: number;
  syncInProgress: boolean;
  syncError?: string;
}

export interface AppSettings {
  offlineMapRadius: number; // in kilometers
  syncOnCellularData: boolean;
  syncInterval: number; // in minutes
  language: string;
  theme: 'light' | 'dark' | 'system';
  locationAccuracyThreshold: number; // in meters
  automaticBackup: boolean;
  backupInterval: number; // in days
}