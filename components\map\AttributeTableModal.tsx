import React, { useState, useCallback, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  ScrollView,
  TextInput,
  FlatList,
  Platform,
} from 'react-native';
import { useTheme } from '@/hooks/useTheme';
import {
  X,
  Search,
  Filter,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  Eye,
  MapPin,
  Download,
  Edit3,
  Trash2,
  ChevronLeft,
  ChevronRight,
  MoreHorizontal,
  Table,
  Grid,
  List,
} from 'lucide-react-native';

interface MapLayer {
  id: string;
  name: string;
  type: 'vector' | 'raster' | 'wms' | 'wmts';
  visible: boolean;
  opacity: number;
  source: string;
  features?: any[];
  metadata?: {
    description?: string;
    source?: string;
    format?: string;
    geometryType?: string;
    featureCount?: number;
    bounds?: any;
    properties?: string[];
  };
}

interface AttributeTableModalProps {
  visible: boolean;
  onClose: () => void;
  layer: MapLayer | null;
  onFeatureSelect?: (feature: any) => void;
  onFeatureEdit?: (feature: any) => void;
  onFeatureDelete?: (featureId: string) => void;
  onZoomToFeature?: (feature: any) => void;
}

type SortDirection = 'asc' | 'desc' | null;
type ViewMode = 'table' | 'grid' | 'list';

export const AttributeTableModal: React.FC<AttributeTableModalProps> = ({
  visible,
  onClose,
  layer,
  onFeatureSelect,
  onFeatureEdit,
  onFeatureDelete,
  onZoomToFeature,
}) => {
  const { theme } = useTheme();
  
  // State management
  const [searchQuery, setSearchQuery] = useState('');
  const [sortColumn, setSortColumn] = useState<string | null>(null);
  const [sortDirection, setSortDirection] = useState<SortDirection>(null);
  const [selectedFeatureId, setSelectedFeatureId] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(50);
  const [viewMode, setViewMode] = useState<ViewMode>('table');
  const [filterColumn, setFilterColumn] = useState<string>('all');
  const [showColumnFilter, setShowColumnFilter] = useState(false);

  // Get features and properties
  const features = layer?.features || [];
  const properties = layer?.metadata?.properties || [];

  // Debug logging
  console.log('AttributeTableModal - Layer:', layer?.name);
  console.log('AttributeTableModal - Features count:', features.length);
  console.log('AttributeTableModal - First feature:', features[0]);
  console.log('AttributeTableModal - First feature geometry:', features[0]?.geometry);

  // Extract all unique properties from features if metadata doesn't have them
  const allProperties = useMemo(() => {
    if (properties.length > 0) return properties;
    
    const propSet = new Set<string>();
    features.forEach(feature => {
      if (feature.properties) {
        Object.keys(feature.properties).forEach(key => propSet.add(key));
      }
    });
    return Array.from(propSet);
  }, [features, properties]);

  // Filter and sort features
  const filteredAndSortedFeatures = useMemo(() => {
    let filtered = features;

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(feature => {
        if (!feature.properties) return false;
        return Object.values(feature.properties).some(value => 
          String(value).toLowerCase().includes(query)
        );
      });
    }

    // Apply column filter
    if (filterColumn !== 'all' && searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(feature => {
        if (!feature.properties || !feature.properties[filterColumn]) return false;
        return String(feature.properties[filterColumn]).toLowerCase().includes(query);
      });
    }

    // Apply sorting
    if (sortColumn && sortDirection) {
      filtered = [...filtered].sort((a, b) => {
        const aVal = a.properties?.[sortColumn] || '';
        const bVal = b.properties?.[sortColumn] || '';
        
        // Handle different data types
        const aNum = Number(aVal);
        const bNum = Number(bVal);
        
        let comparison = 0;
        if (!isNaN(aNum) && !isNaN(bNum)) {
          // Numeric comparison
          comparison = aNum - bNum;
        } else {
          // String comparison
          comparison = String(aVal).localeCompare(String(bVal));
        }
        
        return sortDirection === 'asc' ? comparison : -comparison;
      });
    }

    return filtered;
  }, [features, searchQuery, sortColumn, sortDirection, filterColumn]);

  // Pagination
  const totalPages = Math.ceil(filteredAndSortedFeatures.length / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedFeatures = filteredAndSortedFeatures.slice(startIndex, endIndex);

  // Handle sorting
  const handleSort = useCallback((column: string) => {
    if (sortColumn === column) {
      // Cycle through: asc -> desc -> null
      if (sortDirection === 'asc') {
        setSortDirection('desc');
      } else if (sortDirection === 'desc') {
        setSortColumn(null);
        setSortDirection(null);
      } else {
        setSortDirection('asc');
      }
    } else {
      setSortColumn(column);
      setSortDirection('asc');
    }
  }, [sortColumn, sortDirection]);

  // Handle feature selection
  const handleFeatureSelect = useCallback((feature: any) => {
    const featureId = feature.id || feature.properties?.id || `feature_${features.indexOf(feature)}`;
    setSelectedFeatureId(selectedFeatureId === featureId ? null : featureId);
    if (onFeatureSelect) {
      onFeatureSelect(feature);
    }
  }, [selectedFeatureId, onFeatureSelect, features]);

  // Export data
  const handleExport = useCallback(() => {
    if (!layer || !features.length) return;

    const csvContent = [
      // Header
      allProperties.join(','),
      // Data rows
      ...filteredAndSortedFeatures.map(feature => 
        allProperties.map(prop => {
          const value = feature.properties?.[prop] || '';
          // Escape commas and quotes in CSV
          return typeof value === 'string' && (value.includes(',') || value.includes('"'))
            ? `"${value.replace(/"/g, '""')}"` 
            : value;
        }).join(',')
      )
    ].join('\n');

    // Create and download file
    if (Platform.OS === 'web') {
      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${layer.name}_attributes.csv`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  }, [layer, features, allProperties, filteredAndSortedFeatures]);

  if (!layer) return null;

  const renderSortIcon = (column: string) => {
    if (sortColumn !== column) {
      return <ArrowUpDown size={14} color={theme.colors.muted} />;
    }
    return sortDirection === 'asc' 
      ? <ArrowUp size={14} color={theme.colors.primary} />
      : <ArrowDown size={14} color={theme.colors.primary} />;
  };

  const renderTableHeader = () => (
    <View style={[styles.tableHeader, { backgroundColor: theme.colors.card, borderBottomColor: theme.colors.border }]}>
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        <View style={styles.headerRow}>
          {/* Selection column */}
          <View style={[styles.headerCell, styles.selectionColumn]}>
            <Text style={[styles.headerText, { color: theme.colors.text }]}>#</Text>
          </View>
          
          {/* Property columns */}
          {allProperties.map(property => (
            <TouchableOpacity
              key={property}
              style={[styles.headerCell, styles.propertyColumn]}
              onPress={() => handleSort(property)}
            >
              <View style={styles.headerContent}>
                <Text style={[styles.headerText, { color: theme.colors.text }]} numberOfLines={1}>
                  {property}
                </Text>
                {renderSortIcon(property)}
              </View>
            </TouchableOpacity>
          ))}
          
          {/* Actions column */}
          <View style={[styles.headerCell, styles.actionsColumn]}>
            <Text style={[styles.headerText, { color: theme.colors.text }]}>Actions</Text>
          </View>
        </View>
      </ScrollView>
    </View>
  );

  const renderTableRow = ({ item: feature, index }: { item: any; index: number }) => {
    const featureId = feature.id || feature.properties?.id || `feature_${index}`;
    const isSelected = selectedFeatureId === featureId;
    const rowIndex = startIndex + index + 1;

    return (
      <View style={[
        styles.tableRow,
        {
          backgroundColor: isSelected ? theme.colors.primary + '20' : 'transparent',
          borderBottomColor: theme.colors.border
        }
      ]}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View style={styles.row}>
            {/* Selection column */}
            <TouchableOpacity
              style={[styles.cell, styles.selectionColumn]}
              onPress={() => handleFeatureSelect(feature)}
            >
              <Text style={[styles.cellText, { color: isSelected ? theme.colors.primary : theme.colors.text }]}>
                {rowIndex}
              </Text>
            </TouchableOpacity>

            {/* Property columns */}
            {allProperties.map(property => (
              <View key={property} style={[styles.cell, styles.propertyColumn]}>
                <Text style={[styles.cellText, { color: theme.colors.text }]} numberOfLines={2}>
                  {feature.properties?.[property] || ''}
                </Text>
              </View>
            ))}

            {/* Actions column */}
            <View style={[styles.cell, styles.actionsColumn]}>
              <View style={styles.actionButtons}>
                {onZoomToFeature && (
                  <TouchableOpacity
                    style={[styles.actionButton, { backgroundColor: theme.colors.primary }]}
                    onPress={() => onZoomToFeature(feature)}
                  >
                    <MapPin size={12} color="white" />
                  </TouchableOpacity>
                )}

                {onFeatureEdit && (
                  <TouchableOpacity
                    style={[styles.actionButton, { backgroundColor: theme.colors.secondary }]}
                    onPress={() => onFeatureEdit(feature)}
                  >
                    <Edit3 size={12} color="white" />
                  </TouchableOpacity>
                )}

                {onFeatureDelete && (
                  <TouchableOpacity
                    style={[styles.actionButton, { backgroundColor: theme.colors.destructive }]}
                    onPress={() => {
                      const id = featureId;
                      onFeatureDelete(id);
                    }}
                  >
                    <Trash2 size={12} color="white" />
                  </TouchableOpacity>
                )}
              </View>
            </View>
          </View>
        </ScrollView>
      </View>
    );
  };

  const renderPagination = () => (
    <View style={[styles.pagination, { backgroundColor: theme.colors.card, borderTopColor: theme.colors.border }]}>
      <View style={styles.paginationInfo}>
        <Text style={[styles.paginationText, { color: theme.colors.text }]}>
          Showing {startIndex + 1}-{Math.min(endIndex, filteredAndSortedFeatures.length)} of {filteredAndSortedFeatures.length} features
        </Text>
      </View>

      <View style={styles.paginationControls}>
        <TouchableOpacity
          style={[styles.paginationButton, { opacity: currentPage === 1 ? 0.5 : 1 }]}
          onPress={() => setCurrentPage(Math.max(1, currentPage - 1))}
          disabled={currentPage === 1}
        >
          <ChevronLeft size={16} color={theme.colors.text} />
        </TouchableOpacity>

        <Text style={[styles.paginationText, { color: theme.colors.text }]}>
          Page {currentPage} of {totalPages}
        </Text>

        <TouchableOpacity
          style={[styles.paginationButton, { opacity: currentPage === totalPages ? 0.5 : 1 }]}
          onPress={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
          disabled={currentPage === totalPages}
        >
          <ChevronRight size={16} color={theme.colors.text} />
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle={Platform.OS === 'ios' ? "pageSheet" : "fullScreen"}
      onRequestClose={onClose}
    >
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        {/* Header */}
        <View style={[styles.header, { backgroundColor: theme.colors.card, borderBottomColor: theme.colors.border }]}>
          <View style={styles.headerLeft}>
            <Table size={24} color={theme.colors.primary} />
            <View>
              <Text style={[styles.title, { color: theme.colors.text }]}>
                Attribute Table
              </Text>
              <Text style={[styles.subtitle, { color: theme.colors.muted }]}>
                {layer.name} ({filteredAndSortedFeatures.length} features)
              </Text>
            </View>
          </View>

          <TouchableOpacity onPress={onClose}>
            <X size={24} color={theme.colors.text} />
          </TouchableOpacity>
        </View>

        {/* Toolbar */}
        <View style={[styles.toolbar, { backgroundColor: theme.colors.card, borderBottomColor: theme.colors.border }]}>
          <View style={styles.toolbarLeft}>
            {/* Search */}
            <View style={[styles.searchContainer, { backgroundColor: theme.colors.background }]}>
              <Search size={16} color={theme.colors.muted} />
              <TextInput
                style={[styles.searchInput, { color: theme.colors.text }]}
                placeholder={`Search in ${filterColumn === 'all' ? 'all columns' : filterColumn}...`}
                placeholderTextColor={theme.colors.muted}
                value={searchQuery}
                onChangeText={setSearchQuery}
              />
            </View>

            {/* Column filter */}
            <TouchableOpacity
              style={[styles.toolbarButton, { backgroundColor: theme.colors.background }]}
              onPress={() => setShowColumnFilter(!showColumnFilter)}
            >
              <Filter size={16} color={theme.colors.text} />
            </TouchableOpacity>
          </View>

          <View style={styles.toolbarRight}>
            {/* Export */}
            <TouchableOpacity
              style={[styles.toolbarButton, { backgroundColor: theme.colors.primary }]}
              onPress={handleExport}
            >
              <Download size={16} color="white" />
            </TouchableOpacity>
          </View>
        </View>

        {/* Column filter dropdown */}
        {showColumnFilter && (
          <View style={[styles.columnFilter, { backgroundColor: theme.colors.card, borderBottomColor: theme.colors.border }]}>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <TouchableOpacity
                style={[
                  styles.filterOption,
                  { backgroundColor: filterColumn === 'all' ? theme.colors.primary : 'transparent' }
                ]}
                onPress={() => setFilterColumn('all')}
              >
                <Text style={[
                  styles.filterOptionText,
                  { color: filterColumn === 'all' ? 'white' : theme.colors.text }
                ]}>
                  All Columns
                </Text>
              </TouchableOpacity>

              {allProperties.map(property => (
                <TouchableOpacity
                  key={property}
                  style={[
                    styles.filterOption,
                    { backgroundColor: filterColumn === property ? theme.colors.primary : 'transparent' }
                  ]}
                  onPress={() => setFilterColumn(property)}
                >
                  <Text style={[
                    styles.filterOptionText,
                    { color: filterColumn === property ? 'white' : theme.colors.text }
                  ]}>
                    {property}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        )}

        {/* Table */}
        <View style={styles.tableContainer}>
          {renderTableHeader()}

          <FlatList
            data={paginatedFeatures}
            renderItem={renderTableRow}
            keyExtractor={(item, index) => item.id || `feature_${index}`}
            style={styles.tableBody}
            showsVerticalScrollIndicator={true}
          />
        </View>

        {/* Pagination */}
        {renderPagination()}
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  title: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
  },
  subtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    marginTop: 2,
  },
  toolbar: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  toolbarLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    flex: 1,
  },
  toolbarRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    gap: 8,
    flex: 1,
    maxWidth: 300,
  },
  searchInput: {
    flex: 1,
    fontSize: 14,
    fontFamily: 'Inter-Regular',
  },
  toolbarButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
  },
  columnFilter: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderBottomWidth: 1,
  },
  filterOption: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    marginRight: 8,
  },
  filterOptionText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },
  tableContainer: {
    flex: 1,
  },
  tableHeader: {
    borderBottomWidth: 2,
  },
  headerRow: {
    flexDirection: 'row',
  },
  headerCell: {
    paddingHorizontal: 12,
    paddingVertical: 12,
    justifyContent: 'center',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  headerText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    textTransform: 'uppercase',
  },
  selectionColumn: {
    width: 60,
    alignItems: 'center',
  },
  propertyColumn: {
    width: 150,
    minWidth: 100,
  },
  actionsColumn: {
    width: 120,
    alignItems: 'center',
  },
  tableBody: {
    flex: 1,
  },
  tableRow: {
    borderBottomWidth: 1,
  },
  row: {
    flexDirection: 'row',
  },
  cell: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    justifyContent: 'center',
  },
  cellText: {
    fontSize: 13,
    fontFamily: 'Inter-Regular',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 4,
  },
  actionButton: {
    paddingHorizontal: 6,
    paddingVertical: 4,
    borderRadius: 4,
  },
  pagination: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
  },
  paginationInfo: {
    flex: 1,
  },
  paginationControls: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  paginationButton: {
    padding: 8,
  },
  paginationText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
  },
});
