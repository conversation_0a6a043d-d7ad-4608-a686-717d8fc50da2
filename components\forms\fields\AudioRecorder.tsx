import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Platform,
} from 'react-native';
import { useTheme } from '@/hooks/useTheme';
import { 
  Mic, 
  Play, 
  Pause, 
  Square, 
  RotateCcw, 
  Trash2,
  Download,
  Volume2,
  VolumeX,
} from 'lucide-react-native';
import { Audio } from 'expo-av';
import * as FileSystem from 'expo-file-system';

interface AudioRecorderProps {
  value?: string;
  onChange: (uri: string, metadata?: any) => void;
  placeholder?: string;
  required?: boolean;
  error?: string;
  disabled?: boolean;
  maxDuration?: number; // in seconds
  enablePlayback?: boolean;
  allowRetake?: boolean;
  quality?: 'low' | 'medium' | 'high';
}

export default function AudioRecorder({
  value = '',
  onChange,
  placeholder = 'Record audio',
  required = false,
  error,
  disabled = false,
  maxDuration = 300, // 5 minutes default
  enablePlayback = true,
  allowRetake = true,
  quality = 'medium',
}: AudioRecorderProps) {
  const { theme } = useTheme();
  const [recording, setRecording] = useState<Audio.Recording | null>(null);
  const [sound, setSound] = useState<Audio.Sound | null>(null);
  const [isRecording, setIsRecording] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [recordingDuration, setRecordingDuration] = useState(0);
  const [playbackPosition, setPlaybackPosition] = useState(0);
  const [playbackDuration, setPlaybackDuration] = useState(0);
  const [audioMetadata, setAudioMetadata] = useState<{
    duration: number;
    size: number;
    timestamp: number;
  } | null>(null);
  const [isMuted, setIsMuted] = useState(false);

  const recordingTimer = useRef<NodeJS.Timeout | null>(null);
  const playbackTimer = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    configureAudio();
    return () => {
      cleanup();
    };
  }, []);

  const configureAudio = async () => {
    try {
      await Audio.requestPermissionsAsync();
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
        playThroughEarpieceAndroid: false,
        staysActiveInBackground: false,
      });
    } catch (error) {
      console.error('Failed to configure audio:', error);
    }
  };

  const cleanup = async () => {
    if (recordingTimer.current) {
      clearInterval(recordingTimer.current);
    }
    if (playbackTimer.current) {
      clearInterval(playbackTimer.current);
    }
    
    if (recording) {
      await recording.stopAndUnloadAsync();
    }
    
    if (sound) {
      await sound.unloadAsync();
    }
  };

  const getRecordingOptions = () => {
    const baseOptions = {
      android: {
        extension: '.m4a',
        outputFormat: Audio.RECORDING_OPTION_ANDROID_OUTPUT_FORMAT_MPEG_4,
        audioEncoder: Audio.RECORDING_OPTION_ANDROID_AUDIO_ENCODER_AAC,
      },
      ios: {
        extension: '.m4a',
        outputFormat: Audio.RECORDING_OPTION_IOS_OUTPUT_FORMAT_MPEG4AAC,
        audioQuality: Audio.RECORDING_OPTION_IOS_AUDIO_QUALITY_HIGH,
        sampleRate: 44100,
        numberOfChannels: 2,
        bitRate: 128000,
        linearPCMBitDepth: 16,
        linearPCMIsBigEndian: false,
        linearPCMIsFloat: false,
      },
    };

    const qualitySettings = {
      low: {
        android: { ...baseOptions.android, bitRate: 64000 },
        ios: { ...baseOptions.ios, bitRate: 64000, sampleRate: 22050 },
      },
      medium: {
        android: { ...baseOptions.android, bitRate: 128000 },
        ios: { ...baseOptions.ios, bitRate: 128000, sampleRate: 44100 },
      },
      high: {
        android: { ...baseOptions.android, bitRate: 256000 },
        ios: { ...baseOptions.ios, bitRate: 256000, sampleRate: 48000 },
      },
    };

    return qualitySettings[quality];
  };

  const startRecording = async () => {
    try {
      const { status } = await Audio.requestPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Microphone permission is needed to record audio');
        return;
      }

      const newRecording = new Audio.Recording();
      await newRecording.prepareToRecordAsync(getRecordingOptions());
      await newRecording.startAsync();

      setRecording(newRecording);
      setIsRecording(true);
      setRecordingDuration(0);

      // Start timer
      recordingTimer.current = setInterval(() => {
        setRecordingDuration(prev => {
          const newDuration = prev + 1;
          if (newDuration >= maxDuration) {
            stopRecording();
          }
          return newDuration;
        });
      }, 1000);

    } catch (error) {
      Alert.alert('Error', 'Failed to start recording: ' + error.message);
    }
  };

  const stopRecording = async () => {
    if (!recording) return;

    try {
      await recording.stopAndUnloadAsync();
      const uri = recording.getURI();
      
      if (recordingTimer.current) {
        clearInterval(recordingTimer.current);
        recordingTimer.current = null;
      }

      setRecording(null);
      setIsRecording(false);

      if (uri) {
        // Get file info
        const fileInfo = await FileSystem.getInfoAsync(uri);
        
        const metadata = {
          duration: recordingDuration,
          size: fileInfo.size || 0,
          timestamp: Date.now(),
        };

        setAudioMetadata(metadata);
        onChange(uri, metadata);
        
        Alert.alert('Success', 'Audio recorded successfully!');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to stop recording: ' + error.message);
    }
  };

  const startPlayback = async () => {
    if (!value) return;

    try {
      if (sound) {
        await sound.unloadAsync();
      }

      const { sound: newSound } = await Audio.Sound.createAsync(
        { uri: value },
        { shouldPlay: true, isMuted }
      );

      setSound(newSound);
      setIsPlaying(true);

      // Get duration
      const status = await newSound.getStatusAsync();
      if (status.isLoaded) {
        setPlaybackDuration(status.durationMillis || 0);
      }

      // Start playback position tracking
      playbackTimer.current = setInterval(async () => {
        const status = await newSound.getStatusAsync();
        if (status.isLoaded) {
          setPlaybackPosition(status.positionMillis || 0);
          
          if (status.didJustFinish) {
            setIsPlaying(false);
            setPlaybackPosition(0);
            if (playbackTimer.current) {
              clearInterval(playbackTimer.current);
            }
          }
        }
      }, 100);

    } catch (error) {
      Alert.alert('Error', 'Failed to play audio: ' + error.message);
    }
  };

  const pausePlayback = async () => {
    if (!sound) return;

    try {
      await sound.pauseAsync();
      setIsPlaying(false);
      
      if (playbackTimer.current) {
        clearInterval(playbackTimer.current);
      }
    } catch (error) {
      console.error('Failed to pause playback:', error);
    }
  };

  const resumePlayback = async () => {
    if (!sound) return;

    try {
      await sound.playAsync();
      setIsPlaying(true);
      
      // Resume position tracking
      playbackTimer.current = setInterval(async () => {
        const status = await sound.getStatusAsync();
        if (status.isLoaded) {
          setPlaybackPosition(status.positionMillis || 0);
          
          if (status.didJustFinish) {
            setIsPlaying(false);
            setPlaybackPosition(0);
            if (playbackTimer.current) {
              clearInterval(playbackTimer.current);
            }
          }
        }
      }, 100);
    } catch (error) {
      console.error('Failed to resume playback:', error);
    }
  };

  const handlePlayPause = () => {
    if (!value) return;

    if (!sound) {
      startPlayback();
    } else if (isPlaying) {
      pausePlayback();
    } else {
      resumePlayback();
    }
  };

  const handleRetake = () => {
    Alert.alert(
      'Retake Audio',
      'Are you sure you want to retake this audio? The current recording will be lost.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Retake',
          style: 'destructive',
          onPress: async () => {
            if (sound) {
              await sound.unloadAsync();
              setSound(null);
            }
            onChange('');
            setAudioMetadata(null);
            setPlaybackPosition(0);
            setPlaybackDuration(0);
            setIsPlaying(false);
          },
        },
      ]
    );
  };

  const handleDelete = () => {
    Alert.alert(
      'Delete Audio',
      'Are you sure you want to delete this audio recording?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            if (sound) {
              await sound.unloadAsync();
              setSound(null);
            }
            onChange('');
            setAudioMetadata(null);
            setPlaybackPosition(0);
            setPlaybackDuration(0);
            setIsPlaying(false);
          },
        },
      ]
    );
  };

  const formatDuration = (milliseconds: number) => {
    const totalSeconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  const getProgressPercentage = () => {
    if (playbackDuration === 0) return 0;
    return (playbackPosition / playbackDuration) * 100;
  };

  const renderRecordingControls = () => (
    <View style={[styles.recordingContainer, { backgroundColor: theme.colors.card }]}>
      <View style={styles.recordingHeader}>
        <Mic size={24} color={theme.colors.primary} />
        <Text style={[styles.recordingTitle, { color: theme.colors.text }]}>
          {isRecording ? 'Recording...' : 'Ready to Record'}
        </Text>
        {isRecording && (
          <View style={styles.recordingIndicator}>
            <View style={styles.recordingDot} />
            <Text style={[styles.recordingTime, { color: theme.colors.error }]}>
              {formatDuration(recordingDuration * 1000)} / {formatDuration(maxDuration * 1000)}
            </Text>
          </View>
        )}
      </View>

      <View style={styles.recordingControls}>
        <TouchableOpacity
          style={[
            styles.recordButton,
            {
              backgroundColor: isRecording ? theme.colors.error : theme.colors.primary,
            }
          ]}
          onPress={isRecording ? stopRecording : startRecording}
          disabled={disabled}
        >
          {isRecording ? (
            <Square size={24} color="white" />
          ) : (
            <Mic size={24} color="white" />
          )}
        </TouchableOpacity>
      </View>

      <Text style={[styles.recordingHint, { color: theme.colors.muted }]}>
        {isRecording 
          ? 'Tap to stop recording' 
          : `Tap to start recording (max ${Math.floor(maxDuration / 60)}m ${maxDuration % 60}s)`
        }
      </Text>
    </View>
  );

  const renderPlaybackControls = () => (
    <View style={[styles.playbackContainer, { backgroundColor: theme.colors.card }]}>
      <View style={styles.playbackHeader}>
        <Volume2 size={20} color={theme.colors.primary} />
        <Text style={[styles.playbackTitle, { color: theme.colors.text }]}>
          Audio Recording
        </Text>
      </View>

      <View style={styles.progressContainer}>
        <View style={[styles.progressBar, { backgroundColor: theme.colors.border }]}>
          <View
            style={[
              styles.progressFill,
              {
                width: `${getProgressPercentage()}%`,
                backgroundColor: theme.colors.primary,
              }
            ]}
          />
        </View>
        <Text style={[styles.progressText, { color: theme.colors.muted }]}>
          {formatDuration(playbackPosition)} / {formatDuration(playbackDuration)}
        </Text>
      </View>

      <View style={styles.playbackControls}>
        <TouchableOpacity
          style={[styles.controlButton, { backgroundColor: theme.colors.muted }]}
          onPress={() => setIsMuted(!isMuted)}
        >
          {isMuted ? (
            <VolumeX size={16} color="white" />
          ) : (
            <Volume2 size={16} color="white" />
          )}
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.playButton, { backgroundColor: theme.colors.primary }]}
          onPress={handlePlayPause}
        >
          {isPlaying ? (
            <Pause size={24} color="white" />
          ) : (
            <Play size={24} color="white" />
          )}
        </TouchableOpacity>

        {allowRetake && (
          <TouchableOpacity
            style={[styles.controlButton, { backgroundColor: theme.colors.warning }]}
            onPress={handleRetake}
          >
            <RotateCcw size={16} color="white" />
          </TouchableOpacity>
        )}

        <TouchableOpacity
          style={[styles.controlButton, { backgroundColor: theme.colors.error }]}
          onPress={handleDelete}
        >
          <Trash2 size={16} color="white" />
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderAudioInfo = () => {
    if (!value || !audioMetadata) return null;

    return (
      <View style={styles.audioInfo}>
        <Text style={[styles.infoText, { color: theme.colors.muted }]}>
          Duration: {formatDuration(audioMetadata.duration * 1000)} • 
          Size: {formatFileSize(audioMetadata.size)} • 
          Quality: {quality.toUpperCase()} • 
          Recorded: {new Date(audioMetadata.timestamp).toLocaleString()}
        </Text>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {value ? (
        <>
          {enablePlayback && renderPlaybackControls()}
          {renderAudioInfo()}
        </>
      ) : (
        renderRecordingControls()
      )}

      {error && (
        <Text style={[styles.errorText, { color: theme.colors.error }]}>
          {error}
        </Text>
      )}

      {required && !value && (
        <Text style={[styles.requiredText, { color: theme.colors.muted }]}>
          * Required field
        </Text>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  recordingContainer: {
    borderRadius: 12,
    padding: 16,
    gap: 12,
  },
  recordingHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  recordingTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    flex: 1,
  },
  recordingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  recordingDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'red',
  },
  recordingTime: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },
  recordingControls: {
    alignItems: 'center',
    paddingVertical: 8,
  },
  recordButton: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
  },
  recordingHint: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
  },
  playbackContainer: {
    borderRadius: 12,
    padding: 16,
    gap: 12,
  },
  playbackHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  playbackTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
  },
  progressContainer: {
    gap: 8,
  },
  progressBar: {
    height: 4,
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
  progressText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
  },
  playbackControls: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 12,
  },
  controlButton: {
    padding: 12,
    borderRadius: 8,
  },
  playButton: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
  },
  audioInfo: {
    marginTop: 8,
  },
  infoText: {
    fontSize: 11,
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
  },
  errorText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    marginTop: 4,
  },
  requiredText: {
    fontSize: 11,
    fontFamily: 'Inter-Regular',
    marginTop: 4,
  },
});
