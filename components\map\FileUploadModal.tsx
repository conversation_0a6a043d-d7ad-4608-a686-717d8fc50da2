import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { useTheme } from '@/hooks/useTheme';
import {
  X,
  Upload,
  File,
  FileText,
  Map,
  Database,
  CheckCircle,
  AlertCircle,
  Trash2,
} from 'lucide-react-native';

interface UploadedFile {
  id: string;
  name: string;
  type: string;
  size: number;
  status: 'uploading' | 'processing' | 'completed' | 'error';
  progress: number;
  error?: string;
  layerId?: string;
}

interface FileUploadModalProps {
  visible: boolean;
  onClose: () => void;
  onFileUploaded: (file: File, layerData: any) => void;
}

const SUPPORTED_FORMATS = [
  { ext: '.geojson', type: 'GeoJSON', icon: FileText, description: 'Geographic JSON format' },
  { ext: '.json', type: 'GeoJSON', icon: FileText, description: 'Geographic JSON format' },
  { ext: '.shp', type: 'Shapefile', icon: Database, description: 'ESRI Shapefile (with .dbf, .shx)' },
  { ext: '.kml', type: 'KML', icon: Map, description: 'Keyhole Markup Language' },
  { ext: '.kmz', type: 'KMZ', icon: Map, description: 'Compressed KML' },
  { ext: '.dwg', type: 'DWG', icon: File, description: 'AutoCAD Drawing (basic support)' },
  { ext: '.gpx', type: 'GPX', icon: Map, description: 'GPS Exchange Format' },
  { ext: '.csv', type: 'CSV', icon: FileText, description: 'Comma-separated values with coordinates' },
];

export const FileUploadModal: React.FC<FileUploadModalProps> = ({
  visible,
  onClose,
  onFileUploaded,
}) => {
  const { theme } = useTheme();
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);

  const handleFileSelect = useCallback(() => {
    // Create file input element
    const input = document.createElement('input');
    input.type = 'file';
    input.multiple = true;
    input.accept = SUPPORTED_FORMATS.map(f => f.ext).join(',');
    
    input.onchange = (event) => {
      const files = (event.target as HTMLInputElement).files;
      if (files) {
        Array.from(files).forEach(file => processFile(file));
      }
    };
    
    input.click();
  }, []);

  const processFile = useCallback(async (file: File) => {
    const fileId = `file-${Date.now()}-${Math.random()}`;
    const uploadedFile: UploadedFile = {
      id: fileId,
      name: file.name,
      type: file.type || getFileType(file.name),
      size: file.size,
      status: 'uploading',
      progress: 0,
    };

    setUploadedFiles(prev => [...prev, uploadedFile]);

    try {
      // Simulate upload progress
      for (let progress = 0; progress <= 100; progress += 10) {
        await new Promise(resolve => setTimeout(resolve, 100));
        setUploadedFiles(prev => prev.map(f => 
          f.id === fileId ? { ...f, progress } : f
        ));
      }

      // Change to processing
      setUploadedFiles(prev => prev.map(f => 
        f.id === fileId ? { ...f, status: 'processing', progress: 0 } : f
      ));

      // Process the file based on its type
      const layerData = await parseGeospatialFile(file);
      
      // Simulate processing progress
      for (let progress = 0; progress <= 100; progress += 20) {
        await new Promise(resolve => setTimeout(resolve, 150));
        setUploadedFiles(prev => prev.map(f => 
          f.id === fileId ? { ...f, progress } : f
        ));
      }

      // Mark as completed
      setUploadedFiles(prev => prev.map(f => 
        f.id === fileId ? { ...f, status: 'completed', progress: 100 } : f
      ));

      // Call the callback with the processed data
      onFileUploaded(file, layerData);

    } catch (error) {
      console.error('File processing error:', error);
      setUploadedFiles(prev => prev.map(f => 
        f.id === fileId ? { 
          ...f, 
          status: 'error', 
          error: error instanceof Error ? error.message : 'Unknown error'
        } : f
      ));
    }
  }, [onFileUploaded]);

  const parseGeospatialFile = async (file: File): Promise<any> => {
    const fileName = file.name.toLowerCase();
    
    if (fileName.endsWith('.geojson') || fileName.endsWith('.json')) {
      return parseGeoJSON(file);
    } else if (fileName.endsWith('.kml')) {
      return parseKML(file);
    } else if (fileName.endsWith('.csv')) {
      return parseCSV(file);
    } else if (fileName.endsWith('.shp')) {
      return parseShapefile(file);
    } else if (fileName.endsWith('.dwg')) {
      return parseDWG(file);
    } else {
      throw new Error(`Unsupported file format: ${fileName}`);
    }
  };

  const parseGeoJSON = async (file: File): Promise<any> => {
    const text = await file.text();
    const geojson = JSON.parse(text);
    
    if (!geojson.type || (geojson.type !== 'FeatureCollection' && geojson.type !== 'Feature')) {
      throw new Error('Invalid GeoJSON format');
    }
    
    return {
      type: 'geojson',
      data: geojson,
      features: geojson.type === 'FeatureCollection' ? geojson.features : [geojson],
      metadata: {
        format: 'GeoJSON',
        featureCount: geojson.type === 'FeatureCollection' ? geojson.features.length : 1,
        geometryTypes: extractGeometryTypes(geojson),
        properties: extractProperties(geojson),
        bounds: calculateBounds(geojson),
      }
    };
  };

  const parseKML = async (file: File): Promise<any> => {
    const text = await file.text();
    // Basic KML parsing - in production, use a proper KML parser
    const parser = new DOMParser();
    const doc = parser.parseFromString(text, 'text/xml');
    
    // Convert KML to GeoJSON (simplified)
    const placemarks = doc.getElementsByTagName('Placemark');
    const features = Array.from(placemarks).map(placemark => {
      const name = placemark.getElementsByTagName('name')[0]?.textContent || 'Unnamed';
      const description = placemark.getElementsByTagName('description')[0]?.textContent || '';
      
      return {
        type: 'Feature',
        properties: { name, description },
        geometry: { type: 'Point', coordinates: [0, 0] } // Simplified
      };
    });
    
    return {
      type: 'kml',
      data: { type: 'FeatureCollection', features },
      features,
      metadata: {
        format: 'KML',
        featureCount: features.length,
        geometryTypes: ['Point'],
        properties: ['name', 'description'],
        bounds: { minX: -180, minY: -90, maxX: 180, maxY: 90 },
      }
    };
  };

  const parseCSV = async (file: File): Promise<any> => {
    const text = await file.text();
    const lines = text.split('\n');
    const headers = lines[0].split(',').map(h => h.trim());
    
    // Find coordinate columns
    const latCol = headers.findIndex(h => /lat|latitude|y/i.test(h));
    const lonCol = headers.findIndex(h => /lon|lng|longitude|x/i.test(h));
    
    if (latCol === -1 || lonCol === -1) {
      throw new Error('CSV must contain latitude and longitude columns');
    }
    
    const features = lines.slice(1)
      .filter(line => line.trim())
      .map((line, index) => {
        const values = line.split(',');
        const lat = parseFloat(values[latCol]);
        const lon = parseFloat(values[lonCol]);
        
        if (isNaN(lat) || isNaN(lon)) return null;
        
        const properties: any = {};
        headers.forEach((header, i) => {
          if (i !== latCol && i !== lonCol) {
            properties[header] = values[i]?.trim() || '';
          }
        });
        
        return {
          type: 'Feature',
          properties,
          geometry: {
            type: 'Point',
            coordinates: [lon, lat]
          }
        };
      })
      .filter(Boolean);
    
    return {
      type: 'csv',
      data: { type: 'FeatureCollection', features },
      features,
      metadata: {
        format: 'CSV',
        featureCount: features.length,
        geometryTypes: ['Point'],
        properties: headers.filter((_, i) => i !== latCol && i !== lonCol),
        bounds: calculateBounds({ type: 'FeatureCollection', features }),
      }
    };
  };

  const parseShapefile = async (file: File): Promise<any> => {
    // Shapefile parsing would require a specialized library like shapefile-js
    // For now, return a placeholder
    throw new Error('Shapefile support requires additional files (.dbf, .shx). Please use GeoJSON instead.');
  };

  const parseDWG = async (file: File): Promise<any> => {
    // DWG parsing would require a specialized library
    // For now, return a placeholder
    throw new Error('DWG format support is limited. Please convert to GeoJSON or KML.');
  };

  const getFileType = (fileName: string): string => {
    const ext = fileName.toLowerCase().split('.').pop();
    const format = SUPPORTED_FORMATS.find(f => f.ext === `.${ext}`);
    return format?.type || 'Unknown';
  };

  const extractGeometryTypes = (geojson: any): string[] => {
    const types = new Set<string>();
    if (geojson.type === 'FeatureCollection') {
      geojson.features.forEach((feature: any) => {
        if (feature.geometry) types.add(feature.geometry.type);
      });
    } else if (geojson.geometry) {
      types.add(geojson.geometry.type);
    }
    return Array.from(types);
  };

  const extractProperties = (geojson: any): string[] => {
    const properties = new Set<string>();
    if (geojson.type === 'FeatureCollection') {
      geojson.features.forEach((feature: any) => {
        if (feature.properties) {
          Object.keys(feature.properties).forEach(key => properties.add(key));
        }
      });
    } else if (geojson.properties) {
      Object.keys(geojson.properties).forEach(key => properties.add(key));
    }
    return Array.from(properties);
  };

  const calculateBounds = (geojson: any): any => {
    let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;

    const processCoordinates = (coords: any, type: string) => {
      if (type === 'Point') {
        const [x, y] = coords;
        minX = Math.min(minX, x);
        maxX = Math.max(maxX, x);
        minY = Math.min(minY, y);
        maxY = Math.max(maxY, y);
      } else if (type === 'LineString' || type === 'MultiPoint') {
        coords.forEach((coord: any) => {
          const [x, y] = coord;
          minX = Math.min(minX, x);
          maxX = Math.max(maxX, x);
          minY = Math.min(minY, y);
          maxY = Math.max(maxY, y);
        });
      } else if (type === 'Polygon' || type === 'MultiLineString') {
        coords.forEach((ring: any) => {
          ring.forEach((coord: any) => {
            const [x, y] = coord;
            minX = Math.min(minX, x);
            maxX = Math.max(maxX, x);
            minY = Math.min(minY, y);
            maxY = Math.max(maxY, y);
          });
        });
      } else if (type === 'MultiPolygon') {
        coords.forEach((polygon: any) => {
          polygon.forEach((ring: any) => {
            ring.forEach((coord: any) => {
              const [x, y] = coord;
              minX = Math.min(minX, x);
              maxX = Math.max(maxX, x);
              minY = Math.min(minY, y);
              maxY = Math.max(maxY, y);
            });
          });
        });
      }
    };

    const processFeature = (feature: any) => {
      if (feature.geometry && feature.geometry.coordinates) {
        processCoordinates(feature.geometry.coordinates, feature.geometry.type);
      }
    };

    if (geojson.type === 'FeatureCollection') {
      geojson.features.forEach(processFeature);
    } else if (geojson.type === 'Feature') {
      processFeature(geojson);
    }

    // Return valid bounds or default if no coordinates found
    if (minX === Infinity) {
      return { minX: -180, minY: -90, maxX: 180, maxY: 90 };
    }

    return { minX, minY, maxX, maxY };
  };

  const removeFile = useCallback((fileId: string) => {
    setUploadedFiles(prev => prev.filter(f => f.id !== fileId));
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    files.forEach(file => processFile(file));
  }, [processFile]);

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={[styles.modalContainer, { backgroundColor: theme.colors.background }]}>
        {/* Header */}
        <View style={[styles.modalHeader, { backgroundColor: theme.colors.card, borderBottomColor: theme.colors.border }]}>
          <Text style={[styles.modalTitle, { color: theme.colors.text }]}>Upload Geospatial Files</Text>
          <TouchableOpacity onPress={onClose}>
            <X size={24} color={theme.colors.text} />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content}>
          {/* Upload Area */}
          <View
            style={[
              styles.uploadArea,
              {
                backgroundColor: isDragOver ? theme.colors.primary + '20' : theme.colors.background,
                borderColor: isDragOver ? theme.colors.primary : theme.colors.border,
              }
            ]}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            <Upload size={48} color={theme.colors.muted} />
            <Text style={[styles.uploadTitle, { color: theme.colors.text }]}>
              Drop files here or click to browse
            </Text>
            <Text style={[styles.uploadSubtitle, { color: theme.colors.muted }]}>
              Supports GeoJSON, Shapefile, KML, KMZ, DWG, GPX, CSV
            </Text>
            <TouchableOpacity
              style={[styles.browseButton, { backgroundColor: theme.colors.primary }]}
              onPress={handleFileSelect}
            >
              <Text style={[styles.browseButtonText, { color: 'white' }]}>
                Browse Files
              </Text>
            </TouchableOpacity>
          </View>

          {/* Supported Formats */}
          <View style={styles.formatsSection}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
              Supported Formats
            </Text>
            {SUPPORTED_FORMATS.map((format, index) => (
              <View key={index} style={[styles.formatItem, { borderBottomColor: theme.colors.border }]}>
                <format.icon size={20} color={theme.colors.primary} />
                <View style={styles.formatInfo}>
                  <Text style={[styles.formatName, { color: theme.colors.text }]}>
                    {format.type} ({format.ext})
                  </Text>
                  <Text style={[styles.formatDescription, { color: theme.colors.muted }]}>
                    {format.description}
                  </Text>
                </View>
              </View>
            ))}
          </View>

          {/* Uploaded Files */}
          {uploadedFiles.length > 0 && (
            <View style={styles.filesSection}>
              <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
                Uploaded Files
              </Text>
              {uploadedFiles.map(file => (
                <View key={file.id} style={[styles.fileItem, { backgroundColor: theme.colors.card, borderColor: theme.colors.border }]}>
                  <View style={styles.fileInfo}>
                    <Text style={[styles.fileName, { color: theme.colors.text }]}>
                      {file.name}
                    </Text>
                    <Text style={[styles.fileSize, { color: theme.colors.muted }]}>
                      {(file.size / 1024).toFixed(1)} KB • {file.type}
                    </Text>
                    
                    {file.status === 'uploading' && (
                      <View style={styles.progressContainer}>
                        <View style={[styles.progressBar, { backgroundColor: theme.colors.border }]}>
                          <View
                            style={[
                              styles.progressFill,
                              {
                                backgroundColor: theme.colors.primary,
                                width: `${file.progress}%`,
                              }
                            ]}
                          />
                        </View>
                        <Text style={[styles.progressText, { color: theme.colors.muted }]}>
                          Uploading... {file.progress}%
                        </Text>
                      </View>
                    )}
                    
                    {file.status === 'processing' && (
                      <View style={styles.progressContainer}>
                        <ActivityIndicator size="small" color={theme.colors.primary} />
                        <Text style={[styles.progressText, { color: theme.colors.muted }]}>
                          Processing... {file.progress}%
                        </Text>
                      </View>
                    )}
                    
                    {file.status === 'error' && (
                      <View style={styles.statusContainer}>
                        <AlertCircle size={16} color={theme.colors.destructive} />
                        <Text style={[styles.errorText, { color: theme.colors.destructive }]}>
                          {file.error}
                        </Text>
                      </View>
                    )}
                    
                    {file.status === 'completed' && (
                      <View style={styles.statusContainer}>
                        <CheckCircle size={16} color={theme.colors.success} />
                        <Text style={[styles.successText, { color: theme.colors.success }]}>
                          Successfully imported
                        </Text>
                      </View>
                    )}
                  </View>
                  
                  <TouchableOpacity
                    style={styles.removeButton}
                    onPress={() => removeFile(file.id)}
                  >
                    <Trash2 size={16} color={theme.colors.destructive} />
                  </TouchableOpacity>
                </View>
              ))}
            </View>
          )}
        </ScrollView>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  uploadArea: {
    borderWidth: 2,
    borderStyle: 'dashed',
    borderRadius: 12,
    padding: 32,
    alignItems: 'center',
    marginBottom: 24,
  },
  uploadTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    marginTop: 16,
    marginBottom: 8,
  },
  uploadSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
    marginBottom: 16,
  },
  browseButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  browseButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
  },
  formatsSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 16,
  },
  formatItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    gap: 12,
  },
  formatInfo: {
    flex: 1,
  },
  formatName: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 2,
  },
  formatDescription: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
  },
  filesSection: {
    marginBottom: 24,
  },
  fileItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 8,
  },
  fileInfo: {
    flex: 1,
  },
  fileName: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 4,
  },
  fileSize: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    marginBottom: 8,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  progressBar: {
    flex: 1,
    height: 4,
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
  progressText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    minWidth: 80,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  errorText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
  },
  successText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
  },
  removeButton: {
    padding: 8,
  },
});
