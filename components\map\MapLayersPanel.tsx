import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { useTheme } from '@/hooks/useTheme';
import { Project } from '@/types';
import { X, Layers, CheckCircle } from 'lucide-react-native';

type MapLayersPanelProps = {
  projects: Project[];
  selectedProject: string | null;
  onSelectProject: (projectId: string | null) => void;
  onClose: () => void;
};

export default function MapLayersPanel({ 
  projects, 
  selectedProject, 
  onSelectProject, 
  onClose 
}: MapLayersPanelProps) {
  const { theme } = useTheme();
  
  return (
    <View style={[styles.container, { backgroundColor: theme.colors.card }]}>
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Layers size={20} color={theme.colors.primary} />
          <Text style={[styles.title, { color: theme.colors.text }]}>Map Layers</Text>
        </View>
        <TouchableOpacity style={styles.closeButton} onPress={onClose}>
          <X size={20} color={theme.colors.text} />
        </TouchableOpacity>
      </View>
      
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Projects</Text>
        
        <TouchableOpacity
          style={[
            styles.projectItem,
            { backgroundColor: !selectedProject ? theme.colors.primaryLight : 'transparent' }
          ]}
          onPress={() => onSelectProject(null)}
        >
          <Text 
            style={[
              styles.projectName, 
              { color: !selectedProject ? theme.colors.primary : theme.colors.text }
            ]}
          >
            All Projects
          </Text>
          {!selectedProject && <CheckCircle size={16} color={theme.colors.primary} />}
        </TouchableOpacity>
        
        {projects.map(project => (
          <TouchableOpacity
            key={project.id}
            style={[
              styles.projectItem,
              { backgroundColor: selectedProject === project.id ? theme.colors.primaryLight : 'transparent' }
            ]}
            onPress={() => onSelectProject(project.id)}
          >
            <Text 
              style={[
                styles.projectName, 
                { color: selectedProject === project.id ? theme.colors.primary : theme.colors.text }
              ]}
            >
              {project.name}
            </Text>
            {selectedProject === project.id && <CheckCircle size={16} color={theme.colors.primary} />}
          </TouchableOpacity>
        ))}
        
        <Text style={[styles.sectionTitle, { color: theme.colors.text, marginTop: 16 }]}>Base Maps</Text>
        
        <TouchableOpacity style={[styles.projectItem, { backgroundColor: theme.colors.primaryLight }]}>
          <Text style={[styles.projectName, { color: theme.colors.primary }]}>Satellite</Text>
          <CheckCircle size={16} color={theme.colors.primary} />
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.projectItem}>
          <Text style={[styles.projectName, { color: theme.colors.text }]}>Streets</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.projectItem}>
          <Text style={[styles.projectName, { color: theme.colors.text }]}>Terrain</Text>
        </TouchableOpacity>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 16,
    left: 16,
    width: 250,
    maxHeight: '80%',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  title: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
  },
  closeButton: {
    padding: 4,
  },
  content: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 8,
  },
  projectItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 12,
    borderRadius: 8,
    marginBottom: 4,
  },
  projectName: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
  },
});