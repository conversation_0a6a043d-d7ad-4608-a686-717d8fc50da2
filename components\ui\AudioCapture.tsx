import React from 'react';
import { Platform } from 'react-native';

// Platform-specific imports
const AudioCaptureNative = React.lazy(() => import('./AudioCapture.native'));
const AudioCaptureWeb = React.lazy(() => import('./AudioCapture.web'));

interface AudioCaptureProps {
  value?: { uri: string; duration: number };
  onChange: (recording: { uri: string; duration: number } | null) => void;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  maxDuration?: number;
}

export default function AudioCapture(props: AudioCaptureProps) {
  const AudioCaptureComponent = Platform.OS === 'web' ? AudioCaptureWeb : AudioCaptureNative;
  
  return (
    <React.Suspense fallback={null}>
      <AudioCaptureComponent {...props} />
    </React.Suspense>
  );
}
