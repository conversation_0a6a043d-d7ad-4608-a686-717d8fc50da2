/**
 * Performance monitoring utilities for Advanced Map
 * Tracks rendering performance, memory usage, and user interactions
 */

interface PerformanceMetric {
  id: string;
  name: string;
  value: number;
  unit: string;
  timestamp: string;
  category: 'rendering' | 'memory' | 'interaction' | 'analysis' | 'data';
  severity: 'normal' | 'warning' | 'critical';
}

interface PerformanceReport {
  sessionId: string;
  startTime: string;
  endTime: string;
  metrics: PerformanceMetric[];
  summary: {
    avgFrameRate: number;
    maxMemoryUsage: number;
    totalInteractions: number;
    analysisCount: number;
    errorCount: number;
  };
  deviceInfo: {
    userAgent: string;
    platform: string;
    memory?: number;
    cores?: number;
  };
}

class MapPerformanceMonitor {
  private sessionId: string;
  private startTime: number;
  private metrics: PerformanceMetric[] = [];
  private frameRates: number[] = [];
  private memoryReadings: number[] = [];
  private interactionCount: number = 0;
  private analysisCount: number = 0;
  private errorCount: number = 0;
  private isMonitoring: boolean = false;
  
  private frameRateInterval?: NodeJS.Timeout;
  private memoryInterval?: NodeJS.Timeout;

  constructor() {
    this.sessionId = `session-${Date.now()}`;
    this.startTime = Date.now();
  }

  /**
   * Start performance monitoring
   */
  startMonitoring(): void {
    if (this.isMonitoring) return;
    
    this.isMonitoring = true;
    this.startTime = Date.now();
    console.log(`🔍 MapPerformanceMonitor: Starting monitoring session ${this.sessionId}`);
    
    // Monitor frame rate every second
    this.frameRateInterval = setInterval(() => {
      this.measureFrameRate();
    }, 1000);
    
    // Monitor memory usage every 5 seconds
    this.memoryInterval = setInterval(() => {
      this.measureMemoryUsage();
    }, 5000);
    
    // Initial measurements
    this.measureMemoryUsage();
    this.recordDeviceInfo();
  }

  /**
   * Stop performance monitoring
   */
  stopMonitoring(): PerformanceReport {
    if (!this.isMonitoring) {
      throw new Error('Monitoring is not active');
    }
    
    this.isMonitoring = false;
    
    if (this.frameRateInterval) {
      clearInterval(this.frameRateInterval);
    }
    
    if (this.memoryInterval) {
      clearInterval(this.memoryInterval);
    }
    
    const report = this.generateReport();
    console.log('📊 MapPerformanceMonitor: Monitoring stopped', report);
    
    return report;
  }

  /**
   * Record a custom performance metric
   */
  recordMetric(
    name: string,
    value: number,
    unit: string,
    category: PerformanceMetric['category'] = 'interaction',
    severity: PerformanceMetric['severity'] = 'normal'
  ): void {
    const metric: PerformanceMetric = {
      id: `metric-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      name,
      value,
      unit,
      timestamp: new Date().toISOString(),
      category,
      severity,
    };
    
    this.metrics.push(metric);
    
    if (severity === 'warning' || severity === 'critical') {
      console.warn(`⚠️ Performance ${severity}: ${name} = ${value} ${unit}`);
    }
  }

  /**
   * Record map rendering time
   */
  recordRenderTime(startTime: number, endTime: number): void {
    const renderTime = endTime - startTime;
    this.recordMetric('Map Render Time', renderTime, 'ms', 'rendering', 
      renderTime > 100 ? 'warning' : renderTime > 500 ? 'critical' : 'normal');
  }

  /**
   * Record layer loading time
   */
  recordLayerLoadTime(layerName: string, startTime: number, endTime: number): void {
    const loadTime = endTime - startTime;
    this.recordMetric(`Layer Load: ${layerName}`, loadTime, 'ms', 'data',
      loadTime > 2000 ? 'warning' : loadTime > 5000 ? 'critical' : 'normal');
  }

  /**
   * Record analysis execution time
   */
  recordAnalysisTime(analysisType: string, startTime: number, endTime: number, featureCount: number): void {
    const analysisTime = endTime - startTime;
    this.analysisCount++;
    
    this.recordMetric(`Analysis: ${analysisType}`, analysisTime, 'ms', 'analysis',
      analysisTime > 5000 ? 'warning' : analysisTime > 15000 ? 'critical' : 'normal');
    
    this.recordMetric(`Analysis Features: ${analysisType}`, featureCount, 'count', 'analysis');
  }

  /**
   * Record user interaction
   */
  recordInteraction(interactionType: string, duration?: number): void {
    this.interactionCount++;
    this.recordMetric(`Interaction: ${interactionType}`, duration || 1, duration ? 'ms' : 'count', 'interaction');
  }

  /**
   * Record error occurrence
   */
  recordError(errorType: string, errorMessage: string): void {
    this.errorCount++;
    this.recordMetric(`Error: ${errorType}`, 1, 'count', 'rendering', 'critical');
    console.error(`❌ Map Error [${errorType}]: ${errorMessage}`);
  }

  /**
   * Measure current frame rate
   */
  private measureFrameRate(): void {
    // In React Native, we approximate frame rate by measuring render cycles
    const startTime = performance.now();
    
    requestAnimationFrame(() => {
      const endTime = performance.now();
      const frameTime = endTime - startTime;
      const fps = frameTime > 0 ? 1000 / frameTime : 60;
      
      this.frameRates.push(Math.min(fps, 60)); // Cap at 60 FPS
      this.recordMetric('Frame Rate', fps, 'fps', 'rendering',
        fps < 30 ? 'warning' : fps < 15 ? 'critical' : 'normal');
    });
  }

  /**
   * Measure memory usage
   */
  private measureMemoryUsage(): void {
    // Note: Memory measurement is limited in React Native
    // This is a simplified implementation
    
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      const usedMemory = memory.usedJSHeapSize;
      const totalMemory = memory.totalJSHeapSize;
      const memoryPercent = (usedMemory / totalMemory) * 100;
      
      this.memoryReadings.push(usedMemory);
      this.recordMetric('Memory Usage', usedMemory, 'bytes', 'memory',
        memoryPercent > 80 ? 'warning' : memoryPercent > 95 ? 'critical' : 'normal');
      this.recordMetric('Memory Percentage', memoryPercent, '%', 'memory');
    } else {
      // Fallback for environments without memory API
      this.recordMetric('Memory Usage', 0, 'bytes', 'memory');
    }
  }

  /**
   * Record device information
   */
  private recordDeviceInfo(): void {
    const deviceInfo = {
      userAgent: navigator.userAgent,
      platform: navigator.platform,
      memory: (navigator as any).deviceMemory,
      cores: navigator.hardwareConcurrency,
    };
    
    this.recordMetric('Device Cores', deviceInfo.cores || 1, 'count', 'rendering');
    if (deviceInfo.memory) {
      this.recordMetric('Device Memory', deviceInfo.memory, 'GB', 'memory');
    }
  }

  /**
   * Generate performance report
   */
  private generateReport(): PerformanceReport {
    const endTime = Date.now();
    const avgFrameRate = this.frameRates.length > 0 
      ? this.frameRates.reduce((a, b) => a + b, 0) / this.frameRates.length 
      : 0;
    const maxMemoryUsage = this.memoryReadings.length > 0 
      ? Math.max(...this.memoryReadings) 
      : 0;

    return {
      sessionId: this.sessionId,
      startTime: new Date(this.startTime).toISOString(),
      endTime: new Date(endTime).toISOString(),
      metrics: this.metrics,
      summary: {
        avgFrameRate,
        maxMemoryUsage,
        totalInteractions: this.interactionCount,
        analysisCount: this.analysisCount,
        errorCount: this.errorCount,
      },
      deviceInfo: {
        userAgent: navigator.userAgent,
        platform: navigator.platform,
        memory: (navigator as any).deviceMemory,
        cores: navigator.hardwareConcurrency,
      },
    };
  }

  /**
   * Get current metrics summary
   */
  getCurrentMetrics(): {
    frameRate: number;
    memoryUsage: number;
    interactionCount: number;
    analysisCount: number;
    errorCount: number;
  } {
    const recentFrameRates = this.frameRates.slice(-10);
    const recentMemory = this.memoryReadings.slice(-5);
    
    return {
      frameRate: recentFrameRates.length > 0 
        ? recentFrameRates.reduce((a, b) => a + b, 0) / recentFrameRates.length 
        : 0,
      memoryUsage: recentMemory.length > 0 
        ? recentMemory[recentMemory.length - 1] 
        : 0,
      interactionCount: this.interactionCount,
      analysisCount: this.analysisCount,
      errorCount: this.errorCount,
    };
  }

  /**
   * Check if performance is degraded
   */
  isPerformanceDegraded(): boolean {
    const current = this.getCurrentMetrics();
    return current.frameRate < 20 || this.errorCount > 5;
  }

  /**
   * Get performance recommendations
   */
  getRecommendations(): string[] {
    const recommendations: string[] = [];
    const current = this.getCurrentMetrics();
    
    if (current.frameRate < 30) {
      recommendations.push('Consider reducing the number of visible features or enabling clustering');
    }
    
    if (current.memoryUsage > 100 * 1024 * 1024) { // 100MB
      recommendations.push('High memory usage detected. Try clearing unused layers or features');
    }
    
    if (current.errorCount > 3) {
      recommendations.push('Multiple errors detected. Check console for details and consider refreshing');
    }
    
    if (this.analysisCount > 10) {
      recommendations.push('Many analyses performed. Consider saving results to avoid recomputation');
    }
    
    return recommendations;
  }

  /**
   * Export performance data
   */
  exportData(): string {
    const report = this.generateReport();
    return JSON.stringify(report, null, 2);
  }

  /**
   * Reset monitoring data
   */
  reset(): void {
    this.sessionId = `session-${Date.now()}`;
    this.startTime = Date.now();
    this.metrics = [];
    this.frameRates = [];
    this.memoryReadings = [];
    this.interactionCount = 0;
    this.analysisCount = 0;
    this.errorCount = 0;
  }
}

// Singleton instance for global use
export const mapPerformanceMonitor = new MapPerformanceMonitor();

// React hook for performance monitoring with error handling
export const useMapPerformance = () => {
  const startMonitoring = () => {
    try {
      return mapPerformanceMonitor.startMonitoring();
    } catch (error) {
      console.error('Start monitoring error:', error);
    }
  };
  
  const stopMonitoring = () => {
    try {
      return mapPerformanceMonitor.stopMonitoring();
    } catch (error) {
      console.error('Stop monitoring error:', error);
    }
  };
  
  const recordMetric = (name: string, value: number, unit: string, category?: PerformanceMetric['category']) => {
    try {
      return mapPerformanceMonitor.recordMetric(name, value, unit, category);
    } catch (error) {
      console.error('Record metric error:', error);
    }
  };
  
  const recordInteraction = (type: string, duration?: number) => {
    try {
      return mapPerformanceMonitor.recordInteraction(type, duration);
    } catch (error) {
      console.error('Record interaction error:', error);
    }
  };
  
  const recordError = (type: string, message: string) => {
    try {
      return mapPerformanceMonitor.recordError(type, message);
    } catch (error) {
      console.error('Record error error:', error);
    }
  };
  
  const getCurrentMetrics = () => {
    try {
      return mapPerformanceMonitor.getCurrentMetrics();
    } catch (error) {
      console.error('Get current metrics error:', error);
      return {
        frameRate: 60,
        memoryUsage: 0,
        interactionCount: 0,
        analysisCount: 0,
        errorCount: 0,
      };
    }
  };
  
  const getRecommendations = () => {
    try {
      return mapPerformanceMonitor.getRecommendations();
    } catch (error) {
      console.error('Get recommendations error:', error);
      return [];
    }
  };
  
  const isPerformanceDegraded = () => {
    try {
      return mapPerformanceMonitor.isPerformanceDegraded();
    } catch (error) {
      console.error('Is performance degraded error:', error);
      return false;
    }
  };

  return {
    startMonitoring,
    stopMonitoring,
    recordMetric,
    recordInteraction,
    recordError,
    getCurrentMetrics,
    getRecommendations,
    isPerformanceDegraded,
  };
};

// Performance decorator for timing functions
export const withPerformanceTracking = <T extends (...args: any[]) => any>(
  fn: T,
  metricName: string,
  category: PerformanceMetric['category'] = 'interaction'
): T => {
  return ((...args: Parameters<T>) => {
    const startTime = performance.now();
    try {
      const result = fn(...args);
      
      // Handle both sync and async functions
      if (result instanceof Promise) {
        return result.finally(() => {
          const endTime = performance.now();
          mapPerformanceMonitor.recordMetric(metricName, endTime - startTime, 'ms', category);
        });
      } else {
        const endTime = performance.now();
        mapPerformanceMonitor.recordMetric(metricName, endTime - startTime, 'ms', category);
        return result;
      }
    } catch (error) {
      const endTime = performance.now();
      mapPerformanceMonitor.recordMetric(metricName, endTime - startTime, 'ms', category, 'critical');
      mapPerformanceMonitor.recordError(metricName, error instanceof Error ? error.message : 'Unknown error');
      throw error;
    }
  }) as T;
};

export type { PerformanceMetric, PerformanceReport };
export { MapPerformanceMonitor };
