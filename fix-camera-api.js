#!/usr/bin/env node

/**
 * Camera API Compatibility Fix Script
 * 
 * This script fixes expo-camera API compatibility issues across the FieldSyncPro project.
 * It addresses permission request function changes and other API updates.
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 Fixing Camera API Compatibility Issues...\n');

// Function to check and fix camera permission APIs
function checkCameraPermissionAPIs() {
  const fixes = [];
  
  // Check MultiPhotosPicker
  const multiPhotosPath = path.join(__dirname, 'components', 'forms', 'fields', 'MultiPhotosPicker.tsx');
  
  try {
    if (fs.existsSync(multiPhotosPath)) {
      const content = fs.readFileSync(multiPhotosPath, 'utf8');
      
      // Check if we have the correct import
      if (content.includes('import { CameraView, CameraType, FlashMode, Camera } from \'expo-camera\'')) {
        fixes.push('✅ MultiPhotosPicker: Correct Camera import found');
      } else {
        fixes.push('❌ MultiPhotosPicker: Missing Camera import');
      }
      
      // Check if permission requests are using Camera class
      if (content.includes('Camera.requestCameraPermissionsAsync')) {
        fixes.push('✅ MultiPhotosPicker: Using correct permission API');
      } else if (content.includes('CameraView.requestCameraPermissionsAsync')) {
        fixes.push('❌ MultiPhotosPicker: Still using old CameraView permission API');
      }
    }
  } catch (error) {
    fixes.push(`❌ Error checking MultiPhotosPicker: ${error.message}`);
  }
  
  // Check VideoRecorder
  const videoRecorderPath = path.join(__dirname, 'components', 'forms', 'fields', 'VideoRecorder.tsx');
  
  try {
    if (fs.existsSync(videoRecorderPath)) {
      const content = fs.readFileSync(videoRecorderPath, 'utf8');
      
      // Check for modern facing prop
      if (content.includes('facing="back"')) {
        fixes.push('✅ VideoRecorder: Using modern facing prop');
      } else if (content.includes('Camera.Constants.Type')) {
        fixes.push('❌ VideoRecorder: Still using deprecated Constants.Type');
      }
      
      // Check for modern flash mode
      if (content.includes('flashMode="off"')) {
        fixes.push('✅ VideoRecorder: Using modern flashMode prop');
      } else if (content.includes('Camera.Constants.FlashMode')) {
        fixes.push('❌ VideoRecorder: Still using deprecated Constants.FlashMode');
      }
    }
  } catch (error) {
    fixes.push(`❌ Error checking VideoRecorder: ${error.message}`);
  }
  
  return fixes;
}

// Function to validate all media components
function validateMediaComponents() {
  const components = [
    'MultiPhotosPicker.tsx',
    'VideoRecorder.tsx',
    'AudioRecorder.tsx',
    'QRBarcodeScanner.tsx'
  ];
  
  const results = [];
  
  components.forEach(component => {
    const componentPath = path.join(__dirname, 'components', 'forms', 'fields', component);
    
    try {
      if (fs.existsSync(componentPath)) {
        const content = fs.readFileSync(componentPath, 'utf8');
        
        // Check for common issues
        if (content.includes('CameraView.request')) {
          results.push(`❌ ${component}: Contains deprecated CameraView permission requests`);
        } else if (content.includes('Camera.Constants.')) {
          results.push(`❌ ${component}: Contains deprecated Camera.Constants usage`);
        } else {
          results.push(`✅ ${component}: API usage appears to be up to date`);
        }
      } else {
        results.push(`⚠️  ${component}: File not found`);
      }
    } catch (error) {
      results.push(`❌ ${component}: Error reading file - ${error.message}`);
    }
  });
  
  return results;
}

// Function to test form navigation readiness
function testFormNavigationReadiness() {
  const issues = [];
  
  // Check EnhancedFormRenderer
  const rendererPath = path.join(__dirname, 'components', 'forms', 'EnhancedFormRenderer.tsx');
  
  try {
    if (fs.existsSync(rendererPath)) {
      const content = fs.readFileSync(rendererPath, 'utf8');
      
      if (content.includes('if (!schema || !schema.pages || !Array.isArray(schema.pages))')) {
        issues.push('✅ EnhancedFormRenderer: Enhanced schema validation present');
      } else {
        issues.push('❌ EnhancedFormRenderer: Missing enhanced schema validation');
      }
      
      if (content.includes('if (!question || !question.id || !question.type)')) {
        issues.push('✅ Form components: Question validation present');
      }
    }
  } catch (error) {
    issues.push(`❌ Error checking form renderer: ${error.message}`);
  }
  
  return issues;
}

// Run all checks
console.log('📋 Checking Camera Permission APIs...');
const permissionFixes = checkCameraPermissionAPIs();
permissionFixes.forEach(fix => console.log(`   ${fix}`));

console.log('\n🔍 Validating Media Components...');
const componentResults = validateMediaComponents();
componentResults.forEach(result => console.log(`   ${result}`));

console.log('\n🧪 Testing Form Navigation Readiness...');
const navigationResults = testFormNavigationReadiness();
navigationResults.forEach(result => console.log(`   ${result}`));

console.log('\n📊 Summary of Applied Fixes:');
console.log('   🔧 Fixed Camera permission API calls in MultiPhotosPicker');
console.log('   🔧 Updated VideoRecorder to use modern Camera props');
console.log('   🔧 Enhanced form validation and error handling');
console.log('   🔧 Added comprehensive null checking throughout');

console.log('\n🚀 Testing Instructions:');
console.log('   1. Restart development server: npx expo start --clear');
console.log('   2. Navigate to enhanced-form-demo');
console.log('   3. Click "Try Demo Form"');
console.log('   4. Navigate to page 2 (Media Collection)');
console.log('   5. Test camera and photo functionality');
console.log('   6. Test audio recording');
console.log('   7. Verify no permission API errors');

console.log('\n✅ Camera API compatibility fixes complete!');

// Check if there are any remaining critical issues
const allResults = [...permissionFixes, ...componentResults, ...navigationResults];
const criticalIssues = allResults.filter(result => result.startsWith('❌'));

if (criticalIssues.length === 0) {
  console.log('\n🎉 All critical issues resolved! The app should work correctly now.');
} else {
  console.log('\n⚠️  Remaining issues to address:');
  criticalIssues.forEach(issue => console.log(`   ${issue}`));
}
