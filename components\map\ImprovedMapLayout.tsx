import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Modal,
  TextInput,
  Dimensions,
  Platform,
  Alert,
  SafeAreaView,
} from 'react-native';
import { useTheme } from '@/hooks/useTheme';
import * as Location from 'expo-location';
import {
  MapPin,
  Layers,
  Ruler,
  Search,
  Settings,
  Map as MapIcon,
  Satellite,
  Plus,
  Minus,
  Target,
  Route,
  Pentagon,
  Activity,
  Thermometer,
  Shield,
  Edit3,
  Eye,
  EyeOff,
  Filter,
  BarChart3,
  Zap,
  X,
  Menu,
} from 'lucide-react-native';

// Platform-specific map component import
const PlatformMap = Platform.OS === 'web' 
  ? require('./Map.web').default 
  : require('./Map.native').default;

interface Region {
  latitude: number;
  longitude: number;
  latitudeDelta: number;
  longitudeDelta: number;
}

interface ImprovedMapLayoutProps {
  initialRegion?: Region;
  onLocationSelect?: (location: { latitude: number; longitude: number }) => void;
  geoFeatures?: any[];
  showAnalysisTools?: boolean;
  enableDrawing?: boolean;
  enableMeasurement?: boolean;
  enableGeofencing?: boolean;
  enableRouting?: boolean;
  enableHeatmap?: boolean;
  enableClustering?: boolean;
  offlineMode?: boolean;
}

type DrawingMode = 'none' | 'point' | 'line' | 'polygon' | 'circle' | 'rectangle';
type MapLayer = 'standard' | 'satellite' | 'terrain' | 'hybrid';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

export default function ImprovedMapLayout({
  initialRegion,
  onLocationSelect,
  geoFeatures = [],
  showAnalysisTools = true,
  enableDrawing = true,
  enableMeasurement = true,
  enableGeofencing = true,
  enableRouting = true,
  enableHeatmap = true,
  enableClustering = true,
  offlineMode = false,
}: ImprovedMapLayoutProps) {
  const { theme } = useTheme();
  
  // State management
  const [region] = useState<Region>(initialRegion || {
    latitude: 37.78825,
    longitude: -122.4324,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  });
  
  const [mapType, setMapType] = useState<MapLayer>('standard');
  const [drawingMode, setDrawingMode] = useState<DrawingMode>('none');
  const [measurementMode, setMeasurementMode] = useState(false);
  const [showHeatmap, setShowHeatmap] = useState(false);
  const [userLocation, setUserLocation] = useState<Location.LocationObject | null>(null);
  
  // UI state
  const [showLayersPanel, setShowLayersPanel] = useState(false);
  const [showDrawingTools, setShowDrawingTools] = useState(false);
  const [showAnalysisPanel, setShowAnalysisPanel] = useState(false);
  const [showFeaturesList, setShowFeaturesList] = useState(false);
  const [showSearch, setShowSearch] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [uiExpanded, setUiExpanded] = useState(false);

  useEffect(() => {
    requestLocationPermission();
  }, []);

  const requestLocationPermission = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status === 'granted') {
        const location = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.High,
        });
        setUserLocation(location);
      }
    } catch (error) {
      console.error('Error getting location:', error);
    }
  };

  const generateHeatmapData = () => {
    Alert.alert('Heatmap', 'Heatmap data generation demo');
    setShowHeatmap(true);
  };

  const renderCompactToolbar = () => (
    <View style={[styles.compactToolbar, { backgroundColor: theme.colors.card }]}>
      <TouchableOpacity
        style={styles.expandButton}
        onPress={() => setUiExpanded(!uiExpanded)}
      >
        <Menu size={20} color={theme.colors.text} />
      </TouchableOpacity>
      
      {uiExpanded && (
        <View style={styles.expandedTools}>
          <TouchableOpacity
            style={styles.toolButton}
            onPress={() => {
              setShowLayersPanel(true);
              setUiExpanded(false);
            }}
          >
            <Layers size={20} color={theme.colors.text} />
          </TouchableOpacity>
          
          {enableDrawing && (
            <TouchableOpacity
              style={styles.toolButton}
              onPress={() => {
                setShowDrawingTools(!showDrawingTools);
                setUiExpanded(false);
              }}
            >
              <Edit3 size={20} color={theme.colors.text} />
            </TouchableOpacity>
          )}
          
          {showAnalysisTools && (
            <TouchableOpacity
              style={styles.toolButton}
              onPress={() => {
                setShowAnalysisPanel(true);
                setUiExpanded(false);
              }}
            >
              <BarChart3 size={20} color={theme.colors.text} />
            </TouchableOpacity>
          )}
          
          <TouchableOpacity
            style={styles.toolButton}
            onPress={() => {
              setShowSearch(!showSearch);
              setUiExpanded(false);
            }}
          >
            <Search size={20} color={theme.colors.text} />
          </TouchableOpacity>
        </View>
      )}
    </View>
  );

  const renderMapLayers = () => (
    <Modal
      visible={showLayersPanel}
      transparent
      animationType="slide"
      onRequestClose={() => setShowLayersPanel(false)}
    >
      <TouchableOpacity 
        style={styles.modalOverlay}
        activeOpacity={1}
        onPress={() => setShowLayersPanel(false)}
      >
        <View style={[styles.layersPanel, { backgroundColor: theme.colors.card }]}>
          <View style={styles.panelHeader}>
            <Text style={[styles.panelTitle, { color: theme.colors.text }]}>
              Map Layers
            </Text>
            <TouchableOpacity onPress={() => setShowLayersPanel(false)}>
              <X size={24} color={theme.colors.text} />
            </TouchableOpacity>
          </View>
          
          <TouchableOpacity
            style={[styles.layerOption, mapType === 'standard' && styles.layerOptionActive]}
            onPress={() => {
              setMapType('standard');
              setShowLayersPanel(false);
            }}
          >
            <MapIcon size={24} color={mapType === 'standard' ? theme.colors.primary : theme.colors.text} />
            <Text style={[styles.layerText, { color: theme.colors.text }]}>Standard</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.layerOption, mapType === 'satellite' && styles.layerOptionActive]}
            onPress={() => {
              setMapType('satellite');
              setShowLayersPanel(false);
            }}
          >
            <Satellite size={24} color={mapType === 'satellite' ? theme.colors.primary : theme.colors.text} />
            <Text style={[styles.layerText, { color: theme.colors.text }]}>Satellite</Text>
          </TouchableOpacity>
          
          <View style={styles.panelDivider} />
          
          <Text style={[styles.panelSubtitle, { color: theme.colors.text }]}>
            Data Layers
          </Text>
          
          <TouchableOpacity
            style={[styles.layerOption, showHeatmap && styles.layerOptionActive]}
            onPress={() => {
              setShowHeatmap(!showHeatmap);
              setShowLayersPanel(false);
            }}
          >
            <Thermometer size={24} color={showHeatmap ? theme.colors.primary : theme.colors.text} />
            <Text style={[styles.layerText, { color: theme.colors.text }]}>Heatmap</Text>
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    </Modal>
  );

  const renderDrawingTools = () => (
    <View style={[styles.drawingToolbar, { backgroundColor: theme.colors.card }]}>
      <View style={styles.drawingHeader}>
        <Text style={[styles.drawingTitle, { color: theme.colors.text }]}>Drawing Tools</Text>
        <TouchableOpacity
          onPress={() => setShowDrawingTools(false)}
        >
          <X size={20} color={theme.colors.text} />
        </TouchableOpacity>
      </View>
      
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        <TouchableOpacity
          style={[
            styles.drawingTool,
            drawingMode === 'point' && { backgroundColor: theme.colors.primary }
          ]}
          onPress={() => setDrawingMode(drawingMode === 'point' ? 'none' : 'point')}
        >
          <MapPin size={18} color={drawingMode === 'point' ? 'white' : theme.colors.text} />
          <Text style={[styles.toolText, { color: drawingMode === 'point' ? 'white' : theme.colors.text }]}>
            Point
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.drawingTool,
            drawingMode === 'line' && { backgroundColor: theme.colors.primary }
          ]}
          onPress={() => setDrawingMode(drawingMode === 'line' ? 'none' : 'line')}
        >
          <Route size={18} color={drawingMode === 'line' ? 'white' : theme.colors.text} />
          <Text style={[styles.toolText, { color: drawingMode === 'line' ? 'white' : theme.colors.text }]}>
            Line
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.drawingTool,
            drawingMode === 'polygon' && { backgroundColor: theme.colors.primary }
          ]}
          onPress={() => setDrawingMode(drawingMode === 'polygon' ? 'none' : 'polygon')}
        >
          <Pentagon size={18} color={drawingMode === 'polygon' ? 'white' : theme.colors.text} />
          <Text style={[styles.toolText, { color: drawingMode === 'polygon' ? 'white' : theme.colors.text }]}>
            Polygon
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.drawingTool,
            measurementMode && { backgroundColor: theme.colors.primary }
          ]}
          onPress={() => setMeasurementMode(!measurementMode)}
        >
          <Ruler size={18} color={measurementMode ? 'white' : theme.colors.text} />
          <Text style={[styles.toolText, { color: measurementMode ? 'white' : theme.colors.text }]}>
            Measure
          </Text>
        </TouchableOpacity>
      </ScrollView>
    </View>
  );

  const renderRightControls = () => (
    <View style={styles.rightControlsContainer}>
      {/* Zoom Controls */}
      <View style={[styles.zoomControls, { backgroundColor: theme.colors.card }]}>
        <TouchableOpacity
          style={styles.zoomButton}
          onPress={() => Alert.alert('Zoom In', 'Zoom in functionality')}
        >
          <Plus size={18} color={theme.colors.text} />
        </TouchableOpacity>
        
        <View style={[styles.zoomDivider, { backgroundColor: theme.colors.border }]} />
        
        <TouchableOpacity
          style={styles.zoomButton}
          onPress={() => Alert.alert('Zoom Out', 'Zoom out functionality')}
        >
          <Minus size={18} color={theme.colors.text} />
        </TouchableOpacity>
      </View>
      
      {/* Location Button */}
      <TouchableOpacity
        style={[styles.locationButton, { backgroundColor: theme.colors.card }]}
        onPress={() => {
          if (userLocation) {
            Alert.alert('Location', `Current: ${userLocation.coords.latitude.toFixed(4)}, ${userLocation.coords.longitude.toFixed(4)}`);
          } else {
            requestLocationPermission();
          }
        }}
      >
        <Target size={20} color={theme.colors.primary} />
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Platform-specific Map Component */}
      <PlatformMap
        initialRegion={region}
        mapType={mapType}
        drawingMode={drawingMode}
        measurementMode={measurementMode}
        showHeatmap={showHeatmap}
        userLocation={userLocation}
        geoFeatures={geoFeatures}
        onLocationSelect={onLocationSelect}
      />
      
      {/* Compact Main Toolbar */}
      {renderCompactToolbar()}
      
      {/* Right Side Controls */}
      {renderRightControls()}
      
      {/* Search Bar - Only show when search is active */}
      {showSearch && (
        <View style={[styles.searchBar, { backgroundColor: theme.colors.card }]}>
          <Search size={18} color={theme.colors.muted} />
          <TextInput
            style={[styles.searchInput, { color: theme.colors.text }]}
            placeholder="Search location..."
            placeholderTextColor={theme.colors.placeholder}
            value={searchQuery}
            onChangeText={setSearchQuery}
            onSubmitEditing={() => {
              Alert.alert('Search', `Searching for: ${searchQuery}`);
              setShowSearch(false);
            }}
          />
          <TouchableOpacity onPress={() => setShowSearch(false)}>
            <X size={18} color={theme.colors.muted} />
          </TouchableOpacity>
        </View>
      )}
      
      {/* Drawing Tools - Slide up from bottom */}
      {showDrawingTools && renderDrawingTools()}
      
      {/* Offline Status - Only show when offline */}
      {offlineMode && (
        <View style={[styles.offlineStatus, { backgroundColor: theme.colors.info + '20' }]}>
          <Text style={[styles.offlineText, { color: theme.colors.info }]}>
            📱 Offline Mode
          </Text>
        </View>
      )}
      
      {/* Modals */}
      {renderMapLayers()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  compactToolbar: {
    position: 'absolute',
    top: 50,
    left: 16,
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 25,
    paddingHorizontal: 8,
    paddingVertical: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
    zIndex: 1000,
    maxWidth: SCREEN_WIDTH - 32,
  },
  expandButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  expandedTools: {
    flexDirection: 'row',
    marginLeft: 8,
    gap: 4,
  },
  toolButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  rightControlsContainer: {
    position: 'absolute',
    right: 16,
    top: '50%',
    transform: [{ translateY: -60 }],
    alignItems: 'center',
    gap: 16,
    zIndex: 998,
  },
  zoomControls: {
    borderRadius: 25,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  zoomButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  zoomDivider: {
    height: 1,
    marginHorizontal: 8,
  },
  locationButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  searchBar: {
    position: 'absolute',
    top: 110,
    left: 16,
    right: 16,
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 25,
    paddingHorizontal: 16,
    paddingVertical: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
    gap: 12,
    zIndex: 999,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
  },
  drawingToolbar: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 16,
    paddingBottom: 32,
    paddingHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
    zIndex: 996,
  },
  drawingHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  drawingTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
  },
  drawingTool: {
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    marginHorizontal: 4,
    minWidth: 80,
    gap: 4,
  },
  toolText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },
  offlineStatus: {
    position: 'absolute',
    top: 110,
    right: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
    zIndex: 995,
  },
  offlineText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  layersPanel: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 20,
    paddingHorizontal: 16,
    paddingBottom: 32,
    maxHeight: '70%',
  },
  panelHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  panelTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
  },
  panelSubtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    marginVertical: 12,
  },
  layerOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 8,
    gap: 12,
  },
  layerOptionActive: {
    backgroundColor: 'rgba(0, 122, 255, 0.1)',
  },
  layerText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
  },
  panelDivider: {
    height: 1,
    backgroundColor: '#E0E0E0',
    marginVertical: 16,
  },
});
