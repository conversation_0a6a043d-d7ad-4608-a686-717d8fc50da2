import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Platform,
  Modal,
} from 'react-native';
import { useTheme } from '@/hooks/useTheme';
import { Calendar, Clock, CalendarDays } from 'lucide-react-native';

interface EnhancedDatePickerProps {
  value?: string;
  onChange: (value: string) => void;
  mode: 'date' | 'time' | 'datetime';
  placeholder?: string;
  required?: boolean;
  label?: string;
}

export default function EnhancedDatePicker({
  value,
  onChange,
  mode,
  placeholder,
  required,
  label,
}: EnhancedDatePickerProps) {
  const { theme } = useTheme();
  const [showPicker, setShowPicker] = useState(false);

  const formatValue = (dateValue: string) => {
    if (!dateValue) return '';
    
    try {
      const date = new Date(dateValue);
      
      switch (mode) {
        case 'date':
          return date.toLocaleDateString();
        case 'time':
          return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        case 'datetime':
          return `${date.toLocaleDateString()} ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
        default:
          return dateValue;
      }
    } catch {
      return dateValue;
    }
  };

  const getIcon = () => {
    switch (mode) {
      case 'date':
        return <Calendar size={20} color={theme.colors.primary} />;
      case 'time':
        return <Clock size={20} color={theme.colors.primary} />;
      case 'datetime':
        return <CalendarDays size={20} color={theme.colors.primary} />;
      default:
        return <Calendar size={20} color={theme.colors.primary} />;
    }
  };

  const handlePress = () => {
    if (Platform.OS === 'web') {
      handleWebDatePicker();
    } else {
      setShowPicker(true);
    }
  };

  const handleWebDatePicker = () => {
    const input = document.createElement('input');
    input.type = mode === 'time' ? 'time' : mode === 'datetime' ? 'datetime-local' : 'date';
    
    if (value) {
      if (mode === 'time' && value.includes('T')) {
        // Extract time part from datetime
        input.value = value.split('T')[1]?.substring(0, 5) || '';
      } else if (mode === 'date' && value.includes('T')) {
        // Extract date part from datetime
        input.value = value.split('T')[0] || '';
      } else {
        input.value = value;
      }
    }

    input.onchange = (e) => {
      const target = e.target as HTMLInputElement;
      let newValue = target.value;
      
      if (mode === 'time') {
        // Create a datetime string with today's date and the selected time
        const today = new Date().toISOString().split('T')[0];
        newValue = `${today}T${newValue}:00.000Z`;
      } else if (mode === 'date') {
        // Create a datetime string with the selected date at midnight
        newValue = `${newValue}T00:00:00.000Z`;
      } else if (mode === 'datetime') {
        // Add seconds and timezone if not present
        if (!newValue.includes(':00.')) {
          newValue = `${newValue}:00.000Z`;
        }
      }
      
      onChange(newValue);
    };

    input.click();
  };

  const handleNativeDatePicker = (selectedDate: Date) => {
    setShowPicker(false);
    
    if (selectedDate) {
      let newValue: string;
      
      if (mode === 'time') {
        // Keep today's date and update time
        const today = new Date();
        today.setHours(selectedDate.getHours());
        today.setMinutes(selectedDate.getMinutes());
        today.setSeconds(0);
        today.setMilliseconds(0);
        newValue = today.toISOString();
      } else {
        newValue = selectedDate.toISOString();
      }
      
      onChange(newValue);
    }
  };

  const getCurrentDate = () => {
    if (value) {
      return new Date(value);
    }
    return new Date();
  };

  const renderNativePicker = () => {
    if (Platform.OS === 'web' || !showPicker) return null;

    // For React Native, we would use a proper date/time picker library
    // For now, we'll use a simple modal with date selection
    return (
      <Modal
        visible={showPicker}
        transparent
        animationType="slide"
        onRequestClose={() => setShowPicker(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: theme.colors.card }]}>
            <Text style={[styles.modalTitle, { color: theme.colors.text }]}>
              Select {mode.charAt(0).toUpperCase() + mode.slice(1)}
            </Text>
            
            {/* In a real implementation, you would use a proper date/time picker component here */}
            <View style={styles.pickerPlaceholder}>
              <Text style={[styles.pickerPlaceholderText, { color: theme.colors.muted }]}>
                Native date/time picker would appear here
              </Text>
              <Text style={[styles.pickerPlaceholderText, { color: theme.colors.muted }]}>
                Consider using @react-native-community/datetimepicker
              </Text>
            </View>
            
            <View style={styles.modalActions}>
              <TouchableOpacity
                style={[styles.modalButton, { backgroundColor: theme.colors.background }]}
                onPress={() => setShowPicker(false)}
              >
                <Text style={[styles.modalButtonText, { color: theme.colors.text }]}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.modalButton, { backgroundColor: theme.colors.primary }]}
                onPress={() => handleNativeDatePicker(getCurrentDate())}
              >
                <Text style={[styles.modalButtonText, { color: 'white' }]}>Confirm</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    );
  };

  return (
    <View style={styles.container}>
      {label && (
        <Text style={[styles.label, { color: theme.colors.text }]}>
          {label}
          {required && <Text style={{ color: theme.colors.error }}> *</Text>}
        </Text>
      )}
      
      <TouchableOpacity
        style={[
          styles.pickerButton,
          {
            backgroundColor: theme.colors.background,
            borderColor: theme.colors.border,
          },
        ]}
        onPress={handlePress}
      >
        {getIcon()}
        <Text
          style={[
            styles.pickerText,
            {
              color: value ? theme.colors.text : theme.colors.placeholder,
            },
          ]}
        >
          {value ? formatValue(value) : placeholder || `Select ${mode}`}
        </Text>
      </TouchableOpacity>
      
      {renderNativePicker()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
  },
  label: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    marginBottom: 6,
  },
  pickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 14,
    borderRadius: 8,
    borderWidth: 1,
    gap: 10,
  },
  pickerText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    flex: 1,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '85%',
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 8,
  },
  modalTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    textAlign: 'center',
    marginBottom: 20,
  },
  pickerPlaceholder: {
    paddingVertical: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  pickerPlaceholderText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
    marginBottom: 4,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
    gap: 12,
  },
  modalButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  modalButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
  },
});
