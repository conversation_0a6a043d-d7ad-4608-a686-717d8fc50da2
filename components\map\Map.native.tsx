import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Text,
  Alert,
  Modal,
  TextInput,
  ScrollView,
} from 'react-native';
import MapView, { 
  PROVIDER_GOOGLE, 
  Marker, 
  Polyline, 
  Polygon,
  Region,
  LatLng,
  MapPressEvent,
  MapType,
} from 'react-native-maps';
import * as Location from 'expo-location';
import { useTheme } from '@/hooks/useTheme';
import { useGeoFeatures } from '@/hooks/useGeoFeatures';
import { GeoFeature } from '@/types';
import {
  MapPin,
  Minus,
  Square,
  Navigation,
  Layers,
  Settings,
  Save,
  Trash2,
  Edit3,
  Plus,
  Target,
  Ruler,
  X,
} from 'lucide-react-native';

type DrawingMode = 'none' | 'point' | 'line' | 'polygon' | 'circle' | 'rectangle' | 'freehand';
type MapLayerType = 'standard' | 'satellite' | 'terrain' | 'hybrid';

interface MapProps {
  initialRegion?: Region;
  mapType?: MapLayerType;
  drawingMode?: DrawingMode;
  measurementMode?: boolean;
  showHeatmap?: boolean;
  showClustering?: boolean;
  userLocation?: Location.LocationObject | null;
  geoFeatures?: any[];
  onLocationSelect?: (location: { latitude: number; longitude: number }) => void;
}

interface MapFeatureEditorProps {
  visible: boolean;
  feature: GeoFeature | null;
  onSave: (feature: GeoFeature) => void;
  onCancel: () => void;
}

function MapFeatureEditor({ visible, feature, onSave, onCancel }: MapFeatureEditorProps) {
  const { theme } = useTheme();
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [category, setCategory] = useState('');

  useEffect(() => {
    if (feature) {
      setName(feature.properties.name || '');
      setDescription(feature.properties.description || '');
      setCategory(feature.properties.category || '');
    } else {
      setName('');
      setDescription('');
      setCategory('');
    }
  }, [feature]);

  const handleSave = () => {
    if (!feature) return;

    const updatedFeature: GeoFeature = {
      ...feature,
      properties: {
        ...feature.properties,
        name,
        description,
        category,
      },
    };

    onSave(updatedFeature);
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onCancel}
    >
      <View style={[styles.modal, { backgroundColor: theme.colors.background }]}>
        <View style={[styles.modalHeader, { borderBottomColor: theme.colors.border }]}>
          <TouchableOpacity onPress={onCancel}>
            <X size={24} color={theme.colors.text} />
          </TouchableOpacity>
          <Text style={[styles.modalTitle, { color: theme.colors.text }]}>
            Edit Feature
          </Text>
          <TouchableOpacity onPress={handleSave}>
            <Text style={[styles.saveText, { color: theme.colors.primary }]}>Save</Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.modalContent}>
          <View style={styles.inputGroup}>
            <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Name</Text>
            <TextInput
              style={[styles.input, { backgroundColor: theme.colors.card, color: theme.colors.text, borderColor: theme.colors.border }]}
              value={name}
              onChangeText={setName}
              placeholder="Enter feature name"
              placeholderTextColor={theme.colors.placeholder}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Description</Text>
            <TextInput
              style={[styles.input, styles.textArea, { backgroundColor: theme.colors.card, color: theme.colors.text, borderColor: theme.colors.border }]}
              value={description}
              onChangeText={setDescription}
              placeholder="Enter description"
              placeholderTextColor={theme.colors.placeholder}
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Category</Text>
            <TextInput
              style={[styles.input, { backgroundColor: theme.colors.card, color: theme.colors.text, borderColor: theme.colors.border }]}
              value={category}
              onChangeText={setCategory}
              placeholder="Enter category"
              placeholderTextColor={theme.colors.placeholder}
            />
          </View>

          {feature && (
            <View style={styles.featureInfo}>
              <Text style={[styles.infoTitle, { color: theme.colors.text }]}>Feature Information</Text>
              <Text style={[styles.infoText, { color: theme.colors.muted }]}>
                Type: {feature.type.toUpperCase()}
              </Text>
              <Text style={[styles.infoText, { color: theme.colors.muted }]}>
                Created: {new Date(feature.createdAt).toLocaleDateString()}
              </Text>
              {feature.type === 'line' && Array.isArray(feature.geometry.coordinates) && feature.geometry.coordinates.length > 1 && (
                <Text style={[styles.infoText, { color: theme.colors.muted }]}>
                  Points: {feature.geometry.coordinates.length}
                </Text>
              )}
              {feature.type === 'polygon' && Array.isArray(feature.geometry.coordinates) && 
               Array.isArray(feature.geometry.coordinates[0]) && feature.geometry.coordinates[0].length > 2 && (
                <Text style={[styles.infoText, { color: theme.colors.muted }]}>
                  Vertices: {feature.geometry.coordinates[0].length - 1}
                </Text>
              )}
            </View>
          )}
        </ScrollView>
      </View>
    </Modal>
  );
}

export default function Map({
  initialRegion,
  mapType = 'standard',
  drawingMode = 'none',
  measurementMode = false,
  showHeatmap = false,
  showClustering = false,
  userLocation,
  geoFeatures = [],
  onLocationSelect,
}: MapProps) {
  const { theme } = useTheme();
  const { geoFeatures: localGeoFeatures, createFeature, updateFeature, deleteFeature } = useGeoFeatures();
  const mapRef = useRef<MapView>(null);
  
  const [region, setRegion] = useState<Region>(initialRegion || {
    latitude: 37.78825,
    longitude: -122.4324,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  });
  
  const [currentPath, setCurrentPath] = useState<LatLng[]>([]);
  const [selectedFeature, setSelectedFeature] = useState<GeoFeature | null>(null);
  const [showFeatureEditor, setShowFeatureEditor] = useState(false);
  
  useEffect(() => {
    if (initialRegion) {
      setRegion(initialRegion);
    }
  }, [initialRegion]);

  // Convert mapType string to MapView MapType
  const getMapType = (): MapType => {
    switch (mapType) {
      case 'satellite':
        return 'satellite';
      case 'terrain':
        return 'terrain';
      case 'hybrid':
        return 'hybrid';
      default:
        return 'standard';
    }
  };

  const handleMapPress = (event: MapPressEvent) => {
    const coordinate = event.nativeEvent.coordinate;
    
    if (onLocationSelect) {
      onLocationSelect(coordinate);
    }
  };

  const renderGeoFeatures = () => {
    const allFeatures = [...localGeoFeatures, ...geoFeatures];
    
    return allFeatures.map((feature, index) => {
      // Handle different feature formats
      if (feature.geometry) {
        // GeoJSON format
        if (feature.type === 'point' && Array.isArray(feature.geometry.coordinates) && feature.geometry.coordinates.length === 2) {
          const [lng, lat] = feature.geometry.coordinates as [number, number];
          return (
            <Marker
              key={feature.id || `feature-${index}`}
              coordinate={{ latitude: lat, longitude: lng }}
              title={feature.properties?.name || `Point ${index + 1}`}
              description={feature.properties?.description || ''}
              onPress={() => setSelectedFeature(feature)}
            />
          );
        } else if (feature.type === 'line' && Array.isArray(feature.geometry.coordinates) && feature.geometry.coordinates.length > 0) {
          const coordinates = (feature.geometry.coordinates as [number, number][]).map(
            ([lng, lat]) => ({ latitude: lat, longitude: lng })
          );
          return (
            <Polyline
              key={feature.id || `feature-${index}`}
              coordinates={coordinates}
              strokeColor={feature.properties?.color || theme.colors.primary}
              strokeWidth={3}
              onPress={() => setSelectedFeature(feature)}
            />
          );
        } else if (feature.type === 'polygon' && Array.isArray(feature.geometry.coordinates) && 
                   Array.isArray(feature.geometry.coordinates[0]) && feature.geometry.coordinates[0].length > 0) {
          const coordinates = (feature.geometry.coordinates[0] as [number, number][]).map(
            ([lng, lat]) => ({ latitude: lat, longitude: lng })
          );
          return (
            <Polygon
              key={feature.id || `feature-${index}`}
              coordinates={coordinates}
              fillColor={(feature.properties?.color || theme.colors.primary) + '40'}
              strokeColor={feature.properties?.color || theme.colors.primary}
              strokeWidth={2}
              onPress={() => setSelectedFeature(feature)}
            />
          );
        }
      } else if (feature.coordinates) {
        // Simple coordinate format
        if (feature.type === 'point') {
          return (
            <Marker
              key={feature.id || `feature-${index}`}
              coordinate={feature.coordinates}
              title={feature.properties?.name || `Point ${index + 1}`}
              description={feature.properties?.description || ''}
            />
          );
        } else if (feature.type === 'line' && Array.isArray(feature.coordinates)) {
          return (
            <Polyline
              key={feature.id || `feature-${index}`}
              coordinates={feature.coordinates}
              strokeColor={feature.properties?.color || theme.colors.primary}
              strokeWidth={3}
            />
          );
        } else if (feature.type === 'polygon' && Array.isArray(feature.coordinates)) {
          return (
            <Polygon
              key={feature.id || `feature-${index}`}
              coordinates={feature.coordinates}
              fillColor={(feature.properties?.color || theme.colors.primary) + '40'}
              strokeColor={feature.properties?.color || theme.colors.primary}
              strokeWidth={2}
            />
          );
        }
      }
      
      return null;
    });
  };

  return (
    <View style={styles.container}>
      <MapView
        ref={mapRef}
        style={styles.map}
        provider={PROVIDER_GOOGLE}
        showsUserLocation={!!userLocation}
        showsMyLocationButton={false}
        region={region}
        mapType={getMapType()}
        onRegionChangeComplete={setRegion}
        onPress={handleMapPress}
      >
        {/* User Location Marker */}
        {userLocation && (
          <Marker
            coordinate={{
              latitude: userLocation.coords.latitude,
              longitude: userLocation.coords.longitude,
            }}
            title="Your Location"
          >
            <View style={styles.userLocationMarker}>
              <View style={[styles.userLocationDot, { backgroundColor: theme.colors.primary }]} />
            </View>
          </Marker>
        )}

        {/* Render features */}
        {renderGeoFeatures()}
      </MapView>

      <MapFeatureEditor
        visible={showFeatureEditor}
        feature={selectedFeature}
        onSave={async (feature) => {
          try {
            await updateFeature(feature.id, feature);
            setSelectedFeature(feature);
            setShowFeatureEditor(false);
          } catch (error) {
            Alert.alert('Error', 'Failed to update feature');
          }
        }}
        onCancel={() => setShowFeatureEditor(false)}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  map: {
    flex: 1,
  },
  userLocationMarker: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(0, 122, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  userLocationDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  modal: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
  },
  saveText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 6,
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  featureInfo: {
    marginTop: 24,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  infoTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    marginBottom: 4,
  },
});
