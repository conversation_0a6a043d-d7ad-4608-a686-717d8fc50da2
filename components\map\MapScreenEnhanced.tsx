import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Alert, TouchableOpacity } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import EnhancedMapIntegrationUpdated from './EnhancedMapIntegrationUpdated';
import MapErrorBoundary from './MapErrorBoundary';
import { useTheme } from '@/hooks/useTheme';
import { Settings, Info, Download, Upload } from 'lucide-react-native';
import * as Location from 'expo-location';

interface MapScreenEnhancedProps {
  projectId?: string;
  title?: string;
  showHeader?: boolean;
  enableOfflineStorage?: boolean;
  maxFeatures?: number;
  onFeatureCreated?: (feature: any) => void;
  onFeatureDeleted?: (featureId: string) => void;
}

export default function MapScreenEnhanced({
  projectId = 'default-project',
  title = 'Enhanced Field Map',
  showHeader = true,
  enableOfflineStorage = true,
  maxFeatures = 100,
  onFeatureCreated,
  onFeatureDeleted,
}: MapScreenEnhancedProps) {
  const { theme } = useTheme();
  const [currentLocation, setCurrentLocation] = useState<any>(null);
  const [featureCount, setFeatureCount] = useState(0);
  const [locationPermission, setLocationPermission] = useState<string>('undetermined');
  const [analysisResults, setAnalysisResults] = useState<any[]>([]);
  const [measurementResults, setMeasurementResults] = useState<any[]>([]);

  useEffect(() => {
    checkLocationPermission();
  }, []);

  const checkLocationPermission = async () => {
    try {
      const { status } = await Location.getForegroundPermissionsAsync();
      setLocationPermission(status);
      
      if (status === 'granted') {
        const location = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.High,
        });
        setCurrentLocation({
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        });
      }
    } catch (error) {
      console.error('Error checking location permission:', error);
    }
  };

  const requestLocationPermission = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      setLocationPermission(status);
      
      if (status === 'granted') {
        const location = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.High,
        });
        setCurrentLocation({
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        });
      } else {
        Alert.alert(
          'Location Permission',
          'Location access is required for full map functionality. You can still use the map but some features may be limited.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to request location permission.');
    }
  };

  const handleFeatureCreated = (feature: any) => {
    setFeatureCount(prev => prev + 1);
    
    // Enhanced feature tracking with analytics
    console.log('Enhanced Feature Created:', {
      id: feature.id,
      type: feature.type,
      timestamp: new Date().toISOString(),
      hasGeometry: !!feature.coordinates,
      hasMeasurements: !!feature.properties?.measurements,
    });
    
    if (onFeatureCreated) {
      onFeatureCreated(feature);
    }
  };

  const handleFeatureDeleted = (featureId: string) => {
    setFeatureCount(prev => Math.max(0, prev - 1));
    
    console.log('Enhanced Feature Deleted:', {
      id: featureId,
      timestamp: new Date().toISOString(),
    });
    
    if (onFeatureDeleted) {
      onFeatureDeleted(featureId);
    }
  };

  const handleMapError = (error: Error, errorInfo: any) => {
    console.error('Enhanced Map Error:', error);
    console.error('Error Info:', errorInfo);
    
    // Enhanced error reporting
    const errorReport = {
      error: error.message,
      stack: error.stack,
      errorInfo,
      timestamp: new Date().toISOString(),
      projectId,
      featureCount,
      locationEnabled: locationPermission === 'granted',
    };
    
    console.error('Enhanced Error Report:', errorReport);
    
    // In production, you might want to report this to an error tracking service
    // Example: Sentry.captureException(error, { contexts: { errorInfo, mapState: errorReport } });
  };

  const showMapInfo = () => {
    const stats = {
      projectId,
      features: featureCount,
      location: locationPermission === 'granted' ? 'Enabled' : 'Disabled',
      offlineStorage: enableOfflineStorage ? 'Enabled' : 'Disabled',
      analysisResults: analysisResults.length,
      measurements: measurementResults.length,
    };

    Alert.alert(
      'Enhanced Map Information',
      `Project: ${stats.projectId}\nFeatures: ${stats.features}\nLocation: ${stats.location}\nOffline Storage: ${stats.offlineStorage}\nAnalysis Results: ${stats.analysisResults}\nMeasurements: ${stats.measurements}`,
      [{ text: 'OK' }]
    );
  };

  const exportMapData = () => {
    Alert.alert(
      'Export Map Data',
      'Choose export format:',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'GeoJSON',
          onPress: () => {
            // This would be handled by the enhanced map component
            Alert.alert('Export', 'GeoJSON export initiated');
          },
        },
        {
          text: 'Shapefile',
          onPress: () => {
            // This would be handled by the enhanced map component
            Alert.alert('Export', 'Shapefile export initiated');
          },
        },
      ]
    );
  };

  const importMapData = () => {
    Alert.alert(
      'Import Map Data',
      'Import map layers from files (GeoJSON, Shapefile, KML, GPX)',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Select File',
          onPress: () => {
            // This would be handled by the enhanced map component
            Alert.alert('Import', 'File selection initiated');
          },
        },
      ]
    );
  };

  const renderHeader = () => {
    if (!showHeader) return null;

    return (
      <View style={[styles.header, { backgroundColor: theme.colors.card, borderBottomColor: theme.colors.border }]}>
        <View style={styles.headerContent}>
          <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
            {title}
          </Text>
          <Text style={[styles.headerSubtitle, { color: theme.colors.muted }]}>
            {featureCount} features • {locationPermission === 'granted' ? 'GPS enabled' : 'GPS disabled'}
            {analysisResults.length > 0 && ` • ${analysisResults.length} analysis results`}
            {measurementResults.length > 0 && ` • ${measurementResults.length} measurements`}
          </Text>
        </View>
        
        <View style={styles.headerActions}>
          {/* Export Data */}
          <TouchableOpacity
            style={[styles.headerButton, { backgroundColor: theme.colors.secondary }]}
            onPress={exportMapData}
          >
            <Download size={20} color="white" />
          </TouchableOpacity>

          {/* Import Data */}
          <TouchableOpacity
            style={[styles.headerButton, { backgroundColor: theme.colors.info }]}
            onPress={importMapData}
          >
            <Upload size={20} color="white" />
          </TouchableOpacity>
          
          {/* Map Info */}
          <TouchableOpacity
            style={[styles.headerButton, { backgroundColor: theme.colors.background }]}
            onPress={showMapInfo}
          >
            <Info size={20} color={theme.colors.text} />
          </TouchableOpacity>
          
          {/* Location Permission */}
          {locationPermission !== 'granted' && (
            <TouchableOpacity
              style={[styles.headerButton, { backgroundColor: theme.colors.warning }]}
              onPress={requestLocationPermission}
            >
              <Settings size={20} color="white" />
            </TouchableOpacity>
          )}
        </View>
      </View>
    );
  };

  const renderEnhancedStats = () => {
    if (!showHeader || (analysisResults.length === 0 && measurementResults.length === 0)) return null;

    return (
      <View style={[styles.statsBar, { backgroundColor: theme.colors.background, borderBottomColor: theme.colors.border }]}>
        {analysisResults.length > 0 && (
          <View style={styles.statItem}>
            <Text style={[styles.statLabel, { color: theme.colors.muted }]}>Analysis</Text>
            <Text style={[styles.statValue, { color: theme.colors.primary }]}>{analysisResults.length}</Text>
          </View>
        )}
        
        {measurementResults.length > 0 && (
          <View style={styles.statItem}>
            <Text style={[styles.statLabel, { color: theme.colors.muted }]}>Measurements</Text>
            <Text style={[styles.statValue, { color: theme.colors.secondary }]}>{measurementResults.length}</Text>
          </View>
        )}

        <View style={styles.statItem}>
          <Text style={[styles.statLabel, { color: theme.colors.muted }]}>Features</Text>
          <Text style={[styles.statValue, { color: theme.colors.text }]}>{featureCount}/{maxFeatures}</Text>
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {renderHeader()}
      {renderEnhancedStats()}
      
      <MapErrorBoundary onError={handleMapError} resetOnPropsChange={true}>
        <EnhancedMapIntegrationUpdated
          projectId={projectId}
          initialRegion={currentLocation}
          onFeatureCreated={handleFeatureCreated}
          onFeatureDeleted={handleFeatureDeleted}
          enableOfflineStorage={enableOfflineStorage}
          maxFeatures={maxFeatures}
          enableDrawing={true}
          enableMeasurement={true}
          enableAnalysis={true}
        />
      </MapErrorBoundary>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  headerContent: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
  },
  headerSubtitle: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    marginTop: 2,
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8,
  },
  headerButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  statsBar: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderBottomWidth: 1,
  },
  statItem: {
    alignItems: 'center',
    marginRight: 20,
  },
  statLabel: {
    fontSize: 10,
    fontFamily: 'Inter-Regular',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  statValue: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    marginTop: 2,
  },
});
