import { LatLng } from 'react-native-maps';

export interface MeasurementResult {
  distance?: number; // in meters
  area?: number; // in square meters
  perimeter?: number; // in meters
  formattedDistance?: string;
  formattedArea?: string;
  formattedPerimeter?: string;
}

export interface BufferOptions {
  distance: number; // in meters
  units?: 'kilometers' | 'meters' | 'miles';
  steps?: number;
}

export interface SpatialAnalysisResult {
  id: string;
  type: string;
  feature: string;
  results: Record<string, any>;
  timestamp: string;
  coordinates?: LatLng[];
  metadata?: Record<string, any>;
}

// Advanced spatial analysis functions
export const calculateDistance = (from: LatLng, to: LatLng): number => {
  const R = 6371e3; // Earth's radius in meters
  const φ1 = from.latitude * Math.PI / 180;
  const φ2 = to.latitude * Math.PI / 180;
  const Δφ = (to.latitude - from.latitude) * Math.PI / 180;
  const Δλ = (to.longitude - from.longitude) * Math.PI / 180;

  const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
          Math.cos(φ1) * Math.cos(φ2) *
          Math.sin(Δλ/2) * Math.sin(Δλ/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

  return R * c;
};

export const calculateArea = (points: LatLng[]): number => {
  if (points.length < 3) return 0;
  
  let area = 0;
  const n = points.length;
  
  for (let i = 0; i < n; i++) {
    const j = (i + 1) % n;
    area += points[i].longitude * points[j].latitude;
    area -= points[j].longitude * points[i].latitude;
  }
  
  // Convert to square meters using more accurate calculation
  const earthRadius = 6371000;
  const latFactor = Math.PI / 180;
  const avgLat = points.reduce((sum, p) => sum + p.latitude, 0) / n;
  const metersPerDegreeLat = earthRadius * latFactor;
  const metersPerDegreeLng = earthRadius * latFactor * Math.cos(avgLat * latFactor);
  
  return Math.abs(area) * metersPerDegreeLat * metersPerDegreeLng / 2;
};

export const calculatePerimeter = (points: LatLng[]): number => {
  if (points.length < 2) return 0;
  
  let perimeter = 0;
  for (let i = 0; i < points.length - 1; i++) {
    perimeter += calculateDistance(points[i], points[i + 1]);
  }
  
  // If it's a closed polygon, add distance from last to first point
  if (points.length > 2) {
    perimeter += calculateDistance(points[points.length - 1], points[0]);
  }
  
  return perimeter;
};

export const createBuffer = (coordinate: LatLng, distance: number, steps: number = 32): LatLng[] => {
  const points: LatLng[] = [];
  const earthRadius = 6371000;
  
  for (let i = 0; i < steps; i++) {
    const angle = (i * 2 * Math.PI) / steps;
    const lat = coordinate.latitude + (distance / earthRadius) * (180 / Math.PI) * Math.cos(angle);
    const lng = coordinate.longitude + (distance / earthRadius) * (180 / Math.PI) * Math.sin(angle) / Math.cos(coordinate.latitude * Math.PI / 180);
    
    points.push({ latitude: lat, longitude: lng });
  }
  
  return points;
};

export const createPolygonBuffer = (polygon: LatLng[], distance: number): LatLng[] => {
  // Simplified polygon buffer - in production, use a proper computational geometry library
  const center = getPolygonCenter(polygon);
  return createBuffer(center, distance + Math.sqrt(calculateArea(polygon) / Math.PI));
};

export const pointInPolygon = (point: LatLng, polygon: LatLng[]): boolean => {
  let inside = false;
  const x = point.longitude;
  const y = point.latitude;
  
  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
    const xi = polygon[i].longitude;
    const yi = polygon[i].latitude;
    const xj = polygon[j].longitude;
    const yj = polygon[j].latitude;
    
    if (((yi > y) !== (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi)) {
      inside = !inside;
    }
  }
  
  return inside;
};

export const getPolygonCenter = (points: LatLng[]): LatLng => {
  if (points.length === 0) return { latitude: 0, longitude: 0 };
  
  let x = 0, y = 0, z = 0;
  
  points.forEach(point => {
    const lat = point.latitude * Math.PI / 180;
    const lng = point.longitude * Math.PI / 180;
    
    x += Math.cos(lat) * Math.cos(lng);
    y += Math.cos(lat) * Math.sin(lng);
    z += Math.sin(lat);
  });
  
  x /= points.length;
  y /= points.length;
  z /= points.length;
  
  const lng = Math.atan2(y, x) * 180 / Math.PI;
  const hyp = Math.sqrt(x * x + y * y);
  const lat = Math.atan2(z, hyp) * 180 / Math.PI;
  
  return { latitude: lat, longitude: lng };
};

export const getBoundingBox = (points: LatLng[]): {
  minLat: number;
  maxLat: number;
  minLng: number;
  maxLng: number;
  center: LatLng;
  width: number;
  height: number;
} => {
  if (points.length === 0) {
    return { 
      minLat: 0, maxLat: 0, minLng: 0, maxLng: 0, 
      center: { latitude: 0, longitude: 0 },
      width: 0, height: 0
    };
  }
  
  let minLat = points[0].latitude;
  let maxLat = points[0].latitude;
  let minLng = points[0].longitude;
  let maxLng = points[0].longitude;
  
  points.forEach(point => {
    minLat = Math.min(minLat, point.latitude);
    maxLat = Math.max(maxLat, point.latitude);
    minLng = Math.min(minLng, point.longitude);
    maxLng = Math.max(maxLng, point.longitude);
  });
  
  const center = {
    latitude: (minLat + maxLat) / 2,
    longitude: (minLng + maxLng) / 2,
  };
  
  // Calculate width and height in meters
  const width = calculateDistance(
    { latitude: center.latitude, longitude: minLng },
    { latitude: center.latitude, longitude: maxLng }
  );
  const height = calculateDistance(
    { latitude: minLat, longitude: center.longitude },
    { latitude: maxLat, longitude: center.longitude }
  );
  
  return { minLat, maxLat, minLng, maxLng, center, width, height };
};

export const formatDistance = (meters: number): string => {
  if (meters < 1000) {
    return `${Math.round(meters)} m`;
  } else if (meters < 10000) {
    return `${(meters / 1000).toFixed(2)} km`;
  } else {
    return `${Math.round(meters / 1000)} km`;
  }
};

export const formatArea = (squareMeters: number): string => {
  if (squareMeters < 10000) {
    return `${Math.round(squareMeters)} m²`;
  } else {
    const hectares = squareMeters / 10000;
    if (hectares < 100) {
      return `${hectares.toFixed(2)} ha`;
    } else {
      return `${Math.round(hectares)} ha`;
    }
  }
};

export const formatCoordinate = (coord: LatLng, precision: number = 6): string => {
  return `${coord.latitude.toFixed(precision)}, ${coord.longitude.toFixed(precision)}`;
};
