import React, { Component, ReactNode, ErrorInfo } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { AlertTriangle, RefreshCw, Download, Bug, Info } from 'lucide-react-native';
import { mapPerformanceMonitor } from '@/utils/mapPerformance';

interface MapErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string;
  timestamp: string;
  retryCount: number;
}

interface MapErrorBoundaryProps {
  children: ReactNode;
  fallbackComponent?: React.ComponentType<{
    error: Error;
    resetError: () => void;
    retryCount: number;
  }>;
  onError?: (error: Error, errorInfo: ErrorInfo, errorId: string) => void;
  maxRetries?: number;
  enablePerformanceMonitoring?: boolean;
  enableErrorReporting?: boolean;
}

interface ErrorReport {
  errorId: string;
  timestamp: string;
  error: {
    name: string;
    message: string;
    stack?: string;
  };
  errorInfo: {
    componentStack: string;
  };
  userAgent: string;
  url: string;
  performanceMetrics?: any;
  retryCount: number;
}

export class MapErrorBoundary extends Component<MapErrorBoundaryProps, MapErrorBoundaryState> {
  private retryTimeouts: NodeJS.Timeout[] = [];

  constructor(props: MapErrorBoundaryProps) {
    super(props);
    
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
      timestamp: '',
      retryCount: 0,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<MapErrorBoundaryState> {
    const errorId = `map-error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const timestamp = new Date().toISOString();
    
    return {
      hasError: true,
      error,
      errorId,
      timestamp,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    const { onError, enablePerformanceMonitoring = true, enableErrorReporting = true } = this.props;
    
    this.setState({ errorInfo });
    
    // Record error in performance monitor
    if (enablePerformanceMonitoring) {
      mapPerformanceMonitor.recordError('Map Component Error', error.message);
    }
    
    // Call custom error handler
    if (onError) {
      onError(error, errorInfo, this.state.errorId);
    }
    
    // Generate error report
    if (enableErrorReporting) {
      this.generateErrorReport(error, errorInfo);
    }
    
    // Log error details
    console.group(`🚨 Map Error Boundary - ${this.state.errorId}`);
    console.error('Error:', error);
    console.error('Error Info:', errorInfo);
    console.error('Component Stack:', errorInfo.componentStack);
    console.groupEnd();
  }

  componentWillUnmount() {
    // Clear any pending retry timeouts
    this.retryTimeouts.forEach(timeout => clearTimeout(timeout));
  }

  private generateErrorReport = (error: Error, errorInfo: ErrorInfo): ErrorReport => {
    const report: ErrorReport = {
      errorId: this.state.errorId,
      timestamp: this.state.timestamp,
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack,
      },
      errorInfo: {
        componentStack: errorInfo.componentStack,
      },
      userAgent: navigator.userAgent,
      url: window.location.href,
      retryCount: this.state.retryCount,
    };
    
    // Add performance metrics if available
    if (this.props.enablePerformanceMonitoring) {
      try {
        report.performanceMetrics = mapPerformanceMonitor.getCurrentMetrics();
      } catch (perfError) {
        console.warn('Failed to get performance metrics for error report:', perfError);
      }
    }
    
    // Log the report (in production, this would be sent to an error reporting service)
    console.log('📊 Error Report Generated:', report);
    
    return report;
  };

  private handleRetry = () => {
    const { maxRetries = 3 } = this.props;
    const newRetryCount = this.state.retryCount + 1;
    
    if (newRetryCount > maxRetries) {
      console.warn(`🚨 Max retries (${maxRetries}) exceeded for map error`);
      return;
    }
    
    console.log(`🔄 Retrying map component (attempt ${newRetryCount}/${maxRetries})`);
    
    // Reset error state with incremented retry count
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: newRetryCount,
    });
    
    // If this retry also fails, add a delay before the next retry
    const retryDelay = Math.min(1000 * Math.pow(2, newRetryCount - 1), 10000); // Exponential backoff, max 10s
    
    const timeout = setTimeout(() => {
      if (this.state.hasError) {
        console.log(`⏰ Retry timeout expired, enabling retry button again`);
      }
    }, retryDelay);
    
    this.retryTimeouts.push(timeout);
  };

  private handleReset = () => {
    console.log('🔄 Resetting map error boundary');
    
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
      timestamp: '',
      retryCount: 0,
    });
  };

  private handleExportErrorReport = () => {
    if (!this.state.error || !this.state.errorInfo) return;
    
    const report = this.generateErrorReport(this.state.error, this.state.errorInfo);
    const reportJson = JSON.stringify(report, null, 2);
    
    // In a real implementation, this would trigger a download or copy to clipboard
    console.log('📋 Error report exported:', reportJson);
    
    // For React Native, you could use:
    // - Share API to share the report
    // - Clipboard API to copy the report
    // - File system API to save the report
  };

  private renderErrorDetails = () => {
    const { error, errorInfo, errorId, timestamp, retryCount } = this.state;
    
    if (!error || !errorInfo) return null;
    
    return (
      <ScrollView style={styles.errorDetails}>
        <View style={styles.errorSection}>
          <Text style={styles.errorSectionTitle}>Error Details</Text>
          <Text style={styles.errorText}>ID: {errorId}</Text>
          <Text style={styles.errorText}>Time: {new Date(timestamp).toLocaleString()}</Text>
          <Text style={styles.errorText}>Retries: {retryCount}</Text>
          <Text style={styles.errorText}>Type: {error.name}</Text>
          <Text style={styles.errorText}>Message: {error.message}</Text>
        </View>
        
        <View style={styles.errorSection}>
          <Text style={styles.errorSectionTitle}>Component Stack</Text>
          <Text style={styles.stackText}>{errorInfo.componentStack}</Text>
        </View>
        
        {error.stack && (
          <View style={styles.errorSection}>
            <Text style={styles.errorSectionTitle}>Error Stack</Text>
            <Text style={styles.stackText}>{error.stack}</Text>
          </View>
        )}
      </ScrollView>
    );
  };

  private renderDefaultErrorFallback = () => {
    const { error, retryCount } = this.state;
    const { maxRetries = 3 } = this.props;
    const canRetry = retryCount < maxRetries;
    
    return (
      <View style={styles.errorContainer}>
        <View style={styles.errorHeader}>
          <AlertTriangle size={48} color="#EF4444" />
          <Text style={styles.errorTitle}>Map Error Occurred</Text>
          <Text style={styles.errorMessage}>
            The map component encountered an error and couldn't render properly.
          </Text>
        </View>
        
        <View style={styles.errorActions}>
          {canRetry && (
            <TouchableOpacity style={styles.retryButton} onPress={this.handleRetry}>
              <RefreshCw size={20} color="white" />
              <Text style={styles.retryButtonText}>
                Retry ({retryCount}/{maxRetries})
              </Text>
            </TouchableOpacity>
          )}
          
          <TouchableOpacity style={styles.resetButton} onPress={this.handleReset}>
            <RefreshCw size={20} color="#3B82F6" />
            <Text style={styles.resetButtonText}>Reset</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.exportButton} onPress={this.handleExportErrorReport}>
            <Download size={20} color="#6B7280" />
            <Text style={styles.exportButtonText}>Export Report</Text>
          </TouchableOpacity>
        </View>
        
        <View style={styles.errorInfo}>
          <Info size={16} color="#6B7280" />
          <Text style={styles.errorInfoText}>
            If this problem persists, try refreshing the app or contact support.
          </Text>
        </View>
        
        {this.renderErrorDetails()}
      </View>
    );
  };

  render() {
    const { hasError, error, retryCount } = this.state;
    const { children, fallbackComponent: FallbackComponent } = this.props;
    
    if (hasError && error) {
      if (FallbackComponent) {
        return (
          <FallbackComponent
            error={error}
            resetError={this.handleReset}
            retryCount={retryCount}
          />
        );
      }
      
      return this.renderDefaultErrorFallback();
    }
    
    return children;
  }
}

const styles = StyleSheet.create({
  errorContainer: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    padding: 20,
  },
  errorHeader: {
    alignItems: 'center',
    marginBottom: 24,
  },
  errorTitle: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  errorMessage: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 24,
  },
  errorActions: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 12,
    marginBottom: 24,
    flexWrap: 'wrap',
  },
  retryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#EF4444',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
    gap: 8,
  },
  retryButtonText: {
    color: 'white',
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
  },
  resetButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#3B82F6',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
    gap: 8,
  },
  resetButtonText: {
    color: '#3B82F6',
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
  },
  exportButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#6B7280',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
    gap: 8,
  },
  exportButtonText: {
    color: '#6B7280',
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
  },
  errorInfo: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#F3F4F6',
    padding: 12,
    borderRadius: 8,
    marginBottom: 20,
    gap: 8,
  },
  errorInfoText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    lineHeight: 16,
    flex: 1,
  },
  errorDetails: {
    flex: 1,
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 16,
  },
  errorSection: {
    marginBottom: 16,
  },
  errorSectionTitle: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
    marginBottom: 8,
  },
  errorText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginBottom: 4,
  },
  stackText: {
    fontSize: 11,
    fontFamily: 'Courier New',
    color: '#4B5563',
    lineHeight: 14,
    backgroundColor: '#FFFFFF',
    padding: 8,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
});

export default MapErrorBoundary;
