import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Submission } from '@/types';
import { useTheme } from '@/hooks/useTheme';
import { Calendar, Clock, CheckCircle, AlertCircle } from 'lucide-react-native';

type SubmissionCardProps = {
  submission: Submission;
  formName: string;
  onPress: () => void;
};

export default function SubmissionCard({ submission, formName, onPress }: SubmissionCardProps) {
  const { theme } = useTheme();
  
  const getStatusIcon = () => {
    switch (submission.status) {
      case 'draft':
        return <Clock size={16} color={theme.colors.warning} />;
      case 'completed':
        return <CheckCircle size={16} color={theme.colors.info} />;
      case 'synced':
        return <CheckCircle size={16} color={theme.colors.success} />;
      case 'error':
        return <AlertCircle size={16} color={theme.colors.error} />;
      default:
        return <Clock size={16} color={theme.colors.text} />;
    }
  };
  
  const getStatusText = () => {
    switch (submission.status) {
      case 'draft':
        return 'Draft';
      case 'completed':
        return 'Completed';
      case 'synced':
        return 'Synced';
      case 'error':
        return 'Error';
      default:
        return 'Unknown';
    }
  };
  
  const getStatusColor = () => {
    switch (submission.status) {
      case 'draft':
        return theme.colors.warning;
      case 'completed':
        return theme.colors.info;
      case 'synced':
        return theme.colors.success;
      case 'error':
        return theme.colors.error;
      default:
        return theme.colors.text;
    }
  };
  
  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  };

  return (
    <TouchableOpacity 
      style={[styles.container, { backgroundColor: theme.colors.card, borderColor: theme.colors.border }]} 
      onPress={onPress}
    >
      <View style={styles.content}>
        <View style={styles.header}>
          <Text style={[styles.title, { color: theme.colors.text }]}>{formName}</Text>
          <View style={[styles.statusBadge, { backgroundColor: `${getStatusColor()}20` }]}>
            {getStatusIcon()}
            <Text style={[styles.statusText, { color: getStatusColor() }]}>{getStatusText()}</Text>
          </View>
        </View>
        
        <View style={styles.metadata}>
          <View style={styles.metadataItem}>
            <Calendar size={14} color={theme.colors.muted} />
            <Text style={[styles.metadataText, { color: theme.colors.muted }]}>
              Started: {formatDate(submission.startedAt)}
            </Text>
          </View>
          
          {submission.completedAt && (
            <View style={styles.metadataItem}>
              <CheckCircle size={14} color={theme.colors.muted} />
              <Text style={[styles.metadataText, { color: theme.colors.muted }]}>
                Completed: {formatDate(submission.completedAt)}
              </Text>
            </View>
          )}
        </View>
        
        {submission.location && (
          <View style={[styles.locationBadge, { backgroundColor: theme.colors.secondaryLight }]}>
            <Text style={[styles.locationText, { color: theme.colors.secondary }]}>
              Location: {submission.location.latitude.toFixed(6)}, {submission.location.longitude.toFixed(6)}
            </Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 16,
  },
  content: {
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  title: {
    flex: 1,
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    marginRight: 8,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    gap: 4,
  },
  statusText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },
  metadata: {
    gap: 8,
    marginBottom: 12,
  },
  metadataItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  metadataText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },
  locationBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  locationText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },
});