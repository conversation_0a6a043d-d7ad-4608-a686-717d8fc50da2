/**
 * Error Recovery Component
 * Handles React component import errors and provides fallbacks
 */

import React, { Component, ErrorInfo, ReactNode } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { AlertTriangle, RefreshCw, Home, Bug } from 'lucide-react-native';
import FallbackNavigation from './FallbackNavigation';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  showDetails?: boolean;
  useFallbackNavigation?: boolean;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  retryCount: number;
}

export class ErrorRecovery extends Component<Props, State> {
  private maxRetries = 3;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorRecovery caught an error:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo,
    });

    // Call the onError callback if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Log error details for debugging
    this.logErrorDetails(error, errorInfo);
  }

  private logErrorDetails = (error: Error, errorInfo: ErrorInfo) => {
    const errorDetails = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      platform: Platform.OS,
      retryCount: this.state.retryCount,
    };

    console.group('🚨 Error Recovery Details');
    console.error('Error:', error);
    console.error('Error Info:', errorInfo);
    console.error('Full Details:', errorDetails);
    console.groupEnd();

    // In development, also log to help with debugging
    if (__DEV__) {
      console.warn('Component Stack:', errorInfo.componentStack);
    }
  };

  private handleRetry = () => {
    if (this.state.retryCount < this.maxRetries) {
      console.log(`🔄 Retrying... (${this.state.retryCount + 1}/${this.maxRetries})`);
      
      this.setState(prevState => ({
        hasError: false,
        error: null,
        errorInfo: null,
        retryCount: prevState.retryCount + 1,
      }));
    } else {
      console.warn('⚠️ Maximum retry attempts reached');
    }
  };

  private handleReset = () => {
    console.log('🔄 Resetting error state');
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0,
    });
  };

  private renderErrorDetails = () => {
    const { error, errorInfo } = this.state;
    
    if (!this.props.showDetails || !error) {
      return null;
    }

    return (
      <ScrollView style={styles.detailsContainer}>
        <Text style={styles.detailsTitle}>Error Details:</Text>
        <Text style={styles.errorText}>{error.message}</Text>
        
        {error.stack && (
          <>
            <Text style={styles.detailsTitle}>Stack Trace:</Text>
            <Text style={styles.stackText}>{error.stack}</Text>
          </>
        )}
        
        {errorInfo?.componentStack && (
          <>
            <Text style={styles.detailsTitle}>Component Stack:</Text>
            <Text style={styles.stackText}>{errorInfo.componentStack}</Text>
          </>
        )}
      </ScrollView>
    );
  };

  render() {
    if (this.state.hasError) {
      // Check if this is a navigation-related error
      const isNavigationError = this.state.error?.message?.includes('Screen_1.isScreen') ||
                                this.state.error?.message?.includes('react-native-screens');

      // Show fallback navigation for navigation errors
      if (isNavigationError && this.props.useFallbackNavigation) {
        return (
          <FallbackNavigation
            error={this.state.error}
            onNavigate={(route) => {
              console.log('Fallback navigation to:', route);
              // Reset error state and try to navigate
              this.handleReset();
            }}
          />
        );
      }

      // Show custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI
      return (
        <SafeAreaView style={styles.container}>
          <View style={styles.content}>
            <View style={styles.iconContainer}>
              <AlertTriangle size={64} color="#FF6B6B" />
            </View>
            
            <Text style={styles.title}>Something went wrong</Text>
            <Text style={styles.message}>
              The app encountered an unexpected error. You can try to recover by refreshing the component.
            </Text>

            <View style={styles.buttonContainer}>
              {this.state.retryCount < this.maxRetries && (
                <TouchableOpacity
                  style={[styles.button, styles.retryButton]}
                  onPress={this.handleRetry}
                >
                  <RefreshCw size={20} color="white" />
                  <Text style={styles.buttonText}>
                    Retry ({this.state.retryCount}/{this.maxRetries})
                  </Text>
                </TouchableOpacity>
              )}

              <TouchableOpacity
                style={[styles.button, styles.resetButton]}
                onPress={this.handleReset}
              >
                <Home size={20} color="white" />
                <Text style={styles.buttonText}>Reset</Text>
              </TouchableOpacity>

              {__DEV__ && (
                <TouchableOpacity
                  style={[styles.button, styles.debugButton]}
                  onPress={() => this.setState(prev => ({ 
                    ...prev, 
                    showDetails: !prev.showDetails 
                  }))}
                >
                  <Bug size={20} color="white" />
                  <Text style={styles.buttonText}>Debug</Text>
                </TouchableOpacity>
              )}
            </View>

            {__DEV__ && this.renderErrorDetails()}
          </View>
        </SafeAreaView>
      );
    }

    return this.props.children;
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  iconContainer: {
    marginBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
    textAlign: 'center',
  },
  message: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 30,
    lineHeight: 24,
  },
  buttonContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    gap: 10,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  retryButton: {
    backgroundColor: '#007AFF',
  },
  resetButton: {
    backgroundColor: '#34C759',
  },
  debugButton: {
    backgroundColor: '#FF9500',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  detailsContainer: {
    marginTop: 20,
    maxHeight: 200,
    width: '100%',
  },
  detailsTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 10,
    marginBottom: 5,
  },
  errorText: {
    fontSize: 12,
    color: '#FF6B6B',
    fontFamily: Platform.OS === 'ios' ? 'Menlo' : 'monospace',
  },
  stackText: {
    fontSize: 10,
    color: '#666',
    fontFamily: Platform.OS === 'ios' ? 'Menlo' : 'monospace',
  },
});

export default ErrorRecovery;
