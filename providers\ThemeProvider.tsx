import { createContext, useContext, ReactNode } from 'react';
import { useColorScheme } from 'react-native';
import { lightTheme, darkTheme } from '@/constants/theme';

type ThemeContextType = {
  theme: typeof lightTheme;
  toggleTheme: () => void;
  setTheme: (theme: 'light' | 'dark' | 'auto') => void;
  isDark: boolean;
  effectiveTheme: 'light' | 'dark';
};

export const ThemeContext = createContext<ThemeContextType | null>(null);

export function ThemeProvider({ children }: { children: ReactNode }) {
  const systemColorScheme = useColorScheme();

  // Try to get settings from SettingsProvider if available
  let settingsTheme: 'light' | 'dark' | 'auto' = 'auto';
  let updateSettingTheme: ((theme: 'light' | 'dark' | 'auto') => void) | null = null;

  try {
    // This will be available after we integrate with SettingsProvider
    const settingsContext = require('@/providers/SettingsProvider').useSettings?.();
    if (settingsContext) {
      settingsTheme = settingsContext.settings.theme;
      updateSettingTheme = (theme: 'light' | 'dark' | 'auto') => {
        settingsContext.updateSetting('theme', theme);
      };
    }
  } catch (error) {
    // Settings provider not available, use system theme
  }

  // Calculate effective theme
  const effectiveTheme = settingsTheme === 'auto'
    ? (systemColorScheme || 'light')
    : settingsTheme;
  const isDark = effectiveTheme === 'dark';

  const toggleTheme = () => {
    const newTheme = isDark ? 'light' : 'dark';
    if (updateSettingTheme) {
      updateSettingTheme(newTheme);
    }
  };

  const setTheme = (theme: 'light' | 'dark' | 'auto') => {
    if (updateSettingTheme) {
      updateSettingTheme(theme);
    }
  };

  const theme = isDark ? darkTheme : lightTheme;

  return (
    <ThemeContext.Provider value={{
      theme,
      toggleTheme,
      setTheme,
      isDark,
      effectiveTheme
    }}>
      {children}
    </ThemeContext.Provider>
  );
}

// Hook to use theme
export function useTheme() {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}