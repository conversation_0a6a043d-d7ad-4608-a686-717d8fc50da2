import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Alert, TouchableOpacity } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import EnhancedMapIntegration from './EnhancedMapIntegration';
import MapErrorBoundary from './MapErrorBoundary';
import { useTheme } from '@/hooks/useTheme';
import { Settings, Info, Download, Upload } from 'lucide-react-native';
import * as Location from 'expo-location';

interface MapScreenProps {
  projectId?: string;
  title?: string;
  showHeader?: boolean;
  enableOfflineStorage?: boolean;
  maxFeatures?: number;
}

export default function MapScreen({
  projectId = 'default-project',
  title = 'Field Map',
  showHeader = true,
  enableOfflineStorage = true,
  maxFeatures = 100,
}: MapScreenProps) {
  const { theme } = useTheme();
  const [currentLocation, setCurrentLocation] = useState<any>(null);
  const [featureCount, setFeatureCount] = useState(0);
  const [locationPermission, setLocationPermission] = useState<string>('undetermined');

  useEffect(() => {
    checkLocationPermission();
  }, []);

  const checkLocationPermission = async () => {
    try {
      const { status } = await Location.getForegroundPermissionsAsync();
      setLocationPermission(status);
      
      if (status === 'granted') {
        const location = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.High,
        });
        setCurrentLocation({
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        });
      }
    } catch (error) {
      console.error('Error checking location permission:', error);
    }
  };

  const requestLocationPermission = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      setLocationPermission(status);
      
      if (status === 'granted') {
        const location = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.High,
        });
        setCurrentLocation({
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        });
      } else {
        Alert.alert(
          'Location Permission',
          'Location access is required for full map functionality. You can still use the map but some features may be limited.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to request location permission.');
    }
  };

  const handleFeatureCreated = (feature: any) => {
    setFeatureCount(prev => prev + 1);
    // You could add additional logic here, like syncing to a server
    console.log('Feature created:', feature);
  };

  const handleFeatureDeleted = (featureId: string) => {
    setFeatureCount(prev => Math.max(0, prev - 1));
    // You could add additional logic here, like syncing to a server
    console.log('Feature deleted:', featureId);
  };

  const handleMapError = (error: Error, errorInfo: any) => {
    console.error('Map Error:', error);
    console.error('Error Info:', errorInfo);
    
    // In production, you might want to report this to an error tracking service
    // Example: Sentry.captureException(error, { contexts: { errorInfo } });
  };

  const showMapInfo = () => {
    Alert.alert(
      'Map Information',
      `Project: ${projectId}\nFeatures: ${featureCount}\nLocation: ${locationPermission === 'granted' ? 'Enabled' : 'Disabled'}\nOffline Storage: ${enableOfflineStorage ? 'Enabled' : 'Disabled'}`,
      [{ text: 'OK' }]
    );
  };

  const renderHeader = () => {
    if (!showHeader) return null;

    return (
      <View style={[styles.header, { backgroundColor: theme.colors.card, borderBottomColor: theme.colors.border }]}>
        <View style={styles.headerContent}>
          <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
            {title}
          </Text>
          <Text style={[styles.headerSubtitle, { color: theme.colors.muted }]}>
            {featureCount} features • {locationPermission === 'granted' ? 'GPS enabled' : 'GPS disabled'}
          </Text>
        </View>
        
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={[styles.headerButton, { backgroundColor: theme.colors.background }]}
            onPress={showMapInfo}
          >
            <Info size={20} color={theme.colors.text} />
          </TouchableOpacity>
          
          {locationPermission !== 'granted' && (
            <TouchableOpacity
              style={[styles.headerButton, { backgroundColor: theme.colors.warning }]}
              onPress={requestLocationPermission}
            >
              <Settings size={20} color="white" />
            </TouchableOpacity>
          )}
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {renderHeader()}
      
      <MapErrorBoundary onError={handleMapError} resetOnPropsChange={true}>
        <EnhancedMapIntegration
          projectId={projectId}
          initialRegion={currentLocation}
          onFeatureCreated={handleFeatureCreated}
          onFeatureDeleted={handleFeatureDeleted}
          enableOfflineStorage={enableOfflineStorage}
          maxFeatures={maxFeatures}
          enableDrawing={true}
          enableMeasurement={true}
          enableAnalysis={true}
        />
      </MapErrorBoundary>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  headerContent: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
  },
  headerSubtitle: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    marginTop: 2,
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8,
  },
  headerButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
