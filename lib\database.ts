// Platform-specific database module loader
import { Platform } from 'react-native';

export let SQLiteDatabase: any = null;

// Only load SQLite on native platforms
if (Platform.OS !== 'web') {
  try {
    const SQLite = require('expo-sqlite');
    SQLiteDatabase = SQLite;
  } catch (error) {
    console.warn('SQLite not available:', error);
  }
}

export const isSQLiteAvailable = () => SQLiteDatabase !== null;
