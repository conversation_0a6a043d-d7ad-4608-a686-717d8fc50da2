import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Modal,
  ScrollView,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useTheme } from '@/hooks/useTheme';
import { useProjects } from '@/hooks/useProjects';
import { useTeams } from '@/hooks/useTeams';
import { Project } from '@/types';
import {
  X,
  MapPin,
  Users,
  Calendar,
  FileText,
  AlertCircle,
  CheckCircle2,
} from 'lucide-react-native';

interface CreateProjectModalProps {
  visible: boolean;
  onClose: () => void;
  onSuccess?: (project: Project) => void;
}

interface FormData {
  name: string;
  description: string;
  selectedTeams: string[];
  region: {
    latitude: number;
    longitude: number;
    latitudeDelta: number;
    longitudeDelta: number;
  } | null;
}

export default function CreateProjectModal({
  visible,
  onClose,
  onSuccess,
}: CreateProjectModalProps) {
  const { theme } = useTheme();
  const { createProject } = useProjects();
  const { teams } = useTeams();

  const [formData, setFormData] = useState<FormData>({
    name: '',
    description: '',
    selectedTeams: [],
    region: null,
  });
  
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Partial<FormData>>({});

  const validateForm = (): boolean => {
    const newErrors: Partial<FormData> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Project name is required';
    } else if (formData.name.length < 3) {
      newErrors.name = 'Project name must be at least 3 characters';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Project description is required';
    } else if (formData.description.length < 10) {
      newErrors.description = 'Description must be at least 10 characters';
    }

    if (formData.selectedTeams.length === 0) {
      newErrors.selectedTeams = ['At least one team must be selected'];
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setLoading(true);
    try {
      const newProject = await createProject({
        name: formData.name.trim(),
        description: formData.description.trim(),
        teams: formData.selectedTeams,
        status: 'draft',
        forms: [],
        createdBy: '1', // Current user ID
        region: formData.region || {
          latitude: 39.8283,
          longitude: -98.5795,
          latitudeDelta: 1.0,
          longitudeDelta: 1.0,
        },
      });

      onSuccess?.(newProject);
      handleClose();

      if (Platform.OS === 'web') {
        alert('Project created successfully!');
      } else {
        Alert.alert('Success', 'Project created successfully!');
      }
    } catch (error) {
      console.error('Error creating project:', error);
      const message = 'Failed to create project. Please try again.';
      
      if (Platform.OS === 'web') {
        alert(message);
      } else {
        Alert.alert('Error', message);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setFormData({
      name: '',
      description: '',
      selectedTeams: [],
      region: null,
    });
    setErrors({});
    onClose();
  };

  const toggleTeamSelection = (teamId: string) => {
    setFormData(prev => ({
      ...prev,
      selectedTeams: prev.selectedTeams.includes(teamId)
        ? prev.selectedTeams.filter(id => id !== teamId)
        : [...prev.selectedTeams, teamId],
    }));
  };

  const setDefaultLocation = () => {
    setFormData(prev => ({
      ...prev,
      region: {
        latitude: 39.8283,
        longitude: -98.5795,
        latitudeDelta: 1.0,
        longitudeDelta: 1.0,
      },
    }));
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={handleClose}
    >
      <KeyboardAvoidingView
        style={[styles.container, { backgroundColor: theme.colors.background }]}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <View style={[styles.header, { borderBottomColor: theme.colors.border }]}>
          <Text style={[styles.title, { color: theme.colors.text }]}>
            Create New Project
          </Text>
          <TouchableOpacity onPress={handleClose} style={styles.closeButton}>
            <X size={24} color={theme.colors.text} />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Project Name */}
          <View style={styles.section}>
            <Text style={[styles.label, { color: theme.colors.text }]}>
              Project Name *
            </Text>
            <TextInput
              style={[
                styles.input,
                {
                  backgroundColor: theme.colors.card,
                  borderColor: errors.name ? theme.colors.error : theme.colors.border,
                  color: theme.colors.text,
                },
              ]}
              placeholder="Enter project name"
              placeholderTextColor={theme.colors.placeholder}
              value={formData.name}
              onChangeText={(text) => setFormData(prev => ({ ...prev, name: text }))}
              maxLength={100}
            />
            {errors.name && (
              <View style={styles.errorContainer}>
                <AlertCircle size={16} color={theme.colors.error} />
                <Text style={[styles.errorText, { color: theme.colors.error }]}>
                  {errors.name}
                </Text>
              </View>
            )}
          </View>

          {/* Project Description */}
          <View style={styles.section}>
            <Text style={[styles.label, { color: theme.colors.text }]}>
              Description *
            </Text>
            <TextInput
              style={[
                styles.textArea,
                {
                  backgroundColor: theme.colors.card,
                  borderColor: errors.description ? theme.colors.error : theme.colors.border,
                  color: theme.colors.text,
                },
              ]}
              placeholder="Describe the project objectives and scope"
              placeholderTextColor={theme.colors.placeholder}
              value={formData.description}
              onChangeText={(text) => setFormData(prev => ({ ...prev, description: text }))}
              multiline
              numberOfLines={4}
              maxLength={500}
            />
            {errors.description && (
              <View style={styles.errorContainer}>
                <AlertCircle size={16} color={theme.colors.error} />
                <Text style={[styles.errorText, { color: theme.colors.error }]}>
                  {errors.description}
                </Text>
              </View>
            )}
          </View>

          {/* Team Selection */}
          <View style={styles.section}>
            <Text style={[styles.label, { color: theme.colors.text }]}>
              Assign Teams *
            </Text>
            <View style={styles.teamsContainer}>
              {teams.map((team) => (
                <TouchableOpacity
                  key={team.id}
                  style={[
                    styles.teamCard,
                    {
                      backgroundColor: formData.selectedTeams.includes(team.id)
                        ? theme.colors.primaryLight
                        : theme.colors.card,
                      borderColor: formData.selectedTeams.includes(team.id)
                        ? theme.colors.primary
                        : theme.colors.border,
                    },
                  ]}
                  onPress={() => toggleTeamSelection(team.id)}
                >
                  <View style={styles.teamCardHeader}>
                    <View style={styles.teamInfo}>
                      <Users
                        size={20}
                        color={
                          formData.selectedTeams.includes(team.id)
                            ? theme.colors.primary
                            : theme.colors.text
                        }
                      />
                      <Text
                        style={[
                          styles.teamName,
                          {
                            color: formData.selectedTeams.includes(team.id)
                              ? theme.colors.primary
                              : theme.colors.text,
                          },
                        ]}
                      >
                        {team.name}
                      </Text>
                    </View>
                    {formData.selectedTeams.includes(team.id) && (
                      <CheckCircle2 size={20} color={theme.colors.primary} />
                    )}
                  </View>
                  <Text
                    style={[
                      styles.teamDescription,
                      {
                        color: formData.selectedTeams.includes(team.id)
                          ? theme.colors.primary
                          : theme.colors.muted,
                      },
                    ]}
                  >
                    {team.description}
                  </Text>
                  <Text
                    style={[
                      styles.teamMembers,
                      {
                        color: formData.selectedTeams.includes(team.id)
                          ? theme.colors.primary
                          : theme.colors.muted,
                      },
                    ]}
                  >
                    {team.members.length} member{team.members.length !== 1 ? 's' : ''}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
            {errors.selectedTeams && (
              <View style={styles.errorContainer}>
                <AlertCircle size={16} color={theme.colors.error} />
                <Text style={[styles.errorText, { color: theme.colors.error }]}>
                  {errors.selectedTeams[0]}
                </Text>
              </View>
            )}
          </View>

          {/* Project Region */}
          <View style={styles.section}>
            <Text style={[styles.label, { color: theme.colors.text }]}>
              Project Region
            </Text>
            <TouchableOpacity
              style={[
                styles.locationButton,
                {
                  backgroundColor: theme.colors.card,
                  borderColor: theme.colors.border,
                },
              ]}
              onPress={setDefaultLocation}
            >
              <MapPin size={20} color={theme.colors.primary} />
              <Text style={[styles.locationText, { color: theme.colors.text }]}>
                {formData.region
                  ? `${formData.region.latitude.toFixed(4)}, ${formData.region.longitude.toFixed(4)}`
                  : 'Set project location'}
              </Text>
            </TouchableOpacity>
          </View>
        </ScrollView>

        <View style={[styles.footer, { borderTopColor: theme.colors.border }]}>
          <TouchableOpacity
            style={[styles.cancelButton, { borderColor: theme.colors.border }]}
            onPress={handleClose}
            disabled={loading}
          >
            <Text style={[styles.cancelButtonText, { color: theme.colors.text }]}>
              Cancel
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.createButton,
              {
                backgroundColor: loading ? theme.colors.muted : theme.colors.primary,
              },
            ]}
            onPress={handleSubmit}
            disabled={loading}
          >
            <Text style={styles.createButtonText}>
              {loading ? 'Creating...' : 'Create Project'}
            </Text>
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  closeButton: {
    padding: 8,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  section: {
    marginVertical: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
  },
  textArea: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    minHeight: 100,
    textAlignVertical: 'top',
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  errorText: {
    fontSize: 14,
    marginLeft: 4,
  },
  teamsContainer: {
    gap: 12,
  },
  teamCard: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
  },
  teamCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  teamInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  teamName: {
    fontSize: 16,
    fontWeight: '600',
  },
  teamDescription: {
    fontSize: 14,
    marginBottom: 4,
  },
  teamMembers: {
    fontSize: 12,
    fontWeight: '500',
  },
  locationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderWidth: 1,
    borderRadius: 8,
    gap: 8,
  },
  locationText: {
    fontSize: 16,
  },
  footer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderTopWidth: 1,
    gap: 12,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 12,
    borderWidth: 1,
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  createButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  createButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});
