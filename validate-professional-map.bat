@echo off
REM Final Validation Script for Professional Map UI Implementation - Windows
REM Comprehensive testing and status report

echo 🎯 Professional Map UI - Final Validation
echo ==========================================

set total_checks=0
set passed_checks=0
set warnings=0

REM Function to increment counters and print status
:print_check
set /a total_checks+=1
if "%~2"=="PASS" (
    echo ✅ %~1
    set /a passed_checks+=1
) else if "%~2"=="WARN" (
    echo ⚠️  %~1
    set /a warnings+=1
) else (
    echo ❌ %~1
)
goto :eof

REM Function to check file exists and has content
:check_file_content
if exist "%~1" (
    call :print_check "%~1 exists and has content" "PASS"
) else (
    call :print_check "%~1 missing or empty" "FAIL"
)
goto :eof

REM Function to check for content in file
:check_content
findstr /c:"%~1" "%~2" >nul 2>&1
if %errorlevel% equ 0 (
    call :print_check "%~3" "PASS"
) else (
    call :print_check "%~3" "FAIL"
)
goto :eof

echo 🔍 Phase 1: File Structure Validation
echo ======================================

REM Check core component files
call :check_file_content "components\map\ProfessionalMapUI.tsx"
call :check_file_content "components\map\OptimizedLeafletMap.web.tsx"
call :check_file_content "components\map\EnhancedMapIntegration.tsx"
call :check_file_content "components\map\MapScreen.tsx"
call :check_file_content "components\map\index.ts"

REM Check documentation files
call :check_file_content "PROFESSIONAL_MAP_IMPLEMENTATION_GUIDE.md"
call :check_file_content "QUICK_START_GUIDE.md"

REM Check style and test files
call :check_file_content "components\map\professionalMapStyles.css"
call :check_file_content "test-professional-map-implementation.bat"

echo.
echo 🔧 Phase 2: Component Integration Analysis
echo ==========================================

REM Check MapScreen integration
call :check_content "EnhancedMapIntegration" "components\map\MapScreen.tsx" "MapScreen uses EnhancedMapIntegration"
call :check_content "import.*EnhancedMapIntegration" "components\map\MapScreen.tsx" "EnhancedMapIntegration properly imported"

REM Check component exports
call :check_content "export.*ProfessionalMapUI" "components\map\index.ts" "ProfessionalMapUI exported in index"
call :check_content "export.*EnhancedMapIntegration" "components\map\index.ts" "EnhancedMapIntegration exported in index"
call :check_content "export.*OptimizedLeafletMap" "components\map\index.ts" "OptimizedLeafletMap exported in index"

echo.
echo 🎨 Phase 3: UI Architecture Validation
echo =====================================

REM Check ProfessionalMapUI architecture
call :check_content "TOOLBAR_HEIGHT" "components\map\ProfessionalMapUI.tsx" "Toolbar height constant defined"
call :check_content "SIDEBAR_WIDTH" "components\map\ProfessionalMapUI.tsx" "Sidebar width constant defined"
call :check_content "renderMainToolbar" "components\map\ProfessionalMapUI.tsx" "Main toolbar rendering method"
call :check_content "renderSidebar" "components\map\ProfessionalMapUI.tsx" "Sidebar rendering method"
call :check_content "renderMapControls" "components\map\ProfessionalMapUI.tsx" "Map controls rendering method"

REM Check drawing tool implementation
call :check_content "DrawingTool" "components\map\ProfessionalMapUI.tsx" "Drawing tool types defined"
call :check_content "selectDrawingTool" "components\map\ProfessionalMapUI.tsx" "Drawing tool selection method"
call :check_content "cancelDrawing" "components\map\ProfessionalMapUI.tsx" "Drawing cancellation method"
call :check_content "handleMapPress" "components\map\ProfessionalMapUI.tsx" "Map press handler"

echo.
echo 🌐 Phase 4: Leaflet Integration Validation
echo ========================================

REM Check OptimizedLeafletMap implementation
call :check_content "loadLeafletLibraries" "components\map\OptimizedLeafletMap.web.tsx" "Leaflet library loading"
call :check_content "initializeMap" "components\map\OptimizedLeafletMap.web.tsx" "Map initialization method"
call :check_content "createBaseLayers" "components\map\OptimizedLeafletMap.web.tsx" "Base layers creation"
call :check_content "updateDrawingMode" "components\map\OptimizedLeafletMap.web.tsx" "Drawing mode updates"
call :check_content "handleDrawCreated" "components\map\OptimizedLeafletMap.web.tsx" "Draw creation handler"
call :check_content "convertLayerToFeature" "components\map\OptimizedLeafletMap.web.tsx" "Layer to feature conversion"

REM Check Leaflet CDN integration
call :check_content "unpkg.com/leaflet" "components\map\OptimizedLeafletMap.web.tsx" "Leaflet CDN loading"
call :check_content "leaflet.draw" "components\map\OptimizedLeafletMap.web.tsx" "Leaflet Draw plugin"

echo.
echo 💾 Phase 5: Data Management Validation
echo =====================================

REM Check EnhancedMapIntegration storage
call :check_content "AsyncStorage" "components\map\EnhancedMapIntegration.tsx" "AsyncStorage integration"
call :check_content "loadFeatures" "components\map\EnhancedMapIntegration.tsx" "Feature loading method"
call :check_content "saveFeatures" "components\map\EnhancedMapIntegration.tsx" "Feature saving method"
call :check_content "handleFeatureCreated" "components\map\EnhancedMapIntegration.tsx" "Feature creation handler"
call :check_content "handleFeatureDeleted" "components\map\EnhancedMapIntegration.tsx" "Feature deletion handler"

REM Check feature validation
call :check_content "maxFeatures" "components\map\EnhancedMapIntegration.tsx" "Feature limit validation"
call :check_content "Alert.alert" "components\map\EnhancedMapIntegration.tsx" "User feedback alerts"

echo.
echo 🎯 Phase 6: TypeScript ^& Code Quality
echo ==================================

REM Check TypeScript interfaces
call :check_content "interface.*Region" "components\map\index.ts" "Region interface defined"
call :check_content "interface.*DrawnFeature" "components\map\index.ts" "DrawnFeature interface defined"
call :check_content "interface.*StoredFeature" "components\map\index.ts" "StoredFeature interface defined"
call :check_content "type.*DrawingTool" "components\map\index.ts" "DrawingTool type defined"

REM Check utility functions
call :check_content "createFeatureId" "components\map\index.ts" "Feature ID creation utility"
call :check_content "validateFeature" "components\map\index.ts" "Feature validation utility"
call :check_content "convertFeatureToGeoJSON" "components\map\index.ts" "GeoJSON conversion utility"

echo.
echo 🎨 Phase 7: Styling ^& CSS Validation
echo =================================

REM Check CSS file structure
call :check_content "Professional Map UI Styles" "components\map\professionalMapStyles.css" "CSS header comment"
call :check_content "leaflet-container" "components\map\professionalMapStyles.css" "Leaflet container styles"
call :check_content "leaflet-draw-toolbar" "components\map\professionalMapStyles.css" "Drawing toolbar styles"
call :check_content "leaflet-popup-content-wrapper" "components\map\professionalMapStyles.css" "Popup styling"
call :check_content "max-width.*768px" "components\map\professionalMapStyles.css" "Mobile responsive styles"
call :check_content "prefers-color-scheme.*dark" "components\map\professionalMapStyles.css" "Dark mode support"

echo.
echo 📋 Phase 8: Documentation Validation
echo ==================================

REM Check implementation guide
call :check_content "Professional Map UI Implementation" "PROFESSIONAL_MAP_IMPLEMENTATION_GUIDE.md" "Implementation guide title"
call :check_content "Key Problems Solved" "PROFESSIONAL_MAP_IMPLEMENTATION_GUIDE.md" "Problems solved section"
call :check_content "Architecture Overview" "PROFESSIONAL_MAP_IMPLEMENTATION_GUIDE.md" "Architecture documentation"

REM Check quick start guide
call :check_content "Quick Start Guide" "QUICK_START_GUIDE.md" "Quick start guide title"
call :check_content "Getting Started" "QUICK_START_GUIDE.md" "Getting started section"
call :check_content "Using the Map Interface" "QUICK_START_GUIDE.md" "Usage instructions"

echo.
echo 🔄 Phase 9: Integration Points Check
echo =================================

REM Check proper component usage
call :check_content "ProfessionalMapUI" "components\map\EnhancedMapIntegration.tsx" "Integration uses ProfessionalMapUI"
call :check_content "useTheme" "components\map\ProfessionalMapUI.tsx" "Theme hook usage"

echo.
echo 🧪 Phase 10: Error Handling ^& Safety
echo ==================================

REM Check error handling
call :check_content "try.*catch" "components\map\OptimizedLeafletMap.web.tsx" "Error handling in Leaflet"
call :check_content "try.*catch" "components\map\EnhancedMapIntegration.tsx" "Error handling in data layer"
call :check_content "cleanup" "components\map\OptimizedLeafletMap.web.tsx" "Cleanup implementation"

echo.
echo 📊 VALIDATION SUMMARY
echo ====================

set /a success_rate=(passed_checks * 100) / total_checks

echo Total Checks: %total_checks%
echo Passed: %passed_checks%
echo Warnings: %warnings%
set /a failed_checks=total_checks - passed_checks - warnings
echo Failed: %failed_checks%
echo Success Rate: %success_rate%%%

echo.
if %success_rate% geq 90 (
    echo 🎉 EXCELLENT! Implementation is ready for production
    echo ✨ Your professional map UI is fully functional
) else if %success_rate% geq 80 (
    echo ⚡ GOOD! Implementation is mostly complete
    echo 🔧 Minor fixes may be needed
) else if %success_rate% geq 70 (
    echo ⚠️  FAIR! Implementation needs some attention
    echo 🛠️  Review failed checks above
) else (
    echo ❌ NEEDS WORK! Implementation has significant issues
    echo 🚨 Review and fix failed components
)

echo.
echo 🚀 NEXT STEPS
echo ============

if %success_rate% geq 90 (
    echo 1. Start development server: npm run web
    echo 2. Test drawing tools in browser
    echo 3. Verify feature storage functionality
    echo 4. Test on mobile devices
    echo 5. Deploy to staging environment
) else if %success_rate% geq 80 (
    echo 1. Review warnings and failed checks above
    echo 2. Fix any missing components or functions
    echo 3. Re-run this validation script
    echo 4. Test core functionality
) else if %success_rate% geq 70 (
    echo 1. Address failed checks systematically
    echo 2. Check TypeScript compilation
    echo 3. Verify all imports and exports
    echo 4. Re-run validation after fixes
) else (
    echo 1. Review implementation guide carefully
    echo 2. Check if all files were created correctly
    echo 3. Verify component structure and imports
    echo 4. Consider regenerating components
)

echo.
echo 📚 DOCUMENTATION AVAILABLE
echo =========================
echo • PROFESSIONAL_MAP_IMPLEMENTATION_GUIDE.md - Complete technical guide
echo • QUICK_START_GUIDE.md - User-friendly getting started guide
echo • components\map\professionalMapStyles.css - Professional styling
echo • components\map\index.ts - Type definitions and utilities

echo.
echo 🔧 DEVELOPMENT COMMANDS
echo ====================
echo npm run web      # Start web development server
echo npm run android  # Test on Android device/emulator
echo npm run ios      # Test on iOS device/simulator
echo npm start        # Standard React Native development

echo.
echo 🎯 Professional Map UI Implementation Validation Complete!

pause
