/**
 * Expo App Configuration
 * 
 * This configuration handles environment-specific settings,
 * particularly for Expo Updates to prevent runtime errors.
 */

const IS_DEV = process.env.EXPO_PUBLIC_APP_ENV === 'development';
const DISABLE_UPDATES = process.env.EXPO_PUBLIC_DISABLE_UPDATES === 'true';

export default {
  expo: {
    name: "FieldSync Pro",
    slug: "fieldsync-pro",
    version: "1.0.0",
    orientation: "portrait",
    icon: "./assets/images/icon.png",
    scheme: "fieldsync",
    userInterfaceStyle: "automatic",
    newArchEnabled: true,
    
    // Updates configuration - completely disabled for development
    updates: {
      enabled: false,
      checkAutomatically: "ON_ERROR_RECOVERY",
      fallbackToCacheTimeout: 0,
      ...(IS_DEV || DISABLE_UPDATES ? {
        // Additional safeguards for development
        url: null,
        requestHeaders: {},
      } : {})
    },
    
    ios: {
      supportsTablet: true
    },
    
    android: {
      adaptiveIcon: {
        foregroundImage: "./assets/images/adaptive-icon.png",
        backgroundColor: "#ffffff"
      },
      package: "com.ossamahda.fieldsyncpro"
    },
    
    web: {
      bundler: "metro",
      output: "static",
      favicon: "./assets/images/favicon.png"
    },
    
    plugins: [
      "expo-router",
      // Conditionally add update-related plugins only in production
      ...(IS_DEV || DISABLE_UPDATES ? [] : [
        // Add production update plugins here if needed
      ])
    ],
    
    experiments: {
      typedRoutes: true
    },
    
    // Development-specific configurations
    ...(IS_DEV ? {
      developer: {
        tool: "expo-cli"
      }
    } : {})
  }
};
