import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { useTheme } from '@/hooks/useTheme';
import { QuestionType } from '@/types';
import {
  Type,
  Hash,
  Calendar,
  Clock,
  CalendarDays,
  CheckSquare,
  List,
  MapPin,
  Camera,
  Mic,
  Video,
  Edit3,
  Image,
  BarChart3,
} from 'lucide-react-native';

interface QuestionTypePaletteProps {
  onSelectType: (type: QuestionType) => void;
}

const questionTypes: Array<{
  type: QuestionType;
  label: string;
  description: string;
  icon: React.ReactNode;
  category: 'basic' | 'choice' | 'media' | 'location' | 'advanced';
}> = [
  // Basic Types
  {
    type: 'text',
    label: 'Text',
    description: 'Short text input',
    icon: <Type size={20} />,
    category: 'basic',
  },
  {
    type: 'number',
    label: 'Number',
    description: 'Numeric input',
    icon: <Hash size={20} />,
    category: 'basic',
  },
  {
    type: 'date',
    label: 'Date',
    description: 'Date picker',
    icon: <Calendar size={20} />,
    category: 'basic',
  },
  {
    type: 'time',
    label: 'Time',
    description: 'Time picker',
    icon: <Clock size={20} />,
    category: 'basic',
  },
  {
    type: 'datetime',
    label: 'Date & Time',
    description: 'Date and time picker',
    icon: <CalendarDays size={20} />,
    category: 'basic',
  },
  
  // Choice Types
  {
    type: 'select',
    label: 'Single Choice',
    description: 'Radio buttons',
    icon: <CheckSquare size={20} />,
    category: 'choice',
  },
  {
    type: 'multiselect',
    label: 'Multiple Choice',
    description: 'Checkboxes',
    icon: <List size={20} />,
    category: 'choice',
  },
  
  // Location Types
  {
    type: 'location',
    label: 'Location',
    description: 'GPS coordinates',
    icon: <MapPin size={20} />,
    category: 'location',
  },
  
  // Media Types
  {
    type: 'photo',
    label: 'Photo',
    description: 'Camera capture',
    icon: <Camera size={20} />,
    category: 'media',
  },
  {
    type: 'audio',
    label: 'Audio',
    description: 'Voice recording',
    icon: <Mic size={20} />,
    category: 'media',
  },
  {
    type: 'video',
    label: 'Video',
    description: 'Video recording',
    icon: <Video size={20} />,
    category: 'media',
  },
  
  // Advanced Types
  {
    type: 'signature',
    label: 'Signature',
    description: 'Digital signature',
    icon: <Edit3 size={20} />,
    category: 'advanced',
  },
  {
    type: 'drawing',
    label: 'Drawing',
    description: 'Sketch or drawing',
    icon: <Image size={20} />,
    category: 'advanced',
  },
  {
    type: 'barcode',
    label: 'Barcode',
    description: 'Barcode scanner',
    icon: <BarChart3 size={20} />,
    category: 'advanced',
  },
];

const categoryLabels = {
  basic: 'Basic Inputs',
  choice: 'Choice Questions',
  location: 'Location',
  media: 'Media Capture',
  advanced: 'Advanced',
};

export default function QuestionTypePalette({ onSelectType }: QuestionTypePaletteProps) {
  const { theme } = useTheme();

  const groupedTypes = questionTypes.reduce((acc, questionType) => {
    if (!acc[questionType.category]) {
      acc[questionType.category] = [];
    }
    acc[questionType.category].push(questionType);
    return acc;
  }, {} as Record<string, typeof questionTypes>);

  const renderQuestionType = (questionType: typeof questionTypes[0]) => (
    <TouchableOpacity
      key={questionType.type}
      style={[styles.questionTypeItem, { backgroundColor: theme.colors.card, borderColor: theme.colors.border }]}
      onPress={() => onSelectType(questionType.type)}
      activeOpacity={0.7}
    >
      <View style={[styles.questionTypeIcon, { backgroundColor: theme.colors.primary + '15' }]}>
        {React.cloneElement(questionType.icon as React.ReactElement, {
          color: theme.colors.primary,
        })}
      </View>
      <View style={styles.questionTypeContent}>
        <Text style={[styles.questionTypeLabel, { color: theme.colors.text }]}>
          {questionType.label}
        </Text>
        <Text style={[styles.questionTypeDescription, { color: theme.colors.muted }]}>
          {questionType.description}
        </Text>
      </View>
    </TouchableOpacity>
  );

  const renderCategory = (category: keyof typeof categoryLabels) => (
    <View key={category} style={styles.category}>
      <Text style={[styles.categoryTitle, { color: theme.colors.text }]}>
        {categoryLabels[category]}
      </Text>
      {groupedTypes[category]?.map(renderQuestionType)}
    </View>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.card }]}>
      <View style={[styles.header, { borderBottomColor: theme.colors.border }]}>
        <Text style={[styles.title, { color: theme.colors.text }]}>Question Types</Text>
        <Text style={[styles.subtitle, { color: theme.colors.muted }]}>
          Drag and drop to add
        </Text>
      </View>
      
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {Object.keys(categoryLabels).map(category =>
          renderCategory(category as keyof typeof categoryLabels)
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 16,
    borderBottomWidth: 1,
  },
  title: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  category: {
    marginBottom: 24,
  },
  categoryTitle: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 12,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  questionTypeItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    marginBottom: 8,
    borderRadius: 8,
    borderWidth: 1,
  },
  questionTypeIcon: {
    width: 40,
    height: 40,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  questionTypeContent: {
    flex: 1,
  },
  questionTypeLabel: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 2,
  },
  questionTypeDescription: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
  },
});
