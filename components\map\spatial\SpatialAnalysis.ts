import { LatLng } from 'react-native-maps';

export interface MeasurementResult {
  distance?: number; // in meters
  area?: number; // in square meters
  perimeter?: number; // in meters
  formattedDistance?: string;
  formattedArea?: string;
  formattedPerimeter?: string;
}

export interface BufferOptions {
  distance: number; // in meters
  units?: 'kilometers' | 'meters' | 'miles';
  steps?: number;
}

// Basic implementations without turf dependency
export const calculateDistance = (from: LatLng, to: LatLng): number => {
  const R = 6371e3; // Earth's radius in meters
  const φ1 = from.latitude * Math.PI / 180;
  const φ2 = to.latitude * Math.PI / 180;
  const Δφ = (to.latitude - from.latitude) * Math.PI / 180;
  const Δλ = (to.longitude - from.longitude) * Math.PI / 180;

  const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
          Math.cos(φ1) * Math.cos(φ2) *
          Math.sin(Δλ/2) * Math.sin(Δλ/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

  return R * c;
};

export const calculateArea = (points: LatLng[]): number => {
  if (points.length < 3) return 0;
  
  let area = 0;
  const n = points.length;
  
  for (let i = 0; i < n; i++) {
    const j = (i + 1) % n;
    area += points[i].longitude * points[j].latitude;
    area -= points[j].longitude * points[i].latitude;
  }
  
  // Convert to square meters (rough approximation)
  const earthRadius = 6371000;
  const latFactor = Math.PI / 180;
  const avgLat = points.reduce((sum, p) => sum + p.latitude, 0) / n;
  const metersPerDegreeLat = earthRadius * latFactor;
  const metersPerDegreeLng = earthRadius * latFactor * Math.cos(avgLat * latFactor);
  
  return Math.abs(area) * metersPerDegreeLat * metersPerDegreeLng / 2;
};

export const createBuffer = (coordinate: LatLng, distance: number): LatLng[] => {
  const points: LatLng[] = [];
  const steps = 32;
  const earthRadius = 6371000;
  
  for (let i = 0; i < steps; i++) {
    const angle = (i * 2 * Math.PI) / steps;
    const lat = coordinate.latitude + (distance / earthRadius) * (180 / Math.PI) * Math.cos(angle);
    const lng = coordinate.longitude + (distance / earthRadius) * (180 / Math.PI) * Math.sin(angle) / Math.cos(coordinate.latitude * Math.PI / 180);
    
    points.push({ latitude: lat, longitude: lng });
  }
  
  return points;
};

export const pointInPolygon = (point: LatLng, polygon: LatLng[]): boolean => {
  let inside = false;
  const x = point.longitude;
  const y = point.latitude;
  
  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
    const xi = polygon[i].longitude;
    const yi = polygon[i].latitude;
    const xj = polygon[j].longitude;
    const yj = polygon[j].latitude;
    
    if (((yi > y) !== (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi)) {
      inside = !inside;
    }
  }
  
  return inside;
};

export const getPolygonCenter = (points: LatLng[]): LatLng => {
  const sumLat = points.reduce((sum, p) => sum + p.latitude, 0);
  const sumLng = points.reduce((sum, p) => sum + p.longitude, 0);
  
  return {
    latitude: sumLat / points.length,
    longitude: sumLng / points.length,
  };
};

export const simplifyPolygon = (points: LatLng[], tolerance: number): LatLng[] => {
  // Simple Douglas-Peucker simplification
  if (points.length <= 2) return points;
  
  // For now, return original points (can be enhanced with proper algorithm)
  return points.filter((_, index) => index % Math.max(1, Math.floor(tolerance * 100)) === 0);
};

export const getBoundingBox = (points: LatLng[]): {
  minLat: number;
  maxLat: number;
  minLng: number;
  maxLng: number;
} => {
  if (points.length === 0) {
    return { minLat: 0, maxLat: 0, minLng: 0, maxLng: 0 };
  }
  
  let minLat = points[0].latitude;
  let maxLat = points[0].latitude;
  let minLng = points[0].longitude;
  let maxLng = points[0].longitude;
  
  points.forEach(point => {
    minLat = Math.min(minLat, point.latitude);
    maxLat = Math.max(maxLat, point.latitude);
    minLng = Math.min(minLng, point.longitude);
    maxLng = Math.max(maxLng, point.longitude);
  });
  
  return { minLat, maxLat, minLng, maxLng };
};
