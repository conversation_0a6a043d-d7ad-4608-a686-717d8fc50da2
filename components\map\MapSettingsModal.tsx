import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Switch,
  Alert,
  Platform,
} from 'react-native';
import { useTheme } from '@/hooks/useTheme';
import {
  X,
  Settings,
  Map,
  Palette,
  Globe,
  Ruler,
  Compass,
  Eye,
  Type,
  Image,
  Zap,
  Shield,
  Info,
  Save,
  RotateCcw,
  Monitor,
  Smartphone,
  Tablet,
} from 'lucide-react-native';

interface MapSettings {
  // Basic Properties
  title: string;
  description: string;
  author: string;
  tags: string[];
  
  // Map Extent & View
  defaultExtent: {
    latitude: number;
    longitude: number;
    latitudeDelta: number;
    longitudeDelta: number;
  };
  minZoom: number;
  maxZoom: number;
  restrictPanning: boolean;
  
  // Coordinate System
  coordinateSystem: string;
  displayCoordinates: boolean;
  coordinateFormat: 'decimal' | 'dms' | 'utm' | 'mgrs';
  coordinatePrecision: number;
  
  // Base Map
  baseMapType: string;
  customBaseMapUrl?: string;
  baseMapOpacity: number;
  
  // UI Elements
  showScale: boolean;
  showNorthArrow: boolean;
  showZoomControls: boolean;
  showLayerPanel: boolean;
  showToolbar: boolean;
  showMeasurementTools: boolean;
  
  // Visual Theme
  primaryColor: string;
  secondaryColor: string;
  backgroundColor: string;
  textColor: string;
  fontFamily: string;
  fontSize: number;
  
  // Branding
  logoUrl?: string;
  logoPosition: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  logoSize: number;
  watermark?: string;
  
  // Performance
  enableWebGL: boolean;
  enable3D: boolean;
  renderQuality: 'low' | 'medium' | 'high';
  maxFeatures: number;
  clustering: boolean;
  
  // Security & Access
  requireAuthentication: boolean;
  allowDownload: boolean;
  allowPrint: boolean;
  allowShare: boolean;
  expirationDate?: string;
}

interface MapSettingsModalProps {
  visible: boolean;
  onClose: () => void;
  settings: MapSettings;
  onSettingsUpdate: (settings: MapSettings) => void;
  onExport: () => void;
  onReset: () => void;
}

const COORDINATE_SYSTEMS = [
  { code: 'EPSG:4326', name: 'WGS 84 Geographic', description: 'World Geodetic System 1984' },
  { code: 'EPSG:3857', name: 'Web Mercator', description: 'Google/OpenStreetMap projection' },
  { code: 'EPSG:4269', name: 'NAD83 Geographic', description: 'North American Datum 1983' },
  { code: 'EPSG:32633', name: 'UTM Zone 33N', description: 'Universal Transverse Mercator' },
  { code: 'EPSG:2154', name: 'RGF93 Lambert 93', description: 'French national grid' },
  { code: 'EPSG:27700', name: 'British National Grid', description: 'Ordnance Survey Great Britain' },
  { code: 'EPSG:3395', name: 'World Mercator', description: 'Mercator world projection' },
  { code: 'EPSG:4258', name: 'ETRS89 Geographic', description: 'European Terrestrial Reference System' },
];

const BASE_MAP_TYPES = [
  { key: 'satellite', name: 'Satellite', description: 'High-resolution satellite imagery' },
  { key: 'terrain', name: 'Terrain', description: 'Topographic with elevation shading' },
  { key: 'street', name: 'Street Map', description: 'Road networks and labels' },
  { key: 'dark', name: 'Dark Mode', description: 'Dark theme for night viewing' },
  { key: 'light', name: 'Light Mode', description: 'Clean minimal design' },
  { key: 'hybrid', name: 'Hybrid', description: 'Satellite with road overlay' },
  { key: 'osm', name: 'OpenStreetMap', description: 'Open source mapping' },
  { key: 'custom', name: 'Custom', description: 'Custom tile server' },
];

const FONT_FAMILIES = [
  'Inter-Regular',
  'Inter-Medium',
  'Inter-SemiBold',
  'Inter-Bold',
  'Arial',
  'Helvetica',
  'Times New Roman',
  'Georgia',
  'Courier New',
];

const THEME_COLORS = {
  blue: '#3B82F6',
  green: '#10B981',
  purple: '#8B5CF6',
  red: '#EF4444',
  orange: '#F59E0B',
  pink: '#EC4899',
  indigo: '#6366F1',
  teal: '#14B8A6',
  gray: '#6B7280',
  black: '#1F2937',
};

export const MapSettingsModal: React.FC<MapSettingsModalProps> = ({
  visible,
  onClose,
  settings,
  onSettingsUpdate,
  onExport,
  onReset,
}) => {
  const { theme } = useTheme();
  const [activeTab, setActiveTab] = useState<'general' | 'display' | 'coordinates' | 'theme' | 'performance' | 'security'>('general');
  const [tempSettings, setTempSettings] = useState<MapSettings>(settings);

  const handleSettingChange = useCallback((key: keyof MapSettings, value: any) => {
    setTempSettings(prev => ({ ...prev, [key]: value }));
  }, []);

  const handleNestedSettingChange = useCallback((parentKey: keyof MapSettings, childKey: string, value: any) => {
    setTempSettings(prev => ({
      ...prev,
      [parentKey]: {
        ...(prev[parentKey] as any || {}),
        [childKey]: value,
      },
    }));
  }, []);

  const handleSave = useCallback(() => {
    onSettingsUpdate(tempSettings);
    Alert.alert('Settings Saved', 'Map settings have been updated successfully.');
  }, [tempSettings, onSettingsUpdate]);

  const handleReset = useCallback(() => {
    Alert.alert(
      'Reset Settings',
      'This will reset all settings to default values. Continue?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Reset',
          style: 'destructive',
          onPress: () => {
            onReset();
            setTempSettings(settings);
          },
        },
      ]
    );
  }, [onReset, settings]);

  const renderGeneralTab = () => (
    <ScrollView style={styles.tabContent}>
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Basic Information</Text>
        
        <View style={styles.inputGroup}>
          <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Map Title</Text>
          <TextInput
            style={[styles.textInput, { backgroundColor: theme.colors.background, color: theme.colors.text }]}
            value={tempSettings.title}
            onChangeText={(text) => handleSettingChange('title', text)}
            placeholder="Enter map title..."
            placeholderTextColor={theme.colors.muted}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Description</Text>
          <TextInput
            style={[styles.textAreaInput, { backgroundColor: theme.colors.background, color: theme.colors.text }]}
            value={tempSettings.description}
            onChangeText={(text) => handleSettingChange('description', text)}
            placeholder="Enter map description..."
            placeholderTextColor={theme.colors.muted}
            multiline
            numberOfLines={3}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Author</Text>
          <TextInput
            style={[styles.textInput, { backgroundColor: theme.colors.background, color: theme.colors.text }]}
            value={tempSettings.author}
            onChangeText={(text) => handleSettingChange('author', text)}
            placeholder="Map author..."
            placeholderTextColor={theme.colors.muted}
          />
        </View>
      </View>

      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Default View</Text>
        
        <View style={styles.rowGroup}>
          <View style={styles.halfWidth}>
            <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Latitude</Text>
            <TextInput
              style={[styles.numberInput, { backgroundColor: theme.colors.background, color: theme.colors.text }]}
              value={tempSettings.defaultExtent?.latitude?.toString() || '0'}
              onChangeText={(text) => handleNestedSettingChange('defaultExtent', 'latitude', parseFloat(text) || 0)}
              keyboardType="numeric"
              placeholder="0.0"
              placeholderTextColor={theme.colors.muted}
            />
          </View>
          
          <View style={styles.halfWidth}>
            <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Longitude</Text>
            <TextInput
              style={[styles.numberInput, { backgroundColor: theme.colors.background, color: theme.colors.text }]}
              value={tempSettings.defaultExtent?.longitude?.toString() || '0'}
              onChangeText={(text) => handleNestedSettingChange('defaultExtent', 'longitude', parseFloat(text) || 0)}
              keyboardType="numeric"
              placeholder="0.0"
              placeholderTextColor={theme.colors.muted}
            />
          </View>
        </View>

        <View style={styles.rowGroup}>
          <View style={styles.halfWidth}>
            <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Min Zoom</Text>
            <TextInput
              style={[styles.numberInput, { backgroundColor: theme.colors.background, color: theme.colors.text }]}
              value={tempSettings.minZoom.toString()}
              onChangeText={(text) => handleSettingChange('minZoom', parseInt(text) || 1)}
              keyboardType="numeric"
              placeholder="1"
              placeholderTextColor={theme.colors.muted}
            />
          </View>
          
          <View style={styles.halfWidth}>
            <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Max Zoom</Text>
            <TextInput
              style={[styles.numberInput, { backgroundColor: theme.colors.background, color: theme.colors.text }]}
              value={tempSettings.maxZoom.toString()}
              onChangeText={(text) => handleSettingChange('maxZoom', parseInt(text) || 20)}
              keyboardType="numeric"
              placeholder="20"
              placeholderTextColor={theme.colors.muted}
            />
          </View>
        </View>

        <View style={styles.switchRow}>
          <Text style={[styles.switchLabel, { color: theme.colors.text }]}>Restrict Panning</Text>
          <Switch
            value={tempSettings.restrictPanning}
            onValueChange={(value) => handleSettingChange('restrictPanning', value)}
            trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
            thumbColor={Platform.OS === 'ios' ? undefined : 'white'}
          />
        </View>
      </View>
    </ScrollView>
  );

  const renderDisplayTab = () => (
    <ScrollView style={styles.tabContent}>
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Base Map</Text>
        
        <View style={styles.baseMapGrid}>
          {BASE_MAP_TYPES.map(baseMap => (
            <TouchableOpacity
              key={baseMap.key}
              style={[
                styles.baseMapOption,
                {
                  backgroundColor: tempSettings.baseMapType === baseMap.key ? theme.colors.primary : theme.colors.background,
                  borderColor: theme.colors.border,
                }
              ]}
              onPress={() => handleSettingChange('baseMapType', baseMap.key)}
            >
              <Map size={20} color={tempSettings.baseMapType === baseMap.key ? 'white' : theme.colors.text} />
              <Text style={[
                styles.baseMapName,
                { color: tempSettings.baseMapType === baseMap.key ? 'white' : theme.colors.text }
              ]}>
                {baseMap.name}
              </Text>
              <Text style={[
                styles.baseMapDesc,
                { color: tempSettings.baseMapType === baseMap.key ? 'rgba(255,255,255,0.8)' : theme.colors.muted }
              ]}>
                {baseMap.description}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {tempSettings.baseMapType === 'custom' && (
          <View style={styles.inputGroup}>
            <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Custom Base Map URL</Text>
            <TextInput
              style={[styles.textInput, { backgroundColor: theme.colors.background, color: theme.colors.text }]}
              value={tempSettings.customBaseMapUrl || ''}
              onChangeText={(text) => handleSettingChange('customBaseMapUrl', text)}
              placeholder="https://tile-server.com/{z}/{x}/{y}.png"
              placeholderTextColor={theme.colors.muted}
            />
          </View>
        )}
      </View>

      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>UI Elements</Text>
        
        {[
          { key: 'showScale', label: 'Scale Bar', icon: Ruler },
          { key: 'showNorthArrow', label: 'North Arrow', icon: Compass },
          { key: 'showZoomControls', label: 'Zoom Controls', icon: Zap },
          { key: 'showLayerPanel', label: 'Layer Panel', icon: Eye },
          { key: 'showToolbar', label: 'Toolbar', icon: Settings },
          { key: 'showMeasurementTools', label: 'Measurement Tools', icon: Ruler },
        ].map(item => (
          <View key={item.key} style={styles.switchRow}>
            <View style={styles.switchLabelContainer}>
              <item.icon size={16} color={theme.colors.text} />
              <Text style={[styles.switchLabel, { color: theme.colors.text }]}>{item.label}</Text>
            </View>
            <Switch
              value={tempSettings[item.key as keyof MapSettings] as boolean}
              onValueChange={(value) => handleSettingChange(item.key as keyof MapSettings, value)}
              trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
              thumbColor={Platform.OS === 'ios' ? undefined : 'white'}
            />
          </View>
        ))}
      </View>
    </ScrollView>
  );

  const renderCoordinatesTab = () => (
    <ScrollView style={styles.tabContent}>
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Coordinate System</Text>
        
        <View style={styles.coordinateSystemList}>
          {COORDINATE_SYSTEMS.map(crs => (
            <TouchableOpacity
              key={crs.code}
              style={[
                styles.coordinateSystemOption,
                {
                  backgroundColor: tempSettings.coordinateSystem === crs.code ? theme.colors.primary : theme.colors.background,
                  borderColor: theme.colors.border,
                }
              ]}
              onPress={() => handleSettingChange('coordinateSystem', crs.code)}
            >
              <Globe size={16} color={tempSettings.coordinateSystem === crs.code ? 'white' : theme.colors.text} />
              <View style={styles.crsInfo}>
                <Text style={[
                  styles.crsCode,
                  { color: tempSettings.coordinateSystem === crs.code ? 'white' : theme.colors.text }
                ]}>
                  {crs.code}
                </Text>
                <Text style={[
                  styles.crsName,
                  { color: tempSettings.coordinateSystem === crs.code ? 'white' : theme.colors.text }
                ]}>
                  {crs.name}
                </Text>
                <Text style={[
                  styles.crsDescription,
                  { color: tempSettings.coordinateSystem === crs.code ? 'rgba(255,255,255,0.8)' : theme.colors.muted }
                ]}>
                  {crs.description}
                </Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Display Format</Text>
        
        <View style={styles.switchRow}>
          <Text style={[styles.switchLabel, { color: theme.colors.text }]}>Show Coordinates</Text>
          <Switch
            value={tempSettings.displayCoordinates}
            onValueChange={(value) => handleSettingChange('displayCoordinates', value)}
            trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
            thumbColor={Platform.OS === 'ios' ? undefined : 'white'}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Coordinate Format</Text>
          <View style={styles.segmentedControl}>
            {[
              { key: 'decimal', label: 'Decimal' },
              { key: 'dms', label: 'DMS' },
              { key: 'utm', label: 'UTM' },
              { key: 'mgrs', label: 'MGRS' },
            ].map(format => (
              <TouchableOpacity
                key={format.key}
                style={[
                  styles.segmentedOption,
                  {
                    backgroundColor: tempSettings.coordinateFormat === format.key ? theme.colors.primary : theme.colors.background,
                    borderColor: theme.colors.border,
                  }
                ]}
                onPress={() => handleSettingChange('coordinateFormat', format.key)}
              >
                <Text style={[
                  styles.segmentedText,
                  { color: tempSettings.coordinateFormat === format.key ? 'white' : theme.colors.text }
                ]}>
                  {format.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <View style={styles.inputGroup}>
          <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Decimal Precision</Text>
          <TextInput
            style={[styles.numberInput, { backgroundColor: theme.colors.background, color: theme.colors.text }]}
            value={tempSettings.coordinatePrecision.toString()}
            onChangeText={(text) => handleSettingChange('coordinatePrecision', parseInt(text) || 4)}
            keyboardType="numeric"
            placeholder="4"
            placeholderTextColor={theme.colors.muted}
          />
        </View>
      </View>
    </ScrollView>
  );

  const renderThemeTab = () => (
    <ScrollView style={styles.tabContent}>
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Color Scheme</Text>
        
        <View style={styles.colorGrid}>
          {Object.entries(THEME_COLORS).map(([name, color]) => (
            <TouchableOpacity
              key={name}
              style={[
                styles.colorOption,
                {
                  backgroundColor: color,
                  borderWidth: tempSettings.primaryColor === color ? 3 : 1,
                  borderColor: tempSettings.primaryColor === color ? theme.colors.text : theme.colors.border,
                }
              ]}
              onPress={() => handleSettingChange('primaryColor', color)}
            >
              {tempSettings.primaryColor === color && (
                <View style={styles.colorCheck}>
                  <Text style={styles.colorCheckText}>✓</Text>
                </View>
              )}
            </TouchableOpacity>
          ))}
        </View>
      </View>

      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Typography</Text>
        
        <View style={styles.inputGroup}>
          <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Font Family</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.fontScroll}>
            {FONT_FAMILIES.map(font => (
              <TouchableOpacity
                key={font}
                style={[
                  styles.fontOption,
                  {
                    backgroundColor: tempSettings.fontFamily === font ? theme.colors.primary : theme.colors.background,
                    borderColor: theme.colors.border,
                  }
                ]}
                onPress={() => handleSettingChange('fontFamily', font)}
              >
                <Text style={[
                  styles.fontText,
                  {
                    color: tempSettings.fontFamily === font ? 'white' : theme.colors.text,
                    fontFamily: font,
                  }
                ]}>
                  {font.replace('-', ' ')}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        <View style={styles.inputGroup}>
          <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Font Size</Text>
          <TextInput
            style={[styles.numberInput, { backgroundColor: theme.colors.background, color: theme.colors.text }]}
            value={tempSettings.fontSize.toString()}
            onChangeText={(text) => handleSettingChange('fontSize', parseInt(text) || 14)}
            keyboardType="numeric"
            placeholder="14"
            placeholderTextColor={theme.colors.muted}
          />
        </View>
      </View>

      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Branding</Text>
        
        <View style={styles.inputGroup}>
          <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Logo URL</Text>
          <TextInput
            style={[styles.textInput, { backgroundColor: theme.colors.background, color: theme.colors.text }]}
            value={tempSettings.logoUrl || ''}
            onChangeText={(text) => handleSettingChange('logoUrl', text)}
            placeholder="https://example.com/logo.png"
            placeholderTextColor={theme.colors.muted}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Logo Position</Text>
          <View style={styles.logoPositionGrid}>
            {[
              { key: 'top-left', label: 'Top Left' },
              { key: 'top-right', label: 'Top Right' },
              { key: 'bottom-left', label: 'Bottom Left' },
              { key: 'bottom-right', label: 'Bottom Right' },
            ].map(position => (
              <TouchableOpacity
                key={position.key}
                style={[
                  styles.positionOption,
                  {
                    backgroundColor: tempSettings.logoPosition === position.key ? theme.colors.primary : theme.colors.background,
                    borderColor: theme.colors.border,
                  }
                ]}
                onPress={() => handleSettingChange('logoPosition', position.key)}
              >
                <Text style={[
                  styles.positionText,
                  { color: tempSettings.logoPosition === position.key ? 'white' : theme.colors.text }
                ]}>
                  {position.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </View>
    </ScrollView>
  );

  const renderPerformanceTab = () => (
    <ScrollView style={styles.tabContent}>
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Rendering</Text>
        
        <View style={styles.switchRow}>
          <View style={styles.switchLabelContainer}>
            <Zap size={16} color={theme.colors.text} />
            <Text style={[styles.switchLabel, { color: theme.colors.text }]}>Enable WebGL</Text>
          </View>
          <Switch
            value={tempSettings.enableWebGL}
            onValueChange={(value) => handleSettingChange('enableWebGL', value)}
            trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
            thumbColor={Platform.OS === 'ios' ? undefined : 'white'}
          />
        </View>

        <View style={styles.switchRow}>
          <View style={styles.switchLabelContainer}>
            <Globe size={16} color={theme.colors.text} />
            <Text style={[styles.switchLabel, { color: theme.colors.text }]}>Enable 3D</Text>
          </View>
          <Switch
            value={tempSettings.enable3D}
            onValueChange={(value) => handleSettingChange('enable3D', value)}
            trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
            thumbColor={Platform.OS === 'ios' ? undefined : 'white'}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Render Quality</Text>
          <View style={styles.segmentedControl}>
            {[
              { key: 'low', label: 'Low' },
              { key: 'medium', label: 'Medium' },
              { key: 'high', label: 'High' },
            ].map(quality => (
              <TouchableOpacity
                key={quality.key}
                style={[
                  styles.segmentedOption,
                  {
                    backgroundColor: tempSettings.renderQuality === quality.key ? theme.colors.primary : theme.colors.background,
                    borderColor: theme.colors.border,
                  }
                ]}
                onPress={() => handleSettingChange('renderQuality', quality.key)}
              >
                <Text style={[
                  styles.segmentedText,
                  { color: tempSettings.renderQuality === quality.key ? 'white' : theme.colors.text }
                ]}>
                  {quality.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <View style={styles.inputGroup}>
          <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Max Features</Text>
          <TextInput
            style={[styles.numberInput, { backgroundColor: theme.colors.background, color: theme.colors.text }]}
            value={tempSettings.maxFeatures.toString()}
            onChangeText={(text) => handleSettingChange('maxFeatures', parseInt(text) || 1000)}
            keyboardType="numeric"
            placeholder="1000"
            placeholderTextColor={theme.colors.muted}
          />
        </View>

        <View style={styles.switchRow}>
          <Text style={[styles.switchLabel, { color: theme.colors.text }]}>Enable Clustering</Text>
          <Switch
            value={tempSettings.clustering}
            onValueChange={(value) => handleSettingChange('clustering', value)}
            trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
            thumbColor={Platform.OS === 'ios' ? undefined : 'white'}
          />
        </View>
      </View>
    </ScrollView>
  );

  const renderSecurityTab = () => (
    <ScrollView style={styles.tabContent}>
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Access Control</Text>
        
        <View style={styles.switchRow}>
          <View style={styles.switchLabelContainer}>
            <Shield size={16} color={theme.colors.text} />
            <Text style={[styles.switchLabel, { color: theme.colors.text }]}>Require Authentication</Text>
          </View>
          <Switch
            value={tempSettings.requireAuthentication}
            onValueChange={(value) => handleSettingChange('requireAuthentication', value)}
            trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
            thumbColor={Platform.OS === 'ios' ? undefined : 'white'}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Expiration Date</Text>
          <TextInput
            style={[styles.textInput, { backgroundColor: theme.colors.background, color: theme.colors.text }]}
            value={tempSettings.expirationDate || ''}
            onChangeText={(text) => handleSettingChange('expirationDate', text)}
            placeholder="YYYY-MM-DD (optional)"
            placeholderTextColor={theme.colors.muted}
          />
        </View>
      </View>

      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Permissions</Text>
        
        {[
          { key: 'allowDownload', label: 'Allow Download' },
          { key: 'allowPrint', label: 'Allow Print' },
          { key: 'allowShare', label: 'Allow Share' },
        ].map(permission => (
          <View key={permission.key} style={styles.switchRow}>
            <Text style={[styles.switchLabel, { color: theme.colors.text }]}>{permission.label}</Text>
            <Switch
              value={tempSettings[permission.key as keyof MapSettings] as boolean}
              onValueChange={(value) => handleSettingChange(permission.key as keyof MapSettings, value)}
              trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
              thumbColor={Platform.OS === 'ios' ? undefined : 'white'}
            />
          </View>
        ))}
      </View>
    </ScrollView>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={[styles.modalContainer, { backgroundColor: theme.colors.background }]}>
        {/* Header */}
        <View style={[styles.modalHeader, { backgroundColor: theme.colors.card, borderBottomColor: theme.colors.border }]}>
          <Text style={[styles.modalTitle, { color: theme.colors.text }]}>Map Settings</Text>
          <View style={styles.headerActions}>
            <TouchableOpacity
              style={[styles.headerButton, { backgroundColor: theme.colors.muted }]}
              onPress={handleReset}
            >
              <RotateCcw size={18} color="white" />
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.headerButton, { backgroundColor: theme.colors.primary }]}
              onPress={handleSave}
            >
              <Save size={18} color="white" />
            </TouchableOpacity>
            <TouchableOpacity onPress={onClose}>
              <X size={24} color={theme.colors.text} />
            </TouchableOpacity>
          </View>
        </View>

        {/* Tabs */}
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={[styles.tabsContainer, { backgroundColor: theme.colors.card, borderBottomColor: theme.colors.border }]}>
          {[
            { key: 'general', icon: Settings, label: 'General' },
            { key: 'display', icon: Monitor, label: 'Display' },
            { key: 'coordinates', icon: Globe, label: 'Coordinates' },
            { key: 'theme', icon: Palette, label: 'Theme' },
            { key: 'performance', icon: Zap, label: 'Performance' },
            { key: 'security', icon: Shield, label: 'Security' },
          ].map(tab => (
            <TouchableOpacity
              key={tab.key}
              style={[
                styles.tab,
                { backgroundColor: activeTab === tab.key ? theme.colors.primary : 'transparent' }
              ]}
              onPress={() => setActiveTab(tab.key as any)}
            >
              <tab.icon
                size={18}
                color={activeTab === tab.key ? 'white' : theme.colors.text}
              />
              <Text style={[
                styles.tabText,
                { color: activeTab === tab.key ? 'white' : theme.colors.text }
              ]}>
                {tab.label}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>

        {/* Content */}
        {activeTab === 'general' && renderGeneralTab()}
        {activeTab === 'display' && renderDisplayTab()}
        {activeTab === 'coordinates' && renderCoordinatesTab()}
        {activeTab === 'theme' && renderThemeTab()}
        {activeTab === 'performance' && renderPerformanceTab()}
        {activeTab === 'security' && renderSecurityTab()}
      </View>
    </Modal>
  );
};

// Styles would continue here - this is a partial implementation due to length constraints
const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  headerButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
  },
  tabsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    borderBottomWidth: 1,
  },
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginRight: 8,
    borderRadius: 8,
    gap: 6,
  },
  tabText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    whiteSpace: 'nowrap',
  },
  tabContent: {
    flex: 1,
  },
  section: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 6,
  },
  textInput: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  textAreaInput: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    textAlignVertical: 'top',
  },
  numberInput: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  rowGroup: {
    flexDirection: 'row',
    gap: 12,
  },
  halfWidth: {
    flex: 1,
  },
  switchRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
  },
  switchLabel: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
  },
  switchLabelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  baseMapGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  baseMapOption: {
    width: '48%',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
  },
  baseMapName: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    marginTop: 6,
    textAlign: 'center',
  },
  baseMapDesc: {
    fontSize: 10,
    fontFamily: 'Inter-Regular',
    marginTop: 2,
    textAlign: 'center',
  },
  coordinateSystemList: {
    gap: 12,
  },
  coordinateSystemOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    gap: 12,
  },
  crsInfo: {
    flex: 1,
  },
  crsCode: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
  },
  crsName: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    marginTop: 2,
  },
  crsDescription: {
    fontSize: 11,
    fontFamily: 'Inter-Regular',
    marginTop: 2,
  },
  segmentedControl: {
    flexDirection: 'row',
    borderRadius: 6,
    overflow: 'hidden',
  },
  segmentedOption: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  segmentedText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },
  colorGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  colorOption: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  colorCheck: {
    backgroundColor: 'rgba(255,255,255,0.9)',
    borderRadius: 12,
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  colorCheckText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#000',
  },
  fontScroll: {
    flexDirection: 'row',
  },
  fontOption: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    borderWidth: 1,
    marginRight: 8,
  },
  fontText: {
    fontSize: 12,
  },
  logoPositionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  positionOption: {
    width: '48%',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    borderWidth: 1,
    alignItems: 'center',
  },
  positionText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },
});
