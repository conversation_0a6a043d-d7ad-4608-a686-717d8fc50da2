import { useState, useEffect, useCallback, useRef } from 'react';

interface StorySlide {
  id: string;
  title: string;
  description: string;
  content: string;
  duration: number;
  visibleLayers: string[];
  mapExtent?: {
    latitude: number;
    longitude: number;
    latitudeDelta: number;
    longitudeDelta: number;
  };
  baseMap: string;
  interactive: boolean;
  autoAdvance: boolean;
}

interface MapStory {
  id: string;
  title: string;
  description: string;
  author: string;
  slides: StorySlide[];
  settings: {
    showProgress: boolean;
    showControls: boolean;
    allowSkipping: boolean;
    loop: boolean;
    theme: 'light' | 'dark' | 'auto';
    layout: 'sidebar' | 'overlay' | 'bottom';
  };
}

interface UseStoryPlaybackOptions {
  onSlideChange?: (slideIndex: number, slide: StorySlide) => void;
  onStoryComplete?: () => void;
  onMapUpdate?: (mapExtent: any, visibleLayers: string[], baseMap: string) => void;
  onShowNotification?: (type: 'success' | 'error' | 'warning' | 'info', message: string) => void;
}

interface UseStoryPlaybackReturn {
  // State
  currentStory: MapStory | null;
  currentSlideIndex: number;
  isPlaying: boolean;
  isStoryMode: boolean;
  slideProgress: number; // 0-100
  elapsedTime: number; // seconds
  totalDuration: number; // seconds
  
  // Actions
  startStory: (story: MapStory, slideIndex?: number) => void;
  stopStory: () => void;
  playStory: () => void;
  pauseStory: () => void;
  nextSlide: () => void;
  previousSlide: () => void;
  goToSlide: (slideIndex: number) => void;
  
  // Utilities
  getCurrentSlide: () => StorySlide | null;
  canGoNext: () => boolean;
  canGoPrevious: () => boolean;
  getStoryProgress: () => number; // Overall story progress 0-100
}

export function useStoryPlayback(options: UseStoryPlaybackOptions = {}): UseStoryPlaybackReturn {
  const {
    onSlideChange,
    onStoryComplete,
    onMapUpdate,
    onShowNotification,
  } = options;

  // State
  const [currentStory, setCurrentStory] = useState<MapStory | null>(null);
  const [currentSlideIndex, setCurrentSlideIndex] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isStoryMode, setIsStoryMode] = useState(false);
  const [slideProgress, setSlideProgress] = useState(0);
  const [elapsedTime, setElapsedTime] = useState(0);

  // Refs
  const playbackTimer = useRef<NodeJS.Timeout | null>(null);
  const slideStartTime = useRef<number>(0);
  const totalElapsedTime = useRef<number>(0);

  // Computed values
  const totalDuration = currentStory?.slides.reduce((total, slide) => total + slide.duration, 0) || 0;
  const currentSlide = currentStory?.slides[currentSlideIndex] || null;

  // Clear timer on unmount
  useEffect(() => {
    return () => {
      if (playbackTimer.current) {
        clearInterval(playbackTimer.current);
      }
    };
  }, []);

  // Update map when slide changes
  useEffect(() => {
    if (currentSlide && isStoryMode && onMapUpdate) {
      onMapUpdate(
        currentSlide.mapExtent,
        currentSlide.visibleLayers,
        currentSlide.baseMap
      );
    }
  }, [currentSlideIndex, currentSlide, isStoryMode, onMapUpdate]);

  // Playback timer
  useEffect(() => {
    if (isPlaying && currentSlide) {
      slideStartTime.current = Date.now();
      
      playbackTimer.current = setInterval(() => {
        const now = Date.now();
        const slideElapsed = (now - slideStartTime.current) / 1000;
        const progress = Math.min((slideElapsed / currentSlide.duration) * 100, 100);
        
        setSlideProgress(progress);
        setElapsedTime(totalElapsedTime.current + slideElapsed);

        // Auto-advance to next slide
        if (slideElapsed >= currentSlide.duration && currentSlide.autoAdvance) {
          if (currentSlideIndex < (currentStory?.slides.length || 0) - 1) {
            nextSlide();
          } else {
            // Story completed
            handleStoryComplete();
          }
        }
      }, 100);

      return () => {
        if (playbackTimer.current) {
          clearInterval(playbackTimer.current);
        }
      };
    } else {
      if (playbackTimer.current) {
        clearInterval(playbackTimer.current);
        playbackTimer.current = null;
      }
    }
  }, [isPlaying, currentSlide, currentSlideIndex, currentStory]);

  const handleStoryComplete = useCallback(() => {
    if (currentStory?.settings.loop) {
      // Restart from beginning
      goToSlide(0);
      onShowNotification?.('info', 'Story restarted');
    } else {
      // Stop playback
      setIsPlaying(false);
      onStoryComplete?.();
      onShowNotification?.('success', 'Story completed');
    }
  }, [currentStory, onStoryComplete, onShowNotification]);

  const startStory = useCallback((story: MapStory, slideIndex: number = 0) => {
    setCurrentStory(story);
    setCurrentSlideIndex(slideIndex);
    setIsStoryMode(true);
    setIsPlaying(false);
    setSlideProgress(0);
    setElapsedTime(0);
    totalElapsedTime.current = 0;
    
    // Calculate elapsed time up to the starting slide
    for (let i = 0; i < slideIndex; i++) {
      totalElapsedTime.current += story.slides[i]?.duration || 0;
    }
    
    setElapsedTime(totalElapsedTime.current);
    
    onSlideChange?.(slideIndex, story.slides[slideIndex]);
    onShowNotification?.('info', `Started story: ${story.title}`);
  }, [onSlideChange, onShowNotification]);

  const stopStory = useCallback(() => {
    setIsPlaying(false);
    setIsStoryMode(false);
    setCurrentStory(null);
    setCurrentSlideIndex(0);
    setSlideProgress(0);
    setElapsedTime(0);
    totalElapsedTime.current = 0;
    
    if (playbackTimer.current) {
      clearInterval(playbackTimer.current);
      playbackTimer.current = null;
    }
    
    onShowNotification?.('info', 'Story stopped');
  }, [onShowNotification]);

  const playStory = useCallback(() => {
    if (!currentStory || !currentSlide) return;
    setIsPlaying(true);
    onShowNotification?.('info', 'Story playing');
  }, [currentStory, currentSlide, onShowNotification]);

  const pauseStory = useCallback(() => {
    setIsPlaying(false);
    
    // Update total elapsed time
    if (currentSlide) {
      const slideElapsed = (Date.now() - slideStartTime.current) / 1000;
      totalElapsedTime.current += slideElapsed;
    }
    
    onShowNotification?.('info', 'Story paused');
  }, [currentSlide, onShowNotification]);

  const nextSlide = useCallback(() => {
    if (!currentStory || currentSlideIndex >= currentStory.slides.length - 1) return;
    
    // Update total elapsed time
    if (currentSlide) {
      const slideElapsed = isPlaying ? (Date.now() - slideStartTime.current) / 1000 : 0;
      totalElapsedTime.current += Math.min(slideElapsed, currentSlide.duration);
    }
    
    const newIndex = currentSlideIndex + 1;
    setCurrentSlideIndex(newIndex);
    setSlideProgress(0);
    setElapsedTime(totalElapsedTime.current);
    
    onSlideChange?.(newIndex, currentStory.slides[newIndex]);
  }, [currentStory, currentSlideIndex, currentSlide, isPlaying, onSlideChange]);

  const previousSlide = useCallback(() => {
    if (!currentStory || currentSlideIndex <= 0) return;
    
    const newIndex = currentSlideIndex - 1;
    
    // Recalculate total elapsed time
    totalElapsedTime.current = 0;
    for (let i = 0; i < newIndex; i++) {
      totalElapsedTime.current += currentStory.slides[i]?.duration || 0;
    }
    
    setCurrentSlideIndex(newIndex);
    setSlideProgress(0);
    setElapsedTime(totalElapsedTime.current);
    
    onSlideChange?.(newIndex, currentStory.slides[newIndex]);
  }, [currentStory, currentSlideIndex, onSlideChange]);

  const goToSlide = useCallback((slideIndex: number) => {
    if (!currentStory || slideIndex < 0 || slideIndex >= currentStory.slides.length) return;
    
    // Recalculate total elapsed time
    totalElapsedTime.current = 0;
    for (let i = 0; i < slideIndex; i++) {
      totalElapsedTime.current += currentStory.slides[i]?.duration || 0;
    }
    
    setCurrentSlideIndex(slideIndex);
    setSlideProgress(0);
    setElapsedTime(totalElapsedTime.current);
    
    onSlideChange?.(slideIndex, currentStory.slides[slideIndex]);
  }, [currentStory, onSlideChange]);

  const getCurrentSlide = useCallback(() => {
    return currentSlide;
  }, [currentSlide]);

  const canGoNext = useCallback(() => {
    return currentStory ? currentSlideIndex < currentStory.slides.length - 1 : false;
  }, [currentStory, currentSlideIndex]);

  const canGoPrevious = useCallback(() => {
    return currentSlideIndex > 0;
  }, [currentSlideIndex]);

  const getStoryProgress = useCallback(() => {
    if (!currentStory || totalDuration === 0) return 0;
    return Math.min((elapsedTime / totalDuration) * 100, 100);
  }, [currentStory, totalDuration, elapsedTime]);

  return {
    // State
    currentStory,
    currentSlideIndex,
    isPlaying,
    isStoryMode,
    slideProgress,
    elapsedTime,
    totalDuration,
    
    // Actions
    startStory,
    stopStory,
    playStory,
    pauseStory,
    nextSlide,
    previousSlide,
    goToSlide,
    
    // Utilities
    getCurrentSlide,
    canGoNext,
    canGoPrevious,
    getStoryProgress,
  };
}
