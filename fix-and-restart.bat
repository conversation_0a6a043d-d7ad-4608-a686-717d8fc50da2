@echo off
echo 🔧 FieldSyncPro - Camera API & Form Navigation Fix
echo ===============================================
echo.

echo 📋 Applied fixes:
echo    ✅ Fixed Camera.Constants.Type issue in VideoRecorder
echo    ✅ Fixed CameraView.requestCameraPermissionsAsync in MultiPhotosPicker  
echo    ✅ Added Camera import for correct permission API
echo    ✅ Added null checking for form questions
echo    ✅ Enhanced navigation error handling
echo    ✅ Improved schema validation
echo.

echo 🔍 Running compatibility check...
node fix-camera-api.js
echo.

echo 🛠️  Cleaning up and restarting...
echo.

REM Clear npm cache and reinstall if needed
if exist node_modules\expo-camera (
    echo 📦 Expo Camera module found, checking integrity...
) else (
    echo ❌ Missing expo-camera, reinstalling...
    npm install expo-camera expo-av expo-image-picker
)

REM Clear Metro cache
echo 🧹 Clearing Metro cache...
npx expo start --clear

REM If expo start fails, try alternative
if %ERRORLEVEL% neq 0 (
    echo ⚠️  Expo start failed, trying npm start...
    npm start
)

echo.
echo ✨ Development server should now be running!
echo 🌐 Open http://localhost:8085/enhanced-form-demo to test
echo 📱 Navigate to page 2 to test camera/media functionality
echo.
pause
