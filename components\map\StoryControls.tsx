import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Dimensions,
} from 'react-native';
import { useTheme } from '@/providers/ThemeProvider';
import {
  Play,
  Pause,
  SkipBack,
  SkipForward,
  Square,
  ChevronLeft,
  ChevronRight,
  Settings,
  X,
  Clock,
  Eye,
  Maximize2,
} from 'lucide-react-native';

interface StorySlide {
  id: string;
  title: string;
  description: string;
  content: string;
  duration: number;
  visibleLayers: string[];
  mapExtent?: {
    latitude: number;
    longitude: number;
    latitudeDelta: number;
    longitudeDelta: number;
  };
  baseMap: string;
  interactive: boolean;
  autoAdvance: boolean;
}

interface MapStory {
  id: string;
  title: string;
  description: string;
  author: string;
  slides: StorySlide[];
  settings: {
    showProgress: boolean;
    showControls: boolean;
    allowSkipping: boolean;
    loop: boolean;
    theme: 'light' | 'dark' | 'auto';
    layout: 'sidebar' | 'overlay' | 'bottom';
  };
}

interface StoryControlsProps {
  story: MapStory;
  currentSlideIndex: number;
  isPlaying: boolean;
  isVisible: boolean;
  slideProgress: number; // 0-100
  totalDuration: number; // in seconds
  elapsedTime: number; // in seconds
  onPlay: () => void;
  onPause: () => void;
  onStop: () => void;
  onPrevious: () => void;
  onNext: () => void;
  onSlideSelect: (slideIndex: number) => void;
  onSettingsOpen: () => void;
  onFullscreen: () => void;
  onExit: () => void;
}

const { width: screenWidth } = Dimensions.get('window');

export const StoryControls: React.FC<StoryControlsProps> = ({
  story,
  currentSlideIndex,
  isPlaying,
  isVisible,
  slideProgress,
  totalDuration,
  elapsedTime,
  onPlay,
  onPause,
  onStop,
  onPrevious,
  onNext,
  onSlideSelect,
  onSettingsOpen,
  onFullscreen,
  onExit,
}) => {
  const { theme } = useTheme();
  const [fadeAnim] = useState(new Animated.Value(isVisible ? 1 : 0));
  const [showSlideSelector, setShowSlideSelector] = useState(false);

  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: isVisible ? 1 : 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, [isVisible, fadeAnim]);

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const currentSlide = story.slides[currentSlideIndex];
  const canGoPrevious = currentSlideIndex > 0;
  const canGoNext = currentSlideIndex < story.slides.length - 1;

  const renderProgressBar = () => (
    <View style={styles.progressContainer}>
      <View style={[styles.progressTrack, { backgroundColor: theme.colors.border }]}>
        <View
          style={[
            styles.progressFill,
            {
              backgroundColor: theme.colors.primary,
              width: `${slideProgress}%`,
            }
          ]}
        />
      </View>
      <Text style={[styles.progressText, { color: theme.colors.text }]}>
        {currentSlideIndex + 1} / {story.slides.length}
      </Text>
    </View>
  );

  const renderMainControls = () => (
    <View style={styles.mainControls}>
      {/* Previous Button */}
      <TouchableOpacity
        style={[
          styles.controlButton,
          {
            backgroundColor: canGoPrevious ? theme.colors.background : theme.colors.muted,
            opacity: canGoPrevious ? 1 : 0.5,
          }
        ]}
        onPress={onPrevious}
        disabled={!canGoPrevious}
      >
        <SkipBack size={20} color={theme.colors.text} />
      </TouchableOpacity>

      {/* Play/Pause Button */}
      <TouchableOpacity
        style={[styles.playButton, { backgroundColor: theme.colors.primary }]}
        onPress={isPlaying ? onPause : onPlay}
      >
        {isPlaying ? (
          <Pause size={24} color="white" />
        ) : (
          <Play size={24} color="white" />
        )}
      </TouchableOpacity>

      {/* Next Button */}
      <TouchableOpacity
        style={[
          styles.controlButton,
          {
            backgroundColor: canGoNext ? theme.colors.background : theme.colors.muted,
            opacity: canGoNext ? 1 : 0.5,
          }
        ]}
        onPress={onNext}
        disabled={!canGoNext}
      >
        <SkipForward size={20} color={theme.colors.text} />
      </TouchableOpacity>

      {/* Stop Button */}
      <TouchableOpacity
        style={[styles.controlButton, { backgroundColor: theme.colors.background }]}
        onPress={onStop}
      >
        <Square size={20} color={theme.colors.text} />
      </TouchableOpacity>
    </View>
  );

  const renderSlideInfo = () => (
    <View style={styles.slideInfo}>
      <TouchableOpacity
        style={styles.slideSelector}
        onPress={() => setShowSlideSelector(!showSlideSelector)}
      >
        <Text style={[styles.slideTitle, { color: theme.colors.text }]} numberOfLines={1}>
          {currentSlide?.title || 'Untitled Slide'}
        </Text>
        <ChevronRight size={16} color={theme.colors.muted} />
      </TouchableOpacity>
      
      <View style={styles.timeInfo}>
        <Clock size={14} color={theme.colors.muted} />
        <Text style={[styles.timeText, { color: theme.colors.muted }]}>
          {formatTime(elapsedTime)} / {formatTime(totalDuration)}
        </Text>
      </View>
    </View>
  );

  const renderSecondaryControls = () => (
    <View style={styles.secondaryControls}>
      <TouchableOpacity
        style={[styles.secondaryButton, { backgroundColor: theme.colors.background }]}
        onPress={onSettingsOpen}
      >
        <Settings size={18} color={theme.colors.text} />
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.secondaryButton, { backgroundColor: theme.colors.background }]}
        onPress={onFullscreen}
      >
        <Maximize2 size={18} color={theme.colors.text} />
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.secondaryButton, { backgroundColor: theme.colors.background }]}
        onPress={onExit}
      >
        <X size={18} color={theme.colors.text} />
      </TouchableOpacity>
    </View>
  );

  const renderSlideSelector = () => {
    if (!showSlideSelector) return null;

    return (
      <View style={[styles.slideSelectorDropdown, { backgroundColor: theme.colors.card }]}>
        {story.slides.map((slide, index) => (
          <TouchableOpacity
            key={slide.id}
            style={[
              styles.slideSelectorItem,
              {
                backgroundColor: index === currentSlideIndex ? theme.colors.primary : 'transparent',
                borderBottomColor: theme.colors.border,
              }
            ]}
            onPress={() => {
              onSlideSelect(index);
              setShowSlideSelector(false);
            }}
          >
            <Text style={[
              styles.slideSelectorNumber,
              { color: index === currentSlideIndex ? 'white' : theme.colors.muted }
            ]}>
              {index + 1}
            </Text>
            <Text style={[
              styles.slideSelectorTitle,
              { color: index === currentSlideIndex ? 'white' : theme.colors.text }
            ]} numberOfLines={1}>
              {slide.title}
            </Text>
            <Text style={[
              styles.slideSelectorDuration,
              { color: index === currentSlideIndex ? 'rgba(255,255,255,0.8)' : theme.colors.muted }
            ]}>
              {slide.duration}s
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  if (!story || !isVisible) return null;

  return (
    <Animated.View
      style={[
        styles.container,
        {
          backgroundColor: theme.colors.card,
          borderBottomColor: theme.colors.border,
          opacity: fadeAnim,
        }
      ]}
    >
      {renderProgressBar()}
      
      <View style={styles.controlsRow}>
        {renderMainControls()}
        {renderSlideInfo()}
        {renderSecondaryControls()}
      </View>

      {renderSlideSelector()}
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderBottomWidth: 1,
    paddingHorizontal: 16,
    paddingVertical: 12,
    position: 'relative',
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 12,
  },
  progressTrack: {
    flex: 1,
    height: 4,
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
  progressText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    minWidth: 40,
    textAlign: 'right',
  },
  controlsRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  mainControls: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  controlButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  playButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 8,
  },
  slideInfo: {
    flex: 1,
    marginHorizontal: 16,
  },
  slideSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  slideTitle: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    flex: 1,
  },
  timeInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  timeText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
  },
  secondaryControls: {
    flexDirection: 'row',
    gap: 8,
  },
  secondaryButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
  },
  slideSelectorDropdown: {
    position: 'absolute',
    top: '100%',
    left: 16,
    right: 16,
    maxHeight: 200,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    zIndex: 1000,
  },
  slideSelectorItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 1,
    gap: 12,
  },
  slideSelectorNumber: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    width: 24,
    textAlign: 'center',
  },
  slideSelectorTitle: {
    flex: 1,
    fontSize: 13,
    fontFamily: 'Inter-Medium',
  },
  slideSelectorDuration: {
    fontSize: 11,
    fontFamily: 'Inter-Regular',
  },
});
