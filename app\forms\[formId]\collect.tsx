import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router, useLocalSearchParams } from 'expo-router';
import { useTheme } from '@/hooks/useTheme';
import { Form, Submission } from '@/types';
import FormRenderer from '@/components/forms/FormRenderer';
import { TouchableOpacity } from 'react-native';
import { ArrowLeft } from 'lucide-react-native';

export default function FormCollectionScreen() {
  const { theme } = useTheme();
  const { formId } = useLocalSearchParams<{ formId: string }>();
  const [form, setForm] = useState<Form | null>(null);
  const [initialData, setInitialData] = useState<Record<string, any>>({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadForm();
  }, [formId]);

  const loadForm = async () => {
    if (!formId) {
      Alert.alert('Error', 'No form ID provided', [
        { text: 'OK', onPress: () => router.back() }
      ]);
      return;
    }

    try {
      setLoading(true);
      
      // Load form from storage
      let loadedForm: Form | null = null;
      
      if (Platform.OS === 'web') {
        const stored = localStorage.getItem('fieldsync_forms');
        if (stored) {
          const forms = JSON.parse(stored);
          loadedForm = forms.find((f: Form) => f.id === formId);
        }
      }

      if (!loadedForm) {
        Alert.alert('Form Not Found', 'The requested form could not be found.', [
          { text: 'OK', onPress: () => router.back() }
        ]);
        return;
      }

      setForm(loadedForm);

      // Load any existing draft data
      if (Platform.OS === 'web') {
        const drafts = JSON.parse(localStorage.getItem('fieldsync_drafts') || '[]');
        const existingDraft = drafts.find((d: any) => d.formId === formId);
        if (existingDraft) {
          setInitialData(existingDraft.data || {});
        }
      }
    } catch (error) {
      console.error('Error loading form:', error);
      Alert.alert('Error', 'Failed to load form', [
        { text: 'OK', onPress: () => router.back() }
      ]);
    } finally {
      setLoading(false);
    }
  };

  const handleSaveDraft = async (data: Record<string, any>, isDraft: boolean) => {
    try {
      if (Platform.OS === 'web') {
        const drafts = JSON.parse(localStorage.getItem('fieldsync_drafts') || '[]');
        const existingIndex = drafts.findIndex((d: any) => d.formId === formId);
        
        const draft = {
          id: `draft_${formId}_${Date.now()}`,
          formId,
          formName: form?.name || 'Unknown Form',
          data,
          savedAt: Date.now(),
          isDraft: true,
        };

        if (existingIndex >= 0) {
          drafts[existingIndex] = draft;
        } else {
          drafts.push(draft);
        }

        localStorage.setItem('fieldsync_drafts', JSON.stringify(drafts));
      }

      console.log('Draft saved:', { formId, data, isDraft });
    } catch (error) {
      console.error('Error saving draft:', error);
      throw new Error('Failed to save draft');
    }
  };

  const handleSubmit = async (submission: Partial<Submission>) => {
    try {
      const fullSubmission: Submission = {
        id: `submission_${Date.now()}`,
        formId: formId!,
        projectId: form?.projectId || 'unknown',
        userId: 'current_user', // Would come from auth context
        status: 'completed',
        startedAt: Date.now() - 300000, // Started 5 minutes ago
        completedAt: Date.now(),
        data: submission.data || {},
        location: submission.location,
        media: [],
        ...submission,
      };

      if (Platform.OS === 'web') {
        const submissions = JSON.parse(localStorage.getItem('fieldsync_submissions') || '[]');
        submissions.push(fullSubmission);
        localStorage.setItem('fieldsync_submissions', JSON.stringify(submissions));

        // Remove draft if it exists
        const drafts = JSON.parse(localStorage.getItem('fieldsync_drafts') || '[]');
        const filteredDrafts = drafts.filter((d: any) => d.formId !== formId);
        localStorage.setItem('fieldsync_drafts', JSON.stringify(filteredDrafts));
      }

      console.log('Form submitted:', fullSubmission);
      
      // Navigate back to forms list or show success screen
      router.replace('/forms');
    } catch (error) {
      console.error('Error submitting form:', error);
      throw new Error('Failed to submit form');
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: theme.colors.text }]}>
            Loading form...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!form) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <View style={styles.errorContainer}>
          <Text style={[styles.errorTitle, { color: theme.colors.text }]}>
            Form Not Found
          </Text>
          <Text style={[styles.errorMessage, { color: theme.colors.muted }]}>
            The requested form could not be loaded.
          </Text>
          <TouchableOpacity
            style={[styles.backButton, { backgroundColor: theme.colors.primary }]}
            onPress={() => router.back()}
          >
            <ArrowLeft size={20} color="white" />
            <Text style={styles.backButtonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  // Check if this is an enhanced form with pages/sections structure
  const isEnhancedForm = form.schema.pages && form.schema.pages.length > 0;
  
  if (!isEnhancedForm) {
    // Legacy form structure - convert to new format
    const convertedForm = {
      ...form,
      schema: {
        pages: [
          {
            id: 'page_1',
            title: 'Form Data',
            description: form.description,
            order: 0,
            sections: form.schema.sections || [],
          },
        ],
        logicRules: form.schema.logicRules || [],
      },
    };

    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <FormRenderer
          form={convertedForm}
          initialData={initialData}
          onSave={handleSaveDraft}
          onSubmit={handleSubmit}
          showValidation={true}
        />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <FormRenderer
        form={form}
        initialData={initialData}
        onSave={handleSaveDraft}
        onSubmit={handleSubmit}
        showValidation={true}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  loadingText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  errorTitle: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    marginBottom: 8,
  },
  errorMessage: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 24,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  backButtonText: {
    color: 'white',
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
  },
});
