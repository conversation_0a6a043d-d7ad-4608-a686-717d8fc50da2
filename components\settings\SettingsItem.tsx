import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ActivityIndicator, TextStyle } from 'react-native';
import { useTheme } from '@/hooks/useTheme';
import { ChevronRight } from 'lucide-react-native';

interface SettingsItemProps {
  title: string;
  description?: string;
  icon?: React.ReactNode;
  action?: React.ReactNode;
  onPress?: () => void;
  loading?: boolean;
  disabled?: boolean;
  textStyle?: TextStyle;
  showChevron?: boolean;
}

export default function SettingsItem({
  title,
  description,
  icon,
  action,
  onPress,
  loading = false,
  disabled = false,
  textStyle,
  showChevron = true,
}: SettingsItemProps) {
  const { theme } = useTheme();
  
  const isInteractive = !!onPress && !disabled;
  
  const content = (
    <View style={[
      styles.container,
      { 
        backgroundColor: theme.colors.card,
        opacity: disabled ? 0.5 : 1,
      }
    ]}>
      <View style={styles.leftContent}>
        {icon && (
          <View style={styles.iconContainer}>
            {icon}
          </View>
        )}
        
        <View style={styles.textContent}>
          <Text style={[
            styles.title, 
            { color: theme.colors.text },
            textStyle
          ]}>
            {title}
          </Text>
          {description && (
            <Text style={[styles.description, { color: theme.colors.muted }]}>
              {description}
            </Text>
          )}
        </View>
      </View>
      
      <View style={styles.rightContent}>
        {loading ? (
          <ActivityIndicator size="small" color={theme.colors.primary} />
        ) : (
          <>
            {action}
            {isInteractive && showChevron && !action && (
              <ChevronRight size={16} color={theme.colors.muted} />
            )}
          </>
        )}
      </View>
    </View>
  );
  
  if (isInteractive) {
    return (
      <TouchableOpacity onPress={onPress} disabled={loading || disabled}>
        {content}
      </TouchableOpacity>
    );
  }
  
  return content;
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  leftContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    marginRight: 12,
    width: 24,
    alignItems: 'center',
  },
  textContent: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: '500',
    lineHeight: 20,
  },
  description: {
    fontSize: 14,
    marginTop: 2,
    lineHeight: 18,
  },
  rightContent: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 8,
  },
});
