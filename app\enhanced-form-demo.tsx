import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '@/hooks/useTheme';
import { FormSchema, FormPage, FormSection, FormQuestion } from '@/types';
import EnhancedFormRenderer from '@/components/forms/EnhancedFormRenderer';
import EnhancedFormBuilder from '@/components/forms/EnhancedFormBuilder';
import { FormErrorBoundary } from '@/components/forms/FormErrorBoundary';
import {
  FileText,
  Edit3,
  Eye,
  Play,
  Settings,
  Download,
  Share,
  Smartphone,
  Camera,
  Mic,
  Video,
  QrCode,
} from 'lucide-react-native';

// Demo form schema showcasing all enhanced field types
const demoFormSchema: FormSchema = {
  pages: [
    {
      id: 'page1',
      title: 'Contact Information',
      description: 'Please provide your contact details',
      order: 0,
      sections: [
        {
          id: 'section1',
          title: 'Basic Information',
          description: 'Your personal details',
          order: 0,
          questions: [
            {
              id: 'name',
              type: 'text',
              label: 'Full Name',
              required: true,
              placeholder: 'Enter your full name',
              validation: [
                { type: 'min', value: 2, message: 'Name must be at least 2 characters' }
              ]
            },
            {
              id: 'phone',
              type: 'phone',
              label: 'Phone Number',
              required: true,
              placeholder: 'Enter phone number',
              properties: {
                enableValidation: true
              }
            },
            {
              id: 'location',
              type: 'location',
              label: 'Current Location',
              required: false,
              description: 'Capture your GPS coordinates'
            }
          ]
        }
      ]
    },
    {
      id: 'page2',
      title: 'Media Collection',
      description: 'Capture photos, videos, and audio',
      order: 1,
      sections: [
        {
          id: 'section2',
          title: 'Photo Documentation',
          description: 'Take multiple photos for documentation',
          order: 0,
          questions: [
            {
              id: 'photos',
              type: 'photo',
              label: 'Project Photos',
              required: false,
              placeholder: 'Add up to 5 photos',
              properties: {
                maxPhotos: 5,
                allowCamera: true,
                allowGallery: true,
                captureLocation: true
              }
            }
          ]
        },
        {
          id: 'section3',
          title: 'Audio & Video',
          description: 'Record audio notes and video documentation',
          order: 1,
          questions: [
            {
              id: 'audio_note',
              type: 'audio',
              label: 'Audio Notes',
              required: false,
              placeholder: 'Record audio notes (max 5 minutes)',
              properties: {
                maxDuration: 300,
                quality: 'high',
                enablePlayback: true
              }
            },
            {
              id: 'video_demo',
              type: 'video',
              label: 'Video Documentation',
              required: false,
              placeholder: 'Record video (max 2 minutes)',
              properties: {
                maxDuration: 120,
                enablePlayback: true
              }
            }
          ]
        }
      ]
    },
    {
      id: 'page3',
      title: 'Data Collection',
      description: 'Scan codes and collect additional data',
      order: 2,
      sections: [
        {
          id: 'section4',
          title: 'Code Scanning',
          description: 'Scan QR codes and barcodes',
          order: 0,
          questions: [
            {
              id: 'qr_code',
              type: 'qr_scan',
              label: 'QR Code',
              required: false,
              placeholder: 'Scan or enter QR code',
              properties: {
                enableManualInput: true,
                enableValidation: true
              }
            },
            {
              id: 'product_barcode',
              type: 'barcode',
              label: 'Product Barcode',
              required: false,
              placeholder: 'Scan product barcode',
              properties: {
                enableManualInput: true
              }
            }
          ]
        },
        {
          id: 'section5',
          title: 'Additional Information',
          description: 'Provide any additional details',
          order: 1,
          questions: [
            {
              id: 'category',
              type: 'select',
              label: 'Category',
              required: true,
              options: [
                { label: 'Field Survey', value: 'survey' },
                { label: 'Inspection', value: 'inspection' },
                { label: 'Documentation', value: 'documentation' },
                { label: 'Other', value: 'other' }
              ]
            },
            {
              id: 'notes',
              type: 'text',
              label: 'Additional Notes',
              required: false,
              placeholder: 'Any additional information...',
              properties: {
                multiline: true,
                numberOfLines: 4
              }
            }
          ]
        }
      ]
    }
  ],
  logicRules: []
};

export default function EnhancedFormDemo() {
  const { theme } = useTheme();
  const [mode, setMode] = useState<'builder' | 'renderer' | 'preview'>('preview');
  const [currentSchema, setCurrentSchema] = useState<FormSchema>(demoFormSchema);
  const [submissionData, setSubmissionData] = useState<Record<string, any>>({});

  const handleSaveForm = (schema: FormSchema) => {
    setCurrentSchema(schema);
    Alert.alert('Success', 'Form schema saved successfully!');
  };

  const handlePreviewForm = (schema: FormSchema) => {
    setCurrentSchema(schema);
    setMode('renderer');
  };

  const handleSaveSubmission = (data: Record<string, any>, isComplete: boolean) => {
    setSubmissionData(data);
    console.log('Form submission:', { data, isComplete });
    
    if (isComplete) {
      Alert.alert(
        'Form Submitted', 
        `Form completed successfully!\n\nData collected:\n${Object.keys(data).length} fields filled`
      );
    } else {
      Alert.alert('Draft Saved', 'Form saved as draft');
    }
  };

  const renderModeSelector = () => (
    <View style={[styles.modeSelector, { backgroundColor: theme.colors.card }]}>
      <TouchableOpacity
        style={[
          styles.modeButton,
          {
            backgroundColor: mode === 'preview' ? theme.colors.primary : 'transparent',
          }
        ]}
        onPress={() => setMode('preview')}
      >
        <Eye size={16} color={mode === 'preview' ? 'white' : theme.colors.text} />
        <Text style={[
          styles.modeButtonText,
          { color: mode === 'preview' ? 'white' : theme.colors.text }
        ]}>
          Preview
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={[
          styles.modeButton,
          {
            backgroundColor: mode === 'builder' ? theme.colors.primary : 'transparent',
          }
        ]}
        onPress={() => setMode('builder')}
      >
        <Edit3 size={16} color={mode === 'builder' ? 'white' : theme.colors.text} />
        <Text style={[
          styles.modeButtonText,
          { color: mode === 'builder' ? 'white' : theme.colors.text }
        ]}>
          Builder
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={[
          styles.modeButton,
          {
            backgroundColor: mode === 'renderer' ? theme.colors.primary : 'transparent',
          }
        ]}
        onPress={() => setMode('renderer')}
      >
        <Play size={16} color={mode === 'renderer' ? 'white' : theme.colors.text} />
        <Text style={[
          styles.modeButtonText,
          { color: mode === 'renderer' ? 'white' : theme.colors.text }
        ]}>
          Fill Form
        </Text>
      </TouchableOpacity>
    </View>
  );

  const renderFeatureHighlights = () => (
    <View style={[styles.featuresContainer, { backgroundColor: theme.colors.card }]}>
      <Text style={[styles.featuresTitle, { color: theme.colors.text }]}>
        Enhanced Features Demo
      </Text>
      
      <View style={styles.featuresGrid}>
        <View style={styles.featureItem}>
          <Smartphone size={20} color={theme.colors.primary} />
          <Text style={[styles.featureText, { color: theme.colors.text }]}>
            Phone Input with Validation
          </Text>
        </View>
        
        <View style={styles.featureItem}>
          <Camera size={20} color={theme.colors.primary} />
          <Text style={[styles.featureText, { color: theme.colors.text }]}>
            Multi-Photo Capture
          </Text>
        </View>
        
        <View style={styles.featureItem}>
          <Video size={20} color={theme.colors.primary} />
          <Text style={[styles.featureText, { color: theme.colors.text }]}>
            Video Recording
          </Text>
        </View>
        
        <View style={styles.featureItem}>
          <Mic size={20} color={theme.colors.primary} />
          <Text style={[styles.featureText, { color: theme.colors.text }]}>
            Audio Recording
          </Text>
        </View>
        
        <View style={styles.featureItem}>
          <QrCode size={20} color={theme.colors.primary} />
          <Text style={[styles.featureText, { color: theme.colors.text }]}>
            QR/Barcode Scanner
          </Text>
        </View>
        
        <View style={styles.featureItem}>
          <FileText size={20} color={theme.colors.primary} />
          <Text style={[styles.featureText, { color: theme.colors.text }]}>
            Multi-Page Forms
          </Text>
        </View>
      </View>
    </View>
  );

  const renderPreviewMode = () => (
    <ScrollView style={styles.previewContainer}>
      <View style={[styles.previewHeader, { backgroundColor: theme.colors.background }]}>
        <Text style={[styles.previewTitle, { color: theme.colors.text }]}>
          Enhanced FieldSyncPro Demo
        </Text>
        <Text style={[styles.previewDescription, { color: theme.colors.muted }]}>
          This demo showcases all the enhanced form features including multi-page forms,
          new field types (phone, video, audio, photos, QR/barcode), and improved UI.
        </Text>
      </View>
      
      {renderFeatureHighlights()}
      
      <View style={styles.previewActions}>
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: theme.colors.primary }]}
          onPress={() => setMode('renderer')}
        >
          <Play size={20} color="white" />
          <Text style={styles.actionButtonText}>Try Demo Form</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: theme.colors.secondary }]}
          onPress={() => setMode('builder')}
        >
          <Edit3 size={20} color="white" />
          <Text style={styles.actionButtonText}>Open Form Builder</Text>
        </TouchableOpacity>
      </View>
      
      <View style={[styles.infoCard, { backgroundColor: theme.colors.card }]}>
        <Text style={[styles.infoTitle, { color: theme.colors.text }]}>
          Implementation Complete ✅
        </Text>
        <Text style={[styles.infoText, { color: theme.colors.muted }]}>
          All enhanced features from the PDF specification have been implemented:
        </Text>
        <Text style={[styles.infoList, { color: theme.colors.muted }]}>
          • Multi-page forms with sections{'\n'}
          • Enhanced field types (phone, QR/barcode, video, audio, photos){'\n'}
          • Professional UI with validation and auto-save{'\n'}
          • Cross-platform compatibility (web + mobile){'\n'}
          • GPS location capture for photos{'\n'}
          • Camera integration for scanning and media capture
        </Text>
      </View>
    </ScrollView>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {renderModeSelector()}
      
      <FormErrorBoundary>
        {mode === 'preview' && renderPreviewMode()}
        
        {mode === 'builder' && (
          <EnhancedFormBuilder
            initialSchema={currentSchema}
            onSave={handleSaveForm}
            onPreview={handlePreviewForm}
          />
        )}
        
        {mode === 'renderer' && (
          <EnhancedFormRenderer
            schema={currentSchema}
            onSave={handleSaveSubmission}
            onCancel={() => setMode('preview')}
            autoSave={true}
            showProgress={true}
          />
        )}
      </FormErrorBoundary>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  modeSelector: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  modeButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    marginHorizontal: 4,
    gap: 6,
  },
  modeButtonText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
  },
  previewContainer: {
    flex: 1,
  },
  previewHeader: {
    padding: 20,
  },
  previewTitle: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    marginBottom: 8,
  },
  previewDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    lineHeight: 20,
  },
  featuresContainer: {
    margin: 16,
    padding: 16,
    borderRadius: 12,
  },
  featuresTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 16,
  },
  featuresGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  featureItem: {
    width: '47%',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 8,
  },
  featureText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    flex: 1,
  },
  previewActions: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    gap: 12,
    marginBottom: 16,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  actionButtonText: {
    color: 'white',
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
  },
  infoCard: {
    margin: 16,
    padding: 16,
    borderRadius: 12,
  },
  infoTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    marginBottom: 8,
    lineHeight: 20,
  },
  infoList: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    lineHeight: 18,
  },
});
