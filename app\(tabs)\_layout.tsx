import { Tabs } from 'expo-router';
import { useTheme } from '@/hooks/useTheme';
import { ErrorBoundary } from '@/components/ErrorBoundary';
import { FileText, Map, Settings, Home, Globe } from 'lucide-react-native';
import { getSafeScreenOptions } from '@/utils/screenUtils';

export default function TabLayout() {
  const { theme } = useTheme();
  
  return (
    <ErrorBoundary>
      <Tabs
        screenOptions={{
          ...getSafeScreenOptions(),
          tabBarStyle: {
            backgroundColor: theme.colors.card,
            borderTopColor: theme.colors.border,
          },
          tabBarActiveTintColor: theme.colors.primary,
          tabBarInactiveTintColor: theme.colors.muted,
        }}
      >
        <Tabs.Screen 
          name="index" 
          options={{
            title: 'Dashboard',
            tabBarIcon: ({ color, size }) => <Home size={size} color={color} />,
          }} 
        />
        <Tabs.Screen 
          name="collect" 
          options={{
            title: 'Collect',
            tabBarIcon: ({ color, size }) => <FileText size={size} color={color} />,
          }} 
        />
        <Tabs.Screen 
          name="map" 
          options={{
            title: 'Map',
            tabBarIcon: ({ color, size }) => <Map size={size} color={color} />,
          }} 
        />
        <Tabs.Screen 
          name="advanced-map" 
          options={{
            title: 'Advanced Map',
            tabBarIcon: ({ color, size }) => <Globe size={size} color={color} />,
          }} 
        />
        <Tabs.Screen 
          name="settings" 
          options={{
            title: 'Settings',
            tabBarIcon: ({ color, size }) => <Settings size={size} color={color} />,
          }} 
        />
      </Tabs>
    </ErrorBoundary>
  );
}
