import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { ProjectWithStats } from '@/types/database';
import { useTheme } from '@/hooks/useTheme';
import { ClipboardList, Clock, CheckCircle, Archive, Calendar } from 'lucide-react-native';

type ProjectCardProps = {
  project: ProjectWithStats;
  onPress: () => void;
};

export default function ProjectCard({ project, onPress }: ProjectCardProps) {
  const { theme } = useTheme();
  
  const getStatusIcon = () => {
    switch (project.status) {
      case 'active':
        return <CheckCircle size={16} color={theme.colors.success} />;
      case 'draft':
        return <Clock size={16} color={theme.colors.warning} />;
      case 'completed':
        return <CheckCircle size={16} color={theme.colors.success} />;
      case 'archived':
        return <Archive size={16} color={theme.colors.muted} />;
      default:
        return <ClipboardList size={16} color={theme.colors.text} />;
    }
  };
  
  const getStatusText = () => {
    switch (project.status) {
      case 'active':
        return 'Active';
      case 'draft':
        return 'Draft';
      case 'completed':
        return 'Completed';
      case 'archived':
        return 'Archived';
      default:
        return 'Unknown';
    }
  };
  
  const getStatusColor = () => {
    switch (project.status) {
      case 'active':
        return theme.colors.success;
      case 'draft':
        return theme.colors.warning;
      case 'completed':
        return theme.colors.success;
      case 'archived':
        return theme.colors.muted;
      default:
        return theme.colors.text;
    }
  };
  
  const formatDate = (timestamp: string | number) => {
    const date = new Date(timestamp);
    return date.toLocaleDateString();
  };

  const getFormsCount = () => {
    // First try to use stats if available
    if (project.stats?.total_forms !== undefined) {
      return project.stats.total_forms;
    }
    
    // Fallback to forms array if available
    if (project.forms && Array.isArray(project.forms)) {
      return project.forms.length;
    }
    
    // Default to 0 if no forms data available
    return 0;
  };

  return (
    <TouchableOpacity 
      style={[styles.container, { backgroundColor: theme.colors.card, borderColor: theme.colors.border }]} 
      onPress={onPress}
    >
      <View style={styles.content}>
        <View style={styles.header}>
          <Text style={[styles.title, { color: theme.colors.text }]}>{project.name}</Text>
          <View style={[styles.statusBadge, { backgroundColor: `${getStatusColor()}20` }]}>
            {getStatusIcon()}
            <Text style={[styles.statusText, { color: getStatusColor() }]}>{getStatusText()}</Text>
          </View>
        </View>
        
        {project.description && (
          <Text 
            style={[styles.description, { color: theme.colors.text }]}
            numberOfLines={2}
          >
            {project.description}
          </Text>
        )}
        
        <View style={styles.metadata}>
          <View style={styles.metadataItem}>
            <Calendar size={14} color={theme.colors.muted} />
            <Text style={[styles.metadataText, { color: theme.colors.muted }]}>
              Created: {formatDate(project.created_at)}
            </Text>
          </View>
          
          <View style={styles.metadataItem}>
            <ClipboardList size={14} color={theme.colors.muted} />
            <Text style={[styles.metadataText, { color: theme.colors.muted }]}>
              Forms: {getFormsCount()}
            </Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    borderWidth: 1,
    overflow: 'hidden',
    marginBottom: 16,
  },
  content: {
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  title: {
    flex: 1,
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    marginRight: 8,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    gap: 4,
  },
  statusText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },
  description: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    marginBottom: 16,
  },
  metadata: {
    flexDirection: 'row',
    gap: 16,
  },
  metadataItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  metadataText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },
});