import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  SafeAreaView,
} from 'react-native';
import { useTheme } from '@/hooks/useTheme';
import { FormSchema, FormPage, FormSection, FormQuestion, Submission } from '@/types';
import FormFieldRenderer from './FormFieldRenderer';
import {
  ChevronLeft,
  ChevronRight,
  Save,
  Send,
  FileText,
  CheckCircle,
  AlertCircle,
  Clock,
} from 'lucide-react-native';

interface FormRendererProps {
  form: {
    id: string;
    name: string;
    description?: string;
    schema: FormSchema;
  };
  initialData?: Record<string, any>;
  onSave?: (data: Record<string, any>, isDraft: boolean) => Promise<void>;
  onSubmit?: (submission: Partial<Submission>) => Promise<void>;
  disabled?: boolean;
  showValidation?: boolean;
}

export default function FormRenderer({
  form,
  initialData = {},
  onSave,
  onSubmit,
  disabled = false,
  showValidation = true,
}: FormRendererProps) {
  const { theme } = useTheme();
  const [currentPageIndex, setCurrentPageIndex] = useState(0);
  const [formData, setFormData] = useState<Record<string, any>>(initialData);
  const [isSaving, setIsSaving] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  const currentPage = form.schema.pages[currentPageIndex];
  const totalPages = form.schema.pages.length;
  const isFirstPage = currentPageIndex === 0;
  const isLastPage = currentPageIndex === totalPages - 1;

  const updateFieldValue = useCallback((questionId: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [questionId]: value,
    }));

    // Clear validation error for this field
    if (validationErrors[questionId]) {
      setValidationErrors(prev => {
        const { [questionId]: removed, ...rest } = prev;
        return rest;
      });
    }
  }, [validationErrors]);

  const validatePage = (page: FormPage): Record<string, string> => {
    const errors: Record<string, string> = {};

    page.sections.forEach(section => {
      section.questions.forEach(question => {
        const value = formData[question.id];

        // Required validation
        if (question.required && (!value || value === '' || 
            (Array.isArray(value) && value.length === 0))) {
          errors[question.id] = `${question.label} is required`;
          return;
        }

        // Type-specific validation
        if (value && question.validation) {
          for (const rule of question.validation) {
            switch (rule.type) {
              case 'min':
                if (question.type === 'text' && value.length < rule.value) {
                  errors[question.id] = rule.message;
                }
                if (question.type === 'number' && parseFloat(value) < rule.value) {
                  errors[question.id] = rule.message;
                }
                break;
              case 'max':
                if (question.type === 'text' && value.length > rule.value) {
                  errors[question.id] = rule.message;
                }
                if (question.type === 'number' && parseFloat(value) > rule.value) {
                  errors[question.id] = rule.message;
                }
                break;
              case 'pattern':
                const regex = new RegExp(rule.value);
                if (!regex.test(value)) {
                  errors[question.id] = rule.message;
                }
                break;
            }
          }
        }
      });
    });

    return errors;
  };

  const validateAllPages = (): Record<string, string> => {
    let allErrors: Record<string, string> = {};

    form.schema.pages.forEach(page => {
      const pageErrors = validatePage(page);
      allErrors = { ...allErrors, ...pageErrors };
    });

    return allErrors;
  };

  const goToNextPage = () => {
    if (showValidation) {
      const errors = validatePage(currentPage);
      if (Object.keys(errors).length > 0) {
        setValidationErrors(errors);
        Alert.alert('Validation Error', 'Please fix the errors on this page before continuing.');
        return;
      }
    }

    if (currentPageIndex < totalPages - 1) {
      setCurrentPageIndex(currentPageIndex + 1);
    }
  };

  const goToPreviousPage = () => {
    if (currentPageIndex > 0) {
      setCurrentPageIndex(currentPageIndex - 1);
    }
  };

  const goToPage = (pageIndex: number) => {
    if (pageIndex >= 0 && pageIndex < totalPages) {
      setCurrentPageIndex(pageIndex);
    }
  };

  const handleSaveDraft = async () => {
    if (!onSave) return;

    setIsSaving(true);
    try {
      await onSave(formData, true);
      Alert.alert('Draft Saved', 'Your progress has been saved as a draft.');
    } catch (error) {
      Alert.alert('Save Error', 'Failed to save draft. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  const handleSubmit = async () => {
    if (!onSubmit) return;

    // Validate all pages
    const errors = validateAllPages();
    if (Object.keys(errors).length > 0) {
      setValidationErrors(errors);
      Alert.alert(
        'Form Incomplete',
        'Please complete all required fields before submitting.',
        [
          { text: 'Review', onPress: () => {
            // Find first page with errors
            const firstErrorPageIndex = form.schema.pages.findIndex(page =>
              page.sections.some(section =>
                section.questions.some(question => errors[question.id])
              )
            );
            if (firstErrorPageIndex !== -1) {
              setCurrentPageIndex(firstErrorPageIndex);
            }
          }},
          { text: 'Cancel', style: 'cancel' },
        ]
      );
      return;
    }

    setIsSubmitting(true);
    try {
      const submission: Partial<Submission> = {
        formId: form.id,
        status: 'completed',
        startedAt: Date.now(),
        completedAt: Date.now(),
        data: formData,
        location: await getCurrentLocation(),
      };

      await onSubmit(submission);
      Alert.alert('Form Submitted', 'Your form has been successfully submitted.');
    } catch (error) {
      Alert.alert('Submission Error', 'Failed to submit form. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const getCurrentLocation = async () => {
    try {
      const { Location } = await import('expo-location');
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status === 'granted') {
        const location = await Location.getCurrentPositionAsync({});
        return {
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
          accuracy: location.coords.accuracy,
          altitude: location.coords.altitude,
          timestamp: Date.now(),
        };
      }
    } catch (error) {
      console.log('Could not get location:', error);
    }
    return undefined;
  };

  const getCompletionPercentage = () => {
    const allQuestions = form.schema.pages.flatMap(page =>
      page.sections.flatMap(section => section.questions)
    );
    
    const completedQuestions = allQuestions.filter(question => {
      const value = formData[question.id];
      return value !== undefined && value !== '' && value !== null;
    });

    return Math.round((completedQuestions.length / allQuestions.length) * 100);
  };

  const renderPageIndicator = () => (
    <View style={[styles.pageIndicator, { backgroundColor: theme.colors.card }]}>
      <View style={styles.pageIndicatorContent}>
        <Text style={[styles.pageTitle, { color: theme.colors.text }]}>
          {currentPage.title}
        </Text>
        <Text style={[styles.pageProgress, { color: theme.colors.muted }]}>
          Page {currentPageIndex + 1} of {totalPages}
        </Text>
      </View>
      
      <View style={styles.pageNavigation}>
        {form.schema.pages.map((_, index) => (
          <TouchableOpacity
            key={index}
            style={[
              styles.pageDot,
              {
                backgroundColor: index === currentPageIndex 
                  ? theme.colors.primary 
                  : theme.colors.border,
              },
            ]}
            onPress={() => goToPage(index)}
          />
        ))}
      </View>
    </View>
  );

  const renderSection = (section: FormSection) => (
    <View key={section.id} style={[styles.section, { backgroundColor: theme.colors.card }]}>
      <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
        {section.title}
      </Text>
      
      {section.description && (
        <Text style={[styles.sectionDescription, { color: theme.colors.muted }]}>
          {section.description}
        </Text>
      )}

      {section.questions.map(question => (
        <FormFieldRenderer
          key={question.id}
          question={question}
          value={formData[question.id]}
          onChange={(value) => updateFieldValue(question.id, value)}
          disabled={disabled}
          showValidation={showValidation && !!validationErrors[question.id]}
        />
      ))}
    </View>
  );

  const renderNavigationButtons = () => (
    <View style={[styles.navigationButtons, { backgroundColor: theme.colors.card }]}>
      <View style={styles.leftButtons}>
        {!isFirstPage && (
          <TouchableOpacity
            style={[styles.navButton, { backgroundColor: theme.colors.background }]}
            onPress={goToPreviousPage}
            disabled={disabled}
          >
            <ChevronLeft size={20} color={theme.colors.text} />
            <Text style={[styles.navButtonText, { color: theme.colors.text }]}>
              Previous
            </Text>
          </TouchableOpacity>
        )}
      </View>

      <View style={styles.centerInfo}>
        <Text style={[styles.completionText, { color: theme.colors.muted }]}>
          {getCompletionPercentage()}% complete
        </Text>
      </View>

      <View style={styles.rightButtons}>
        {onSave && (
          <TouchableOpacity
            style={[styles.saveButton, { backgroundColor: theme.colors.secondary }]}
            onPress={handleSaveDraft}
            disabled={disabled || isSaving}
          >
            {isSaving ? (
              <Clock size={16} color="white" />
            ) : (
              <Save size={16} color="white" />
            )}
            <Text style={styles.saveButtonText}>
              {isSaving ? 'Saving...' : 'Save Draft'}
            </Text>
          </TouchableOpacity>
        )}

        {isLastPage ? (
          <TouchableOpacity
            style={[styles.submitButton, { backgroundColor: theme.colors.primary }]}
            onPress={handleSubmit}
            disabled={disabled || isSubmitting}
          >
            {isSubmitting ? (
              <Clock size={16} color="white" />
            ) : (
              <Send size={16} color="white" />
            )}
            <Text style={styles.submitButtonText}>
              {isSubmitting ? 'Submitting...' : 'Submit'}
            </Text>
          </TouchableOpacity>
        ) : (
          <TouchableOpacity
            style={[styles.navButton, { backgroundColor: theme.colors.primary }]}
            onPress={goToNextPage}
            disabled={disabled}
          >
            <Text style={[styles.navButtonText, { color: 'white' }]}>
              Next
            </Text>
            <ChevronRight size={20} color="white" />
          </TouchableOpacity>
        )}
      </View>
    </View>
  );

  const renderValidationSummary = () => {
    const errorCount = Object.keys(validationErrors).length;
    if (errorCount === 0) return null;

    return (
      <View style={[styles.validationSummary, { backgroundColor: theme.colors.error + '10' }]}>
        <AlertCircle size={20} color={theme.colors.error} />
        <Text style={[styles.validationText, { color: theme.colors.error }]}>
          {errorCount} field{errorCount !== 1 ? 's' : ''} require{errorCount === 1 ? 's' : ''} attention
        </Text>
      </View>
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Form Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.card, borderBottomColor: theme.colors.border }]}>
        <View style={styles.headerContent}>
          <View style={styles.headerIcon}>
            <FileText size={24} color={theme.colors.primary} />
          </View>
          <View style={styles.headerText}>
            <Text style={[styles.formTitle, { color: theme.colors.text }]}>
              {form.name}
            </Text>
            {form.description && (
              <Text style={[styles.formDescription, { color: theme.colors.muted }]}>
                {form.description}
              </Text>
            )}
          </View>
        </View>
      </View>

      {renderPageIndicator()}
      {renderValidationSummary()}

      {/* Form Content */}
      <ScrollView 
        style={styles.content}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {currentPage.description && (
          <Text style={[styles.pageDescription, { color: theme.colors.muted }]}>
            {currentPage.description}
          </Text>
        )}

        {currentPage.sections.map(renderSection)}
      </ScrollView>

      {/* Navigation */}
      {renderNavigationButtons()}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  headerIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#E3F2FD',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerText: {
    flex: 1,
  },
  formTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 2,
  },
  formDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    lineHeight: 20,
  },
  pageIndicator: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  pageIndicatorContent: {
    marginBottom: 8,
  },
  pageTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 2,
  },
  pageProgress: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
  },
  pageNavigation: {
    flexDirection: 'row',
    gap: 6,
  },
  pageDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  validationSummary: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    gap: 8,
  },
  validationText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 100,
  },
  pageDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    lineHeight: 20,
    marginBottom: 20,
  },
  section: {
    marginBottom: 24,
    padding: 16,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    lineHeight: 20,
    marginBottom: 16,
  },
  navigationButtons: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  leftButtons: {
    flex: 1,
    alignItems: 'flex-start',
  },
  centerInfo: {
    flex: 1,
    alignItems: 'center',
  },
  rightButtons: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'flex-end',
    gap: 8,
  },
  navButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
    gap: 6,
  },
  navButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
  },
  saveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    gap: 4,
  },
  saveButtonText: {
    color: 'white',
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
  },
  submitButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
    gap: 6,
  },
  submitButtonText: {
    color: 'white',
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
  },
  completionText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
  },
});
