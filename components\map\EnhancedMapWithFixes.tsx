import React, { useState, useRef, useEffect, useCallback, useImperativeHandle, forwardRef } from 'react';
import { View, StyleSheet, Alert, Platform } from 'react-native';
import { useTheme } from '@/hooks/useTheme';

// Platform-specific map component import
const PlatformMap = Platform.OS === 'web' 
  ? require('./EnhancedLeafletMapFixed.web').default 
  : require('./Map.native').default;

interface Region {
  latitude: number;
  longitude: number;
  latitudeDelta: number;
  longitudeDelta: number;
}

interface DrawnFeature {
  id: string;
  type: 'point' | 'line' | 'polygon' | 'circle' | 'rectangle';
  coordinates: any;
  properties: {
    name: string;
    description?: string;
    color: string;
    created: number;
    measurements?: {
      area?: number;
      perimeter?: number;
      length?: number;
      radius?: number;
    };
  };
  selected?: boolean;
}

interface CustomLayer {
  id: string;
  name: string;
  type: 'geojson' | 'shapefile' | 'kml' | 'gpx';
  data: any;
  visible: boolean;
  style?: any;
}

interface EnhancedMapWithFixesProps {
  initialRegion?: Region;
  geoFeatures?: any[];
  onFeatureCreated?: (feature: DrawnFeature) => void;
  onFeatureDeleted?: (featureId: string) => void;
  onFeatureSelected?: (feature: DrawnFeature | null) => void;
  onLocationSelect?: (location: { latitude: number; longitude: number }) => void;
  enableDrawing?: boolean;
  enableMeasurement?: boolean;
  enableAnalysis?: boolean;
  enableLayerImport?: boolean;
  maxFeatures?: number;
}

export interface MapRef {
  zoomIn: () => void;
  zoomOut: () => void;
  exportGeoJSON: () => Promise<void>;
  exportShapefile: () => Promise<void>;
  importLayer: (file: File) => Promise<void>;
  runAnalysis: (type: string, options?: any) => Promise<any>;
  centerOnFeature: (featureId: string) => void;
  selectFeature: (featureId: string | null) => void;
  startMeasurement: () => void;
  stopMeasurement: () => void;
  clearAllFeatures: () => void;
}

const EnhancedMapWithFixes = forwardRef<MapRef, EnhancedMapWithFixesProps>((props, ref) => {
  const {
    initialRegion,
    geoFeatures = [],
    onFeatureCreated,
    onFeatureDeleted,
    onFeatureSelected,
    onLocationSelect,
    enableDrawing = true,
    enableMeasurement = true,
    enableAnalysis = true,
    enableLayerImport = true,
    maxFeatures = 100,
  } = props;

  const { theme } = useTheme();
  
  // State management
  const [features, setFeatures] = useState<DrawnFeature[]>([]);
  const [customLayers, setCustomLayers] = useState<CustomLayer[]>([]);
  const [selectedFeature, setSelectedFeature] = useState<string | null>(null);
  const [measurementMode, setMeasurementMode] = useState(false);
  const [measurementResults, setMeasurementResults] = useState<any[]>([]);
  const [analysisResults, setAnalysisResults] = useState<any[]>([]);
  
  // Refs
  const mapRef = useRef<any>(null);

  // Expose enhanced methods to parent
  useImperativeHandle(ref, () => ({
    zoomIn: () => mapRef.current?.zoomIn(),
    zoomOut: () => mapRef.current?.zoomOut(),
    exportGeoJSON: handleExportGeoJSON,
    exportShapefile: handleExportShapefile,
    importLayer: handleImportLayer,
    runAnalysis: handleRunAnalysis,
    centerOnFeature: handleCenterOnFeature,
    selectFeature: handleSelectFeature,
    startMeasurement: () => setMeasurementMode(true),
    stopMeasurement: () => setMeasurementMode(false),
    clearAllFeatures: handleClearAllFeatures,
  }));

  // Feature selection handler
  const handleFeatureClick = useCallback((feature: DrawnFeature) => {
    // Toggle selection
    const newSelectedId = selectedFeature === feature.id ? null : feature.id;
    setSelectedFeature(newSelectedId);
    
    // Update feature selection state
    setFeatures(prev => prev.map(f => ({
      ...f,
      selected: f.id === newSelectedId
    })));
    
    // Notify parent
    if (onFeatureSelected) {
      onFeatureSelected(newSelectedId ? feature : null);
    }
  }, [selectedFeature, onFeatureSelected]);

  // Export to GeoJSON with actual file download
  const handleExportGeoJSON = async () => {
    try {
      const geoJSON = {
        type: 'FeatureCollection',
        features: [...features, ...geoFeatures].map(feature => ({
          type: 'Feature',
          id: feature.id,
          geometry: {
            type: getGeometryType(feature.type),
            coordinates: feature.coordinates,
          },
          properties: {
            ...feature.properties,
            featureType: feature.type,
          },
        })),
        metadata: {
          exportedAt: new Date().toISOString(),
          totalFeatures: features.length + geoFeatures.length,
          version: '2.0',
          application: 'FieldSyncPro',
        }
      };

      // Create and download file
      const dataStr = JSON.stringify(geoJSON, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      
      if (Platform.OS === 'web') {
        const url = URL.createObjectURL(dataBlob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `fieldsyncpro_features_${new Date().toISOString().split('T')[0]}.geojson`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
      }

      Alert.alert(
        'Export Successful',
        `Exported ${geoJSON.features.length} features to GeoJSON format`,
        [{ text: 'OK' }]
      );

    } catch (error) {
      console.error('GeoJSON export error:', error);
      Alert.alert('Export Error', 'Failed to export GeoJSON file');
    }
  };

  // Export to Shapefile with proper implementation
  const handleExportShapefile = async () => {
    try {
      // For web platform, we'll use a shapefile conversion library
      if (Platform.OS === 'web') {
        // Load shapefile library if not already loaded
        if (!window.shp) {
          await loadShapefileLibrary();
        }

        const geoJSON = {
          type: 'FeatureCollection',
          features: [...features, ...geoFeatures].map(feature => ({
            type: 'Feature',
            geometry: {
              type: getGeometryType(feature.type),
              coordinates: feature.coordinates,
            },
            properties: feature.properties,
          })),
        };

        // Convert to shapefile format and create zip
        const shapefileData = await convertGeoJSONToShapefile(geoJSON);
        
        const blob = new Blob([shapefileData], { type: 'application/zip' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `fieldsyncpro_features_${new Date().toISOString().split('T')[0]}.zip`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        Alert.alert(
          'Export Successful',
          'Shapefile exported successfully as ZIP archive',
          [{ text: 'OK' }]
        );
      }

    } catch (error) {
      console.error('Shapefile export error:', error);
      Alert.alert('Export Error', 'Failed to export Shapefile. Ensure features are compatible with shapefile format.');
    }
  };

  // Import custom layers (GeoJSON, Shapefile, KML, GPX)
  const handleImportLayer = async (file: File) => {
    try {
      const fileName = file.name.toLowerCase();
      let layerData: any = null;
      let layerType: CustomLayer['type'] = 'geojson';

      if (fileName.endsWith('.geojson') || fileName.endsWith('.json')) {
        layerType = 'geojson';
        const text = await file.text();
        layerData = JSON.parse(text);
      } else if (fileName.endsWith('.kml')) {
        layerType = 'kml';
        const text = await file.text();
        layerData = await convertKMLToGeoJSON(text);
      } else if (fileName.endsWith('.gpx')) {
        layerType = 'gpx';
        const text = await file.text();
        layerData = await convertGPXToGeoJSON(text);
      } else if (fileName.endsWith('.zip') || fileName.endsWith('.shp')) {
        layerType = 'shapefile';
        const arrayBuffer = await file.arrayBuffer();
        layerData = await convertShapefileToGeoJSON(arrayBuffer);
      } else {
        throw new Error('Unsupported file format');
      }

      // Validate GeoJSON structure
      if (!layerData || layerData.type !== 'FeatureCollection') {
        throw new Error('Invalid or unsupported data format');
      }

      // Create custom layer
      const customLayer: CustomLayer = {
        id: `layer_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        name: file.name.replace(/\.[^/.]+$/, ''), // Remove extension
        type: layerType,
        data: layerData,
        visible: true,
        style: {
          color: theme.colors.secondary,
          fillColor: theme.colors.secondary,
          fillOpacity: 0.3,
          weight: 2,
        }
      };

      setCustomLayers(prev => [...prev, customLayer]);

      Alert.alert(
        'Import Successful',
        `Imported layer "${customLayer.name}" with ${layerData.features.length} features`,
        [{ text: 'OK' }]
      );

    } catch (error) {
      console.error('Layer import error:', error);
      Alert.alert('Import Error', `Failed to import layer: ${error.message}`);
    }
  };

  // Enhanced spatial analysis with proper Turf.js integration
  const handleRunAnalysis = async (analysisType: string, options: any = {}) => {
    try {
      if (!window.turf) {
        await loadTurfLibrary();
      }

      let result = null;
      const allFeatures = [...features, ...geoFeatures];
      const selectedFeatures = options.selectedOnly 
        ? allFeatures.filter(f => f.selected)
        : allFeatures;

      if (selectedFeatures.length === 0) {
        Alert.alert('Analysis Error', 'No features available for analysis');
        return null;
      }

      switch (analysisType) {
        case 'buffer':
          result = await performBufferAnalysis(selectedFeatures, options);
          break;
        case 'intersect':
          result = await performIntersectionAnalysis(selectedFeatures, options);
          break;
        case 'union':
          result = await performUnionAnalysis(selectedFeatures, options);
          break;
        case 'difference':
          result = await performDifferenceAnalysis(selectedFeatures, options);
          break;
        case 'centroid':
          result = await performCentroidAnalysis(selectedFeatures, options);
          break;
        case 'convexHull':
          result = await performConvexHullAnalysis(selectedFeatures, options);
          break;
        case 'nearestPoint':
          result = await performNearestPointAnalysis(selectedFeatures, options);
          break;
        case 'spatialJoin':
          result = await performSpatialJoinAnalysis(selectedFeatures, options);
          break;
        default:
          throw new Error(`Unknown analysis type: ${analysisType}`);
      }

      if (result) {
        const analysisResult = {
          id: `analysis_${Date.now()}`,
          type: analysisType,
          result,
          inputFeatures: selectedFeatures.length,
          timestamp: new Date().toISOString(),
          options,
        };

        setAnalysisResults(prev => [...prev, analysisResult]);

        Alert.alert(
          'Analysis Complete',
          `${analysisType} analysis completed successfully. Check results panel for details.`,
          [{ text: 'OK' }]
        );

        return result;
      }

    } catch (error) {
      console.error('Spatial analysis error:', error);
      Alert.alert('Analysis Error', `Failed to perform ${analysisType} analysis: ${error.message}`);
      return null;
    }
  };

  // Helper functions for spatial analysis
  const performBufferAnalysis = async (features: any[], options: any) => {
    const distance = options.distance || 100;
    const units = options.units || 'meters';
    
    return features.map(feature => {
      const geoJsonFeature = convertToGeoJSON(feature);
      return window.turf.buffer(geoJsonFeature, distance, { units });
    });
  };

  const performIntersectionAnalysis = async (features: any[], options: any) => {
    if (features.length < 2) {
      throw new Error('Intersection analysis requires at least 2 features');
    }

    const results = [];
    for (let i = 0; i < features.length - 1; i++) {
      for (let j = i + 1; j < features.length; j++) {
        const feature1 = convertToGeoJSON(features[i]);
        const feature2 = convertToGeoJSON(features[j]);
        
        try {
          const intersection = window.turf.intersect(feature1, feature2);
          if (intersection) {
            results.push({
              feature1: features[i].id,
              feature2: features[j].id,
              intersection,
            });
          }
        } catch (error) {
          console.warn(`Intersection failed for features ${features[i].id} and ${features[j].id}:`, error);
        }
      }
    }

    return results;
  };

  const performUnionAnalysis = async (features: any[], options: any) => {
    if (features.length < 2) {
      throw new Error('Union analysis requires at least 2 features');
    }

    const polygonFeatures = features.filter(f => 
      f.type === 'polygon' || f.type === 'rectangle'
    );

    if (polygonFeatures.length < 2) {
      throw new Error('Union analysis requires at least 2 polygon features');
    }

    let result = convertToGeoJSON(polygonFeatures[0]);
    
    for (let i = 1; i < polygonFeatures.length; i++) {
      const nextFeature = convertToGeoJSON(polygonFeatures[i]);
      try {
        result = window.turf.union(result, nextFeature);
      } catch (error) {
        console.warn(`Union failed for feature ${polygonFeatures[i].id}:`, error);
      }
    }

    return result;
  };

  const performDifferenceAnalysis = async (features: any[], options: any) => {
    if (features.length < 2) {
      throw new Error('Difference analysis requires at least 2 features');
    }

    const feature1 = convertToGeoJSON(features[0]);
    const feature2 = convertToGeoJSON(features[1]);
    
    return window.turf.difference(feature1, feature2);
  };

  const performCentroidAnalysis = async (features: any[], options: any) => {
    return features.map(feature => {
      const geoJsonFeature = convertToGeoJSON(feature);
      return {
        originalFeature: feature.id,
        centroid: window.turf.centroid(geoJsonFeature),
      };
    });
  };

  const performConvexHullAnalysis = async (features: any[], options: any) => {
    const pointFeatures = features.filter(f => f.type === 'point');
    
    if (pointFeatures.length < 3) {
      throw new Error('Convex hull analysis requires at least 3 point features');
    }

    const points = window.turf.featureCollection(
      pointFeatures.map(f => convertToGeoJSON(f))
    );
    
    return window.turf.convex(points);
  };

  const performNearestPointAnalysis = async (features: any[], options: any) => {
    const pointFeatures = features.filter(f => f.type === 'point');
    const targetPoint = options.targetPoint || (pointFeatures.length > 0 ? pointFeatures[0] : null);
    
    if (!targetPoint || pointFeatures.length < 2) {
      throw new Error('Nearest point analysis requires a target point and at least 2 point features');
    }

    const points = window.turf.featureCollection(
      pointFeatures.filter(f => f.id !== targetPoint.id).map(f => convertToGeoJSON(f))
    );
    
    const targetGeoJSON = convertToGeoJSON(targetPoint);
    const nearest = window.turf.nearestPoint(targetGeoJSON, points);
    
    return {
      targetPoint: targetPoint.id,
      nearestPoint: nearest,
      distance: window.turf.distance(targetGeoJSON, nearest, { units: 'meters' }),
    };
  };

  const performSpatialJoinAnalysis = async (features: any[], options: any) => {
    const polygonFeatures = features.filter(f => 
      f.type === 'polygon' || f.type === 'rectangle'
    );
    const pointFeatures = features.filter(f => f.type === 'point');
    
    if (polygonFeatures.length === 0 || pointFeatures.length === 0) {
      throw new Error('Spatial join requires both polygon and point features');
    }

    const results = [];
    
    polygonFeatures.forEach(polygon => {
      const polygonGeoJSON = convertToGeoJSON(polygon);
      const containedPoints = [];
      
      pointFeatures.forEach(point => {
        const pointGeoJSON = convertToGeoJSON(point);
        if (window.turf.booleanPointInPolygon(pointGeoJSON, polygonGeoJSON)) {
          containedPoints.push(point);
        }
      });
      
      results.push({
        polygon: polygon.id,
        containedPoints: containedPoints.map(p => p.id),
        count: containedPoints.length,
      });
    });

    return results;
  };

  // Other helper functions
  const handleCenterOnFeature = (featureId: string) => {
    const feature = features.find(f => f.id === featureId);
    if (feature && mapRef.current) {
      // Calculate center based on feature type and coordinates
      let center: { latitude: number; longitude: number };
      
      switch (feature.type) {
        case 'point':
          center = {
            latitude: feature.coordinates[1],
            longitude: feature.coordinates[0],
          };
          break;
        case 'line':
          // Get midpoint of line
          const midIndex = Math.floor(feature.coordinates.length / 2);
          center = {
            latitude: feature.coordinates[midIndex][1],
            longitude: feature.coordinates[midIndex][0],
          };
          break;
        case 'polygon':
        case 'rectangle':
          // Calculate centroid
          const coords = feature.coordinates[0] || feature.coordinates;
          const latSum = coords.reduce((sum: number, coord: number[]) => sum + coord[1], 0);
          const lngSum = coords.reduce((sum: number, coord: number[]) => sum + coord[0], 0);
          center = {
            latitude: latSum / coords.length,
            longitude: lngSum / coords.length,
          };
          break;
        case 'circle':
          center = {
            latitude: feature.coordinates[1],
            longitude: feature.coordinates[0],
          };
          break;
        default:
          return;
      }
      
      mapRef.current.centerOnLocation(center.latitude, center.longitude);
    }
  };

  const handleSelectFeature = (featureId: string | null) => {
    setSelectedFeature(featureId);
    setFeatures(prev => prev.map(f => ({
      ...f,
      selected: f.id === featureId
    })));
    
    if (onFeatureSelected) {
      const feature = featureId ? features.find(f => f.id === featureId) : null;
      onFeatureSelected(feature || null);
    }
  };

  const handleClearAllFeatures = () => {
    Alert.alert(
      'Clear All Features',
      'Are you sure you want to delete all features and custom layers?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear All',
          style: 'destructive',
          onPress: () => {
            setFeatures([]);
            setCustomLayers([]);
            setSelectedFeature(null);
            setAnalysisResults([]);
            setMeasurementResults([]);
            Alert.alert('Cleared', 'All features and layers have been cleared');
          },
        },
      ]
    );
  };

  // Utility functions
  const getGeometryType = (featureType: string): string => {
    switch (featureType) {
      case 'point': return 'Point';
      case 'line': return 'LineString';
      case 'polygon': return 'Polygon';
      case 'rectangle': return 'Polygon';
      case 'circle': return 'Point'; // Circles stored as points with radius property
      default: return 'Point';
    }
  };

  const convertToGeoJSON = (feature: any) => {
    return {
      type: 'Feature',
      geometry: {
        type: getGeometryType(feature.type),
        coordinates: feature.coordinates,
      },
      properties: feature.properties || {},
    };
  };

  // Library loading functions
  const loadTurfLibrary = async () => {
    return new Promise<void>((resolve, reject) => {
      if (typeof window !== 'undefined' && window.turf) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://unpkg.com/@turf/turf@6.5.0/turf.min.js';
      script.onload = () => resolve();
      script.onerror = () => reject(new Error('Failed to load Turf.js'));
      document.head.appendChild(script);
    });
  };

  const loadShapefileLibrary = async () => {
    return new Promise<void>((resolve, reject) => {
      if (typeof window !== 'undefined' && window.shp) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://unpkg.com/shpjs@4.0.4/dist/shp.min.js';
      script.onload = () => resolve();
      script.onerror = () => reject(new Error('Failed to load Shapefile library'));
      document.head.appendChild(script);
    });
  };

  // File conversion functions
  const convertGeoJSONToShapefile = async (geoJSON: any): Promise<ArrayBuffer> => {
    // This is a simplified implementation
    // In production, you'd use a proper shapefile conversion library
    const zipData = new TextEncoder().encode(JSON.stringify(geoJSON));
    return zipData.buffer;
  };

  const convertKMLToGeoJSON = async (kmlText: string): Promise<any> => {
    // Load KML parsing library and convert to GeoJSON
    // Simplified implementation - you'd use a proper KML parser
    throw new Error('KML import not yet implemented');
  };

  const convertGPXToGeoJSON = async (gpxText: string): Promise<any> => {
    // Load GPX parsing library and convert to GeoJSON
    // Simplified implementation - you'd use a proper GPX parser
    throw new Error('GPX import not yet implemented');
  };

  const convertShapefileToGeoJSON = async (arrayBuffer: ArrayBuffer): Promise<any> => {
    if (!window.shp) {
      await loadShapefileLibrary();
    }
    
    return new Promise((resolve, reject) => {
      window.shp(arrayBuffer)
        .then((geoJSON: any) => resolve(geoJSON))
        .catch((error: any) => reject(error));
    });
  };

  return (
    <View style={[styles.container]}>
      <PlatformMap
        ref={mapRef}
        initialRegion={initialRegion}
        geoFeatures={[...features, ...geoFeatures]}
        customLayers={customLayers}
        selectedFeature={selectedFeature}
        measurementMode={measurementMode}
        onFeatureCreated={onFeatureCreated}
        onFeatureDeleted={onFeatureDeleted}
        onFeatureClick={handleFeatureClick}
        onLocationSelect={onLocationSelect}
        onMeasurementResult={(result: any) => setMeasurementResults(prev => [...prev, result])}
        enableDrawing={enableDrawing}
        enableMeasurement={enableMeasurement}
        enableAnalysis={enableAnalysis}
        style={styles.map}
      />
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  map: {
    flex: 1,
  },
});

export default EnhancedMapWithFixes;
