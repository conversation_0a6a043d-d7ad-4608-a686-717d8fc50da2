import React, { useRef, useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Modal,
  Dimensions,
} from 'react-native';
import { useTheme } from '@/hooks/useTheme';
import { Edit3, Trash2, RotateCcw, Check, X } from 'lucide-react-native';

interface SignatureCaptureProps {
  value?: string;
  onChange: (signature: string | null) => void;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
}

export default function SignatureCapture({
  value,
  onChange,
  placeholder = 'Add signature',
  required = false,
  disabled = false,
}: SignatureCaptureProps) {
  const { theme } = useTheme();
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [showSignaturePad, setShowSignaturePad] = useState(false);
  const [hasDrawn, setHasDrawn] = useState(false);

  const screenWidth = Dimensions.get('window').width;
  const padWidth = Math.min(screenWidth - 32, 400);
  const padHeight = 200;

  useEffect(() => {
    if (showSignaturePad && canvasRef.current) {
      const canvas = canvasRef.current;
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.strokeStyle = '#000000';
        ctx.lineWidth = 2;
        ctx.lineCap = 'round';
        ctx.lineJoin = 'round';
      }
    }
  }, [showSignaturePad]);

  const startDrawing = (event: React.MouseEvent<HTMLCanvasElement>) => {
    if (disabled) return;
    
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    const ctx = canvas.getContext('2d');
    if (ctx) {
      ctx.beginPath();
      ctx.moveTo(x, y);
      setIsDrawing(true);
    }
  };

  const draw = (event: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing || disabled) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    const ctx = canvas.getContext('2d');
    if (ctx) {
      ctx.lineTo(x, y);
      ctx.stroke();
      setHasDrawn(true);
    }
  };

  const stopDrawing = () => {
    setIsDrawing(false);
  };

  const clearSignature = () => {
    const canvas = canvasRef.current;
    if (canvas) {
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        setHasDrawn(false);
      }
    }
  };

  const saveSignature = () => {
    if (!hasDrawn) {
      Alert.alert('No Signature', 'Please draw a signature before saving.');
      return;
    }

    const canvas = canvasRef.current;
    if (canvas) {
      const dataURL = canvas.toDataURL('image/png');
      onChange(dataURL);
      setShowSignaturePad(false);
    }
  };

  const removeSignature = () => {
    Alert.alert(
      'Remove Signature',
      'Are you sure you want to remove this signature?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: () => onChange(null),
        },
      ]
    );
  };

  const SignaturePadModal = () => (
    <Modal
      visible={showSignaturePad}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={() => setShowSignaturePad(false)}
    >
      <View style={[styles.modal, { backgroundColor: theme.colors.background }]}>
        <View style={[styles.modalHeader, { borderBottomColor: theme.colors.border }]}>
          <TouchableOpacity onPress={() => setShowSignaturePad(false)}>
            <X size={24} color={theme.colors.text} />
          </TouchableOpacity>
          <Text style={[styles.modalTitle, { color: theme.colors.text }]}>
            Draw Your Signature
          </Text>
          <TouchableOpacity onPress={saveSignature}>
            <Text style={[styles.saveText, { color: theme.colors.primary }]}>Save</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.signaturePadContainer}>
          <View
            style={[
              styles.canvasContainer,
              { 
                backgroundColor: theme.colors.background,
                borderColor: theme.colors.border,
                width: padWidth,
                height: padHeight,
              }
            ]}
          >
            <canvas
              ref={canvasRef}
              width={padWidth}
              height={padHeight}
              style={styles.canvas}
              onMouseDown={startDrawing}
              onMouseMove={draw}
              onMouseUp={stopDrawing}
              onMouseLeave={stopDrawing}
            />
            
            {!hasDrawn && (
              <View style={styles.placeholderOverlay}>
                <Text style={[styles.placeholderText, { color: theme.colors.muted }]}>
                  Draw your signature here
                </Text>
              </View>
            )}
          </View>
        </View>

        <View style={styles.signatureControls}>
          <TouchableOpacity
            style={[styles.controlButton, { backgroundColor: theme.colors.muted }]}
            onPress={clearSignature}
          >
            <Trash2 size={16} color="white" />
            <Text style={styles.controlButtonText}>Clear</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.controlButton, { backgroundColor: theme.colors.primary }]}
            onPress={saveSignature}
          >
            <Check size={16} color="white" />
            <Text style={styles.controlButtonText}>Save Signature</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );

  if (value) {
    return (
      <View style={[styles.container, { borderColor: theme.colors.border }]}>
        <View style={styles.signatureDisplay}>
          <Text style={[styles.signatureLabel, { color: theme.colors.text }]}>
            Signature Captured
          </Text>
          
          <View style={[styles.signaturePreview, { backgroundColor: theme.colors.background, borderColor: theme.colors.border }]}>
            <img src={value} alt="Signature" style={{ maxWidth: '100%', maxHeight: 100 }} />
          </View>
        </View>

        <View style={styles.signatureActions}>
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: theme.colors.secondary }]}
            onPress={() => setShowSignaturePad(true)}
            disabled={disabled}
          >
            <RotateCcw size={16} color="white" />
            <Text style={styles.actionButtonText}>Replace</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: theme.colors.error }]}
            onPress={removeSignature}
            disabled={disabled}
          >
            <Trash2 size={16} color="white" />
            <Text style={styles.actionButtonText}>Remove</Text>
          </TouchableOpacity>
        </View>

        {showSignaturePad && <SignaturePadModal />}
      </View>
    );
  }

  return (
    <>
      <TouchableOpacity
        style={[
          styles.captureButton,
          {
            backgroundColor: theme.colors.background,
            borderColor: theme.colors.border,
            opacity: disabled ? 0.5 : 1,
          },
        ]}
        onPress={() => setShowSignaturePad(true)}
        disabled={disabled}
      >
        <Edit3 size={32} color={theme.colors.muted} />
        <Text style={[styles.captureText, { color: theme.colors.muted }]}>
          {placeholder}
        </Text>
        {required && (
          <Text style={[styles.requiredText, { color: theme.colors.error }]}>
            Required
          </Text>
        )}
      </TouchableOpacity>

      {showSignaturePad && <SignaturePadModal />}
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 8,
    borderWidth: 1,
    padding: 16,
    gap: 12,
  },
  signatureDisplay: {
    gap: 8,
  },
  signatureLabel: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
  },
  signaturePreview: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
    borderRadius: 8,
    borderWidth: 1,
    minHeight: 100,
  },
  signatureActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    borderRadius: 6,
    gap: 4,
  },
  actionButtonText: {
    color: 'white',
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
  },
  captureButton: {
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderStyle: 'dashed',
    borderRadius: 8,
    paddingVertical: 40,
    gap: 8,
  },
  captureText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
  },
  requiredText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },
  modal: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
  },
  saveText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
  },
  signaturePadContainer: {
    padding: 16,
    alignItems: 'center',
  },
  canvasContainer: {
    borderWidth: 1,
    borderRadius: 8,
    position: 'relative',
    overflow: 'hidden',
  },
  canvas: {
    display: 'block',
    cursor: 'crosshair',
  },
  placeholderOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    pointerEvents: 'none',
  },
  placeholderText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
  },
  signatureControls: {
    flexDirection: 'row',
    padding: 16,
    gap: 12,
  },
  controlButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    gap: 6,
  },
  controlButtonText: {
    color: 'white',
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
  },
});
