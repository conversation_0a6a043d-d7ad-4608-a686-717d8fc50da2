import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router, useLocalSearchParams } from 'expo-router';
import { useTheme } from '@/hooks/useTheme';
import {
  ArrowLeft,
  Calendar,
  Camera,
  MapPin,
  Send,
  CheckSquare,
  Square,
} from 'lucide-react-native';
import { FormField } from './builder';

interface FormData {
  name: string;
  description: string;
  fields: FormField[];
}

interface FieldRendererProps {
  field: FormField;
  value: any;
  onChange: (value: any) => void;
  error?: string;
}

function FieldRenderer({ field, value, onChange, error }: FieldRendererProps) {
  const { theme } = useTheme();

  const renderField = () => {
    switch (field.type) {
      case 'text':
        return (
          <TextInput
            style={[styles.input, { backgroundColor: theme.colors.background, color: theme.colors.text }]}
            value={value || ''}
            onChangeText={onChange}
            placeholder={field.placeholder || `Enter ${field.label.toLowerCase()}`}
            placeholderTextColor={theme.colors.muted}
          />
        );

      case 'textarea':
        return (
          <TextInput
            style={[styles.textArea, { backgroundColor: theme.colors.background, color: theme.colors.text }]}
            value={value || ''}
            onChangeText={onChange}
            placeholder={field.placeholder || `Enter ${field.label.toLowerCase()}`}
            placeholderTextColor={theme.colors.muted}
            multiline
            numberOfLines={4}
          />
        );

      case 'number':
        return (
          <TextInput
            style={[styles.input, { backgroundColor: theme.colors.background, color: theme.colors.text }]}
            value={value || ''}
            onChangeText={onChange}
            placeholder={field.placeholder || 'Enter number'}
            placeholderTextColor={theme.colors.muted}
            keyboardType="numeric"
          />
        );

      case 'select':
        return (
          <View>
            {(field.options || []).map((option, index) => (
              <TouchableOpacity
                key={index}
                style={[
                  styles.optionButton,
                  { backgroundColor: value === option ? theme.colors.primary + '20' : theme.colors.background },
                ]}
                onPress={() => onChange(option)}
              >
                <View style={[
                  styles.radio,
                  { borderColor: value === option ? theme.colors.primary : theme.colors.border },
                  value === option && { backgroundColor: theme.colors.primary }
                ]}>
                  {value === option && <View style={styles.radioInner} />}
                </View>
                <Text style={[styles.optionText, { color: theme.colors.text }]}>{option}</Text>
              </TouchableOpacity>
            ))}
          </View>
        );

      case 'multiselect':
        const selectedValues = Array.isArray(value) ? value : [];
        return (
          <View>
            {(field.options || []).map((option, index) => {
              const isSelected = selectedValues.includes(option);
              return (
                <TouchableOpacity
                  key={index}
                  style={[styles.optionButton, { backgroundColor: theme.colors.background }]}
                  onPress={() => {
                    if (isSelected) {
                      onChange(selectedValues.filter(v => v !== option));
                    } else {
                      onChange([...selectedValues, option]);
                    }
                  }}
                >
                  {isSelected ? (
                    <CheckSquare size={20} color={theme.colors.primary} />
                  ) : (
                    <Square size={20} color={theme.colors.border} />
                  )}
                  <Text style={[styles.optionText, { color: theme.colors.text }]}>{option}</Text>
                </TouchableOpacity>
              );
            })}
          </View>
        );

      case 'date':
        return (
          <TouchableOpacity
            style={[styles.specialButton, { backgroundColor: theme.colors.background }]}
            onPress={() => Alert.alert('Date Picker', 'Date picker would open here')}
          >
            <Calendar size={20} color={theme.colors.primary} />
            <Text style={[styles.specialButtonText, { color: value ? theme.colors.text : theme.colors.muted }]}>
              {value || 'Select date'}
            </Text>
          </TouchableOpacity>
        );

      case 'location':
        return (
          <TouchableOpacity
            style={[styles.specialButton, { backgroundColor: theme.colors.background }]}
            onPress={() => Alert.alert('Location', 'GPS coordinates would be captured here')}
          >
            <MapPin size={20} color={theme.colors.primary} />
            <Text style={[styles.specialButtonText, { color: value ? theme.colors.text : theme.colors.muted }]}>
              {value ? `${value.lat}, ${value.lng}` : 'Capture location'}
            </Text>
          </TouchableOpacity>
        );

      case 'photo':
        return (
          <TouchableOpacity
            style={[styles.specialButton, { backgroundColor: theme.colors.background }]}
            onPress={() => Alert.alert('Camera', 'Camera would open here')}
          >
            <Camera size={20} color={theme.colors.primary} />
            <Text style={[styles.specialButtonText, { color: value ? theme.colors.text : theme.colors.muted }]}>
              {value ? 'Photo captured' : 'Take photo'}
            </Text>
          </TouchableOpacity>
        );

      default:
        return (
          <Text style={[styles.unsupportedField, { color: theme.colors.muted }]}>
            Unsupported field type: {field.type}
          </Text>
        );
    }
  };

  return (
    <View style={styles.fieldContainer}>
      <View style={styles.fieldHeader}>
        <Text style={[styles.fieldLabel, { color: theme.colors.text }]}>
          {field.label}
          {field.required && <Text style={[styles.required, { color: theme.colors.error }]}> *</Text>}
        </Text>
      </View>
      {renderField()}
      {error && (
        <Text style={[styles.errorText, { color: theme.colors.error }]}>{error}</Text>
      )}
    </View>
  );
}

export default function FormPreviewScreen() {
  const { theme } = useTheme();
  const params = useLocalSearchParams();
  const [formValues, setFormValues] = useState<Record<string, any>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});

  let formData: FormData;
  try {
    formData = JSON.parse(params.formData as string);
  } catch (error) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <Text style={[styles.errorMessage, { color: theme.colors.error }]}>
          Error loading form data
        </Text>
      </SafeAreaView>
    );
  }

  const updateFieldValue = (fieldId: string, value: any) => {
    setFormValues(prev => ({ ...prev, [fieldId]: value }));
    // Clear error when user starts typing
    if (errors[fieldId]) {
      setErrors(prev => ({ ...prev, [fieldId]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    formData.fields.forEach(field => {
      const value = formValues[field.id];

      // Required field validation
      if (field.required) {
        if (!value || (Array.isArray(value) && value.length === 0) || value.toString().trim() === '') {
          newErrors[field.id] = `${field.label} is required`;
        }
      }

      // Number validation
      if (field.type === 'number' && value) {
        const numValue = parseFloat(value);
        if (isNaN(numValue)) {
          newErrors[field.id] = 'Please enter a valid number';
        } else {
          if (field.validation?.min !== undefined && numValue < field.validation.min) {
            newErrors[field.id] = `Minimum value is ${field.validation.min}`;
          }
          if (field.validation?.max !== undefined && numValue > field.validation.max) {
            newErrors[field.id] = `Maximum value is ${field.validation.max}`;
          }
        }
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const submitForm = () => {
    if (validateForm()) {
      Alert.alert(
        'Form Submitted',
        'This is a preview. In a real app, the data would be saved.',
        [{ text: 'OK' }]
      );
      console.log('Form values:', formValues);
    } else {
      Alert.alert('Validation Error', 'Please fix the errors and try again');
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={[styles.headerButton, { backgroundColor: theme.colors.card }]}
          onPress={() => router.back()}
        >
          <ArrowLeft size={20} color={theme.colors.text} />
        </TouchableOpacity>
        <View style={styles.headerContent}>
          <Text style={[styles.headerTitle, { color: theme.colors.text }]}>Form Preview</Text>
          <Text style={[styles.headerSubtitle, { color: theme.colors.muted }]}>
            👀 See how your form will look
          </Text>
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Form Header */}
        <View style={[styles.formHeader, { backgroundColor: theme.colors.card }]}>
          <Text style={[styles.formTitle, { color: theme.colors.text }]}>
            {formData.name || 'Untitled Form'}
          </Text>
          {formData.description && (
            <Text style={[styles.formDescription, { color: theme.colors.muted }]}>
              {formData.description}
            </Text>
          )}
        </View>

        {/* Form Fields */}
        <View style={[styles.formSection, { backgroundColor: theme.colors.card }]}>
          {formData.fields.map((field) => (
            <FieldRenderer
              key={field.id}
              field={field}
              value={formValues[field.id]}
              onChange={(value) => updateFieldValue(field.id, value)}
              error={errors[field.id]}
            />
          ))}

          {formData.fields.length === 0 && (
            <View style={styles.emptyState}>
              <Text style={[styles.emptyStateText, { color: theme.colors.muted }]}>
                No fields in this form yet
              </Text>
            </View>
          )}
        </View>

        <View style={styles.bottomSpace} />
      </ScrollView>

      {/* Submit Button */}
      {formData.fields.length > 0 && (
        <View style={styles.footer}>
          <TouchableOpacity
            style={[styles.submitButton, { backgroundColor: theme.colors.primary }]}
            onPress={submitForm}
          >
            <Send size={20} color="white" />
            <Text style={styles.submitButtonText}>Submit Form</Text>
          </TouchableOpacity>
        </View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    gap: 12,
  },
  headerButton: {
    padding: 12,
    borderRadius: 12,
  },
  headerContent: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  headerSubtitle: {
    fontSize: 14,
    marginTop: 2,
  },
  content: {
    flex: 1,
  },
  formHeader: {
    margin: 20,
    padding: 24,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  formTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  formDescription: {
    fontSize: 16,
    lineHeight: 24,
  },
  formSection: {
    margin: 20,
    marginTop: 0,
    padding: 24,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  fieldContainer: {
    marginBottom: 24,
  },
  fieldHeader: {
    marginBottom: 8,
  },
  fieldLabel: {
    fontSize: 16,
    fontWeight: '600',
  },
  required: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  input: {
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  textArea: {
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    height: 100,
    textAlignVertical: 'top',
  },
  optionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    gap: 12,
  },
  radio: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioInner: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'white',
  },
  optionText: {
    fontSize: 16,
    flex: 1,
  },
  specialButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    gap: 12,
  },
  specialButtonText: {
    fontSize: 16,
    flex: 1,
  },
  unsupportedField: {
    fontSize: 14,
    fontStyle: 'italic',
    padding: 16,
  },
  errorText: {
    fontSize: 14,
    marginTop: 4,
  },
  emptyState: {
    padding: 40,
    alignItems: 'center',
  },
  emptyStateText: {
    fontSize: 16,
    textAlign: 'center',
  },
  footer: {
    padding: 20,
  },
  submitButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 18,
    borderRadius: 16,
    gap: 12,
  },
  submitButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  errorMessage: {
    fontSize: 16,
    textAlign: 'center',
    margin: 20,
  },
  bottomSpace: {
    height: 100,
  },
});
