import { useState, useEffect } from 'react';
import { Submission } from '@/types';

// Simple ID generator for demo purposes
const generateId = () => Math.random().toString(36).substr(2, 9);

// Sample data for demo purposes
const SAMPLE_SUBMISSIONS: Submission[] = [
  {
    id: 'sub1',
    formId: 'form1',
    projectId: '1',
    userId: '1',
    status: 'synced',
    startedAt: Date.now() - 600000,
    completedAt: Date.now() - 590000,
    syncedAt: Date.now() - 580000,
    data: {
      species: 'Douglas Fir',
      diameter: 45,
      height: 22,
      health: 'healthy',
      notes: 'Excellent specimen in a protected area.',
    },
    location: {
      latitude: 45.5231,
      longitude: -122.6765,
      accuracy: 5,
      timestamp: Date.now() - 595000,
    },
    media: [
      {
        id: 'media1',
        questionId: 'photo',
        type: 'photo',
        uri: 'https://images.pexels.com/photos/167698/pexels-photo-167698.jpeg',
        synced: true,
        timestamp: Date.now() - 593000,
      }
    ]
  },
  {
    id: 'sub2',
    formId: 'form2',
    projectId: '1',
    userId: '1',
    status: 'completed',
    startedAt: Date.now() - 500000,
    completedAt: Date.now() - 490000,
    data: {
      species: 'Black Bear',
      count: 1,
      behavior: ['feeding', 'traveling'],
      notes: 'Foraging for berries along the forest edge.',
    },
    location: {
      latitude: 45.5245,
      longitude: -122.6789,
      accuracy: 8,
      timestamp: Date.now() - 495000,
    },
    media: [
      {
        id: 'media2',
        questionId: 'photo',
        type: 'photo',
        uri: 'https://images.pexels.com/photos/158109/kodiak-brown-bear-adult-portrait-wildlife-158109.jpeg',
        localUri: 'file:///data/media/bear.jpg',
        synced: false,
        timestamp: Date.now() - 493000,
      }
    ]
  },
  {
    id: 'sub3',
    formId: 'form3',
    projectId: '2',
    userId: '2',
    status: 'draft',
    startedAt: Date.now() - 400000,
    data: {
      sample_id: 'WQ-2023-001',
      temperature: 18.5,
    },
    location: {
      latitude: 40.7128,
      longitude: -74.0060,
      accuracy: 3,
      timestamp: Date.now() - 395000,
    }
  }
];

export function useSubmissions() {
  const [submissions, setSubmissions] = useState<Submission[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  
  const fetchSubmissions = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // In a real app, this would be an API call or database query
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setSubmissions(SAMPLE_SUBMISSIONS);
    } catch (err) {
      console.error('Error fetching submissions:', err);
      setError(err instanceof Error ? err : new Error('Unknown error occurred'));
    } finally {
      setLoading(false);
    }
  };
  
  const getSubmissionById = (id: string) => {
    return submissions.find(submission => submission.id === id) || null;
  };
  
  const getSubmissionsByForm = (formId: string) => {
    return submissions.filter(submission => submission.formId === formId);
  };
  
  const getSubmissionsByProject = (projectId: string) => {
    return submissions.filter(submission => submission.projectId === projectId);
  };
  
  const createSubmission = async (submission: Partial<Submission>) => {
    try {
      const newSubmission: Submission = {
        id: generateId(),
        formId: submission.formId || '',
        projectId: submission.projectId || '',
        userId: submission.userId || '',
        status: 'draft',
        startedAt: Date.now(),
        data: submission.data || {},
        location: submission.location,
        media: submission.media || [],
      };
      
      // In a real app, this would be an API call or database insertion
      setSubmissions(prev => [...prev, newSubmission]);
      return newSubmission;
    } catch (err) {
      console.error('Error creating submission:', err);
      throw err;
    }
  };
  
  const updateSubmission = async (id: string, updates: Partial<Submission>) => {
    try {
      // In a real app, this would be an API call or database update
      setSubmissions(prev => 
        prev.map(submission => 
          submission.id === id ? { ...submission, ...updates } : submission
        )
      );
    } catch (err) {
      console.error('Error updating submission:', err);
      throw err;
    }
  };
  
  const deleteSubmission = async (id: string) => {
    try {
      // In a real app, this would be an API call or database deletion
      setSubmissions(prev => prev.filter(submission => submission.id !== id));
    } catch (err) {
      console.error('Error deleting submission:', err);
      throw err;
    }
  };
  
  const completeSubmission = async (id: string) => {
    try {
      updateSubmission(id, {
        status: 'completed',
        completedAt: Date.now(),
      });
    } catch (err) {
      console.error('Error completing submission:', err);
      throw err;
    }
  };
  
  const syncSubmission = async (id: string) => {
    try {
      // In a real app, this would be an API call to sync with server
      updateSubmission(id, {
        status: 'synced',
        syncedAt: Date.now(),
      });
    } catch (err) {
      console.error('Error syncing submission:', err);
      throw err;
    }
  };
  
  // Load submissions on mount
  useEffect(() => {
    fetchSubmissions();
  }, []);
  
  return {
    submissions,
    loading,
    error,
    fetchSubmissions,
    getSubmissionById,
    getSubmissionsByForm,
    getSubmissionsByProject,
    createSubmission,
    updateSubmission,
    deleteSubmission,
    completeSubmission,
    syncSubmission,
  };
}