/**
 * Component Import Validator
 * Validates React component imports and provides helpful error messages
 */

import React from 'react';

interface ComponentValidationResult {
  isValid: boolean;
  componentName: string;
  errors: string[];
  warnings: string[];
  suggestions: string[];
}

/**
 * Validates if a component is properly imported and usable
 */
export const validateComponent = (
  component: any,
  componentName: string = 'Unknown'
): ComponentValidationResult => {
  const result: ComponentValidationResult = {
    isValid: true,
    componentName,
    errors: [],
    warnings: [],
    suggestions: [],
  };

  // Check if component exists
  if (component === undefined) {
    result.isValid = false;
    result.errors.push(`Component '${componentName}' is undefined`);
    result.suggestions.push(`Check if '${componentName}' is properly exported from its module`);
    result.suggestions.push(`Verify the import path is correct`);
    return result;
  }

  if (component === null) {
    result.isValid = false;
    result.errors.push(`Component '${componentName}' is null`);
    result.suggestions.push(`Check if the component file exists`);
    return result;
  }

  // Check if it's a valid React component
  if (typeof component !== 'function' && typeof component !== 'object') {
    result.isValid = false;
    result.errors.push(`'${componentName}' is not a valid React component (got ${typeof component})`);
    result.suggestions.push(`Ensure '${componentName}' is a React function or class component`);
    return result;
  }

  // Check for common React component patterns
  if (typeof component === 'function') {
    // Function component checks
    if (component.length > 1) {
      result.warnings.push(`Function component '${componentName}' has ${component.length} parameters (expected 0-1)`);
    }

    // Check if it has a displayName
    if (!component.displayName && !component.name) {
      result.warnings.push(`Component '${componentName}' has no displayName or function name`);
    }
  } else if (typeof component === 'object') {
    // Object/class component checks
    if (!component.render && !component.$$typeof) {
      result.warnings.push(`Object '${componentName}' doesn't appear to be a React component`);
    }
  }

  // Check for React.memo, React.forwardRef, etc.
  if (component.$$typeof) {
    const symbolDescription = component.$$typeof.description || component.$$typeof.toString();
    if (symbolDescription.includes('react.memo')) {
      result.warnings.push(`'${componentName}' is wrapped with React.memo`);
    }
    if (symbolDescription.includes('react.forward_ref')) {
      result.warnings.push(`'${componentName}' is wrapped with React.forwardRef`);
    }
  }

  return result;
};

/**
 * Safe component wrapper that validates before rendering
 */
export const SafeComponent: React.FC<{
  component: any;
  componentName?: string;
  props?: any;
  fallback?: React.ReactNode;
  onError?: (error: Error) => void;
}> = ({ 
  component, 
  componentName = 'SafeComponent', 
  props = {}, 
  fallback = null,
  onError 
}) => {
  try {
    // Validate the component
    const validation = validateComponent(component, componentName);
    
    if (!validation.isValid) {
      const error = new Error(`Component validation failed: ${validation.errors.join(', ')}`);
      console.error('🚨 Component validation failed:', validation);
      
      if (onError) {
        onError(error);
      }
      
      return fallback || (
        <div style={{ 
          padding: '10px', 
          backgroundColor: '#ffebee', 
          border: '1px solid #f44336',
          borderRadius: '4px',
          color: '#c62828' 
        }}>
          <strong>Component Error:</strong> {componentName} failed validation
          <ul>
            {validation.errors.map((error, index) => (
              <li key={index}>{error}</li>
            ))}
          </ul>
        </div>
      );
    }

    // Log warnings in development
    if (__DEV__ && validation.warnings.length > 0) {
      console.warn(`⚠️ Component warnings for '${componentName}':`, validation.warnings);
    }

    // Render the component
    return React.createElement(component, props);
  } catch (error) {
    console.error(`🚨 Error rendering component '${componentName}':`, error);
    
    if (onError) {
      onError(error as Error);
    }
    
    return fallback || (
      <div style={{ 
        padding: '10px', 
        backgroundColor: '#ffebee', 
        border: '1px solid #f44336',
        borderRadius: '4px',
        color: '#c62828' 
      }}>
        <strong>Render Error:</strong> {componentName} failed to render
        <br />
        {(error as Error).message}
      </div>
    );
  }
};

/**
 * Validates multiple components at once
 */
export const validateComponents = (
  components: Record<string, any>
): Record<string, ComponentValidationResult> => {
  const results: Record<string, ComponentValidationResult> = {};
  
  for (const [name, component] of Object.entries(components)) {
    results[name] = validateComponent(component, name);
  }
  
  return results;
};

/**
 * Creates a safe import wrapper
 */
export const createSafeImport = <T = any>(
  importFn: () => Promise<T>,
  componentName: string
): Promise<T> => {
  return importFn().catch((error) => {
    console.error(`🚨 Failed to import '${componentName}':`, error);
    
    // Return a fallback component
    return {
      default: () => React.createElement('div', {
        style: {
          padding: '20px',
          backgroundColor: '#fff3cd',
          border: '1px solid #ffeaa7',
          borderRadius: '4px',
          color: '#856404',
        }
      }, `Failed to load component: ${componentName}`)
    } as T;
  });
};

/**
 * HOC for adding component validation
 */
export const withValidation = <P extends object>(
  Component: React.ComponentType<P>,
  componentName?: string
) => {
  const WrappedComponent = React.forwardRef<any, P>((props, ref) => {
    const validation = validateComponent(Component, componentName || Component.name);
    
    if (!validation.isValid) {
      console.error(`🚨 Component validation failed for ${componentName}:`, validation);
      return (
        <div style={{ 
          padding: '10px', 
          backgroundColor: '#ffebee', 
          border: '1px solid #f44336',
          borderRadius: '4px',
          color: '#c62828' 
        }}>
          Component validation failed: {validation.errors.join(', ')}
        </div>
      );
    }
    
    return React.createElement(Component, { ...props, ref });
  });
  
  WrappedComponent.displayName = `withValidation(${componentName || Component.name || 'Component'})`;
  
  return WrappedComponent;
};

/**
 * Debug utility to log component information
 */
export const debugComponent = (component: any, componentName: string = 'Unknown') => {
  if (!__DEV__) return;
  
  console.group(`🔍 Component Debug: ${componentName}`);
  console.log('Type:', typeof component);
  console.log('Value:', component);
  
  if (typeof component === 'function') {
    console.log('Function name:', component.name);
    console.log('Display name:', component.displayName);
    console.log('Parameters:', component.length);
  }
  
  if (component && component.$$typeof) {
    console.log('React type:', component.$$typeof);
  }
  
  const validation = validateComponent(component, componentName);
  console.log('Validation:', validation);
  
  console.groupEnd();
};

export default {
  validateComponent,
  SafeComponent,
  validateComponents,
  createSafeImport,
  withValidation,
  debugComponent,
};
