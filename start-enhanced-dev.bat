@echo off
echo 🚀 Starting Enhanced FieldSyncPro Development Server...
echo =====================================================

cd /d "D:\devprojects\FieldSyncPro"

echo ✅ Changed to project directory
echo.

echo 📦 Checking dependencies...
if not exist node_modules (
    echo Installing dependencies...
    npm install
)

echo.
echo 🏗️  Starting development server...
echo.
echo 📱 Access the app at:
echo    Web: http://localhost:8089
echo    Mobile: Scan QR code with Expo Go
echo.
echo 🎯 Enhanced Features Available:
echo    • Multi-page forms with sections
echo    • Phone input with OCR scanning
echo    • QR/Barcode scanner
echo    • Video recording with playback
echo    • Audio recording with controls
echo    • Multi-photo capture with GPS
echo    • Enhanced form builder
echo    • Cross-platform compatibility
echo.
echo ✨ Demo available at: /enhanced-form-demo
echo.

npx expo start --port 8089 --web --clear
