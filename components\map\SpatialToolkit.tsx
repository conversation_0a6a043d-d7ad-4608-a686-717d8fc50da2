import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Modal,
  Alert,
  Dimensions,
} from 'react-native';
import { useTheme } from '@/hooks/useTheme';
import {
  BarChart3,
  Calculator,
  Layers,
  Zap,
  MapPin,
  TrendingUp,
  PieChart,
  Target,
  Compass,
  Route,
  Activity,
  Thermometer,
  Droplets,
  Wind,
  Sun,
  Cloud,
  AlertTriangle,
  Shield,
  ChevronDown,
  ChevronRight,
} from 'lucide-react-native';
import { LatLng } from 'react-native-maps';
import {
  calculateDistance,
  calculateArea,
  createBuffer,
  pointInPolygon,
  getPolygonCenter,
  getBoundingBox,
} from './spatial/SpatialAnalysis';

interface SpatialToolkitProps {
  visible: boolean;
  onClose: () => void;
  features: any[];
  selectedFeature: any;
  onAnalysisResult: (result: any) => void;
}

const { width: SCREEN_WIDTH } = Dimensions.get('window');

export default function SpatialToolkit({
  visible,
  onClose,
  features,
  selectedFeature,
  onAnalysisResult,
}: SpatialToolkitProps) {
  const { theme } = useTheme();
  const [activeCategory, setActiveCategory] = useState<string>('measurement');
  const [analysisResults, setAnalysisResults] = useState<any[]>([]);
  const [expandedSections, setExpandedSections] = useState<{ [key: string]: boolean }>({
    measurement: true,
    spatial: false,
    environmental: false,
    network: false,
  });

  const analysisCategories = [
    {
      id: 'measurement',
      name: 'Measurement & Geometry',
      icon: Calculator,
      color: theme.colors.primary,
      tools: [
        { id: 'distance', name: 'Distance Measurement', description: 'Calculate distances between points' },
        { id: 'area', name: 'Area Calculation', description: 'Calculate polygon areas and perimeters' },
        { id: 'buffer', name: 'Buffer Analysis', description: 'Create buffer zones around features' },
        { id: 'centroid', name: 'Centroid Analysis', description: 'Find geometric centers' },
        { id: 'bounding_box', name: 'Bounding Box', description: 'Calculate feature extents' },
      ],
    },
    {
      id: 'spatial',
      name: 'Spatial Relationships',
      icon: Layers,
      color: theme.colors.secondary,
      tools: [
        { id: 'intersection', name: 'Intersection Analysis', description: 'Find overlapping areas' },
        { id: 'proximity', name: 'Proximity Analysis', description: 'Find nearby features' },
        { id: 'containment', name: 'Point-in-Polygon', description: 'Check spatial containment' },
        { id: 'voronoi', name: 'Voronoi Diagram', description: 'Create proximity zones' },
        { id: 'convex_hull', name: 'Convex Hull', description: 'Find outer boundary' },
      ],
    },
    {
      id: 'environmental',
      name: 'Environmental Analysis',
      icon: Thermometer,
      color: theme.colors.success,
      tools: [
        { id: 'elevation', name: 'Elevation Profile', description: 'Analyze terrain elevation' },
        { id: 'slope', name: 'Slope Analysis', description: 'Calculate terrain slopes' },
        { id: 'watershed', name: 'Watershed Analysis', description: 'Identify drainage basins' },
        { id: 'viewshed', name: 'Viewshed Analysis', description: 'Analyze visibility areas' },
        { id: 'climate', name: 'Climate Zones', description: 'Environmental zone analysis' },
      ],
    },
    {
      id: 'network',
      name: 'Network & Routing',
      icon: Route,
      color: theme.colors.warning,
      tools: [
        { id: 'shortest_path', name: 'Shortest Path', description: 'Find optimal routes' },
        { id: 'service_area', name: 'Service Area', description: 'Calculate accessibility zones' },
        { id: 'flow_analysis', name: 'Flow Analysis', description: 'Analyze movement patterns' },
        { id: 'connectivity', name: 'Connectivity', description: 'Network connectivity analysis' },
        { id: 'isochrone', name: 'Isochrone Analysis', description: 'Time-based accessibility' },
      ],
    },
  ];

  const performAnalysis = (toolId: string) => {
    if (!selectedFeature) {
      Alert.alert('No Feature Selected', 'Please select a feature to analyze.');
      return;
    }

    switch (toolId) {
      case 'distance':
        performDistanceAnalysis();
        break;
      case 'area':
        performAreaAnalysis();
        break;
      case 'buffer':
        performBufferAnalysis();
        break;
      case 'centroid':
        performCentroidAnalysis();
        break;
      case 'bounding_box':
        performBoundingBoxAnalysis();
        break;
      case 'intersection':
        performIntersectionAnalysis();
        break;
      case 'proximity':
        performProximityAnalysis();
        break;
      case 'containment':
        performContainmentAnalysis();
        break;
      default:
        Alert.alert('Coming Soon', `${toolId} analysis will be implemented in a future update.`);
    }
  };

  const performDistanceAnalysis = () => {
    if (selectedFeature.type === 'line') {
      let totalDistance = 0;
      const coordinates = selectedFeature.coordinates;
      
      for (let i = 0; i < coordinates.length - 1; i++) {
        totalDistance += calculateDistance(coordinates[i], coordinates[i + 1]);
      }
      
      const result = {
        id: `distance_${Date.now()}`,
        type: 'Distance Analysis',
        feature: selectedFeature.properties.name,
        results: {
          totalDistance: `${(totalDistance / 1000).toFixed(2)} km`,
          segments: coordinates.length - 1,
          averageSegmentLength: `${(totalDistance / (coordinates.length - 1)).toFixed(0)} m`,
        },
        timestamp: new Date().toLocaleString(),
      };
      
      setAnalysisResults([...analysisResults, result]);
      onAnalysisResult(result);
    }
  };

  const performAreaAnalysis = () => {
    if (selectedFeature.type === 'polygon') {
      const area = calculateArea(selectedFeature.coordinates);
      const center = getPolygonCenter(selectedFeature.coordinates);
      const bbox = getBoundingBox(selectedFeature.coordinates);
      
      const result = {
        id: `area_${Date.now()}`,
        type: 'Area Analysis',
        feature: selectedFeature.properties.name,
        results: {
          area: `${(area / 10000).toFixed(2)} hectares`,
          areaSquareMeters: `${area.toFixed(0)} m²`,
          centroid: `${center.latitude.toFixed(6)}, ${center.longitude.toFixed(6)}`,
          boundingBox: {
            width: `${((bbox.maxLng - bbox.minLng) * 111000).toFixed(0)} m`,
            height: `${((bbox.maxLat - bbox.minLat) * 111000).toFixed(0)} m`,
          },
        },
        timestamp: new Date().toLocaleString(),
      };
      
      setAnalysisResults([...analysisResults, result]);
      onAnalysisResult(result);
    }
  };

  const performBufferAnalysis = () => {
    Alert.prompt(
      'Buffer Analysis',
      'Enter buffer distance in meters:',
      (distance) => {
        if (distance && selectedFeature) {
          const bufferDistance = parseFloat(distance);
          const buffer = createBuffer(selectedFeature.coordinates, bufferDistance);
          const bufferArea = calculateArea(buffer);
          
          const result = {
            id: `buffer_${Date.now()}`,
            type: 'Buffer Analysis',
            feature: selectedFeature.properties.name,
            results: {
              bufferDistance: `${bufferDistance} m`,
              bufferArea: `${(bufferArea / 10000).toFixed(2)} hectares`,
              bufferCoordinates: buffer,
            },
            timestamp: new Date().toLocaleString(),
          };
          
          setAnalysisResults([...analysisResults, result]);
          onAnalysisResult(result);
        }
      },
      'plain-text',
      '100'
    );
  };

  const performCentroidAnalysis = () => {
    const center = getPolygonCenter(selectedFeature.coordinates);
    
    const result = {
      id: `centroid_${Date.now()}`,
      type: 'Centroid Analysis',
      feature: selectedFeature.properties.name,
      results: {
        centroid: `${center.latitude.toFixed(6)}, ${center.longitude.toFixed(6)}`,
        method: 'Geometric centroid',
      },
      timestamp: new Date().toLocaleString(),
    };
    
    setAnalysisResults([...analysisResults, result]);
    onAnalysisResult(result);
  };

  const performBoundingBoxAnalysis = () => {
    const bbox = getBoundingBox(selectedFeature.coordinates);
    const width = (bbox.maxLng - bbox.minLng) * 111000; // Approximate meters
    const height = (bbox.maxLat - bbox.minLat) * 111000;
    
    const result = {
      id: `bbox_${Date.now()}`,
      type: 'Bounding Box Analysis',
      feature: selectedFeature.properties.name,
      results: {
        minLatitude: bbox.minLat.toFixed(6),
        maxLatitude: bbox.maxLat.toFixed(6),
        minLongitude: bbox.minLng.toFixed(6),
        maxLongitude: bbox.maxLng.toFixed(6),
        width: `${width.toFixed(0)} m`,
        height: `${height.toFixed(0)} m`,
        area: `${((width * height) / 10000).toFixed(2)} hectares`,
      },
      timestamp: new Date().toLocaleString(),
    };
    
    setAnalysisResults([...analysisResults, result]);
    onAnalysisResult(result);
  };

  const performIntersectionAnalysis = () => {
    const intersectingFeatures = features.filter(feature => 
      feature.id !== selectedFeature.id && feature.type === 'polygon'
    );
    
    if (intersectingFeatures.length === 0) {
      Alert.alert('No Features', 'No other polygon features found for intersection analysis.');
      return;
    }
    
    // Simplified intersection check (point-in-polygon for centroids)
    const intersections = intersectingFeatures.filter(feature => {
      const otherCenter = getPolygonCenter(feature.coordinates);
      return pointInPolygon(otherCenter, selectedFeature.coordinates);
    });
    
    const result = {
      id: `intersection_${Date.now()}`,
      type: 'Intersection Analysis',
      feature: selectedFeature.properties.name,
      results: {
        totalFeatures: intersectingFeatures.length,
        intersectingFeatures: intersections.length,
        intersections: intersections.map(f => f.properties.name),
      },
      timestamp: new Date().toLocaleString(),
    };
    
    setAnalysisResults([...analysisResults, result]);
    onAnalysisResult(result);
  };

  const performProximityAnalysis = () => {
    Alert.prompt(
      'Proximity Analysis',
      'Enter search radius in meters:',
      (radius) => {
        if (radius && selectedFeature) {
          const searchRadius = parseFloat(radius);
          const center = selectedFeature.type === 'point' 
            ? selectedFeature.coordinates 
            : getPolygonCenter(selectedFeature.coordinates);
          
          const nearbyFeatures = features.filter(feature => {
            if (feature.id === selectedFeature.id) return false;
            
            const featureCenter = feature.type === 'point' 
              ? feature.coordinates 
              : getPolygonCenter(feature.coordinates);
            
            const distance = calculateDistance(center, featureCenter);
            return distance <= searchRadius;
          });
          
          const result = {
            id: `proximity_${Date.now()}`,
            type: 'Proximity Analysis',
            feature: selectedFeature.properties.name,
            results: {
              searchRadius: `${searchRadius} m`,
              nearbyFeatures: nearbyFeatures.length,
              features: nearbyFeatures.map(f => ({
                name: f.properties.name,
                distance: `${calculateDistance(center, 
                  f.type === 'point' ? f.coordinates : getPolygonCenter(f.coordinates)
                ).toFixed(0)} m`,
              })),
            },
            timestamp: new Date().toLocaleString(),
          };
          
          setAnalysisResults([...analysisResults, result]);
          onAnalysisResult(result);
        }
      },
      'plain-text',
      '1000'
    );
  };

  const performContainmentAnalysis = () => {
    if (selectedFeature.type !== 'polygon') {
      Alert.alert('Invalid Feature', 'Containment analysis requires a polygon feature.');
      return;
    }
    
    const pointFeatures = features.filter(feature => 
      feature.type === 'point' && feature.id !== selectedFeature.id
    );
    
    const containedPoints = pointFeatures.filter(feature =>
      pointInPolygon(feature.coordinates, selectedFeature.coordinates)
    );
    
    const result = {
      id: `containment_${Date.now()}`,
      type: 'Point-in-Polygon Analysis',
      feature: selectedFeature.properties.name,
      results: {
        totalPoints: pointFeatures.length,
        containedPoints: containedPoints.length,
        percentage: pointFeatures.length > 0 
          ? `${((containedPoints.length / pointFeatures.length) * 100).toFixed(1)}%`
          : '0%',
        points: containedPoints.map(f => f.properties.name),
      },
      timestamp: new Date().toLocaleString(),
    };
    
    setAnalysisResults([...analysisResults, result]);
    onAnalysisResult(result);
  };

  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [sectionId]: !prev[sectionId],
    }));
  };

  const renderAnalysisCategory = (category: any) => (
    <View key={category.id} style={[styles.categoryContainer, { backgroundColor: theme.colors.card }]}>
      <TouchableOpacity
        style={styles.categoryHeader}
        onPress={() => toggleSection(category.id)}
      >
        <View style={styles.categoryInfo}>
          <View style={[styles.categoryIcon, { backgroundColor: category.color + '20' }]}>
            <category.icon size={20} color={category.color} />
          </View>
          <Text style={[styles.categoryName, { color: theme.colors.text }]}>
            {category.name}
          </Text>
        </View>
        {expandedSections[category.id] ? (
          <ChevronDown size={20} color={theme.colors.muted} />
        ) : (
          <ChevronRight size={20} color={theme.colors.muted} />
        )}
      </TouchableOpacity>
      
      {expandedSections[category.id] && (
        <View style={styles.toolsList}>
          {category.tools.map((tool: any) => (
            <TouchableOpacity
              key={tool.id}
              style={[styles.toolItem, { borderLeftColor: category.color }]}
              onPress={() => performAnalysis(tool.id)}
            >
              <View style={styles.toolInfo}>
                <Text style={[styles.toolName, { color: theme.colors.text }]}>
                  {tool.name}
                </Text>
                <Text style={[styles.toolDescription, { color: theme.colors.muted }]}>
                  {tool.description}
                </Text>
              </View>
              <ChevronRight size={16} color={theme.colors.muted} />
            </TouchableOpacity>
          ))}
        </View>
      )}
    </View>
  );

  const renderAnalysisResults = () => (
    <View style={[styles.resultsContainer, { backgroundColor: theme.colors.card }]}>
      <Text style={[styles.resultsTitle, { color: theme.colors.text }]}>
        Analysis Results ({analysisResults.length})
      </Text>
      
      <ScrollView style={styles.resultsList}>
        {analysisResults.map((result) => (
          <View key={result.id} style={[styles.resultItem, { backgroundColor: theme.colors.background }]}>
            <View style={styles.resultHeader}>
              <Text style={[styles.resultType, { color: theme.colors.text }]}>
                {result.type}
              </Text>
              <Text style={[styles.resultTimestamp, { color: theme.colors.muted }]}>
                {result.timestamp}
              </Text>
            </View>
            
            <Text style={[styles.resultFeature, { color: theme.colors.muted }]}>
              Feature: {result.feature}
            </Text>
            
            <View style={styles.resultData}>
              {Object.entries(result.results).map(([key, value]) => (
                <View key={key} style={styles.resultRow}>
                  <Text style={[styles.resultKey, { color: theme.colors.muted }]}>
                    {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}:
                  </Text>
                  <Text style={[styles.resultValue, { color: theme.colors.text }]}>
                    {typeof value === 'object' ? JSON.stringify(value, null, 2) : String(value)}
                  </Text>
                </View>
              ))}
            </View>
          </View>
        ))}
      </ScrollView>
    </View>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <View style={[styles.header, { backgroundColor: theme.colors.card }]}>
          <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
            Spatial Analysis Toolkit
          </Text>
          <TouchableOpacity onPress={onClose}>
            <Text style={[styles.closeButton, { color: theme.colors.primary }]}>
              Done
            </Text>
          </TouchableOpacity>
        </View>
        
        {!selectedFeature && (
          <View style={[styles.noFeatureContainer, { backgroundColor: theme.colors.card + '80' }]}>
            <AlertTriangle size={48} color={theme.colors.warning} />
            <Text style={[styles.noFeatureText, { color: theme.colors.text }]}>
              No Feature Selected
            </Text>
            <Text style={[styles.noFeatureDescription, { color: theme.colors.muted }]}>
              Select a feature on the map to perform spatial analysis
            </Text>
          </View>
        )}
        
        <ScrollView style={styles.content}>
          {analysisCategories.map(renderAnalysisCategory)}
          
          {analysisResults.length > 0 && renderAnalysisResults()}
        </ScrollView>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  headerTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
  },
  closeButton: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
  },
  noFeatureContainer: {
    margin: 16,
    padding: 24,
    borderRadius: 12,
    alignItems: 'center',
    gap: 8,
  },
  noFeatureText: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
  },
  noFeatureDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  categoryContainer: {
    marginBottom: 12,
    borderRadius: 12,
    overflow: 'hidden',
  },
  categoryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
  },
  categoryInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  categoryIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  categoryName: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
  },
  toolsList: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  toolItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderLeftWidth: 3,
    marginBottom: 8,
    borderRadius: 8,
  },
  toolInfo: {
    flex: 1,
  },
  toolName: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 2,
  },
  toolDescription: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
  },
  resultsContainer: {
    marginTop: 16,
    borderRadius: 12,
    padding: 16,
  },
  resultsTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 12,
  },
  resultsList: {
    maxHeight: 300,
  },
  resultItem: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  resultHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  resultType: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
  },
  resultTimestamp: {
    fontSize: 11,
    fontFamily: 'Inter-Regular',
  },
  resultFeature: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    marginBottom: 8,
  },
  resultData: {
    gap: 4,
  },
  resultRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  resultKey: {
    fontSize: 11,
    fontFamily: 'Inter-Regular',
    flex: 1,
  },
  resultValue: {
    fontSize: 11,
    fontFamily: 'Inter-Medium',
    flex: 2,
    textAlign: 'right',
  },
});
