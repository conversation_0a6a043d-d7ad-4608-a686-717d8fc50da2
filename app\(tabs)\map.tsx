import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert, Platform } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '@/hooks/useTheme';
import { Globe, Download, Upload, Plus, Layers, Settings2, Zap, TestTube } from 'lucide-react-native';
// Import the enhanced map components
import { MapScreen } from '@/components/map';
import ProfessionalMapUIEnhanced from '@/components/map/ProfessionalMapUIEnhanced';
import EnhancedMapDemo from '@/components/map/EnhancedMapDemo';

interface Project {
  id: string;
  name: string;
  region?: {
    latitude: number;
    longitude: number;
    latitudeDelta: number;
    longitudeDelta: number;
  };
}

function MapTabScreen() {
  const { theme } = useTheme();
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [projects, setProjects] = useState<Project[]>([]);
  const [isOfflineMode, setIsOfflineMode] = useState(false);
  const [featureCount, setFeatureCount] = useState(0);
  const [mapMode, setMapMode] = useState<'professional' | 'demo'>('professional');
  const [enhancedFeatures, setEnhancedFeatures] = useState<any[]>([]);
  const [analysisResults, setAnalysisResults] = useState<any[]>([]);
  const [measurementResults, setMeasurementResults] = useState<any[]>([]);

  useEffect(() => {
    loadProjects();
  }, []);

  const loadProjects = async () => {
    try {
      // Load projects from storage or API
      const demoProjects: Project[] = [
        {
          id: '1',
          name: 'Forest Survey - Northern California',
          region: {
            latitude: 37.78825,
            longitude: -122.4324,
            latitudeDelta: 0.0922,
            longitudeDelta: 0.0421,
          },
        },
        {
          id: '2', 
          name: 'Urban Planning - Downtown Area',
          region: {
            latitude: 40.7589,
            longitude: -73.9851,
            latitudeDelta: 0.0922,
            longitudeDelta: 0.0421,
          },
        },
        {
          id: '3',
          name: 'Environmental Study - Bay Area',
          region: {
            latitude: 37.4419,
            longitude: -122.1430,
            latitudeDelta: 0.1,
            longitudeDelta: 0.1,
          },
        },
      ];
      
      setProjects(demoProjects);
      if (demoProjects.length > 0) {
        setSelectedProject(demoProjects[0]);
      }
    } catch (error) {
      console.error('Failed to load projects:', error);
    }
  };

  const handleProjectSelect = (project: Project) => {
    setSelectedProject(project);
    Alert.alert(
      'Project Selected',
      `Switched to: ${project.name}`,
      [{ text: 'OK' }]
    );
  };

  const handleOfflineToggle = () => {
    setIsOfflineMode(!isOfflineMode);
    Alert.alert(
      'Offline Mode',
      isOfflineMode 
        ? 'Switched to online mode. Map data will be downloaded from the internet.'
        : 'Switched to offline mode. Using cached map data.',
      [{ text: 'OK' }]
    );
  };

  const handleExportData = () => {
    if (featureCount === 0) {
      Alert.alert('No Data', 'No features to export. Create some map features first.');
      return;
    }

    Alert.alert(
      'Export Map Data',
      `Export ${featureCount} features and collected data for this project?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Export',
          onPress: () => {
            Alert.alert('Export Complete', 'Map data exported as GeoJSON format');
          },
        },
      ]
    );
  };

  const handleImportData = () => {
    Alert.alert(
      'Import Map Data',
      'Import map features from a file (GPX, KML, GeoJSON)?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Import',
          onPress: () => {
            Alert.alert('Import Complete', 'Map data imported successfully');
          },
        },
      ]
    );
  };

  const handleCreateProject = () => {
    Alert.alert(
      'Create New Project',
      'Create a new mapping project for this area?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Create',
          onPress: () => {
            const newProject: Project = {
              id: `project_${Date.now()}`,
              name: `Project ${projects.length + 1}`,
              region: selectedProject?.region,
            };
            setProjects(prev => [...prev, newProject]);
            setSelectedProject(newProject);
            Alert.alert('Success', `Created: ${newProject.name}`);
          },
        },
      ]
    );
  };

  const handleFeatureCreated = (feature: any) => {
    setFeatureCount(prev => prev + 1);
    setEnhancedFeatures(prev => [...prev, feature]);
    console.log('Enhanced Feature created in project:', selectedProject?.id, feature);
  };

  const handleFeatureDeleted = (featureId: string) => {
    setFeatureCount(prev => Math.max(0, prev - 1));
    setEnhancedFeatures(prev => prev.filter(f => f.id !== featureId));
    console.log('Enhanced Feature deleted from project:', selectedProject?.id, featureId);
  };

  const handleFeatureSelected = (feature: any) => {
    console.log('Feature selected:', feature);
  };

  const handleAnalysisComplete = (result: any) => {
    setAnalysisResults(prev => [...prev, result]);
    console.log('Analysis completed:', result);
  };

  const handleMeasurementComplete = (result: any) => {
    setMeasurementResults(prev => [...prev, result]);
    console.log('Measurement completed:', result);
  };

  const renderHeader = () => (
    <View style={[styles.header, { backgroundColor: theme.colors.card, borderBottomColor: theme.colors.border }]}>
      <View style={styles.headerLeft}>
        <Globe size={24} color={theme.colors.primary} />
        <View>
          <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
            Enhanced Professional Map
          </Text>
          <Text style={[styles.headerSubtitle, { color: theme.colors.muted }]}>
            {selectedProject ? selectedProject.name : 'No project selected'} • {featureCount} features • {mapMode} mode
            {analysisResults.length > 0 && ` • ${analysisResults.length} analyses`}
            {measurementResults.length > 0 && ` • ${measurementResults.length} measurements`}
          </Text>
        </View>
      </View>

      <View style={styles.headerActions}>
        {/* Project Selector */}
        <TouchableOpacity
          style={[styles.headerButton, { backgroundColor: theme.colors.background }]}
          onPress={() => {
            // Show project selector
            const projectNames = projects.map((p, i) => `${i + 1}. ${p.name}`).join('\n');
            Alert.alert(
              'Select Project',
              projectNames,
              [
                { text: 'Cancel', style: 'cancel' },
                ...projects.map((project, index) => ({
                  text: `Select #${index + 1}`,
                  onPress: () => handleProjectSelect(project),
                })),
              ]
            );
          }}
        >
          <Settings2 size={18} color={theme.colors.text} />
        </TouchableOpacity>

        {/* Offline Mode Toggle */}
        <TouchableOpacity
          style={[
            styles.headerButton,
            {
              backgroundColor: isOfflineMode ? theme.colors.warning + '20' : theme.colors.background,
            },
          ]}
          onPress={handleOfflineToggle}
        >
          <Layers 
            size={18} 
            color={isOfflineMode ? theme.colors.warning : theme.colors.text} 
          />
        </TouchableOpacity>

        {/* Export Data */}
        <TouchableOpacity
          style={[styles.headerButton, { backgroundColor: theme.colors.background }]}
          onPress={handleExportData}
        >
          <Download size={18} color={theme.colors.text} />
        </TouchableOpacity>

        {/* Import Data */}
        <TouchableOpacity
          style={[styles.headerButton, { backgroundColor: theme.colors.background }]}
          onPress={handleImportData}
        >
          <Upload size={18} color={theme.colors.text} />
        </TouchableOpacity>

        {/* Toggle Map Mode */}
        <TouchableOpacity
          style={[styles.headerButton, { backgroundColor: mapMode === 'demo' ? theme.colors.warning : theme.colors.info }]}
          onPress={() => setMapMode(mapMode === 'professional' ? 'demo' : 'professional')}
        >
          {mapMode === 'demo' ? <Zap size={18} color="white" /> : <TestTube size={18} color="white" />}
        </TouchableOpacity>

        {/* Create Project */}
        <TouchableOpacity
          style={[styles.headerButton, { backgroundColor: theme.colors.primary }]}
          onPress={handleCreateProject}
        >
          <Plus size={18} color="white" />
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderOfflineBanner = () => {
    if (!isOfflineMode) return null;

    return (
      <View style={[styles.offlineBanner, { backgroundColor: theme.colors.warning }]}>
        <Text style={styles.offlineBannerText}>
          📱 Offline Mode Active - Using cached map data
        </Text>
      </View>
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {renderHeader()}
      {renderOfflineBanner()}

      {/* Enhanced Map Implementation */}
      <View style={styles.mapContainer}>
        {mapMode === 'professional' ? (
          <ProfessionalMapUIEnhanced
            initialRegion={selectedProject?.region || {
              latitude: 37.78825,
              longitude: -122.4324,
              latitudeDelta: 0.0922,
              longitudeDelta: 0.0421,
            }}
            geoFeatures={enhancedFeatures}
            onFeatureCreated={handleFeatureCreated}
            onFeatureDeleted={handleFeatureDeleted}
            onLocationSelect={(location) => console.log('Location selected:', location)}
            enableDrawing={true}
            enableMeasurement={true}
            enableAnalysis={true}
            enableLayerImport={true}
            maxFeatures={100}
          />
        ) : (
          <EnhancedMapDemo />
        )}
      </View>
    </SafeAreaView>
  );
}

// Ensure this is properly exported as default for Expo Router
export default MapTabScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    zIndex: 100, // Ensure header stays on top
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    flex: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 2,
  },
  headerSubtitle: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8,
  },
  headerButton: {
    padding: 10,
    borderRadius: 8,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  offlineBanner: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    alignItems: 'center',
    zIndex: 90, // Below header but above map
  },
  offlineBannerText: {
    color: 'white',
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
  },
  mapContainer: {
    flex: 1,
    position: 'relative',
  },
});
