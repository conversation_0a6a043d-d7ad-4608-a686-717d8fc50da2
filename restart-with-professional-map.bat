@echo off
REM Professional Map UI - Restart Development Server (Windows)
REM This ensures all new components and changes are loaded

echo 🔄 Restarting FieldSyncPro with Professional Map UI...
echo ==================================================

REM Kill any existing Metro processes
echo 🛑 Stopping existing development servers...
taskkill /f /im node.exe /t >nul 2>&1
timeout /t 3 /nobreak >nul

REM Clear Metro cache
echo 🧹 Clearing Metro cache...
call npx expo start --clear >nul 2>&1 || call npx react-native start --reset-cache >nul 2>&1 || echo Cache clear attempted
timeout /t 2 /nobreak >nul

echo.
echo 🚀 Starting development server with new Professional Map UI...
echo ============================================================

REM Start the development server
call npx expo start --web 2>nul || call npm run web

echo.
echo ✅ Professional Map UI should now be available at:
echo    🌐 Web: http://localhost:8081/map
echo    📱 Mobile: Use Expo Go app to scan QR code
echo.
echo 🎯 New Features Available:
echo    • No overlapping UI elements
echo    • Functional drawing tools (Point, Line, Polygon, Rectangle, Circle)
echo    • Professional layout with sidebar panels
echo    • Enhanced feature management
echo    • Responsive design
echo.
echo 📝 Testing Instructions:
echo    1. Navigate to the map tab
echo    2. Click 'Draw' in the main toolbar
echo    3. Select a drawing tool from the sidebar
echo    4. Draw on the map - no more overlapping elements!
echo    5. Check 'Features' panel to manage created features

pause
