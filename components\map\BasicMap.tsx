import React, { useState, useRef } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Text,
  Alert,
} from 'react-native';
import MapView, { 
  PROVIDER_GOOGLE, 
  Marker,
  Region,
  MapPressEvent,
  MapType,
} from 'react-native-maps';
import {
  MapPin,
  Plus,
  Minus,
  Target,
  Layers,
} from 'lucide-react-native';

interface BasicMapProps {
  initialRegion?: Region;
  onLocationSelect?: (location: { latitude: number; longitude: number }) => void;
}

export default function BasicMap({
  initialRegion,
  onLocationSelect,
}: BasicMapProps) {
  const mapRef = useRef<MapView>(null);
  
  const [region, setRegion] = useState<Region>(initialRegion || {
    latitude: 37.78825,
    longitude: -122.4324,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  });

  const [mapType, setMapType] = useState<MapType>('standard');
  const [markers, setMarkers] = useState<Array<{id: string, coordinate: {latitude: number, longitude: number}, title: string}>>([]);

  const handleMapPress = (event: MapPressEvent) => {
    const coordinate = event.nativeEvent.coordinate;
    
    // Add a marker
    const newMarker = {
      id: `marker_${Date.now()}`,
      coordinate,
      title: `Point ${markers.length + 1}`,
    };
    
    setMarkers([...markers, newMarker]);
    
    if (onLocationSelect) {
      onLocationSelect(coordinate);
    }
  };

  const zoomIn = () => {
    const newRegion = {
      ...region,
      latitudeDelta: region.latitudeDelta * 0.5,
      longitudeDelta: region.longitudeDelta * 0.5,
    };
    mapRef.current?.animateToRegion(newRegion, 300);
  };

  const zoomOut = () => {
    const newRegion = {
      ...region,
      latitudeDelta: region.latitudeDelta * 2,
      longitudeDelta: region.longitudeDelta * 2,
    };
    mapRef.current?.animateToRegion(newRegion, 300);
  };

  const toggleMapType = () => {
    setMapType(mapType === 'standard' ? 'satellite' : 'standard');
  };

  const clearMarkers = () => {
    setMarkers([]);
  };

  return (
    <View style={styles.container}>
      <MapView
        ref={mapRef}
        style={styles.map}
        provider={PROVIDER_GOOGLE}
        showsUserLocation
        showsMyLocationButton={false}
        region={region}
        mapType={mapType}
        onRegionChangeComplete={setRegion}
        onPress={handleMapPress}
      >
        {markers.map((marker) => (
          <Marker
            key={marker.id}
            coordinate={marker.coordinate}
            title={marker.title}
          />
        ))}
      </MapView>

      {/* Controls */}
      <View style={styles.controls}>
        <TouchableOpacity
          style={[styles.controlButton, { backgroundColor: '#007AFF' }]}
          onPress={toggleMapType}
        >
          <Layers size={20} color="white" />
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.controlButton, { backgroundColor: '#FF3B30' }]}
          onPress={clearMarkers}
        >
          <Text style={styles.controlText}>Clear</Text>
        </TouchableOpacity>
      </View>

      {/* Zoom Controls */}
      <View style={styles.zoomControls}>
        <TouchableOpacity
          style={styles.zoomButton}
          onPress={zoomIn}
        >
          <Plus size={20} color="#007AFF" />
        </TouchableOpacity>
        
        <View style={styles.zoomDivider} />
        
        <TouchableOpacity
          style={styles.zoomButton}
          onPress={zoomOut}
        >
          <Minus size={20} color="#007AFF" />
        </TouchableOpacity>
      </View>
      
      {/* Info */}
      <View style={styles.info}>
        <Text style={styles.infoText}>
          Tap map to add markers • {markers.length} markers
        </Text>
        <Text style={styles.infoText}>
          Map: {mapType} • Zoom controls on right
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  map: {
    flex: 1,
  },
  controls: {
    position: 'absolute',
    bottom: 40,
    left: 20,
    flexDirection: 'row',
    gap: 10,
  },
  controlButton: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 25,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  controlText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 14,
  },
  zoomControls: {
    position: 'absolute',
    right: 20,
    top: '50%',
    transform: [{ translateY: -50 }],
    backgroundColor: 'white',
    borderRadius: 25,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  zoomButton: {
    width: 44,
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
  },
  zoomDivider: {
    height: 1,
    backgroundColor: '#E0E0E0',
    marginHorizontal: 8,
  },
  info: {
    position: 'absolute',
    top: 60,
    left: 20,
    right: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    padding: 12,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  infoText: {
    fontSize: 12,
    color: '#333',
    textAlign: 'center',
  },
});
