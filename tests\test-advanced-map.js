#!/usr/bin/env node

/**
 * Comprehensive Test Suite for Advanced Map Feature
 * 
 * This script runs various tests to ensure the Advanced Map implementation
 * is working correctly, including component tests, performance tests, and
 * integration tests.
 */

const fs = require('fs');
const path = require('path');

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  bold: '\x1b[1m',
  dim: '\x1b[2m',
};

function log(message, color = 'white') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSection(title) {
  log(`\n${'='.repeat(60)}`, 'cyan');
  log(`${title}`, 'bold');
  log(`${'='.repeat(60)}`, 'cyan');
}

function logSubsection(title) {
  log(`\n${'-'.repeat(40)}`, 'blue');
  log(`${title}`, 'blue');
  log(`${'-'.repeat(40)}`, 'blue');
}

function logTest(testName, passed, details = '') {
  const icon = passed ? '✅' : '❌';
  const color = passed ? 'green' : 'red';
  log(`${icon} ${testName}`, color);
  if (details && !passed) {
    log(`   ${details}`, 'dim');
  }
}

// Test results tracking
let totalTests = 0;
let passedTests = 0;
let failedTests = [];

function runTest(testName, testFunction) {
  totalTests++;
  try {
    const result = testFunction();
    if (result) {
      passedTests++;
      logTest(testName, true);
      return true;
    } else {
      failedTests.push(testName);
      logTest(testName, false);
      return false;
    }
  } catch (error) {
    failedTests.push(`${testName}: ${error.message}`);
    logTest(testName, false, error.message);
    return false;
  }
}

// Utility functions
function fileExists(filePath) {
  return fs.existsSync(path.resolve(filePath));
}

function readFile(filePath) {
  try {
    return fs.readFileSync(path.resolve(filePath), 'utf8');
  } catch (error) {
    return null;
  }
}

function checkImports(filePath, requiredImports) {
  const content = readFile(filePath);
  if (!content) return false;
  
  return requiredImports.every(importItem => {
    if (typeof importItem === 'string') {
      return content.includes(importItem);
    } else if (importItem instanceof RegExp) {
      return importItem.test(content);
    }
    return false;
  });
}

function checkTypeDefinitions(filePath, requiredTypes) {
  const content = readFile(filePath);
  if (!content) return false;
  
  return requiredTypes.every(type => {
    const typeRegex = new RegExp(`(interface|type|class)\\s+${type}\\b`);
    return typeRegex.test(content);
  });
}

function checkExports(filePath, requiredExports) {
  const content = readFile(filePath);
  if (!content) return false;
  
  return requiredExports.every(exportItem => {
    const exportRegex = new RegExp(`export.*${exportItem}`);
    return exportRegex.test(content);
  });
}

function checkFunctionExists(filePath, functionNames) {
  const content = readFile(filePath);
  if (!content) return false;
  
  return functionNames.every(funcName => {
    const funcRegex = new RegExp(`(const|function|async)\\s+${funcName}\\b`);
    return funcRegex.test(content);
  });
}

// Test suites
function runComponentTests() {
  logSection('Component Structure Tests');
  
  runTest('Advanced Map Screen exists', () => 
    fileExists('app/(tabs)/advanced-map.tsx')
  );
  
  runTest('GIS Map Viewer exists', () => 
    fileExists('components/map/GISMapViewer.tsx')
  );
  
  runTest('Layer Manager Modal exists', () => 
    fileExists('components/map/LayerManagerModal.tsx')
  );
  
  runTest('Spatial Analysis Modal exists', () => 
    fileExists('components/map/SpatialAnalysisModal.tsx')
  );
  
  runTest('Map Settings Modal exists', () => 
    fileExists('components/map/MapSettingsModal.tsx')
  );
  
  runTest('Story Builder Modal exists', () => 
    fileExists('components/map/StoryBuilderModal.tsx')
  );
  
  runTest('Bookmark Manager Modal exists', () => 
    fileExists('components/map/BookmarkManagerModal.tsx')
  );
  
  runTest('Coordinate Display exists', () => 
    fileExists('components/map/CoordinateDisplay.tsx')
  );
  
  runTest('Measurement Display exists', () => 
    fileExists('components/map/MeasurementDisplay.tsx')
  );
  
  runTest('Map Error Boundary exists', () => 
    fileExists('components/map/MapErrorBoundary.tsx')
  );
  
  runTest('Spatial Analysis Engine exists', () => 
    fileExists('components/map/spatial/SpatialAnalysisEngine.ts')
  );
}

function runTypeDefinitionTests() {
  logSection('Type Definition Tests');
  
  const typesPath = 'types/gis.ts';
  
  runTest('GIS types file exists', () => 
    fileExists(typesPath)
  );
  
  runTest('Core interfaces defined', () => 
    checkTypeDefinitions(typesPath, [
      'Coordinate',
      'MapExtent',
      'MapLayer',
      'GISFeature',
      'MeasurementResult',
      'AnalysisResult',
      'MapBookmark',
      'StorySlide',
      'MapStory',
      'MapSettings',
    ])
  );
  
  runTest('Utility types defined', () => 
    checkTypeDefinitions(typesPath, [
      'GeometryType',
      'LayerType',
      'CoordinateFormat',
      'AnalysisType',
      'MeasurementType',
      'DrawingTool',
      'BaseMapType',
    ])
  );
  
  runTest('Event types defined', () => 
    checkTypeDefinitions(typesPath, [
      'MapEvent',
      'LayerEvent',
      'AnalysisEvent',
    ])
  );
  
  runTest('Hook types defined', () => 
    checkTypeDefinitions(typesPath, [
      'UseMapState',
      'UseMapActions',
    ])
  );
}

function runHookTests() {
  logSection('Hook Implementation Tests');
  
  const hookPath = 'hooks/useGISMap.ts';
  
  runTest('GIS hook file exists', () => 
    fileExists(hookPath)
  );
  
  runTest('Hook exports useGISMap', () => 
    checkExports(hookPath, ['useGISMap'])
  );
  
  runTest('Hook implements core functions', () => 
    checkFunctionExists(hookPath, [
      'addLayer',
      'removeLayer',
      'createFeature',
      'addMeasurement',
      'addBookmark',
      'showNotification',
      'calculateDistance',
      'calculateArea',
      'calculateBearing',
    ])
  );
  
  runTest('Hook includes performance tracking', () => 
    checkImports(hookPath, ['mapPerformanceMonitor', 'PerformanceMetric'])
  );
}

function runSpatialAnalysisTests() {
  logSection('Spatial Analysis Tests');
  
  const enginePath = 'components/map/spatial/SpatialAnalysisEngine.ts';
  
  runTest('Spatial engine file exists', () => 
    fileExists(enginePath)
  );
  
  runTest('Core analysis functions exported', () => 
    checkExports(enginePath, [
      'createBuffer',
      'clipFeatures',
      'dissolveFeatures',
      'spatialJoin',
      'proximityAnalysis',
    ])
  );
  
  runTest('Utility functions exported', () => 
    checkExports(enginePath, [
      'calculateDistance',
      'calculateBearing',
      'getCentroid',
    ])
  );
  
  runTest('Analysis options interfaces defined', () => 
    checkTypeDefinitions(enginePath, [
      'BufferOptions',
      'ClipOptions',
      'DissolveOptions',
      'SpatialJoinOptions',
    ])
  );
}

function runModalComponentTests() {
  logSection('Modal Component Tests');
  
  const modals = [
    'LayerManagerModal',
    'SpatialAnalysisModal',
    'MapSettingsModal',
    'StoryBuilderModal',
    'BookmarkManagerModal',
  ];
  
  modals.forEach(modalName => {
    const modalPath = `components/map/${modalName}.tsx`;
    
    runTest(`${modalName} component structure`, () => 
      checkImports(modalPath, [
        'React',
        'Modal',
        'TouchableOpacity',
        'useTheme',
      ])
    );
    
    runTest(`${modalName} exports component`, () => 
      checkExports(modalPath, [modalName])
    );
  });
}

function runUtilityTests() {
  logSection('Utility Component Tests');
  
  runTest('Performance monitoring utility', () => 
    fileExists('utils/mapPerformance.ts') &&
    checkExports('utils/mapPerformance.ts', [
      'mapPerformanceMonitor',
      'useMapPerformance',
      'withPerformanceTracking',
      'MapPerformanceMonitor',
    ])
  );
  
  runTest('Demo data utility', () => 
    fileExists('data/demoData.ts') &&
    checkExports('data/demoData.ts', [
      'DEMO_LAYERS',
      'DEMO_FEATURES',
      'DEMO_BOOKMARKS',
      'getDemoData',
      'generateRandomFeatures',
    ])
  );
  
  runTest('Coordinate display component', () => 
    checkImports('components/map/CoordinateDisplay.tsx', [
      'formatToDMS',
      'formatToUTM',
      'formatToMGRS',
      'COORDINATE_FORMATS',
    ])
  );
  
  runTest('Measurement display component', () => 
    checkImports('components/map/MeasurementDisplay.tsx', [
      'formatMeasurementValue',
      'getMeasurementIcon',
      'getMeasurementColor',
    ])
  );
}

function runIntegrationTests() {
  logSection('Integration Tests');
  
  runTest('Advanced Map Screen integrates all modals', () => 
    checkImports('app/(tabs)/advanced-map.tsx', [
      'LayerManagerModal',
      'SpatialAnalysisModal',
      'MapSettingsModal',
      'StoryBuilderModal',
      'BookmarkManagerModal',
      'GISMapViewer',
    ])
  );
  
  runTest('Tab layout includes Advanced Map', () => 
    checkImports('app/(tabs)/_layout.tsx', [
      'advanced-map',
      'Advanced Map',
      'Globe',
    ])
  );
  
  runTest('Map components index exports all components', () => 
    checkExports('components/map/index.ts', [
      'GISMapViewer',
      'LayerManagerModal',
      'SpatialAnalysisModal',
      'MapSettingsModal',
      'StoryBuilderModal',
      'BookmarkManagerModal',
      'CoordinateDisplay',
      'MeasurementDisplay',
    ])
  );
  
  runTest('Error boundary properly integrated', () => 
    checkImports('components/map/MapErrorBoundary.tsx', [
      'mapPerformanceMonitor',
      'Component',
      'ErrorInfo',
    ])
  );
}

function runDependencyTests() {
  logSection('Dependency Tests');
  
  const packageJsonPath = 'package.json';
  const packageContent = readFile(packageJsonPath);
  
  if (!packageContent) {
    runTest('Package.json exists', () => false);
    return;
  }
  
  let packageJson;
  try {
    packageJson = JSON.parse(packageContent);
  } catch (error) {
    runTest('Package.json is valid JSON', () => false);
    return;
  }
  
  const requiredDeps = [
    'react-native-maps',
    'react-native-svg',
    'lucide-react-native',
    'expo-location',
  ];
  
  runTest('Required dependencies installed', () => 
    requiredDeps.every(dep => 
      packageJson.dependencies && packageJson.dependencies[dep]
    )
  );
  
  runTest('Advanced Map scripts added', () => 
    packageJson.scripts && 
    packageJson.scripts['validate:advanced-map'] &&
    packageJson.scripts['test:advanced-map']
  );
}

function runDocumentationTests() {
  logSection('Documentation Tests');
  
  runTest('Implementation guide exists', () => 
    fileExists('ADVANCED_MAP_IMPLEMENTATION_GUIDE.md')
  );
  
  runTest('README documentation exists', () => 
    fileExists('ADVANCED_MAP_README.md')
  );
  
  runTest('Quick start guide exists', () => 
    fileExists('ADVANCED_MAP_QUICK_START.md')
  );
  
  runTest('Validation script exists', () => 
    fileExists('tests/validate-advanced-map.js')
  );
  
  const readmePath = 'ADVANCED_MAP_README.md';
  const readmeContent = readFile(readmePath);
  
  runTest('README contains essential sections', () => 
    readmeContent && 
    readmeContent.includes('## ✨ Key Features') &&
    readmeContent.includes('## 🚀 Getting Started') &&
    readmeContent.includes('## 🛠️ Tools & Features') &&
    readmeContent.includes('## 📊 Data Management')
  );
}

function runPerformanceTests() {
  logSection('Performance Configuration Tests');
  
  runTest('Performance monitoring configured', () => 
    checkImports('utils/mapPerformance.ts', [
      'PerformanceMetric',
      'PerformanceReport',
      'MapPerformanceMonitor',
    ])
  );
  
  runTest('Performance hooks available', () => 
    checkExports('utils/mapPerformance.ts', [
      'useMapPerformance',
      'withPerformanceTracking',
    ])
  );
  
  runTest('Error boundary includes performance tracking', () => 
    checkImports('components/map/MapErrorBoundary.tsx', [
      'mapPerformanceMonitor',
    ])
  );
}

function runConfigurationTests() {
  logSection('Configuration Tests');
  
  const advancedMapPath = 'app/(tabs)/advanced-map.tsx';
  const advancedMapContent = readFile(advancedMapPath);
  
  runTest('Map settings properly configured', () => 
    advancedMapContent &&
    advancedMapContent.includes('mapSettings') &&
    advancedMapContent.includes('coordinateSystem') &&
    advancedMapContent.includes('baseMapType')
  );
  
  runTest('Notification system integrated', () => 
    advancedMapContent &&
    advancedMapContent.includes('showNotification') &&
    advancedMapContent.includes('renderNotifications')
  );
  
  runTest('Error handling configured', () => 
    advancedMapContent &&
    advancedMapContent.includes('ErrorBoundary')
  );
}

// Main test runner
function runAllTests() {
  logSection('Advanced Map Feature Test Suite');
  log('Running comprehensive tests for Advanced Map implementation...', 'blue');
  
  // Reset counters
  totalTests = 0;
  passedTests = 0;
  failedTests = [];
  
  // Run test suites
  runComponentTests();
  runTypeDefinitionTests();
  runHookTests();
  runSpatialAnalysisTests();
  runModalComponentTests();
  runUtilityTests();
  runIntegrationTests();
  runDependencyTests();
  runDocumentationTests();
  runPerformanceTests();
  runConfigurationTests();
  
  // Generate report
  logSection('Test Results Summary');
  
  const successRate = ((passedTests / totalTests) * 100).toFixed(1);
  
  log(`\nTotal Tests: ${totalTests}`, 'blue');
  log(`Passed: ${passedTests}`, 'green');
  log(`Failed: ${totalTests - passedTests}`, 'red');
  log(`Success Rate: ${successRate}%`, successRate >= 95 ? 'green' : successRate >= 80 ? 'yellow' : 'red');
  
  if (failedTests.length > 0) {
    logSubsection('Failed Tests');
    failedTests.forEach(test => log(`❌ ${test}`, 'red'));
  }
  
  // Final assessment
  logSection('Assessment');
  
  if (successRate >= 95) {
    log('🎉 EXCELLENT! Advanced Map implementation is comprehensive and ready for production.', 'green');
    log('All core features are implemented with proper error handling and performance monitoring.', 'green');
  } else if (successRate >= 85) {
    log('✅ GOOD! Advanced Map implementation is solid with minor issues to address.', 'yellow');
    log('Core functionality is complete. Review failed tests for improvements.', 'yellow');
  } else if (successRate >= 70) {
    log('⚠️  FAIR! Advanced Map implementation needs attention.', 'yellow');
    log('Essential features are present but several components need work.', 'yellow');
  } else {
    log('❌ NEEDS WORK! Advanced Map implementation is incomplete.', 'red');
    log('Significant components are missing or improperly implemented.', 'red');
  }
  
  // Recommendations
  logSubsection('Recommendations');
  
  if (successRate >= 95) {
    log('• Run integration tests with real devices', 'green');
    log('• Perform load testing with large datasets', 'green');
    log('• Validate accessibility compliance', 'green');
    log('• Consider user acceptance testing', 'green');
  } else {
    log('• Address all failed test cases', 'yellow');
    log('• Complete missing components', 'yellow');
    log('• Verify all imports and exports', 'yellow');
    log('• Test with sample data', 'yellow');
    if (successRate < 70) {
      log('• Review implementation guide thoroughly', 'red');
      log('• Consider starting with basic components first', 'red');
    }
  }
  
  return {
    totalTests,
    passedTests,
    failedTests,
    successRate: parseFloat(successRate),
  };
}

// Export for programmatic use
module.exports = {
  runAllTests,
  runComponentTests,
  runTypeDefinitionTests,
  runHookTests,
  runSpatialAnalysisTests,
  runModalComponentTests,
  runUtilityTests,
  runIntegrationTests,
  runDependencyTests,
  runDocumentationTests,
  runPerformanceTests,
  runConfigurationTests,
};

// Run tests if this script is executed directly
if (require.main === module) {
  try {
    const results = runAllTests();
    
    // Exit with appropriate code
    if (results.successRate >= 95) {
      process.exit(0); // Success
    } else if (results.successRate >= 70) {
      process.exit(1); // Warning
    } else {
      process.exit(2); // Error
    }
  } catch (error) {
    log(`\n❌ Test suite failed to run: ${error.message}`, 'red');
    console.error(error.stack);
    process.exit(3); // Critical error
  }
}
