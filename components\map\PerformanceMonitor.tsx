/**
 * Advanced Performance Monitor for FieldSyncPro Map Features
 * 
 * Provides real-time performance monitoring, memory tracking, and optimization
 * recommendations for the advanced map implementation.
 * 
 * @version 2.0.0
 * <AUTHOR> Development Team
 */

import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Dimensions,
  Platform,
} from 'react-native';
import { 
  Activity, 
  Cpu, 
  Database, 
  Clock, 
  TrendingUp, 
  AlertTriangle, 
  CheckCircle, 
  Zap,
  BarChart3,
  Settings,
  X,
  RefreshCw,
} from 'lucide-react-native';
import { useTheme } from '@/hooks/useTheme';

interface PerformanceMetrics {
  // Rendering performance
  frameRate: number;
  renderTime: number;
  frameDrops: number;
  
  // Memory usage
  jsHeapUsed: number;
  jsHeapTotal: number;
  memoryWarnings: number;
  
  // Feature statistics
  totalFeatures: number;
  visibleFeatures: number;
  layerCount: number;
  
  // Analysis performance
  lastAnalysisTime: number;
  averageAnalysisTime: number;
  failedAnalyses: number;
  
  // Network and storage
  cacheHitRate: number;
  storageUsed: number;
  networkRequests: number;
  
  // User interaction
  responseTime: number;
  interactionCount: number;
  errorCount: number;
}

interface PerformanceAlert {
  id: string;
  type: 'warning' | 'error' | 'info';
  title: string;
  message: string;
  recommendation?: string;
  timestamp: number;
  acknowledged: boolean;
}

interface PerformanceMonitorProps {
  visible: boolean;
  onClose: () => void;
  metrics: Partial<PerformanceMetrics>;
  onOptimize?: () => void;
  onExportReport?: () => void;
}

const { width: SCREEN_WIDTH } = Dimensions.get('window');

export default function PerformanceMonitor({
  visible,
  onClose,
  metrics,
  onOptimize,
  onExportReport,
}: PerformanceMonitorProps) {
  const { theme } = useTheme();
  const [currentMetrics, setCurrentMetrics] = useState<PerformanceMetrics>({
    frameRate: 60,
    renderTime: 16,
    frameDrops: 0,
    jsHeapUsed: 0,
    jsHeapTotal: 0,
    memoryWarnings: 0,
    totalFeatures: 0,
    visibleFeatures: 0,
    layerCount: 0,
    lastAnalysisTime: 0,
    averageAnalysisTime: 0,
    failedAnalyses: 0,
    cacheHitRate: 95,
    storageUsed: 0,
    networkRequests: 0,
    responseTime: 50,
    interactionCount: 0,
    errorCount: 0,
    ...metrics,
  });

  const [alerts, setAlerts] = useState<PerformanceAlert[]>([]);
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [selectedTab, setSelectedTab] = useState<'overview' | 'details' | 'alerts' | 'recommendations'>('overview');
  
  const monitoringInterval = useRef<NodeJS.Timeout>();
  const frameTimeRef = useRef<number>(Date.now());
  const frameCountRef = useRef<number>(0);

  // Update metrics when props change
  useEffect(() => {
    setCurrentMetrics(prev => ({ ...prev, ...metrics }));
  }, [metrics]);

  // Performance monitoring loop
  useEffect(() => {
    if (isMonitoring && visible) {
      monitoringInterval.current = setInterval(() => {
        updatePerformanceMetrics();
        checkForAlerts();
      }, 1000);
    } else {
      if (monitoringInterval.current) {
        clearInterval(monitoringInterval.current);
      }
    }

    return () => {
      if (monitoringInterval.current) {
        clearInterval(monitoringInterval.current);
      }
    };
  }, [isMonitoring, visible]);

  /**
   * Update real-time performance metrics
   */
  const updatePerformanceMetrics = useCallback(() => {
    const now = Date.now();
    const deltaTime = now - frameTimeRef.current;
    frameTimeRef.current = now;
    frameCountRef.current++;

    // Calculate frame rate
    const frameRate = deltaTime > 0 ? Math.min(60, 1000 / deltaTime) : 60;
    
    // Simulate memory usage (in a real app, this would come from actual monitoring)
    const jsHeapUsed = Platform.OS === 'web' 
      ? (performance as any)?.memory?.usedJSHeapSize || 0
      : Math.random() * 50 * 1024 * 1024; // Simulated for mobile

    const jsHeapTotal = Platform.OS === 'web'
      ? (performance as any)?.memory?.totalJSHeapSize || 0
      : 100 * 1024 * 1024; // Simulated for mobile

    setCurrentMetrics(prev => ({
      ...prev,
      frameRate: Math.round(frameRate),
      renderTime: Math.round(deltaTime),
      jsHeapUsed: Math.round(jsHeapUsed),
      jsHeapTotal: Math.round(jsHeapTotal),
      responseTime: Math.random() * 100 + 20, // Simulated response time
      interactionCount: prev.interactionCount + Math.floor(Math.random() * 3),
    }));
  }, []);

  /**
   * Check for performance alerts
   */
  const checkForAlerts = useCallback(() => {
    const newAlerts: PerformanceAlert[] = [];

    // Frame rate alerts
    if (currentMetrics.frameRate < 30) {
      newAlerts.push({
        id: `framerate_${Date.now()}`,
        type: 'error',
        title: 'Low Frame Rate',
        message: `Frame rate dropped to ${currentMetrics.frameRate} FPS`,
        recommendation: 'Consider reducing visible features or simplifying rendering',
        timestamp: Date.now(),
        acknowledged: false,
      });
    } else if (currentMetrics.frameRate < 45) {
      newAlerts.push({
        id: `framerate_warning_${Date.now()}`,
        type: 'warning',
        title: 'Frame Rate Warning',
        message: `Frame rate is ${currentMetrics.frameRate} FPS`,
        recommendation: 'Monitor performance and consider optimizations',
        timestamp: Date.now(),
        acknowledged: false,
      });
    }

    // Memory alerts
    const memoryUsagePercent = (currentMetrics.jsHeapUsed / currentMetrics.jsHeapTotal) * 100;
    if (memoryUsagePercent > 90) {
      newAlerts.push({
        id: `memory_${Date.now()}`,
        type: 'error',
        title: 'High Memory Usage',
        message: `Memory usage at ${memoryUsagePercent.toFixed(1)}%`,
        recommendation: 'Clear unused features or restart the application',
        timestamp: Date.now(),
        acknowledged: false,
      });
    }

    // Feature count alerts
    if (currentMetrics.totalFeatures > 10000) {
      newAlerts.push({
        id: `features_${Date.now()}`,
        type: 'warning',
        title: 'High Feature Count',
        message: `${currentMetrics.totalFeatures} features loaded`,
        recommendation: 'Consider using feature clustering or pagination',
        timestamp: Date.now(),
        acknowledged: false,
      });
    }

    // Add new alerts
    if (newAlerts.length > 0) {
      setAlerts(prev => [...newAlerts, ...prev].slice(0, 10)); // Keep last 10 alerts
    }
  }, [currentMetrics]);

  /**
   * Acknowledge an alert
   */
  const acknowledgeAlert = useCallback((alertId: string) => {
    setAlerts(prev => prev.map(alert => 
      alert.id === alertId ? { ...alert, acknowledged: true } : alert
    ));
  }, []);

  /**
   * Clear all acknowledged alerts
   */
  const clearAcknowledgedAlerts = useCallback(() => {
    setAlerts(prev => prev.filter(alert => !alert.acknowledged));
  }, []);

  /**
   * Get performance status
   */
  const getPerformanceStatus = useCallback(() => {
    const { frameRate, jsHeapUsed, jsHeapTotal, totalFeatures, errorCount } = currentMetrics;
    const memoryUsage = (jsHeapUsed / jsHeapTotal) * 100;
    
    if (frameRate < 30 || memoryUsage > 90 || errorCount > 5) {
      return { status: 'poor', color: theme.colors.error, icon: AlertTriangle };
    } else if (frameRate < 45 || memoryUsage > 70 || totalFeatures > 5000) {
      return { status: 'fair', color: theme.colors.warning, icon: Activity };
    } else {
      return { status: 'good', color: theme.colors.success, icon: CheckCircle };
    }
  }, [currentMetrics, theme]);

  if (!visible) return null;

  const performanceStatus = getPerformanceStatus();
  const memoryUsagePercent = (currentMetrics.jsHeapUsed / currentMetrics.jsHeapTotal) * 100;
  const unacknowledgedAlerts = alerts.filter(alert => !alert.acknowledged);

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.card, borderBottomColor: theme.colors.border }]}>
        <View style={styles.headerLeft}>
          <performanceStatus.icon size={24} color={performanceStatus.color} />
          <Text style={[styles.title, { color: theme.colors.text }]}>Performance Monitor</Text>
          <View style={[styles.statusBadge, { backgroundColor: performanceStatus.color }]}>
            <Text style={styles.statusText}>{performanceStatus.status.toUpperCase()}</Text>
          </View>
        </View>
        
        <View style={styles.headerRight}>
          <TouchableOpacity
            style={[styles.headerButton, { backgroundColor: isMonitoring ? theme.colors.error : theme.colors.primary }]}
            onPress={() => setIsMonitoring(!isMonitoring)}
          >
            {isMonitoring ? <RefreshCw size={16} color="white" /> : <Activity size={16} color="white" />}
            <Text style={styles.headerButtonText}>
              {isMonitoring ? 'Stop' : 'Start'}
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <X size={20} color={theme.colors.text} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Tab Navigation */}
      <View style={[styles.tabContainer, { backgroundColor: theme.colors.card }]}>
        {['overview', 'details', 'alerts', 'recommendations'].map((tab) => (
          <TouchableOpacity
            key={tab}
            style={[
              styles.tab,
              selectedTab === tab && styles.activeTab,
              { backgroundColor: selectedTab === tab ? theme.colors.primary : 'transparent' }
            ]}
            onPress={() => setSelectedTab(tab as any)}
          >
            <Text style={[
              styles.tabText,
              { color: selectedTab === tab ? 'white' : theme.colors.text }
            ]}>
              {tab.charAt(0).toUpperCase() + tab.slice(1)}
            </Text>
            {tab === 'alerts' && unacknowledgedAlerts.length > 0 && (
              <View style={[styles.alertBadge, { backgroundColor: theme.colors.error }]}>
                <Text style={styles.alertBadgeText}>{unacknowledgedAlerts.length}</Text>
              </View>
            )}
          </TouchableOpacity>
        ))}
      </View>

      {/* Content */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {selectedTab === 'overview' && renderOverviewTab()}
        {selectedTab === 'details' && renderDetailsTab()}
        {selectedTab === 'alerts' && renderAlertsTab()}
        {selectedTab === 'recommendations' && renderRecommendationsTab()}
      </ScrollView>

      {/* Action Buttons */}
      <View style={[styles.actionBar, { backgroundColor: theme.colors.card, borderTopColor: theme.colors.border }]}>
        {onOptimize && (
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: theme.colors.primary }]}
            onPress={onOptimize}
          >
            <Zap size={16} color="white" />
            <Text style={styles.actionButtonText}>Optimize</Text>
          </TouchableOpacity>
        )}
        
        {onExportReport && (
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: theme.colors.secondary }]}
            onPress={onExportReport}
          >
            <BarChart3 size={16} color="white" />
            <Text style={styles.actionButtonText}>Export Report</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );

  function renderOverviewTab() {
    return (
      <View style={styles.tabContent}>
        {/* Key Metrics Grid */}
        <View style={styles.metricsGrid}>
          <MetricCard
            title="Frame Rate"
            value={`${currentMetrics.frameRate} FPS`}
            icon={Activity}
            color={currentMetrics.frameRate >= 45 ? theme.colors.success : currentMetrics.frameRate >= 30 ? theme.colors.warning : theme.colors.error}
            subtitle={`${currentMetrics.frameDrops} drops`}
          />
          
          <MetricCard
            title="Memory Usage"
            value={`${memoryUsagePercent.toFixed(1)}%`}
            icon={Database}
            color={memoryUsagePercent < 70 ? theme.colors.success : memoryUsagePercent < 90 ? theme.colors.warning : theme.colors.error}
            subtitle={`${(currentMetrics.jsHeapUsed / (1024 * 1024)).toFixed(1)} MB`}
          />
          
          <MetricCard
            title="Features"
            value={currentMetrics.totalFeatures.toString()}
            icon={BarChart3}
            color={currentMetrics.totalFeatures < 1000 ? theme.colors.success : currentMetrics.totalFeatures < 5000 ? theme.colors.warning : theme.colors.error}
            subtitle={`${currentMetrics.visibleFeatures} visible`}
          />
          
          <MetricCard
            title="Response Time"
            value={`${currentMetrics.responseTime.toFixed(0)}ms`}
            icon={Clock}
            color={currentMetrics.responseTime < 100 ? theme.colors.success : currentMetrics.responseTime < 300 ? theme.colors.warning : theme.colors.error}
            subtitle="Average"
          />
        </View>

        {/* Performance Trend Chart Placeholder */}
        <View style={[styles.chartContainer, { backgroundColor: theme.colors.card }]}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Performance Trend</Text>
          <View style={styles.chartPlaceholder}>
            <TrendingUp size={48} color={theme.colors.muted} />
            <Text style={[styles.chartPlaceholderText, { color: theme.colors.muted }]}>
              Chart visualization would appear here
            </Text>
          </View>
        </View>
      </View>
    );
  }

  function renderDetailsTab() {
    return (
      <View style={styles.tabContent}>
        <DetailSection title="Rendering Performance" icon={Activity}>
          <DetailRow label="Frame Rate" value={`${currentMetrics.frameRate} FPS`} />
          <DetailRow label="Render Time" value={`${currentMetrics.renderTime}ms`} />
          <DetailRow label="Frame Drops" value={currentMetrics.frameDrops.toString()} />
        </DetailSection>

        <DetailSection title="Memory Usage" icon={Database}>
          <DetailRow label="JS Heap Used" value={`${(currentMetrics.jsHeapUsed / (1024 * 1024)).toFixed(2)} MB`} />
          <DetailRow label="JS Heap Total" value={`${(currentMetrics.jsHeapTotal / (1024 * 1024)).toFixed(2)} MB`} />
          <DetailRow label="Memory Warnings" value={currentMetrics.memoryWarnings.toString()} />
        </DetailSection>

        <DetailSection title="Feature Statistics" icon={BarChart3}>
          <DetailRow label="Total Features" value={currentMetrics.totalFeatures.toString()} />
          <DetailRow label="Visible Features" value={currentMetrics.visibleFeatures.toString()} />
          <DetailRow label="Layer Count" value={currentMetrics.layerCount.toString()} />
        </DetailSection>

        <DetailSection title="Analysis Performance" icon={Cpu}>
          <DetailRow label="Last Analysis" value={`${currentMetrics.lastAnalysisTime}ms`} />
          <DetailRow label="Average Time" value={`${currentMetrics.averageAnalysisTime}ms`} />
          <DetailRow label="Failed Analyses" value={currentMetrics.failedAnalyses.toString()} />
        </DetailSection>

        <DetailSection title="Network & Storage" icon={Settings}>
          <DetailRow label="Cache Hit Rate" value={`${currentMetrics.cacheHitRate}%`} />
          <DetailRow label="Storage Used" value={`${(currentMetrics.storageUsed / (1024 * 1024)).toFixed(2)} MB`} />
          <DetailRow label="Network Requests" value={currentMetrics.networkRequests.toString()} />
        </DetailSection>
      </View>
    );
  }

  function renderAlertsTab() {
    return (
      <View style={styles.tabContent}>
        <View style={styles.alertsHeader}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Performance Alerts ({alerts.length})
          </Text>
          {alerts.some(alert => alert.acknowledged) && (
            <TouchableOpacity
              style={[styles.clearButton, { backgroundColor: theme.colors.muted }]}
              onPress={clearAcknowledgedAlerts}
            >
              <Text style={[styles.clearButtonText, { color: theme.colors.background }]}>
                Clear Acknowledged
              </Text>
            </TouchableOpacity>
          )}
        </View>

        {alerts.length === 0 ? (
          <View style={styles.emptyState}>
            <CheckCircle size={48} color={theme.colors.success} />
            <Text style={[styles.emptyStateText, { color: theme.colors.text }]}>
              No performance alerts
            </Text>
            <Text style={[styles.emptyStateSubtext, { color: theme.colors.muted }]}>
              System is running smoothly
            </Text>
          </View>
        ) : (
          alerts.map((alert) => (
            <AlertCard
              key={alert.id}
              alert={alert}
              onAcknowledge={() => acknowledgeAlert(alert.id)}
              theme={theme}
            />
          ))
        )}
      </View>
    );
  }

  function renderRecommendationsTab() {
    const recommendations = generateRecommendations();
    
    return (
      <View style={styles.tabContent}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Performance Recommendations
        </Text>
        
        {recommendations.map((recommendation, index) => (
          <RecommendationCard
            key={index}
            recommendation={recommendation}
            theme={theme}
          />
        ))}
      </View>
    );
  }

  function generateRecommendations() {
    const recommendations = [];
    
    if (currentMetrics.frameRate < 45) {
      recommendations.push({
        title: 'Optimize Rendering Performance',
        description: 'Frame rate is below optimal levels',
        actions: [
          'Reduce the number of visible features',
          'Use feature clustering for dense areas',
          'Simplify feature geometries',
          'Enable rendering optimizations'
        ],
        priority: 'high'
      });
    }

    if (memoryUsagePercent > 70) {
      recommendations.push({
        title: 'Optimize Memory Usage',
        description: 'Memory usage is approaching critical levels',
        actions: [
          'Clear unused feature data',
          'Implement feature pagination',
          'Reduce image sizes and quality',
          'Enable memory management optimizations'
        ],
        priority: 'high'
      });
    }

    if (currentMetrics.totalFeatures > 5000) {
      recommendations.push({
        title: 'Manage Feature Count',
        description: 'Large number of features may impact performance',
        actions: [
          'Implement feature clustering',
          'Use level-of-detail rendering',
          'Load features on demand',
          'Consider server-side filtering'
        ],
        priority: 'medium'
      });
    }

    if (currentMetrics.cacheHitRate < 80) {
      recommendations.push({
        title: 'Improve Cache Performance',
        description: 'Cache hit rate is below optimal levels',
        actions: [
          'Increase cache size',
          'Optimize cache eviction strategy',
          'Preload frequently accessed data',
          'Review cache configuration'
        ],
        priority: 'medium'
      });
    }

    return recommendations;
  }

  function MetricCard({ title, value, icon: Icon, color, subtitle }: any) {
    return (
      <View style={[styles.metricCard, { backgroundColor: theme.colors.card }]}>
        <View style={styles.metricHeader}>
          <Icon size={20} color={color} />
          <Text style={[styles.metricTitle, { color: theme.colors.text }]}>{title}</Text>
        </View>
        <Text style={[styles.metricValue, { color }]}>{value}</Text>
        {subtitle && (
          <Text style={[styles.metricSubtitle, { color: theme.colors.muted }]}>{subtitle}</Text>
        )}
      </View>
    );
  }

  function DetailSection({ title, icon: Icon, children }: any) {
    return (
      <View style={[styles.detailSection, { backgroundColor: theme.colors.card }]}>
        <View style={styles.detailHeader}>
          <Icon size={20} color={theme.colors.primary} />
          <Text style={[styles.detailTitle, { color: theme.colors.text }]}>{title}</Text>
        </View>
        {children}
      </View>
    );
  }

  function DetailRow({ label, value }: { label: string; value: string }) {
    return (
      <View style={styles.detailRow}>
        <Text style={[styles.detailLabel, { color: theme.colors.muted }]}>{label}</Text>
        <Text style={[styles.detailValue, { color: theme.colors.text }]}>{value}</Text>
      </View>
    );
  }

  function AlertCard({ alert, onAcknowledge, theme }: any) {
    const alertColor = alert.type === 'error' ? theme.colors.error : 
                     alert.type === 'warning' ? theme.colors.warning : theme.colors.info;
    
    return (
      <View style={[
        styles.alertCard, 
        { 
          backgroundColor: theme.colors.card,
          borderLeftColor: alertColor,
          opacity: alert.acknowledged ? 0.6 : 1
        }
      ]}>
        <View style={styles.alertHeader}>
          <View style={styles.alertTitleRow}>
            <AlertTriangle size={16} color={alertColor} />
            <Text style={[styles.alertTitle, { color: theme.colors.text }]}>{alert.title}</Text>
          </View>
          <Text style={[styles.alertTime, { color: theme.colors.muted }]}>
            {new Date(alert.timestamp).toLocaleTimeString()}
          </Text>
        </View>
        
        <Text style={[styles.alertMessage, { color: theme.colors.text }]}>{alert.message}</Text>
        
        {alert.recommendation && (
          <Text style={[styles.alertRecommendation, { color: theme.colors.muted }]}>
            💡 {alert.recommendation}
          </Text>
        )}
        
        {!alert.acknowledged && (
          <TouchableOpacity
            style={[styles.acknowledgeButton, { backgroundColor: alertColor }]}
            onPress={onAcknowledge}
          >
            <Text style={styles.acknowledgeButtonText}>Acknowledge</Text>
          </TouchableOpacity>
        )}
      </View>
    );
  }

  function RecommendationCard({ recommendation, theme }: any) {
    const priorityColor = recommendation.priority === 'high' ? theme.colors.error :
                         recommendation.priority === 'medium' ? theme.colors.warning : theme.colors.info;
    
    return (
      <View style={[styles.recommendationCard, { backgroundColor: theme.colors.card }]}>
        <View style={styles.recommendationHeader}>
          <Text style={[styles.recommendationTitle, { color: theme.colors.text }]}>
            {recommendation.title}
          </Text>
          <View style={[styles.priorityBadge, { backgroundColor: priorityColor }]}>
            <Text style={styles.priorityText}>{recommendation.priority.toUpperCase()}</Text>
          </View>
        </View>
        
        <Text style={[styles.recommendationDescription, { color: theme.colors.muted }]}>
          {recommendation.description}
        </Text>
        
        <View style={styles.actionsList}>
          {recommendation.actions.map((action: string, index: number) => (
            <Text key={index} style={[styles.actionItem, { color: theme.colors.text }]}>
              • {action}
            </Text>
          ))}
        </View>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 12,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginLeft: 12,
  },
  statusText: {
    color: 'white',
    fontSize: 10,
    fontWeight: '600',
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  headerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    gap: 4,
  },
  headerButtonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '500',
  },
  closeButton: {
    padding: 8,
  },
  tabContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 16,
    marginHorizontal: 2,
  },
  activeTab: {
    // Styles applied via backgroundColor prop
  },
  tabText: {
    fontSize: 12,
    fontWeight: '500',
  },
  alertBadge: {
    width: 16,
    height: 16,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 4,
  },
  alertBadgeText: {
    color: 'white',
    fontSize: 10,
    fontWeight: '600',
  },
  content: {
    flex: 1,
  },
  tabContent: {
    padding: 16,
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginBottom: 20,
  },
  metricCard: {
    width: (SCREEN_WIDTH - 56) / 2,
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  metricHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  metricTitle: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 6,
  },
  metricValue: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 4,
  },
  metricSubtitle: {
    fontSize: 10,
  },
  chartContainer: {
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 16,
  },
  chartPlaceholder: {
    height: 200,
    alignItems: 'center',
    justifyContent: 'center',
  },
  chartPlaceholderText: {
    marginTop: 8,
    fontSize: 14,
    textAlign: 'center',
  },
  detailSection: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  detailHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  detailTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  detailLabel: {
    fontSize: 12,
  },
  detailValue: {
    fontSize: 12,
    fontWeight: '500',
  },
  alertsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  clearButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  clearButtonText: {
    fontSize: 12,
    fontWeight: '500',
  },
  emptyState: {
    alignItems: 'center',
    padding: 40,
  },
  emptyStateText: {
    fontSize: 16,
    fontWeight: '500',
    marginTop: 12,
  },
  emptyStateSubtext: {
    fontSize: 14,
    marginTop: 4,
  },
  alertCard: {
    padding: 16,
    borderRadius: 12,
    borderLeftWidth: 4,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  alertHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  alertTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  alertTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
  },
  alertTime: {
    fontSize: 10,
  },
  alertMessage: {
    fontSize: 12,
    marginBottom: 8,
    lineHeight: 16,
  },
  alertRecommendation: {
    fontSize: 11,
    fontStyle: 'italic',
    marginBottom: 12,
    lineHeight: 14,
  },
  acknowledgeButton: {
    alignSelf: 'flex-start',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  acknowledgeButtonText: {
    color: 'white',
    fontSize: 11,
    fontWeight: '500',
  },
  recommendationCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  recommendationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  recommendationTitle: {
    fontSize: 14,
    fontWeight: '600',
    flex: 1,
  },
  priorityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  priorityText: {
    color: 'white',
    fontSize: 10,
    fontWeight: '600',
  },
  recommendationDescription: {
    fontSize: 12,
    marginBottom: 12,
    lineHeight: 16,
  },
  actionsList: {
    gap: 4,
  },
  actionItem: {
    fontSize: 11,
    lineHeight: 16,
  },
  actionBar: {
    flexDirection: 'row',
    padding: 16,
    borderTopWidth: 1,
    gap: 12,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    gap: 6,
  },
  actionButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
  },
});
