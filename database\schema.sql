-- FieldSync Pro Database Schema
-- Supabase + PostGIS Setup

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- <PERSON><PERSON> custom types
CREATE TYPE user_role AS ENUM ('admin', 'manager', 'collector', 'viewer');
CREATE TYPE project_status AS ENUM ('draft', 'active', 'completed', 'archived');
CREATE TYPE submission_status AS ENUM ('draft', 'completed', 'synced', 'rejected');
CREATE TYPE form_status AS ENUM ('draft', 'published', 'archived');
CREATE TYPE upload_status AS ENUM ('pending', 'uploading', 'completed', 'failed');

-- =====================================================
-- USER PROFILES TABLE
-- =====================================================
CREATE TABLE user_profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    email TEXT NOT NULL,
    full_name TEXT,
    avatar_url TEXT,
    role user_role DEFAULT 'collector',
    organization TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_seen TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT TRUE,
    preferences JSONB DEFAULT '{}',
    PRIMARY KEY (id)
);

-- Create indexes for user_profiles
CREATE INDEX idx_user_profiles_email ON user_profiles(email);
CREATE INDEX idx_user_profiles_role ON user_profiles(role);
CREATE INDEX idx_user_profiles_organization ON user_profiles(organization);
CREATE INDEX idx_user_profiles_active ON user_profiles(is_active);

-- =====================================================
-- TEAMS TABLE
-- =====================================================
CREATE TABLE teams (
    id UUID DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    description TEXT,
    created_by UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE,
    organization TEXT,
    PRIMARY KEY (id)
);

-- Create indexes for teams
CREATE INDEX idx_teams_created_by ON teams(created_by);
CREATE INDEX idx_teams_organization ON teams(organization);
CREATE INDEX idx_teams_active ON teams(is_active);

-- =====================================================
-- TEAM MEMBERS TABLE
-- =====================================================
CREATE TABLE team_members (
    id UUID DEFAULT uuid_generate_v4(),
    team_id UUID REFERENCES teams(id) ON DELETE CASCADE,
    user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    role TEXT DEFAULT 'member' CHECK (role IN ('lead', 'member')),
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE,
    PRIMARY KEY (id),
    UNIQUE(team_id, user_id)
);

-- Create indexes for team_members
CREATE INDEX idx_team_members_team_id ON team_members(team_id);
CREATE INDEX idx_team_members_user_id ON team_members(user_id);
CREATE INDEX idx_team_members_active ON team_members(is_active);

-- =====================================================
-- PROJECTS TABLE
-- =====================================================
CREATE TABLE projects (
    id UUID DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    description TEXT,
    status project_status DEFAULT 'draft',
    created_by UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    organization TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    start_date DATE,
    end_date DATE,
    region JSONB, -- GeoJSON for project boundary
    settings JSONB DEFAULT '{}',
    metadata JSONB DEFAULT '{}',
    PRIMARY KEY (id)
);

-- Create indexes for projects
CREATE INDEX idx_projects_status ON projects(status);
CREATE INDEX idx_projects_created_by ON projects(created_by);
CREATE INDEX idx_projects_organization ON projects(organization);
CREATE INDEX idx_projects_created_at ON projects(created_at);

-- =====================================================
-- PROJECT TEAMS TABLE (Many-to-Many)
-- =====================================================
CREATE TABLE project_teams (
    id UUID DEFAULT uuid_generate_v4(),
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    team_id UUID REFERENCES teams(id) ON DELETE CASCADE,
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    assigned_by UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    is_active BOOLEAN DEFAULT TRUE,
    PRIMARY KEY (id),
    UNIQUE(project_id, team_id)
);

-- Create indexes for project_teams
CREATE INDEX idx_project_teams_project_id ON project_teams(project_id);
CREATE INDEX idx_project_teams_team_id ON project_teams(team_id);
CREATE INDEX idx_project_teams_active ON project_teams(is_active);

-- =====================================================
-- FORMS TABLE
-- =====================================================
CREATE TABLE forms (
    id UUID DEFAULT uuid_generate_v4(),
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    version INTEGER DEFAULT 1,
    status form_status DEFAULT 'draft',
    schema JSONB NOT NULL, -- Form field definitions
    created_by UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    published_at TIMESTAMP WITH TIME ZONE,
    settings JSONB DEFAULT '{}',
    PRIMARY KEY (id)
);

-- Create indexes for forms
CREATE INDEX idx_forms_project_id ON forms(project_id);
CREATE INDEX idx_forms_status ON forms(status);
CREATE INDEX idx_forms_created_by ON forms(created_by);

-- =====================================================
-- SUBMISSIONS TABLE
-- =====================================================
CREATE TABLE submissions (
    id UUID DEFAULT uuid_generate_v4(),
    form_id UUID REFERENCES forms(id) ON DELETE CASCADE,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    status submission_status DEFAULT 'draft',
    data JSONB NOT NULL DEFAULT '{}',
    location JSONB, -- GPS coordinates and metadata
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    synced_at TIMESTAMP WITH TIME ZONE,
    device_info JSONB,
    metadata JSONB DEFAULT '{}',
    PRIMARY KEY (id)
);

-- Create indexes for submissions
CREATE INDEX idx_submissions_form_id ON submissions(form_id);
CREATE INDEX idx_submissions_project_id ON submissions(project_id);
CREATE INDEX idx_submissions_user_id ON submissions(user_id);
CREATE INDEX idx_submissions_status ON submissions(status);
CREATE INDEX idx_submissions_created_at ON submissions(started_at);

-- =====================================================
-- MEDIA ATTACHMENTS TABLE
-- =====================================================
CREATE TABLE media_attachments (
    id UUID DEFAULT uuid_generate_v4(),
    submission_id UUID REFERENCES submissions(id) ON DELETE CASCADE,
    field_id TEXT NOT NULL,
    file_name TEXT NOT NULL,
    file_type TEXT NOT NULL,
    file_size BIGINT NOT NULL,
    storage_path TEXT NOT NULL,
    thumbnail_path TEXT,
    upload_status upload_status DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB DEFAULT '{}',
    PRIMARY KEY (id)
);

-- Create indexes for media_attachments
CREATE INDEX idx_media_attachments_submission_id ON media_attachments(submission_id);
CREATE INDEX idx_media_attachments_status ON media_attachments(upload_status);

-- =====================================================
-- TRIGGERS FOR UPDATED_AT
-- =====================================================
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers
CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON user_profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_teams_updated_at BEFORE UPDATE ON teams FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON projects FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_forms_updated_at BEFORE UPDATE ON forms FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- VIEWS FOR ANALYTICS
-- =====================================================

-- Project Statistics View
CREATE OR REPLACE VIEW project_stats AS
SELECT 
    p.id as project_id,
    COUNT(DISTINCT f.id) as total_forms,
    COUNT(DISTINCT s.id) as total_submissions,
    COUNT(DISTINCT CASE WHEN s.status IN ('completed', 'synced') THEN s.id END) as completed_submissions,
    COUNT(DISTINCT pt.team_id) as team_count,
    CASE 
        WHEN COUNT(DISTINCT s.id) > 0 
        THEN ROUND((COUNT(DISTINCT CASE WHEN s.status IN ('completed', 'synced') THEN s.id END)::DECIMAL / COUNT(DISTINCT s.id)) * 100, 2)
        ELSE 0 
    END as completion_rate
FROM projects p
LEFT JOIN forms f ON p.id = f.project_id
LEFT JOIN submissions s ON f.id = s.form_id
LEFT JOIN project_teams pt ON p.id = pt.project_id AND pt.is_active = TRUE
GROUP BY p.id;

-- Team Performance View
CREATE OR REPLACE VIEW team_performance AS
SELECT 
    t.id as team_id,
    t.name as team_name,
    COUNT(DISTINCT tm.user_id) as member_count,
    COUNT(DISTINCT s.id) as total_submissions,
    ROUND(AVG(EXTRACT(EPOCH FROM (s.completed_at - s.started_at))/3600), 2) as avg_completion_time,
    MAX(s.completed_at) as last_activity
FROM teams t
LEFT JOIN team_members tm ON t.id = tm.team_id AND tm.is_active = TRUE
LEFT JOIN submissions s ON tm.user_id = s.user_id AND s.status IN ('completed', 'synced')
GROUP BY t.id, t.name;

-- =====================================================
-- FUNCTIONS
-- =====================================================

-- Function to get project analytics
CREATE OR REPLACE FUNCTION get_project_analytics(
    project_id UUID,
    date_from DATE DEFAULT NULL,
    date_to DATE DEFAULT NULL
)
RETURNS JSONB AS $$
DECLARE
    result JSONB;
    submissions_by_date JSONB;
    completion_rates JSONB;
    team_performance JSONB;
    geographic_distribution JSONB;
BEGIN
    -- Set default date range if not provided
    IF date_from IS NULL THEN
        date_from := CURRENT_DATE - INTERVAL '30 days';
    END IF;
    IF date_to IS NULL THEN
        date_to := CURRENT_DATE;
    END IF;

    -- Submissions by date
    SELECT jsonb_agg(
        jsonb_build_object(
            'date', date_series.date,
            'count', COALESCE(daily_counts.count, 0)
        )
    ) INTO submissions_by_date
    FROM generate_series(date_from, date_to, '1 day'::interval) AS date_series(date)
    LEFT JOIN (
        SELECT DATE(started_at) as date, COUNT(*) as count
        FROM submissions s
        JOIN forms f ON s.form_id = f.id
        WHERE f.project_id = get_project_analytics.project_id
        AND DATE(s.started_at) BETWEEN date_from AND date_to
        GROUP BY DATE(s.started_at)
    ) daily_counts ON date_series.date = daily_counts.date;

    -- Team performance for this project
    SELECT jsonb_agg(
        jsonb_build_object(
            'team_name', tp.team_name,
            'submissions', tp.total_submissions,
            'completion_rate', COALESCE(tp.completion_rate, 0)
        )
    ) INTO team_performance
    FROM (
        SELECT 
            t.name as team_name,
            COUNT(s.id) as total_submissions,
            ROUND((COUNT(CASE WHEN s.status IN ('completed', 'synced') THEN 1 END)::DECIMAL / NULLIF(COUNT(s.id), 0)) * 100, 2) as completion_rate
        FROM teams t
        JOIN project_teams pt ON t.id = pt.team_id
        JOIN team_members tm ON t.id = tm.team_id
        LEFT JOIN submissions s ON tm.user_id = s.user_id 
        WHERE pt.project_id = get_project_analytics.project_id
        AND pt.is_active = TRUE
        AND tm.is_active = TRUE
        GROUP BY t.id, t.name
    ) tp;

    -- Build final result
    result := jsonb_build_object(
        'submissions_by_date', submissions_by_date,
        'completion_rates', completion_rates,
        'team_performance', team_performance,
        'geographic_distribution', geographic_distribution
    );

    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to search projects
CREATE OR REPLACE FUNCTION search_projects(
    search_term TEXT,
    user_id UUID,
    limit_count INTEGER DEFAULT 10
)
RETURNS TABLE (
    id UUID,
    name TEXT,
    description TEXT,
    status project_status,
    created_at TIMESTAMP WITH TIME ZONE,
    match_score REAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.name,
        p.description,
        p.status,
        p.created_at,
        ts_rank_cd(
            to_tsvector('english', p.name || ' ' || COALESCE(p.description, '')),
            plainto_tsquery('english', search_term)
        ) AS match_score
    FROM projects p
    LEFT JOIN project_teams pt ON p.id = pt.project_id
    LEFT JOIN team_members tm ON pt.team_id = tm.team_id
    WHERE (
        tm.user_id = search_projects.user_id 
        OR p.created_by = search_projects.user_id
        OR EXISTS (
            SELECT 1 FROM user_profiles up 
            WHERE up.id = search_projects.user_id 
            AND up.role IN ('admin', 'manager')
        )
    )
    AND (
        p.name ILIKE '%' || search_term || '%'
        OR p.description ILIKE '%' || search_term || '%'
        OR to_tsvector('english', p.name || ' ' || COALESCE(p.description, '')) @@ plainto_tsquery('english', search_term)
    )
    ORDER BY match_score DESC, p.updated_at DESC
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE teams ENABLE ROW LEVEL SECURITY;
ALTER TABLE team_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE project_teams ENABLE ROW LEVEL SECURITY;
ALTER TABLE forms ENABLE ROW LEVEL SECURITY;
ALTER TABLE submissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE media_attachments ENABLE ROW LEVEL SECURITY;

-- User Profiles Policies
CREATE POLICY "Users can view all profiles" ON user_profiles FOR SELECT USING (true);
CREATE POLICY "Users can update own profile" ON user_profiles FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Users can insert own profile" ON user_profiles FOR INSERT WITH CHECK (auth.uid() = id);

-- Teams Policies
CREATE POLICY "Users can view teams they belong to" ON teams FOR SELECT USING (
    id IN (
        SELECT team_id FROM team_members 
        WHERE user_id = auth.uid() AND is_active = true
    )
    OR created_by = auth.uid()
    OR EXISTS (
        SELECT 1 FROM user_profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'manager')
    )
);

CREATE POLICY "Team leads and admins can update teams" ON teams FOR UPDATE USING (
    created_by = auth.uid()
    OR EXISTS (
        SELECT 1 FROM team_members 
        WHERE team_id = teams.id AND user_id = auth.uid() AND role = 'lead'
    )
    OR EXISTS (
        SELECT 1 FROM user_profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'manager')
    )
);

CREATE POLICY "Users can create teams" ON teams FOR INSERT WITH CHECK (
    created_by = auth.uid()
    OR EXISTS (
        SELECT 1 FROM user_profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'manager')
    )
);

-- Team Members Policies
CREATE POLICY "Users can view team members" ON team_members FOR SELECT USING (
    team_id IN (
        SELECT team_id FROM team_members 
        WHERE user_id = auth.uid() AND is_active = true
    )
    OR user_id = auth.uid()
    OR EXISTS (
        SELECT 1 FROM user_profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'manager')
    )
);

-- Projects Policies
CREATE POLICY "Users can view projects they have access to" ON projects FOR SELECT USING (
    created_by = auth.uid()
    OR id IN (
        SELECT pt.project_id FROM project_teams pt
        JOIN team_members tm ON pt.team_id = tm.team_id
        WHERE tm.user_id = auth.uid() AND tm.is_active = true AND pt.is_active = true
    )
    OR EXISTS (
        SELECT 1 FROM user_profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'manager')
    )
);

CREATE POLICY "Project creators and managers can update projects" ON projects FOR UPDATE USING (
    created_by = auth.uid()
    OR EXISTS (
        SELECT 1 FROM user_profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'manager')
    )
);

CREATE POLICY "Users can create projects" ON projects FOR INSERT WITH CHECK (
    created_by = auth.uid()
    OR EXISTS (
        SELECT 1 FROM user_profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'manager', 'collector')
    )
);

-- Forms Policies
CREATE POLICY "Users can view forms from accessible projects" ON forms FOR SELECT USING (
    project_id IN (
        SELECT id FROM projects 
        WHERE created_by = auth.uid()
        OR id IN (
            SELECT pt.project_id FROM project_teams pt
            JOIN team_members tm ON pt.team_id = tm.team_id
            WHERE tm.user_id = auth.uid() AND tm.is_active = true AND pt.is_active = true
        )
        OR EXISTS (
            SELECT 1 FROM user_profiles 
            WHERE id = auth.uid() AND role IN ('admin', 'manager')
        )
    )
);

-- Submissions Policies
CREATE POLICY "Users can view own submissions" ON submissions FOR SELECT USING (
    user_id = auth.uid()
    OR form_id IN (
        SELECT f.id FROM forms f
        JOIN projects p ON f.project_id = p.id
        WHERE p.created_by = auth.uid()
        OR p.id IN (
            SELECT pt.project_id FROM project_teams pt
            JOIN team_members tm ON pt.team_id = tm.team_id
            WHERE tm.user_id = auth.uid() AND tm.is_active = true AND pt.is_active = true
        )
        OR EXISTS (
            SELECT 1 FROM user_profiles 
            WHERE id = auth.uid() AND role IN ('admin', 'manager')
        )
    )
);

CREATE POLICY "Users can create submissions" ON submissions FOR INSERT WITH CHECK (
    user_id = auth.uid()
    AND form_id IN (
        SELECT f.id FROM forms f
        JOIN projects p ON f.project_id = p.id
        WHERE p.status = 'active' AND f.status = 'published'
    )
);

CREATE POLICY "Users can update own submissions" ON submissions FOR UPDATE USING (
    user_id = auth.uid()
);

-- Media Attachments Policies
CREATE POLICY "Users can view media from accessible submissions" ON media_attachments FOR SELECT USING (
    submission_id IN (
        SELECT id FROM submissions 
        WHERE user_id = auth.uid()
        OR form_id IN (
            SELECT f.id FROM forms f
            JOIN projects p ON f.project_id = p.id
            WHERE p.created_by = auth.uid()
            OR EXISTS (
                SELECT 1 FROM user_profiles 
                WHERE id = auth.uid() AND role IN ('admin', 'manager')
            )
        )
    )
);

-- =====================================================
-- INITIAL DATA SETUP
-- =====================================================

-- Function to handle new user signup
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO user_profiles (id, email, full_name)
    VALUES (NEW.id, NEW.email, NEW.raw_user_meta_data->>'full_name');
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to create profile when user signs up
CREATE OR REPLACE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Text search indexes
CREATE INDEX idx_projects_search ON projects USING gin(to_tsvector('english', name || ' ' || COALESCE(description, '')));
CREATE INDEX idx_forms_search ON forms USING gin(to_tsvector('english', name || ' ' || COALESCE(description, '')));

-- Composite indexes for common queries
CREATE INDEX idx_submissions_project_status ON submissions(project_id, status);
CREATE INDEX idx_submissions_user_date ON submissions(user_id, started_at);
CREATE INDEX idx_team_members_active ON team_members(team_id, is_active) WHERE is_active = true;
CREATE INDEX idx_project_teams_active ON project_teams(project_id, is_active) WHERE is_active = true;

-- Partial indexes
CREATE INDEX idx_active_projects ON projects(id) WHERE status = 'active';
CREATE INDEX idx_published_forms ON forms(id) WHERE status = 'published';
CREATE INDEX idx_pending_submissions ON submissions(id) WHERE status IN ('draft', 'completed');

COMMENT ON DATABASE postgres IS 'FieldSync Pro - Professional Field Data Collection Platform';
