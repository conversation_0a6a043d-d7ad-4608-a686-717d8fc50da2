import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { useTheme } from '@/hooks/useTheme';
import {
  Plus,
  Type,
  List,
  CheckSquare,
  Calendar,
  MapPin,
  Camera,
  FileText,
  Trash2,
  Save,
  Eye,
  ArrowLeft,
  Move,
  Copy,
} from 'lucide-react-native';

export interface FormField {
  id: string;
  type: 'text' | 'number' | 'select' | 'multiselect' | 'date' | 'location' | 'photo' | 'textarea';
  label: string;
  placeholder?: string;
  required: boolean;
  options?: string[];
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
  };
}

export interface FormSchema {
  id: string;
  name: string;
  description: string;
  version: number;
  fields: FormField[];
  createdAt: number;
  updatedAt: number;
}

const FIELD_TYPES = [
  { type: 'text', label: 'Text Input', icon: Type, description: 'Single line text field' },
  { type: 'textarea', label: 'Text Area', icon: FileText, description: 'Multi-line text field' },
  { type: 'number', label: 'Number', icon: Type, description: 'Numeric input field' },
  { type: 'select', label: 'Single Choice', icon: List, description: 'Dropdown selection' },
  { type: 'multiselect', label: 'Multiple Choice', icon: CheckSquare, description: 'Multiple selections' },
  { type: 'date', label: 'Date', icon: Calendar, description: 'Date picker' },
  { type: 'location', label: 'Location', icon: MapPin, description: 'GPS coordinates' },
  { type: 'photo', label: 'Photo', icon: Camera, description: 'Image capture' },
] as const;

interface FieldTypeButtonProps {
  type: typeof FIELD_TYPES[number];
  onPress: () => void;
}

function FieldTypeButton({ type, onPress }: FieldTypeButtonProps) {
  const { theme } = useTheme();
  const IconComponent = type.icon;

  return (
    <TouchableOpacity style={[styles.fieldTypeButton, { backgroundColor: theme.colors.card }]} onPress={onPress}>
      <View style={[styles.fieldTypeIcon, { backgroundColor: theme.colors.primary + '15' }]}>
        <IconComponent size={24} color={theme.colors.primary} />
      </View>
      <Text style={[styles.fieldTypeLabel, { color: theme.colors.text }]}>{type.label}</Text>
      <Text style={[styles.fieldTypeDescription, { color: theme.colors.muted }]}>{type.description}</Text>
    </TouchableOpacity>
  );
}

interface FormFieldEditorProps {
  field: FormField;
  onUpdate: (field: FormField) => void;
  onDelete: () => void;
  onDuplicate: () => void;
}

function FormFieldEditor({ field, onUpdate, onDelete, onDuplicate }: FormFieldEditorProps) {
  const { theme } = useTheme();
  const [isExpanded, setIsExpanded] = useState(false);

  const updateField = (updates: Partial<FormField>) => {
    onUpdate({ ...field, ...updates });
  };

  const addOption = () => {
    const options = field.options || [];
    updateField({ options: [...options, `Option ${options.length + 1}`] });
  };

  const updateOption = (index: number, value: string) => {
    const options = [...(field.options || [])];
    options[index] = value;
    updateField({ options });
  };

  const removeOption = (index: number) => {
    const options = [...(field.options || [])];
    options.splice(index, 1);
    updateField({ options });
  };

  return (
    <View style={[styles.fieldEditor, { backgroundColor: theme.colors.card }]}>
      <TouchableOpacity
        style={styles.fieldHeader}
        onPress={() => setIsExpanded(!isExpanded)}
      >
        <View style={styles.fieldHeaderLeft}>
          <View style={[styles.fieldTypeIndicator, { backgroundColor: theme.colors.primary + '20' }]}>
            <Text style={[styles.fieldTypeText, { color: theme.colors.primary }]}>
              {field.type.toUpperCase()}
            </Text>
          </View>
          <Text style={[styles.fieldTitle, { color: theme.colors.text }]}>
            {field.label || 'Untitled Field'}
          </Text>
          {field.required && (
            <Text style={[styles.requiredIndicator, { color: theme.colors.error }]}>*</Text>
          )}
        </View>
        <View style={styles.fieldActions}>
          <TouchableOpacity style={styles.actionButton} onPress={onDuplicate}>
            <Copy size={16} color={theme.colors.muted} />
          </TouchableOpacity>
          <TouchableOpacity style={styles.actionButton} onPress={onDelete}>
            <Trash2 size={16} color={theme.colors.error} />
          </TouchableOpacity>
        </View>
      </TouchableOpacity>

      {isExpanded && (
        <View style={styles.fieldDetails}>
          <View style={styles.fieldRow}>
            <Text style={[styles.fieldLabel, { color: theme.colors.text }]}>Label *</Text>
            <TextInput
              style={[styles.fieldInput, { backgroundColor: theme.colors.background, color: theme.colors.text }]}
              value={field.label}
              onChangeText={(label) => updateField({ label })}
              placeholder="Enter field label"
              placeholderTextColor={theme.colors.muted}
            />
          </View>

          <View style={styles.fieldRow}>
            <Text style={[styles.fieldLabel, { color: theme.colors.text }]}>Placeholder</Text>
            <TextInput
              style={[styles.fieldInput, { backgroundColor: theme.colors.background, color: theme.colors.text }]}
              value={field.placeholder}
              onChangeText={(placeholder) => updateField({ placeholder })}
              placeholder="Enter placeholder text"
              placeholderTextColor={theme.colors.muted}
            />
          </View>

          <View style={styles.fieldRow}>
            <TouchableOpacity
              style={[styles.checkbox, field.required && { backgroundColor: theme.colors.primary }]}
              onPress={() => updateField({ required: !field.required })}
            >
              {field.required && <Text style={styles.checkmark}>✓</Text>}
            </TouchableOpacity>
            <Text style={[styles.checkboxLabel, { color: theme.colors.text }]}>Required field</Text>
          </View>

          {(field.type === 'select' || field.type === 'multiselect') && (
            <View style={styles.optionsSection}>
              <Text style={[styles.fieldLabel, { color: theme.colors.text }]}>Options</Text>
              {(field.options || []).map((option, index) => (
                <View key={index} style={styles.optionRow}>
                  <TextInput
                    style={[styles.optionInput, { backgroundColor: theme.colors.background, color: theme.colors.text }]}
                    value={option}
                    onChangeText={(value) => updateOption(index, value)}
                    placeholder={`Option ${index + 1}`}
                    placeholderTextColor={theme.colors.muted}
                  />
                  <TouchableOpacity
                    style={styles.removeOptionButton}
                    onPress={() => removeOption(index)}
                  >
                    <Trash2 size={16} color={theme.colors.error} />
                  </TouchableOpacity>
                </View>
              ))}
              <TouchableOpacity
                style={[styles.addOptionButton, { backgroundColor: theme.colors.primary + '15' }]}
                onPress={addOption}
              >
                <Plus size={16} color={theme.colors.primary} />
                <Text style={[styles.addOptionText, { color: theme.colors.primary }]}>Add Option</Text>
              </TouchableOpacity>
            </View>
          )}

          {field.type === 'number' && (
            <View style={styles.validationSection}>
              <Text style={[styles.fieldLabel, { color: theme.colors.text }]}>Validation</Text>
              <View style={styles.validationRow}>
                <TextInput
                  style={[styles.validationInput, { backgroundColor: theme.colors.background, color: theme.colors.text }]}
                  value={field.validation?.min?.toString()}
                  onChangeText={(value) => updateField({ 
                    validation: { ...field.validation, min: value ? parseInt(value) : undefined }
                  })}
                  placeholder="Min value"
                  placeholderTextColor={theme.colors.muted}
                  keyboardType="numeric"
                />
                <TextInput
                  style={[styles.validationInput, { backgroundColor: theme.colors.background, color: theme.colors.text }]}
                  value={field.validation?.max?.toString()}
                  onChangeText={(value) => updateField({ 
                    validation: { ...field.validation, max: value ? parseInt(value) : undefined }
                  })}
                  placeholder="Max value"
                  placeholderTextColor={theme.colors.muted}
                  keyboardType="numeric"
                />
              </View>
            </View>
          )}
        </View>
      )}
    </View>
  );
}

export default function FormBuilderScreen() {
  const { theme } = useTheme();
  const [formName, setFormName] = useState('');
  const [formDescription, setFormDescription] = useState('');
  const [fields, setFields] = useState<FormField[]>([]);
  const [showFieldTypes, setShowFieldTypes] = useState(false);

  const addField = (type: FormField['type']) => {
    const newField: FormField = {
      id: `field_${Date.now()}`,
      type,
      label: '',
      required: false,
      ...(type === 'select' || type === 'multiselect' ? { options: ['Option 1'] } : {}),
    };
    setFields([...fields, newField]);
    setShowFieldTypes(false);
  };

  const updateField = (index: number, updatedField: FormField) => {
    const newFields = [...fields];
    newFields[index] = updatedField;
    setFields(newFields);
  };

  const deleteField = (index: number) => {
    const newFields = fields.filter((_, i) => i !== index);
    setFields(newFields);
  };

  const duplicateField = (index: number) => {
    const fieldToDuplicate = fields[index];
    const duplicatedField: FormField = {
      ...fieldToDuplicate,
      id: `field_${Date.now()}`,
      label: `${fieldToDuplicate.label} (Copy)`,
    };
    const newFields = [...fields];
    newFields.splice(index + 1, 0, duplicatedField);
    setFields(newFields);
  };

  const saveForm = async () => {
    if (!formName.trim()) {
      Alert.alert('Error', 'Please enter a form name');
      return;
    }

    if (fields.length === 0) {
      Alert.alert('Error', 'Please add at least one field');
      return;
    }

    // Validate fields
    for (const field of fields) {
      if (!field.label.trim()) {
        Alert.alert('Error', 'All fields must have a label');
        return;
      }
    }

    const formSchema: FormSchema = {
      id: `form_${Date.now()}`,
      name: formName,
      description: formDescription,
      version: 1,
      fields,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    };

    try {
      // Here you would save to your backend or local storage
      console.log('Saving form schema:', formSchema);
      
      if (Platform.OS === 'web') {
        const existingForms = JSON.parse(localStorage.getItem('fieldsync_forms') || '[]');
        existingForms.push(formSchema);
        localStorage.setItem('fieldsync_forms', JSON.stringify(existingForms));
      }

      Alert.alert('Success', 'Form created successfully!', [
        { text: 'OK', onPress: () => router.back() }
      ]);
    } catch (error) {
      console.error('Error saving form:', error);
      Alert.alert('Error', 'Failed to save form. Please try again.');
    }
  };

  const previewForm = () => {
    router.push({
      pathname: '/forms/preview',
      params: { formData: JSON.stringify({ name: formName, description: formDescription, fields }) }
    });
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <TouchableOpacity
            style={[styles.headerButton, { backgroundColor: theme.colors.card }]}
            onPress={() => router.back()}
          >
            <ArrowLeft size={20} color={theme.colors.text} />
          </TouchableOpacity>
          <View>
            <Text style={[styles.headerTitle, { color: theme.colors.text }]}>Form Builder</Text>
            <Text style={[styles.headerSubtitle, { color: theme.colors.muted }]}>
              🎯 Design custom data collection forms
            </Text>
          </View>
        </View>
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={[styles.headerButton, { backgroundColor: theme.colors.card }]}
            onPress={previewForm}
          >
            <Eye size={20} color={theme.colors.text} />
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.saveButton, { backgroundColor: theme.colors.primary }]}
            onPress={saveForm}
          >
            <Save size={20} color="white" />
            <Text style={styles.saveButtonText}>Save</Text>
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Form Details */}
        <View style={[styles.section, { backgroundColor: theme.colors.card }]}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Form Details</Text>
          
          <View style={styles.inputGroup}>
            <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Form Name *</Text>
            <TextInput
              style={[styles.input, { backgroundColor: theme.colors.background, color: theme.colors.text }]}
              value={formName}
              onChangeText={setFormName}
              placeholder="Enter form name"
              placeholderTextColor={theme.colors.muted}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Description</Text>
            <TextInput
              style={[styles.textArea, { backgroundColor: theme.colors.background, color: theme.colors.text }]}
              value={formDescription}
              onChangeText={setFormDescription}
              placeholder="Describe what this form is used for"
              placeholderTextColor={theme.colors.muted}
              multiline
              numberOfLines={3}
            />
          </View>
        </View>

        {/* Form Fields */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Form Fields</Text>
            <TouchableOpacity
              style={[styles.addFieldButton, { backgroundColor: theme.colors.primary }]}
              onPress={() => setShowFieldTypes(true)}
            >
              <Plus size={20} color="white" />
              <Text style={styles.addFieldButtonText}>Add Field</Text>
            </TouchableOpacity>
          </View>

          {fields.map((field, index) => (
            <FormFieldEditor
              key={field.id}
              field={field}
              onUpdate={(updatedField) => updateField(index, updatedField)}
              onDelete={() => deleteField(index)}
              onDuplicate={() => duplicateField(index)}
            />
          ))}

          {fields.length === 0 && (
            <View style={[styles.emptyState, { backgroundColor: theme.colors.card }]}>
              <FileText size={48} color={theme.colors.muted} />
              <Text style={[styles.emptyStateTitle, { color: theme.colors.text }]}>
                No fields added yet
              </Text>
              <Text style={[styles.emptyStateDescription, { color: theme.colors.muted }]}>
                Add your first field to start building your form
              </Text>
            </View>
          )}
        </View>

        <View style={styles.bottomSpace} />
      </ScrollView>

      {/* Field Type Selector Modal */}
      {showFieldTypes && (
        <View style={styles.modal}>
          <View style={[styles.modalContent, { backgroundColor: theme.colors.card }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: theme.colors.text }]}>Choose Field Type</Text>
              <TouchableOpacity onPress={() => setShowFieldTypes(false)}>
                <Text style={[styles.modalClose, { color: theme.colors.primary }]}>Cancel</Text>
              </TouchableOpacity>
            </View>
            <ScrollView>
              {FIELD_TYPES.map((type) => (
                <FieldTypeButton
                  key={type.type}
                  type={type}
                  onPress={() => addField(type.type)}
                />
              ))}
            </ScrollView>
          </View>
        </View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  headerButton: {
    padding: 12,
    borderRadius: 12,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  headerSubtitle: {
    fontSize: 14,
    marginTop: 2,
  },
  headerActions: {
    flexDirection: 'row',
    gap: 12,
  },
  saveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    gap: 8,
  },
  saveButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  content: {
    flex: 1,
  },
  section: {
    margin: 20,
    padding: 20,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  inputGroup: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  input: {
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  textArea: {
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    height: 80,
    textAlignVertical: 'top',
  },
  addFieldButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    gap: 8,
  },
  addFieldButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  fieldEditor: {
    marginBottom: 16,
    borderRadius: 12,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  fieldHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  fieldHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    flex: 1,
  },
  fieldTypeIndicator: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  fieldTypeText: {
    fontSize: 10,
    fontWeight: 'bold',
  },
  fieldTitle: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  requiredIndicator: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  fieldActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    padding: 8,
  },
  fieldDetails: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  fieldRow: {
    marginBottom: 16,
  },
  fieldLabel: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  fieldInput: {
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: '#E0E0E0',
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkmark: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  checkboxLabel: {
    fontSize: 14,
    flex: 1,
  },
  optionsSection: {
    marginTop: 8,
  },
  optionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 8,
  },
  optionInput: {
    flex: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  removeOptionButton: {
    padding: 8,
  },
  addOptionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    gap: 8,
    marginTop: 8,
  },
  addOptionText: {
    fontSize: 14,
    fontWeight: '600',
  },
  validationSection: {
    marginTop: 8,
  },
  validationRow: {
    flexDirection: 'row',
    gap: 12,
  },
  validationInput: {
    flex: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  emptyState: {
    padding: 40,
    borderRadius: 12,
    alignItems: 'center',
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateDescription: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
  modal: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    margin: 20,
    borderRadius: 16,
    maxHeight: '80%',
    width: '90%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  modalClose: {
    fontSize: 16,
    fontWeight: '600',
  },
  fieldTypeButton: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  fieldTypeIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  fieldTypeLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  fieldTypeDescription: {
    fontSize: 14,
    lineHeight: 18,
  },
  bottomSpace: {
    height: 100,
  },
});
