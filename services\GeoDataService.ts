/**
 * Service for fetching geospatial data from various APIs
 */

export interface GeoDataSource {
  id: string;
  name: string;
  description: string;
  baseUrl: string;
  type: 'wms' | 'wfs' | 'geojson' | 'tiles' | 'rest';
  apiKey?: string;
  attribution: string;
  maxZoom?: number;
  minZoom?: number;
}

export interface LayerDefinition {
  id: string;
  name: string;
  description: string;
  source: GeoDataSource;
  layerName: string;
  category: string;
  geometryType?: string;
  style?: any;
  metadata?: {
    updateFrequency?: string;
    coverage?: string;
    dataFormat?: string;
    license?: string;
  };
}

// Real data sources with public APIs
export const GEO_DATA_SOURCES: GeoDataSource[] = [
  {
    id: 'natural-earth',
    name: 'Natural Earth',
    description: 'Free vector and raster map data at 1:10m, 1:50m, and 1:110m scales',
    baseUrl: 'https://raw.githubusercontent.com/nvkelso/natural-earth-vector/master/geojson',
    type: 'geojson',
    attribution: '© Natural Earth',
  },
  {
    id: 'openstreetmap-overpass',
    name: 'OpenStreetMap Overpass API',
    description: 'Real-time OpenStreetMap data via Overpass API',
    baseUrl: 'https://overpass-api.de/api/interpreter',
    type: 'geojson',
    attribution: '© OpenStreetMap contributors',
  },
  {
    id: 'usgs-earthquake',
    name: 'USGS Earthquake Hazards Program',
    description: 'Real-time earthquake data from USGS',
    baseUrl: 'https://earthquake.usgs.gov/earthquakes/feed/v1.0/summary',
    type: 'geojson',
    attribution: '© USGS',
  },
  {
    id: 'world-bank',
    name: 'World Bank Open Data',
    description: 'World Bank development indicators and statistics',
    baseUrl: 'https://api.worldbank.org/v2',
    type: 'rest',
    attribution: '© World Bank',
  },
  {
    id: 'nasa-firms',
    name: 'NASA FIRMS',
    description: 'Fire Information for Resource Management System',
    baseUrl: 'https://firms.modaps.eosdis.nasa.gov/api',
    type: 'geojson',
    attribution: '© NASA',
  },
  {
    id: 'noaa-weather',
    name: 'NOAA Weather Service',
    description: 'Weather data and forecasts from NOAA',
    baseUrl: 'https://api.weather.gov',
    type: 'geojson',
    attribution: '© NOAA',
  },
  {
    id: 'census-tiger',
    name: 'US Census TIGER',
    description: 'US Census Bureau geographic data',
    baseUrl: 'https://tigerweb.geo.census.gov/arcgis/rest/services',
    type: 'rest',
    attribution: '© US Census Bureau',
  },
];

// Predefined layer definitions using real APIs
export const REAL_LAYER_DEFINITIONS: LayerDefinition[] = [
  // Natural Earth layers
  {
    id: 'ne-countries',
    name: 'World Countries',
    description: 'Country boundaries with basic attributes',
    source: GEO_DATA_SOURCES[0],
    layerName: 'ne_50m_admin_0_countries.geojson',
    category: 'Administrative',
    geometryType: 'Polygon',
  },
  {
    id: 'ne-populated-places',
    name: 'Populated Places',
    description: 'Cities and towns around the world',
    source: GEO_DATA_SOURCES[0],
    layerName: 'ne_50m_populated_places.geojson',
    category: 'Administrative',
    geometryType: 'Point',
  },
  {
    id: 'ne-rivers',
    name: 'Rivers and Lakes',
    description: 'Major rivers and water bodies',
    source: GEO_DATA_SOURCES[0],
    layerName: 'ne_50m_rivers_lake_centerlines.geojson',
    category: 'Hydrology',
    geometryType: 'LineString',
  },
  {
    id: 'ne-roads',
    name: 'Roads',
    description: 'Major roads and highways',
    source: GEO_DATA_SOURCES[0],
    layerName: 'ne_10m_roads.geojson',
    category: 'Transportation',
    geometryType: 'LineString',
  },
  
  // USGS Earthquake data
  {
    id: 'usgs-earthquakes-day',
    name: 'Earthquakes (Last 24 Hours)',
    description: 'All earthquakes in the last 24 hours',
    source: GEO_DATA_SOURCES[2],
    layerName: 'all_day.geojson',
    category: 'Natural Hazards',
    geometryType: 'Point',
    metadata: {
      updateFrequency: 'Real-time',
      coverage: 'Global',
      dataFormat: 'GeoJSON',
    },
  },
  {
    id: 'usgs-earthquakes-week',
    name: 'Earthquakes (Last 7 Days)',
    description: 'All earthquakes in the last 7 days',
    source: GEO_DATA_SOURCES[2],
    layerName: 'all_week.geojson',
    category: 'Natural Hazards',
    geometryType: 'Point',
  },
  {
    id: 'usgs-earthquakes-significant',
    name: 'Significant Earthquakes (Last 30 Days)',
    description: 'Significant earthquakes in the last 30 days',
    source: GEO_DATA_SOURCES[2],
    layerName: 'significant_month.geojson',
    category: 'Natural Hazards',
    geometryType: 'Point',
  },
];

export class GeoDataService {
  private static instance: GeoDataService;
  private cache: Map<string, any> = new Map();
  private cacheExpiry: Map<string, number> = new Map();

  static getInstance(): GeoDataService {
    if (!GeoDataService.instance) {
      GeoDataService.instance = new GeoDataService();
    }
    return GeoDataService.instance;
  }

  /**
   * Fetch layer data from a real API source
   */
  async fetchLayerData(layerDef: LayerDefinition): Promise<any> {
    const cacheKey = `${layerDef.source.id}-${layerDef.layerName}`;
    
    // Check cache first (with 1 hour expiry for most data)
    if (this.cache.has(cacheKey)) {
      const expiry = this.cacheExpiry.get(cacheKey) || 0;
      if (Date.now() < expiry) {
        return this.cache.get(cacheKey);
      }
    }

    try {
      let data;
      
      switch (layerDef.source.type) {
        case 'geojson':
          data = await this.fetchGeoJSON(layerDef);
          break;
        case 'rest':
          data = await this.fetchREST(layerDef);
          break;
        default:
          throw new Error(`Unsupported source type: ${layerDef.source.type}`);
      }

      // Cache the data (1 hour for real-time data, 24 hours for static data)
      const cacheTime = layerDef.metadata?.updateFrequency === 'Real-time' 
        ? 60 * 60 * 1000  // 1 hour
        : 24 * 60 * 60 * 1000; // 24 hours
      
      this.cache.set(cacheKey, data);
      this.cacheExpiry.set(cacheKey, Date.now() + cacheTime);

      return data;
    } catch (error) {
      console.error(`Failed to fetch layer data for ${layerDef.id}:`, error);
      throw error;
    }
  }

  private async fetchGeoJSON(layerDef: LayerDefinition): Promise<any> {
    const url = `${layerDef.source.baseUrl}/${layerDef.layerName}`;
    
    const response = await fetch(url, {
      headers: {
        'Accept': 'application/json',
        ...(layerDef.source.apiKey && { 'Authorization': `Bearer ${layerDef.source.apiKey}` }),
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const geojson = await response.json();
    
    // Validate GeoJSON structure
    if (!geojson.type || (geojson.type !== 'FeatureCollection' && geojson.type !== 'Feature')) {
      throw new Error('Invalid GeoJSON format');
    }

    return {
      type: 'geojson',
      data: geojson,
      features: geojson.type === 'FeatureCollection' ? geojson.features : [geojson],
      metadata: {
        format: 'GeoJSON',
        featureCount: geojson.type === 'FeatureCollection' ? geojson.features.length : 1,
        source: layerDef.source.name,
        attribution: layerDef.source.attribution,
        lastFetched: new Date().toISOString(),
      }
    };
  }

  private async fetchREST(layerDef: LayerDefinition): Promise<any> {
    // Implementation for REST APIs would depend on the specific API
    // This is a placeholder for custom REST API implementations
    throw new Error('REST API fetching not implemented yet');
  }

  /**
   * Fetch OpenStreetMap data using Overpass API
   */
  async fetchOSMData(query: string, bbox?: [number, number, number, number]): Promise<any> {
    const overpassUrl = 'https://overpass-api.de/api/interpreter';
    
    let overpassQuery = query;
    if (bbox) {
      const [south, west, north, east] = bbox;
      overpassQuery = `[bbox:${south},${west},${north},${east}];\n${query}`;
    }

    const response = await fetch(overpassUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: `data=${encodeURIComponent(overpassQuery)}`,
    });

    if (!response.ok) {
      throw new Error(`Overpass API error: ${response.status}`);
    }

    const osmData = await response.json();
    
    // Convert OSM data to GeoJSON
    return this.convertOSMToGeoJSON(osmData);
  }

  private convertOSMToGeoJSON(osmData: any): any {
    // Simplified OSM to GeoJSON conversion
    // In production, use a proper library like osmtogeojson
    const features = osmData.elements?.map((element: any) => {
      let geometry;
      
      if (element.type === 'node') {
        geometry = {
          type: 'Point',
          coordinates: [element.lon, element.lat]
        };
      } else if (element.type === 'way') {
        geometry = {
          type: 'LineString',
          coordinates: element.nodes?.map((nodeId: number) => {
            const node = osmData.elements.find((el: any) => el.id === nodeId);
            return node ? [node.lon, node.lat] : [0, 0];
          }) || []
        };
      }

      return {
        type: 'Feature',
        properties: element.tags || {},
        geometry
      };
    }).filter((f: any) => f.geometry) || [];

    return {
      type: 'FeatureCollection',
      features
    };
  }

  /**
   * Get all available layer definitions
   */
  getAvailableLayers(): LayerDefinition[] {
    return REAL_LAYER_DEFINITIONS;
  }

  /**
   * Search layers by name or category
   */
  searchLayers(query: string): LayerDefinition[] {
    const lowerQuery = query.toLowerCase();
    return REAL_LAYER_DEFINITIONS.filter(layer =>
      layer.name.toLowerCase().includes(lowerQuery) ||
      layer.description.toLowerCase().includes(lowerQuery) ||
      layer.category.toLowerCase().includes(lowerQuery)
    );
  }

  /**
   * Get layers by category
   */
  getLayersByCategory(category: string): LayerDefinition[] {
    return REAL_LAYER_DEFINITIONS.filter(layer => 
      layer.category.toLowerCase() === category.toLowerCase()
    );
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.cache.clear();
    this.cacheExpiry.clear();
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { size: number; entries: string[] } {
    return {
      size: this.cache.size,
      entries: Array.from(this.cache.keys())
    };
  }
}

// Export singleton instance
export const geoDataService = GeoDataService.getInstance();
