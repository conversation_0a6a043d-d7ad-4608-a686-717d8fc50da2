import { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  RefreshControl,
  Modal,
  TextInput,
  Alert,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '@/hooks/useTheme';
import { useTeams } from '@/hooks/useTeams';
import { useAuth } from '@/hooks/useAuth';
import { Team, User } from '@/types';
import {
  Users,
  Plus,
  Search,
  MoreVertical,
  UserPlus,
  UserMinus,
  Edit3,
  Trash2,
  Clock,
  MapPin,
  CheckCircle,
  AlertCircle,
  X,
  Mail,
  Shield,
  Settings,
} from 'lucide-react-native';

interface TeamCardProps {
  team: Team;
  members: User[];
  onEdit: (team: Team) => void;
  onDelete: (team: Team) => void;
  onManageMembers: (team: Team) => void;
}

function TeamCard({ team, members, onEdit, onDelete, onManageMembers }: TeamCardProps) {
  const { theme } = useTheme();
  const [showMenu, setShowMenu] = useState(false);

  const onlineMembers = members.filter(member => 
    Date.now() - (member.lastSyncTimestamp || 0) < 5 * 60 * 1000
  );

  return (
    <View style={[styles.teamCard, { backgroundColor: theme.colors.card }]}>
      <View style={styles.teamCardHeader}>
        <View style={styles.teamInfo}>
          <View style={[styles.teamIcon, { backgroundColor: theme.colors.primaryLight }]}>
            <Users size={24} color={theme.colors.primary} />
          </View>
          <View style={styles.teamDetails}>
            <Text style={[styles.teamName, { color: theme.colors.text }]}>
              {team.name}
            </Text>
            <Text style={[styles.teamDescription, { color: theme.colors.muted }]}>
              {team.description}
            </Text>
          </View>
        </View>
        <TouchableOpacity onPress={() => setShowMenu(!showMenu)}>
          <MoreVertical size={20} color={theme.colors.text} />
        </TouchableOpacity>
      </View>

      <View style={styles.teamStats}>
        <View style={styles.stat}>
          <Text style={[styles.statValue, { color: theme.colors.text }]}>
            {members.length}
          </Text>
          <Text style={[styles.statLabel, { color: theme.colors.muted }]}>
            Members
          </Text>
        </View>
        <View style={styles.stat}>
          <Text style={[styles.statValue, { color: theme.colors.success }]}>
            {onlineMembers.length}
          </Text>
          <Text style={[styles.statLabel, { color: theme.colors.muted }]}>
            Online
          </Text>
        </View>
        <View style={styles.stat}>
          <Text style={[styles.statValue, { color: theme.colors.warning }]}>
            {members.length - onlineMembers.length}
          </Text>
          <Text style={[styles.statLabel, { color: theme.colors.muted }]}>
            Offline
          </Text>
        </View>
      </View>

      <View style={styles.memberPreview}>
        {members.slice(0, 3).map((member, index) => (
          <View
            key={member.id}
            style={[
              styles.memberAvatar,
              { backgroundColor: theme.colors.primary },
              index > 0 && { marginLeft: -8 },
            ]}
          >
            <Text style={styles.memberInitial}>
              {member.name.charAt(0).toUpperCase()}
            </Text>
          </View>
        ))}
        {members.length > 3 && (
          <View
            style={[
              styles.memberAvatar,
              { backgroundColor: theme.colors.muted, marginLeft: -8 },
            ]}
          >
            <Text style={styles.memberInitial}>+{members.length - 3}</Text>
          </View>
        )}
      </View>

      <TouchableOpacity
        style={[styles.manageButton, { backgroundColor: theme.colors.primary }]}
        onPress={() => onManageMembers(team)}
      >
        <UserPlus size={16} color="white" />
        <Text style={styles.manageButtonText}>Manage Team</Text>
      </TouchableOpacity>

      {showMenu && (
        <View style={[styles.actionMenu, { backgroundColor: theme.colors.card }]}>
          <TouchableOpacity
            style={styles.menuItem}
            onPress={() => {
              setShowMenu(false);
              onEdit(team);
            }}
          >
            <Edit3 size={16} color={theme.colors.text} />
            <Text style={[styles.menuText, { color: theme.colors.text }]}>Edit</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.menuItem}
            onPress={() => {
              setShowMenu(false);
              onDelete(team);
            }}
          >
            <Trash2 size={16} color={theme.colors.error} />
            <Text style={[styles.menuText, { color: theme.colors.error }]}>Delete</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
}

interface CreateTeamModalProps {
  visible: boolean;
  onClose: () => void;
  onSuccess?: (team: Team) => void;
  editingTeam?: Team | null;
}

function CreateTeamModal({ visible, onClose, onSuccess, editingTeam }: CreateTeamModalProps) {
  const { theme } = useTheme();
  const { createTeam, updateTeam } = useTeams();
  
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (editingTeam) {
      setName(editingTeam.name);
      setDescription(editingTeam.description || '');
    } else {
      setName('');
      setDescription('');
    }
  }, [editingTeam, visible]);

  const handleSubmit = async () => {
    if (!name.trim()) {
      const message = 'Team name is required';
      if (Platform.OS === 'web') {
        alert(message);
      } else {
        Alert.alert('Error', message);
      }
      return;
    }

    setLoading(true);
    try {
      if (editingTeam) {
        await updateTeam(editingTeam.id, {
          name: name.trim(),
          description: description.trim(),
        });
      } else {
        const newTeam = await createTeam({
          name: name.trim(),
          description: description.trim(),
          members: [],
        });
        onSuccess?.(newTeam);
      }
      
      onClose();
      const message = editingTeam ? 'Team updated successfully!' : 'Team created successfully!';
      if (Platform.OS === 'web') {
        alert(message);
      } else {
        Alert.alert('Success', message);
      }
    } catch (error) {
      console.error('Error saving team:', error);
      const message = 'Failed to save team. Please try again.';
      if (Platform.OS === 'web') {
        alert(message);
      } else {
        Alert.alert('Error', message);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <SafeAreaView style={[styles.modalContainer, { backgroundColor: theme.colors.background }]}>
        <View style={[styles.modalHeader, { borderBottomColor: theme.colors.border }]}>
          <Text style={[styles.modalTitle, { color: theme.colors.text }]}>
            {editingTeam ? 'Edit Team' : 'Create New Team'}
          </Text>
          <TouchableOpacity onPress={onClose}>
            <X size={24} color={theme.colors.text} />
          </TouchableOpacity>
        </View>

        <View style={styles.modalContent}>
          <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Team Name</Text>
          <TextInput
            style={[
              styles.textInput,
              {
                backgroundColor: theme.colors.card,
                borderColor: theme.colors.border,
                color: theme.colors.text,
              },
            ]}
            placeholder="Enter team name"
            placeholderTextColor={theme.colors.placeholder}
            value={name}
            onChangeText={setName}
          />

          <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Description</Text>
          <TextInput
            style={[
              styles.textArea,
              {
                backgroundColor: theme.colors.card,
                borderColor: theme.colors.border,
                color: theme.colors.text,
              },
            ]}
            placeholder="Enter team description"
            placeholderTextColor={theme.colors.placeholder}
            value={description}
            onChangeText={setDescription}
            multiline
            numberOfLines={3}
          />

          <TouchableOpacity
            style={[
              styles.submitButton,
              {
                backgroundColor: loading ? theme.colors.muted : theme.colors.primary,
              },
            ]}
            onPress={handleSubmit}
            disabled={loading}
          >
            <Text style={styles.submitButtonText}>
              {loading ? 'Saving...' : editingTeam ? 'Update Team' : 'Create Team'}
            </Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </Modal>
  );
}

interface MemberDetailProps {
  member: User;
  onRemove: () => void;
}

function MemberDetail({ member, onRemove }: MemberDetailProps) {
  const { theme } = useTheme();
  
  const isOnline = Date.now() - (member.lastSyncTimestamp || 0) < 5 * 60 * 1000;
  const lastSeenText = member.lastSyncTimestamp 
    ? new Date(member.lastSyncTimestamp).toLocaleString()
    : 'Never';

  return (
    <View style={[styles.memberCard, { backgroundColor: theme.colors.card }]}>
      <View style={styles.memberInfo}>
        <View style={[styles.memberAvatar, { backgroundColor: theme.colors.primary }]}>
          <Text style={styles.memberInitial}>
            {member.name.charAt(0).toUpperCase()}
          </Text>
        </View>
        <View style={styles.memberDetails}>
          <Text style={[styles.memberName, { color: theme.colors.text }]}>
            {member.name}
          </Text>
          <Text style={[styles.memberEmail, { color: theme.colors.muted }]}>
            {member.email}
          </Text>
          <View style={styles.memberStatus}>
            <View
              style={[
                styles.statusIndicator,
                { backgroundColor: isOnline ? theme.colors.success : theme.colors.warning },
              ]}
            />
            <Text style={[styles.statusText, { color: theme.colors.muted }]}>
              {isOnline ? 'Online' : `Last seen: ${lastSeenText}`}
            </Text>
          </View>
        </View>
      </View>
      <View style={styles.memberActions}>
        <View style={[styles.roleBadge, { backgroundColor: theme.colors.primaryLight }]}>
          <Shield size={12} color={theme.colors.primary} />
          <Text style={[styles.roleText, { color: theme.colors.primary }]}>
            {member.role}
          </Text>
        </View>
        <TouchableOpacity onPress={onRemove} style={styles.removeButton}>
          <UserMinus size={16} color={theme.colors.error} />
        </TouchableOpacity>
      </View>
    </View>
  );
}

export default function TeamManagementScreen() {
  const { theme } = useTheme();
  const { user } = useAuth();
  const {
    teams,
    teamMembers,
    loading,
    error,
    fetchTeams,
    deleteTeam,
    getTeamMembers,
    addTeamMember,
    removeTeamMember,
  } = useTeams();
  
  const [searchQuery, setSearchQuery] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingTeam, setEditingTeam] = useState<Team | null>(null);
  const [selectedTeam, setSelectedTeam] = useState<Team | null>(null);
  const [showMemberModal, setShowMemberModal] = useState(false);
  const [newMemberEmail, setNewMemberEmail] = useState('');

  const filteredTeams = teams.filter(team =>
    team.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (team.description && team.description.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  const onRefresh = async () => {
    setRefreshing(true);
    await fetchTeams();
    setRefreshing(false);
  };

  const handleDeleteTeam = (team: Team) => {
    const confirmDelete = () => {
      Alert.alert(
        'Delete Team',
        `Are you sure you want to delete "${team.name}"? This action cannot be undone.`,
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Delete',
            style: 'destructive',
            onPress: async () => {
              try {
                await deleteTeam(team.id);
                const message = 'Team deleted successfully';
                if (Platform.OS === 'web') {
                  alert(message);
                } else {
                  Alert.alert('Success', message);
                }
              } catch (error) {
                console.error('Error deleting team:', error);
                const message = 'Failed to delete team. Please try again.';
                if (Platform.OS === 'web') {
                  alert(message);
                } else {
                  Alert.alert('Error', message);
                }
              }
            },
          },
        ]
      );
    };

    if (Platform.OS === 'web') {
      if (confirm(`Are you sure you want to delete "${team.name}"? This action cannot be undone.`)) {
        deleteTeam(team.id).catch(console.error);
      }
    } else {
      confirmDelete();
    }
  };

  const handleAddMember = async () => {
    if (!selectedTeam || !newMemberEmail.trim()) return;

    try {
      await addTeamMember(selectedTeam.id, newMemberEmail.trim());
      setNewMemberEmail('');
      const message = 'Member added successfully';
      if (Platform.OS === 'web') {
        alert(message);
      } else {
        Alert.alert('Success', message);
      }
    } catch (error) {
      console.error('Error adding member:', error);
      const message = 'Failed to add member. Please try again.';
      if (Platform.OS === 'web') {
        alert(message);
      } else {
        Alert.alert('Error', message);
      }
    }
  };

  const handleRemoveMember = async (memberId: string) => {
    if (!selectedTeam) return;

    try {
      await removeTeamMember(selectedTeam.id, memberId);
      const message = 'Member removed successfully';
      if (Platform.OS === 'web') {
        alert(message);
      } else {
        Alert.alert('Success', message);
      }
    } catch (error) {
      console.error('Error removing member:', error);
      const message = 'Failed to remove member. Please try again.';
      if (Platform.OS === 'web') {
        alert(message);
      } else {
        Alert.alert('Error', message);
      }
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={[styles.loadingText, { color: theme.colors.text }]}>
            Loading teams...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <View style={styles.header}>
        <View>
          <Text style={[styles.title, { color: theme.colors.text }]}>Team Management</Text>
          <Text style={[styles.subtitle, { color: theme.colors.muted }]}>
            👥 Manage teams and collaborate effectively
          </Text>
        </View>
        <TouchableOpacity
          style={[styles.addButton, { backgroundColor: theme.colors.primary }]}
          onPress={() => setShowCreateModal(true)}
        >
          <Plus size={20} color="white" />
        </TouchableOpacity>
      </View>

      <View style={styles.searchContainer}>
        <View style={[styles.searchBar, { backgroundColor: theme.colors.card }]}>
          <Search size={20} color={theme.colors.muted} />
          <TextInput
            style={[styles.searchInput, { color: theme.colors.text }]}
            placeholder="Search teams..."
            placeholderTextColor={theme.colors.placeholder}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>
      </View>

      <FlatList
        data={filteredTeams}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <TeamCard
            team={item}
            members={getTeamMembers(item.id)}
            onEdit={(team) => {
              setEditingTeam(team);
              setShowCreateModal(true);
            }}
            onDelete={handleDeleteTeam}
            onManageMembers={(team) => {
              setSelectedTeam(team);
              setShowMemberModal(true);
            }}
          />
        )}
        contentContainerStyle={styles.listContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
        showsVerticalScrollIndicator={false}
      />

      {/* Create/Edit Team Modal */}
      <CreateTeamModal
        visible={showCreateModal}
        onClose={() => {
          setShowCreateModal(false);
          setEditingTeam(null);
        }}
        editingTeam={editingTeam}
      />

      {/* Manage Members Modal */}
      <Modal
        visible={showMemberModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={[styles.modalContainer, { backgroundColor: theme.colors.background }]}>
          <View style={[styles.modalHeader, { borderBottomColor: theme.colors.border }]}>
            <Text style={[styles.modalTitle, { color: theme.colors.text }]}>
              Manage Team Members
            </Text>
            <TouchableOpacity onPress={() => setShowMemberModal(false)}>
              <X size={24} color={theme.colors.text} />
            </TouchableOpacity>
          </View>

          <View style={styles.modalContent}>
            <Text style={[styles.teamNameHeader, { color: theme.colors.text }]}>
              {selectedTeam?.name}
            </Text>

            <View style={styles.addMemberSection}>
              <Text style={[styles.inputLabel, { color: theme.colors.text }]}>
                Add New Member
              </Text>
              <View style={styles.addMemberRow}>
                <TextInput
                  style={[
                    styles.emailInput,
                    {
                      backgroundColor: theme.colors.card,
                      borderColor: theme.colors.border,
                      color: theme.colors.text,
                    },
                  ]}
                  placeholder="Enter email address"
                  placeholderTextColor={theme.colors.placeholder}
                  value={newMemberEmail}
                  onChangeText={setNewMemberEmail}
                  keyboardType="email-address"
                  autoCapitalize="none"
                />
                <TouchableOpacity
                  style={[styles.addMemberButton, { backgroundColor: theme.colors.primary }]}
                  onPress={handleAddMember}
                >
                  <UserPlus size={16} color="white" />
                </TouchableOpacity>
              </View>
            </View>

            <Text style={[styles.membersHeader, { color: theme.colors.text }]}>
              Current Members ({selectedTeam ? getTeamMembers(selectedTeam.id).length : 0})
            </Text>

            <FlatList
              data={selectedTeam ? getTeamMembers(selectedTeam.id) : []}
              keyExtractor={(item) => item.id}
              renderItem={({ item }) => (
                <MemberDetail
                  member={item}
                  onRemove={() => handleRemoveMember(item.id)}
                />
              )}
              showsVerticalScrollIndicator={false}
            />
          </View>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    fontWeight: '500',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingHorizontal: 16,
    paddingTop: 8,
    paddingBottom: 16,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  subtitle: {
    fontSize: 14,
    marginTop: 4,
  },
  addButton: {
    padding: 12,
    borderRadius: 8,
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
  },
  listContent: {
    paddingHorizontal: 16,
    paddingBottom: 100,
  },
  teamCard: {
    marginBottom: 16,
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  teamCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  teamInfo: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flex: 1,
  },
  teamIcon: {
    padding: 12,
    borderRadius: 8,
    marginRight: 12,
  },
  teamDetails: {
    flex: 1,
  },
  teamName: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  teamDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  teamStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 16,
    paddingVertical: 12,
  },
  stat: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  statLabel: {
    fontSize: 12,
    marginTop: 2,
  },
  memberPreview: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  memberAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'white',
  },
  memberInitial: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  manageButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    borderRadius: 6,
    gap: 6,
  },
  manageButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  actionMenu: {
    position: 'absolute',
    top: 45,
    right: 16,
    borderRadius: 8,
    padding: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3,
    elevation: 5,
    zIndex: 1000,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    gap: 8,
  },
  menuText: {
    fontSize: 14,
    fontWeight: '500',
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    marginTop: 16,
  },
  textInput: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
  },
  textArea: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    minHeight: 80,
    textAlignVertical: 'top',
  },
  submitButton: {
    marginTop: 24,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  submitButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  teamNameHeader: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  addMemberSection: {
    marginBottom: 24,
  },
  addMemberRow: {
    flexDirection: 'row',
    gap: 8,
  },
  emailInput: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
  },
  addMemberButton: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  membersHeader: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  memberCard: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    marginBottom: 8,
    borderRadius: 8,
  },
  memberInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  memberDetails: {
    marginLeft: 12,
    flex: 1,
  },
  memberName: {
    fontSize: 16,
    fontWeight: '600',
  },
  memberEmail: {
    fontSize: 14,
    marginTop: 2,
  },
  memberStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  statusText: {
    fontSize: 12,
  },
  memberActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  roleBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    gap: 4,
  },
  roleText: {
    fontSize: 12,
    fontWeight: '500',
  },
  removeButton: {
    padding: 4,
  },
});
