import { createClient } from '@supabase/supabase-js';
import { Database } from '@/types/database';

// Supabase configuration
// For production, these should be environment variables
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL || 'https://dxfzgqjqmqnbycgiriam.supabase.co';
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.pL-nM39Esfx8RFbM6jsX7Vr4WH0AynfnUwAq3RwMc_E';

// Create Supabase client with TypeScript support
export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: undefined, // We'll handle storage in AuthProvider
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
  realtime: {
    params: {
      eventsPerSecond: 10,
    },
  },
});

// Database table names for type safety
export const TABLES = {
  USERS: 'users',
  PROJECTS: 'projects',
  TEAMS: 'teams',
  TEAM_MEMBERS: 'team_members',
  FORMS: 'forms',
  FORM_FIELDS: 'form_fields',
  SUBMISSIONS: 'submissions',
  SUBMISSION_DATA: 'submission_data',
  MEDIA_ATTACHMENTS: 'media_attachments',
  PROJECT_TEAMS: 'project_teams',
  USER_PROFILES: 'user_profiles',
} as const;

// Real-time channel names
export const CHANNELS = {
  PROJECTS: 'projects-channel',
  SUBMISSIONS: 'submissions-channel',
  TEAMS: 'teams-channel',
  USERS: 'users-channel',
} as const;

// Helper function to check if Supabase is properly configured
export const isSupabaseConfigured = (): boolean => {
  return (
    supabaseUrl !== 'https://your-project.supabase.co' &&
    supabaseAnonKey !== 'your-anon-key' &&
    supabaseUrl.includes('supabase.co')
  );
};

// Development mode checker
export const isDevelopmentMode = (): boolean => {
  return !isSupabaseConfigured();
};

// Error handler for Supabase operations
export const handleSupabaseError = (error: any, operation: string) => {
  console.error(`Supabase ${operation} error:`, error);
  
  if (error?.code === 'PGRST301') {
    throw new Error('Unauthorized access. Please check your authentication.');
  }
  
  if (error?.code === 'PGRST116') {
    throw new Error('The requested resource was not found.');
  }
  
  if (error?.message) {
    throw new Error(error.message);
  }
  
  throw new Error(`An error occurred during ${operation}`);
};

export default supabase;
