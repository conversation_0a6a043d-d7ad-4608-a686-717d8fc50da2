import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Switch,
  ScrollView,
  Alert,
} from 'react-native';
import { useTheme } from '@/hooks/useTheme';
import { FormQuestion, ValidationRule } from '@/types';
import {
  Plus,
  Trash2,
  Settings,
  CheckSquare,
  Square,
} from 'lucide-react-native';

interface QuestionEditorProps {
  question: FormQuestion;
  onUpdate: (updates: Partial<FormQuestion>) => void;
}

export default function QuestionEditor({ question, onUpdate }: QuestionEditorProps) {
  const { theme } = useTheme();
  const [showAdvanced, setShowAdvanced] = useState(false);

  const updateLabel = (label: string) => {
    onUpdate({ label });
  };

  const updateDescription = (description: string) => {
    onUpdate({ description });
  };

  const updatePlaceholder = (placeholder: string) => {
    onUpdate({ placeholder });
  };

  const updateRequired = (required: boolean) => {
    onUpdate({ required });
  };

  const updateDefaultValue = (defaultValue: any) => {
    onUpdate({ defaultValue });
  };

  const addOption = () => {
    const currentOptions = question.options || [];
    const newOption = {
      label: `Option ${currentOptions.length + 1}`,
      value: `option_${currentOptions.length + 1}`,
    };
    onUpdate({ options: [...currentOptions, newOption] });
  };

  const updateOption = (index: number, updates: { label?: string; value?: string }) => {
    const currentOptions = question.options || [];
    const updatedOptions = currentOptions.map((option, i) =>
      i === index ? { ...option, ...updates } : option
    );
    onUpdate({ options: updatedOptions });
  };

  const removeOption = (index: number) => {
    const currentOptions = question.options || [];
    if (currentOptions.length <= 1) {
      Alert.alert('Cannot Remove', 'Must have at least one option.');
      return;
    }
    const updatedOptions = currentOptions.filter((_, i) => i !== index);
    onUpdate({ options: updatedOptions });
  };

  const addValidationRule = (type: ValidationRule['type']) => {
    const currentRules = question.validation || [];
    const newRule: ValidationRule = {
      type,
      message: `Please provide a valid ${type}`,
    };
    
    if (type === 'min' || type === 'max') {
      newRule.value = 0;
    }
    
    onUpdate({ validation: [...currentRules, newRule] });
  };

  const updateValidationRule = (index: number, updates: Partial<ValidationRule>) => {
    const currentRules = question.validation || [];
    const updatedRules = currentRules.map((rule, i) =>
      i === index ? { ...rule, ...updates } : rule
    );
    onUpdate({ validation: updatedRules });
  };

  const removeValidationRule = (index: number) => {
    const currentRules = question.validation || [];
    const updatedRules = currentRules.filter((_, i) => i !== index);
    onUpdate({ validation: updatedRules });
  };

  const updateProperties = (updates: Record<string, any>) => {
    onUpdate({ properties: { ...question.properties, ...updates } });
  };

  const renderBasicFields = () => (
    <View style={styles.section}>
      <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Basic Settings</Text>
      
      <View style={styles.field}>
        <Text style={[styles.fieldLabel, { color: theme.colors.text }]}>Question Label</Text>
        <TextInput
          style={[styles.textInput, { backgroundColor: theme.colors.background, color: theme.colors.text, borderColor: theme.colors.border }]}
          value={question.label}
          onChangeText={updateLabel}
          placeholder="Enter question text"
          placeholderTextColor={theme.colors.placeholder}
        />
      </View>

      <View style={styles.field}>
        <Text style={[styles.fieldLabel, { color: theme.colors.text }]}>Description (Optional)</Text>
        <TextInput
          style={[styles.textInput, { backgroundColor: theme.colors.background, color: theme.colors.text, borderColor: theme.colors.border }]}
          value={question.description || ''}
          onChangeText={updateDescription}
          placeholder="Add helpful text or instructions"
          placeholderTextColor={theme.colors.placeholder}
          multiline
          numberOfLines={2}
        />
      </View>

      {(question.type === 'text' || question.type === 'number' || question.type === 'phone') && (
        <View style={styles.field}>
          <Text style={[styles.fieldLabel, { color: theme.colors.text }]}>Placeholder</Text>
          <TextInput
            style={[styles.textInput, { backgroundColor: theme.colors.background, color: theme.colors.text, borderColor: theme.colors.border }]}
            value={question.placeholder || ''}
            onChangeText={updatePlaceholder}
            placeholder="Enter placeholder text"
            placeholderTextColor={theme.colors.placeholder}
          />
        </View>
      )}

      <View style={styles.switchField}>
        <Text style={[styles.fieldLabel, { color: theme.colors.text }]}>Required</Text>
        <Switch
          value={question.required}
          onValueChange={updateRequired}
          trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
          thumbColor={question.required ? 'white' : theme.colors.muted}
        />
      </View>
    </View>
  );

  const renderChoiceOptions = () => {
    if (!['select', 'multiselect'].includes(question.type)) return null;

    return (
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Options</Text>
          <TouchableOpacity
            style={[styles.addButton, { backgroundColor: theme.colors.primary }]}
            onPress={addOption}
          >
            <Plus size={16} color="white" />
          </TouchableOpacity>
        </View>

        {(question.options || []).map((option, index) => (
          <View key={index} style={styles.optionRow}>
            <View style={styles.optionInputs}>
              <TextInput
                style={[styles.optionInput, { backgroundColor: theme.colors.background, color: theme.colors.text, borderColor: theme.colors.border }]}
                value={option.label}
                onChangeText={(text) => updateOption(index, { label: text })}
                placeholder="Option text"
                placeholderTextColor={theme.colors.placeholder}
              />
              <TextInput
                style={[styles.optionInput, { backgroundColor: theme.colors.background, color: theme.colors.text, borderColor: theme.colors.border }]}
                value={option.value}
                onChangeText={(text) => updateOption(index, { value: text })}
                placeholder="Value"
                placeholderTextColor={theme.colors.placeholder}
              />
            </View>
            <TouchableOpacity
              style={styles.removeButton}
              onPress={() => removeOption(index)}
            >
              <Trash2 size={16} color={theme.colors.error} />
            </TouchableOpacity>
          </View>
        ))}
      </View>
    );
  };

  const renderPhoneSettings = () => {
    if (question.type !== 'phone') return null;

    return (
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Phone Settings</Text>
        
        <View style={styles.field}>
          <Text style={[styles.fieldLabel, { color: theme.colors.text }]}>Country Code</Text>
          <TextInput
            style={[styles.textInput, { backgroundColor: theme.colors.background, color: theme.colors.text, borderColor: theme.colors.border }]}
            value={question.properties?.countryCode || '+1'}
            onChangeText={(text) => updateProperties({ countryCode: text })}
            placeholder="+1"
            placeholderTextColor={theme.colors.placeholder}
          />
        </View>

        <View style={styles.field}>
          <Text style={[styles.fieldLabel, { color: theme.colors.text }]}>Format Pattern</Text>
          <TextInput
            style={[styles.textInput, { backgroundColor: theme.colors.background, color: theme.colors.text, borderColor: theme.colors.border }]}
            value={question.properties?.format || '(###) ###-####'}
            onChangeText={(text) => updateProperties({ format: text })}
            placeholder="(###) ###-####"
            placeholderTextColor={theme.colors.placeholder}
          />
          <Text style={[styles.helpText, { color: theme.colors.muted }]}>
            Use # for digits. Example: (###) ###-#### for US format
          </Text>
        </View>

        <View style={styles.switchField}>
          <Text style={[styles.fieldLabel, { color: theme.colors.text }]}>International Numbers</Text>
          <Switch
            value={question.properties?.allowInternational || false}
            onValueChange={(value) => updateProperties({ allowInternational: value })}
            trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
            thumbColor={question.properties?.allowInternational ? 'white' : theme.colors.muted}
          />
        </View>
      </View>
    );
  };

  const renderMediaSettings = () => {
    if (!['photo', 'audio', 'video'].includes(question.type)) return null;

    return (
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Media Settings</Text>
        
        {question.type === 'photo' && (
          <>
            <View style={styles.switchField}>
              <Text style={[styles.fieldLabel, { color: theme.colors.text }]}>Allow Multiple Photos</Text>
              <Switch
                value={question.properties?.allowMultiple || false}
                onValueChange={(value) => updateProperties({ allowMultiple: value })}
                trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
                thumbColor={question.properties?.allowMultiple ? 'white' : theme.colors.muted}
              />
            </View>

            <View style={styles.field}>
              <Text style={[styles.fieldLabel, { color: theme.colors.text }]}>Maximum Photos</Text>
              <TextInput
                style={[styles.textInput, { backgroundColor: theme.colors.background, color: theme.colors.text, borderColor: theme.colors.border }]}
                value={question.properties?.maxCount?.toString() || '5'}
                onChangeText={(text) => updateProperties({ maxCount: parseInt(text) || 5 })}
                placeholder="5"
                placeholderTextColor={theme.colors.placeholder}
                keyboardType="numeric"
                editable={question.properties?.allowMultiple}
              />
            </View>
          </>
        )}

        {question.type === 'audio' && (
          <View style={styles.field}>
            <Text style={[styles.fieldLabel, { color: theme.colors.text }]}>Max Duration (seconds)</Text>
            <TextInput
              style={[styles.textInput, { backgroundColor: theme.colors.background, color: theme.colors.text, borderColor: theme.colors.border }]}
              value={question.properties?.maxDuration?.toString() || '300'}
              onChangeText={(text) => updateProperties({ maxDuration: parseInt(text) || 300 })}
              placeholder="300"
              placeholderTextColor={theme.colors.placeholder}
              keyboardType="numeric"
            />
          </View>
        )}

        {question.type === 'video' && (
          <>
            <View style={styles.field}>
              <Text style={[styles.fieldLabel, { color: theme.colors.text }]}>Max Duration (seconds)</Text>
              <TextInput
                style={[styles.textInput, { backgroundColor: theme.colors.background, color: theme.colors.text, borderColor: theme.colors.border }]}
                value={question.properties?.maxDuration?.toString() || '600'}
                onChangeText={(text) => updateProperties({ maxDuration: parseInt(text) || 600 })}
                placeholder="600"
                placeholderTextColor={theme.colors.placeholder}
                keyboardType="numeric"
              />
            </View>

            <View style={styles.field}>
              <Text style={[styles.fieldLabel, { color: theme.colors.text }]}>Video Quality</Text>
              <View style={styles.qualityButtons}>
                {['low', 'medium', 'high'].map((quality) => (
                  <TouchableOpacity
                    key={quality}
                    style={[
                      styles.qualityButton,
                      {
                        backgroundColor: question.properties?.quality === quality 
                          ? theme.colors.primary 
                          : theme.colors.background,
                        borderColor: question.properties?.quality === quality 
                          ? theme.colors.primary 
                          : theme.colors.border,
                      }
                    ]}
                    onPress={() => updateProperties({ quality })}
                  >
                    <Text style={[
                      styles.qualityButtonText,
                      { 
                        color: question.properties?.quality === quality 
                          ? 'white' 
                          : theme.colors.text 
                      }
                    ]}>
                      {quality.charAt(0).toUpperCase() + quality.slice(1)}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          </>
        )}

        <View style={styles.switchField}>
          <Text style={[styles.fieldLabel, { color: theme.colors.text }]}>Compress Media</Text>
          <Switch
            value={question.properties?.compress !== false}
            onValueChange={(value) => updateProperties({ compress: value })}
            trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
            thumbColor={question.properties?.compress !== false ? 'white' : theme.colors.muted}
          />
        </View>
      </View>
    );
  };

  const renderSignatureSettings = () => {
    if (question.type !== 'signature') return null;

    return (
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Signature Settings</Text>
        
        <View style={styles.field}>
          <Text style={[styles.fieldLabel, { color: theme.colors.text }]}>Signature Prompt</Text>
          <TextInput
            style={[styles.textInput, { backgroundColor: theme.colors.background, color: theme.colors.text, borderColor: theme.colors.border }]}
            value={question.properties?.prompt || 'Please sign here'}
            onChangeText={(text) => updateProperties({ prompt: text })}
            placeholder="Please sign here"
            placeholderTextColor={theme.colors.placeholder}
          />
        </View>

        <View style={styles.switchField}>
          <Text style={[styles.fieldLabel, { color: theme.colors.text }]}>Clear Button</Text>
          <Switch
            value={question.properties?.showClearButton !== false}
            onValueChange={(value) => updateProperties({ showClearButton: value })}
            trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
            thumbColor={question.properties?.showClearButton !== false ? 'white' : theme.colors.muted}
          />
        </View>

        <View style={styles.field}>
          <Text style={[styles.fieldLabel, { color: theme.colors.text }]}>Pen Color</Text>
          <View style={styles.colorButtons}>
            {['#000000', '#0066CC', '#CC0000', '#006600'].map((color) => (
              <TouchableOpacity
                key={color}
                style={[
                  styles.colorButton,
                  { 
                    backgroundColor: color,
                    borderColor: question.properties?.penColor === color 
                      ? theme.colors.primary 
                      : theme.colors.border,
                    borderWidth: 3,
                  }
                ]}
                onPress={() => updateProperties({ penColor: color })}
              />
            ))}
          </View>
        </View>
      </View>
    );
  };

  const renderQRSettings = () => {
    if (question.type !== 'qr_scan') return null;

    return (
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>QR/Barcode Settings</Text>
        
        <View style={styles.switchField}>
          <Text style={[styles.fieldLabel, { color: theme.colors.text }]}>Allow Manual Entry</Text>
          <Switch
            value={question.properties?.allowManualEntry || false}
            onValueChange={(value) => updateProperties({ allowManualEntry: value })}
            trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
            thumbColor={question.properties?.allowManualEntry ? 'white' : theme.colors.muted}
          />
        </View>

        <View style={styles.field}>
          <Text style={[styles.fieldLabel, { color: theme.colors.text }]}>Expected Format</Text>
          <View style={styles.formatButtons}>
            {['any', 'qr', 'ean', 'code128', 'datamatrix'].map((format) => (
              <TouchableOpacity
                key={format}
                style={[
                  styles.formatButton,
                  {
                    backgroundColor: question.properties?.expectedFormat === format 
                      ? theme.colors.primary 
                      : theme.colors.background,
                    borderColor: question.properties?.expectedFormat === format 
                      ? theme.colors.primary 
                      : theme.colors.border,
                  }
                ]}
                onPress={() => updateProperties({ expectedFormat: format })}
              >
                <Text style={[
                  styles.formatButtonText,
                  { 
                    color: question.properties?.expectedFormat === format 
                      ? 'white' 
                      : theme.colors.text 
                  }
                ]}>
                  {format.toUpperCase()}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {question.properties?.allowManualEntry && (
          <View style={styles.field}>
            <Text style={[styles.fieldLabel, { color: theme.colors.text }]}>Manual Entry Placeholder</Text>
            <TextInput
              style={[styles.textInput, { backgroundColor: theme.colors.background, color: theme.colors.text, borderColor: theme.colors.border }]}
              value={question.properties?.manualPlaceholder || 'Enter code manually'}
              onChangeText={(text) => updateProperties({ manualPlaceholder: text })}
              placeholder="Enter code manually"
              placeholderTextColor={theme.colors.placeholder}
            />
          </View>
        )}
      </View>
    );
  };

  const renderValidationRules = () => {
    if (!showAdvanced) return null;

    return (
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Validation Rules</Text>
          <View style={styles.validationButtons}>
            {question.type === 'text' && (
              <TouchableOpacity
                style={[styles.smallButton, { backgroundColor: theme.colors.secondary }]}
                onPress={() => addValidationRule('min')}
              >
                <Text style={styles.smallButtonText}>Min Length</Text>
              </TouchableOpacity>
            )}
            {question.type === 'number' && (
              <>
                <TouchableOpacity
                  style={[styles.smallButton, { backgroundColor: theme.colors.secondary }]}
                  onPress={() => addValidationRule('min')}
                >
                  <Text style={styles.smallButtonText}>Min Value</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.smallButton, { backgroundColor: theme.colors.secondary }]}
                  onPress={() => addValidationRule('max')}
                >
                  <Text style={styles.smallButtonText}>Max Value</Text>
                </TouchableOpacity>
              </>
            )}
            <TouchableOpacity
              style={[styles.smallButton, { backgroundColor: theme.colors.secondary }]}
              onPress={() => addValidationRule('pattern')}
            >
              <Text style={styles.smallButtonText}>Pattern</Text>
            </TouchableOpacity>
          </View>
        </View>

        {(question.validation || []).map((rule, index) => (
          <View key={index} style={styles.validationRule}>
            <View style={styles.validationRuleHeader}>
              <Text style={[styles.validationRuleType, { color: theme.colors.text }]}>
                {rule.type.toUpperCase()}
              </Text>
              <TouchableOpacity
                style={styles.removeButton}
                onPress={() => removeValidationRule(index)}
              >
                <Trash2 size={14} color={theme.colors.error} />
              </TouchableOpacity>
            </View>
            
            {(rule.type === 'min' || rule.type === 'max') && (
              <TextInput
                style={[styles.textInput, { backgroundColor: theme.colors.background, color: theme.colors.text, borderColor: theme.colors.border }]}
                value={rule.value?.toString() || ''}
                onChangeText={(text) => updateValidationRule(index, { value: parseInt(text) || 0 })}
                placeholder="Enter value"
                placeholderTextColor={theme.colors.placeholder}
                keyboardType="numeric"
              />
            )}
            
            {rule.type === 'pattern' && (
              <TextInput
                style={[styles.textInput, { backgroundColor: theme.colors.background, color: theme.colors.text, borderColor: theme.colors.border }]}
                value={rule.value?.toString() || ''}
                onChangeText={(text) => updateValidationRule(index, { value: text })}
                placeholder="Enter regex pattern"
                placeholderTextColor={theme.colors.placeholder}
              />
            )}
            
            <TextInput
              style={[styles.textInput, { backgroundColor: theme.colors.background, color: theme.colors.text, borderColor: theme.colors.border }]}
              value={rule.message}
              onChangeText={(text) => updateValidationRule(index, { message: text })}
              placeholder="Error message"
              placeholderTextColor={theme.colors.placeholder}
            />
          </View>
        ))}
      </View>
    );
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {renderBasicFields()}
      {renderChoiceOptions()}
      {renderPhoneSettings()}
      {renderMediaSettings()}
      {renderSignatureSettings()}
      {renderQRSettings()}
      
      <TouchableOpacity
        style={styles.advancedToggle}
        onPress={() => setShowAdvanced(!showAdvanced)}
      >
        <Settings size={16} color={theme.colors.primary} />
        <Text style={[styles.advancedToggleText, { color: theme.colors.primary }]}>
          {showAdvanced ? 'Hide' : 'Show'} Advanced Settings
        </Text>
      </TouchableOpacity>
      
      {renderValidationRules()}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  section: {
    marginBottom: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
  },
  field: {
    marginBottom: 16,
  },
  fieldLabel: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    marginBottom: 6,
  },
  textInput: {
    borderWidth: 1,
    borderRadius: 6,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 14,
    fontFamily: 'Inter-Regular',
  },
  switchField: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 8,
  },
  addButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
  },
  optionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  optionInputs: {
    flex: 1,
    flexDirection: 'row',
    gap: 8,
  },
  optionInput: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 6,
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 14,
    fontFamily: 'Inter-Regular',
  },
  removeButton: {
    padding: 8,
    marginLeft: 8,
  },
  advancedToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    gap: 8,
  },
  advancedToggleText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
  },
  validationButtons: {
    flexDirection: 'row',
    gap: 6,
  },
  smallButton: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  smallButtonText: {
    color: 'white',
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },
  validationRule: {
    padding: 12,
    borderRadius: 6,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  validationRuleHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  validationRuleType: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    letterSpacing: 0.5,
  },
  helpText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    marginTop: 4,
    fontStyle: 'italic',
  },
  qualityButtons: {
    flexDirection: 'row',
    gap: 8,
    marginTop: 8,
  },
  qualityButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    borderWidth: 1,
    alignItems: 'center',
  },
  qualityButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
  },
  colorButtons: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 8,
  },
  colorButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  formatButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginTop: 8,
  },
  formatButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 4,
    borderWidth: 1,
    alignItems: 'center',
  },
  formatButtonText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },
});
