import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Platform,
} from 'react-native';
import { useTheme } from '@/hooks/useTheme';
import { Mic, Square, Play, Pause, Trash2, RotateCcw } from 'lucide-react-native';
import { Audio } from 'expo-av';

interface AudioCaptureProps {
  value?: { uri: string; duration: number }; // Audio recording data
  onChange: (recording: { uri: string; duration: number } | null) => void;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  maxDuration?: number; // in seconds
}

export default function AudioCapture({
  value,
  onChange,
  placeholder = 'Record audio',
  required = false,
  disabled = false,
  maxDuration = 300, // 5 minutes default
}: AudioCaptureProps) {
  const { theme } = useTheme();
  const [recording, setRecording] = useState<Audio.Recording | null>(null);
  const [sound, setSound] = useState<Audio.Sound | null>(null);
  const [isRecording, setIsRecording] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [recordingDuration, setRecordingDuration] = useState(0);
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);

  useEffect(() => {
    checkPermissions();
    return () => {
      if (sound) {
        sound.unloadAsync();
      }
    };
  }, []);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isRecording) {
      interval = setInterval(() => {
        setRecordingDuration(prev => {
          if (prev >= maxDuration) {
            stopRecording();
            return prev;
          }
          return prev + 1;
        });
      }, 1000);
    }
    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [isRecording, maxDuration]);

  const checkPermissions = async () => {
    try {
      const { status } = await Audio.requestPermissionsAsync();
      setHasPermission(status === 'granted');
      
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
      });
    } catch (error) {
      console.error('Error checking audio permissions:', error);
      setHasPermission(false);
    }
  };

  const startRecording = async () => {
    if (!hasPermission) {
      Alert.alert(
        'Microphone Permission Required',
        'Please grant microphone permission to record audio.',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Settings', onPress: () => checkPermissions() },
        ]
      );
      return;
    }

    try {
      const { recording } = await Audio.Recording.createAsync(
        Audio.RecordingOptionsPresets.HIGH_QUALITY
      );
      
      setRecording(recording);
      setIsRecording(true);
      setRecordingDuration(0);
    } catch (error) {
      Alert.alert('Error', 'Failed to start recording');
      console.error('Recording error:', error);
    }
  };

  const stopRecording = async () => {
    if (!recording) return;

    try {
      setIsRecording(false);
      await recording.stopAndUnloadAsync();
      
      const uri = recording.getURI();
      if (uri) {
        onChange({
          uri,
          duration: recordingDuration,
        });
      }
      
      setRecording(null);
      setRecordingDuration(0);
    } catch (error) {
      Alert.alert('Error', 'Failed to stop recording');
      console.error('Stop recording error:', error);
    }
  };

  const playRecording = async () => {
    if (!value?.uri) return;

    try {
      if (sound) {
        await sound.unloadAsync();
      }

      const { sound: newSound } = await Audio.Sound.createAsync(
        { uri: value.uri },
        { shouldPlay: true }
      );

      setSound(newSound);
      setIsPlaying(true);

      newSound.setOnPlaybackStatusUpdate((status) => {
        if (status.isLoaded && status.didJustFinish) {
          setIsPlaying(false);
        }
      });
    } catch (error) {
      Alert.alert('Error', 'Failed to play recording');
      console.error('Playback error:', error);
    }
  };

  const pauseRecording = async () => {
    if (sound) {
      await sound.pauseAsync();
      setIsPlaying(false);
    }
  };

  const removeRecording = () => {
    Alert.alert(
      'Remove Recording',
      'Are you sure you want to remove this audio recording?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: () => {
            if (sound) {
              sound.unloadAsync();
            }
            onChange(null);
          },
        },
      ]
    );
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (isRecording) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.card, borderColor: theme.colors.error }]}>
        <View style={styles.recordingIndicator}>
          <View style={[styles.recordingDot, { backgroundColor: theme.colors.error }]} />
          <Text style={[styles.recordingText, { color: theme.colors.text }]}>
            Recording... {formatDuration(recordingDuration)}
          </Text>
        </View>
        
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: theme.colors.error }]}
          onPress={stopRecording}
        >
          <Square size={20} color="white" />
          <Text style={styles.actionButtonText}>Stop</Text>
        </TouchableOpacity>
      </View>
    );
  }

  if (value) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.card, borderColor: theme.colors.border }]}>
        <View style={styles.audioInfo}>
          <Mic size={24} color={theme.colors.primary} />
          <View style={styles.audioDetails}>
            <Text style={[styles.audioTitle, { color: theme.colors.text }]}>
              Audio Recording
            </Text>
            <Text style={[styles.audioDuration, { color: theme.colors.muted }]}>
              Duration: {formatDuration(value.duration)}
            </Text>
          </View>
        </View>

        <View style={styles.audioControls}>
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: theme.colors.primary }]}
            onPress={isPlaying ? pauseRecording : playRecording}
            disabled={disabled}
          >
            {isPlaying ? (
              <Pause size={16} color="white" />
            ) : (
              <Play size={16} color="white" />
            )}
            <Text style={styles.actionButtonText}>
              {isPlaying ? 'Pause' : 'Play'}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: theme.colors.secondary }]}
            onPress={startRecording}
            disabled={disabled}
          >
            <RotateCcw size={16} color="white" />
            <Text style={styles.actionButtonText}>Re-record</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: theme.colors.error }]}
            onPress={removeRecording}
            disabled={disabled}
          >
            <Trash2 size={16} color="white" />
            <Text style={styles.actionButtonText}>Remove</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <TouchableOpacity
      style={[
        styles.captureButton,
        {
          backgroundColor: theme.colors.background,
          borderColor: theme.colors.border,
          opacity: disabled ? 0.5 : 1,
        },
      ]}
      onPress={startRecording}
      disabled={disabled}
    >
      <Mic size={32} color={theme.colors.muted} />
      <Text style={[styles.captureText, { color: theme.colors.muted }]}>
        {placeholder}
      </Text>
      {required && (
        <Text style={[styles.requiredText, { color: theme.colors.error }]}>
          Required
        </Text>
      )}
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 8,
    borderWidth: 1,
    padding: 16,
    gap: 12,
  },
  recordingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  recordingDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  recordingText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
  },
  audioInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  audioDetails: {
    flex: 1,
  },
  audioTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 2,
  },
  audioDuration: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
  },
  audioControls: {
    flexDirection: 'row',
    gap: 8,
    flexWrap: 'wrap',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    gap: 4,
  },
  actionButtonText: {
    color: 'white',
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
  },
  captureButton: {
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderStyle: 'dashed',
    borderRadius: 8,
    paddingVertical: 40,
    gap: 8,
  },
  captureText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
  },
  requiredText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },
});
