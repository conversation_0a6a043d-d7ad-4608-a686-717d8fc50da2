import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router, useLocalSearchParams } from 'expo-router';
import { useTheme } from '@/hooks/useTheme';
import { useProjects } from '@/hooks/useProjects';
import { useForms } from '@/hooks/useForms';
import {
  ArrowLeft,
  Plus,
  Settings,
  FileInput,
  Edit3,
  Copy,
  Trash2,
} from 'lucide-react-native';
import FormCard from '@/components/forms/FormCard';
import EmptyState from '@/components/ui/EmptyState';

export default function ProjectDetailScreen() {
  const { theme } = useTheme();
  const { projectId } = useLocalSearchParams<{ projectId: string }>();
  const { projects } = useProjects();
  const { forms, getFormsByProject, loading } = useForms();
  
  const project = projects.find(p => p.id === projectId);
  const projectForms = projectId ? getFormsByProject(projectId) : [];

  if (!project) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: theme.colors.error }]}>Project not found</Text>
          <TouchableOpacity
            style={[styles.backButton, { backgroundColor: theme.colors.primary }]}
            onPress={() => router.back()}
          >
            <Text style={styles.backButtonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  const handleCreateForm = () => {
    router.push({
      pathname: '/forms/builder',
      params: { projectId: project.id },
    });
  };

  const handleEditForm = (formId: string) => {
    router.push({
      pathname: '/forms/builder',
      params: { formId, projectId: project.id },
    });
  };

  const handleCollectData = (formId: string) => {
    router.push(`/forms/${formId}/collect`);
  };

  const renderFormActions = (formId: string) => (
    <View style={styles.formActions}>
      <TouchableOpacity
        style={[styles.actionButton, { backgroundColor: theme.colors.primary }]}
        onPress={() => handleCollectData(formId)}
      >
        <FileInput size={16} color="white" />
        <Text style={styles.actionButtonText}>Collect</Text>
      </TouchableOpacity>
      
      <TouchableOpacity
        style={[styles.actionButton, { backgroundColor: theme.colors.secondary }]}
        onPress={() => handleEditForm(formId)}
      >
        <Edit3 size={16} color="white" />
        <Text style={styles.actionButtonText}>Edit</Text>
      </TouchableOpacity>
    </View>
  );

  const renderFormCard = ({ item }: { item: any }) => (
    <View style={styles.formCardContainer}>
      <FormCard
        form={item}
        onPress={() => handleCollectData(item.id)}
      />
      {renderFormActions(item.id)}
    </View>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]} edges={['bottom']}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.card, borderBottomColor: theme.colors.border }]}>
        <TouchableOpacity onPress={() => router.back()} style={styles.headerButton}>
          <ArrowLeft size={24} color={theme.colors.text} />
        </TouchableOpacity>
        
        <View style={styles.headerContent}>
          <Text style={[styles.headerTitle, { color: theme.colors.text }]}>{project.name}</Text>
          <Text style={[styles.headerSubtitle, { color: theme.colors.muted }]}>
            {projectForms.length} form{projectForms.length !== 1 ? 's' : ''}
          </Text>
        </View>

        <TouchableOpacity style={styles.headerButton}>
          <Settings size={24} color={theme.colors.text} />
        </TouchableOpacity>
      </View>

      {/* Project Info */}
      <View style={[styles.projectInfo, { backgroundColor: theme.colors.card }]}>
        {project.description && (
          <Text style={[styles.projectDescription, { color: theme.colors.text }]}>
            {project.description}
          </Text>
        )}
        
        <View style={styles.projectMeta}>
          <View style={[styles.statusBadge, { backgroundColor: getStatusColor(project.status) + '20' }]}>
            <Text style={[styles.statusText, { color: getStatusColor(project.status) }]}>
              {project.status.toUpperCase()}
            </Text>
          </View>
          
          <Text style={[styles.metaText, { color: theme.colors.muted }]}>
            Created {new Date(project.createdAt).toLocaleDateString()}
          </Text>
        </View>
      </View>

      {/* Forms Section */}
      <View style={styles.content}>
        <View style={styles.sectionHeader}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Forms</Text>
          <TouchableOpacity
            style={[styles.createButton, { backgroundColor: theme.colors.primary }]}
            onPress={handleCreateForm}
          >
            <Plus size={20} color="white" />
            <Text style={styles.createButtonText}>Create Form</Text>
          </TouchableOpacity>
        </View>

        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={theme.colors.primary} />
            <Text style={[styles.loadingText, { color: theme.colors.text }]}>Loading forms...</Text>
          </View>
        ) : (
          <FlatList
            data={projectForms}
            keyExtractor={(item) => item.id}
            renderItem={renderFormCard}
            contentContainerStyle={styles.listContent}
            showsVerticalScrollIndicator={false}
            ListEmptyComponent={
              <EmptyState
                icon={<FileInput size={64} color={theme.colors.muted} />}
                title="No forms yet"
                description="Create your first form to start collecting data"
                action={
                  <TouchableOpacity
                    style={[styles.emptyActionButton, { backgroundColor: theme.colors.primary }]}
                    onPress={handleCreateForm}
                  >
                    <Plus size={20} color="white" />
                    <Text style={styles.emptyActionButtonText}>Create Form</Text>
                  </TouchableOpacity>
                }
              />
            }
          />
        )}
      </View>
    </SafeAreaView>
  );

  function getStatusColor(status: string) {
    switch (status) {
      case 'active': return theme.colors.success;
      case 'draft': return theme.colors.warning;
      case 'completed': return theme.colors.info;
      case 'archived': return theme.colors.muted;
      default: return theme.colors.text;
    }
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  headerButton: {
    padding: 8,
  },
  headerContent: {
    flex: 1,
    marginHorizontal: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
  },
  headerSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    marginTop: 2,
  },
  projectInfo: {
    padding: 16,
  },
  projectDescription: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    lineHeight: 24,
    marginBottom: 12,
  },
  projectMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  statusText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    letterSpacing: 0.5,
  },
  metaText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
  },
  createButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    gap: 6,
  },
  createButtonText: {
    color: 'white',
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
  },
  listContent: {
    paddingBottom: 20,
  },
  formCardContainer: {
    marginBottom: 16,
  },
  formActions: {
    flexDirection: 'row',
    gap: 8,
    marginTop: 8,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    gap: 4,
  },
  actionButtonText: {
    color: 'white',
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    fontFamily: 'Inter-Medium',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  errorText: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 24,
  },
  backButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  backButtonText: {
    color: 'white',
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
  },
  emptyActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
    marginTop: 16,
  },
  emptyActionButtonText: {
    color: 'white',
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
  },
});
