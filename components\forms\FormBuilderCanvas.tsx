import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
} from 'react-native';
import { useTheme } from '@/hooks/useTheme';
import { FormSchema, FormSection, FormQuestion } from '@/types';
import {
  Plus,
  Settings,
  Trash2,
  GripVertical,
  ChevronDown,
  ChevronRight,
  Edit3,
} from 'lucide-react-native';
import QuestionEditor from './QuestionEditor';

interface FormBuilderCanvasProps {
  formSchema: FormSchema;
  selectedSectionId: string | null;
  selectedQuestionId: string | null;
  onSelectSection: (sectionId: string) => void;
  onSelectQuestion: (questionId: string) => void;
  onUpdateQuestion: (questionId: string, updates: Partial<FormQuestion>) => void;
  onDeleteQuestion: (questionId: string) => void;
  onUpdateSection: (sectionId: string, updates: Partial<FormSection>) => void;
  onAddSection: () => void;
}

export default function FormBuilderCanvas({
  formSchema,
  selectedSectionId,
  selectedQuestionId,
  onSelectSection,
  onSelectQuestion,
  onUpdateQuestion,
  onDeleteQuestion,
  onUpdateSection,
  onAddSection,
}: FormBuilderCanvasProps) {
  const { theme } = useTheme();
  const [expandedSections, setExpandedSections] = useState<Set<string>>(
    new Set(formSchema.sections.map(s => s.id))
  );
  const [editingSectionId, setEditingSectionId] = useState<string | null>(null);

  const toggleSection = (sectionId: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(sectionId)) {
      newExpanded.delete(sectionId);
    } else {
      newExpanded.add(sectionId);
    }
    setExpandedSections(newExpanded);
  };

  const startEditingSection = (sectionId: string) => {
    setEditingSectionId(sectionId);
  };

  const finishEditingSection = () => {
    setEditingSectionId(null);
  };

  const deleteSection = (sectionId: string) => {
    if (formSchema.sections.length <= 1) {
      Alert.alert('Cannot Delete', 'A form must have at least one section.');
      return;
    }

    Alert.alert(
      'Delete Section',
      'Are you sure you want to delete this section and all its questions?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            // This would need to be handled by the parent component
            // For now, we'll just show an alert
            Alert.alert('Feature Coming Soon', 'Section deletion will be implemented soon.');
          },
        },
      ]
    );
  };

  const renderQuestion = (question: FormQuestion, sectionId: string) => {
    const isSelected = selectedQuestionId === question.id;

    return (
      <TouchableOpacity
        key={question.id}
        style={[
          styles.questionItem,
          {
            backgroundColor: isSelected ? theme.colors.primary + '10' : theme.colors.background,
            borderColor: isSelected ? theme.colors.primary : theme.colors.border,
          },
        ]}
        onPress={() => onSelectQuestion(question.id)}
      >
        <View style={styles.questionHeader}>
          <View style={styles.questionInfo}>
            <Text style={[styles.questionLabel, { color: theme.colors.text }]}>
              {question.label}
            </Text>
            {question.description && (
              <Text style={[styles.questionDescription, { color: theme.colors.muted }]}>
                {question.description}
              </Text>
            )}
            <Text style={[styles.questionType, { color: theme.colors.muted }]}>
              {question.type.toUpperCase()} {question.required && '• Required'}
            </Text>
          </View>
          
          <View style={styles.questionActions}>
            <TouchableOpacity
              style={styles.questionActionButton}
              onPress={() => onSelectQuestion(question.id)}
            >
              <Edit3 size={16} color={theme.colors.muted} />
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.questionActionButton}
              onPress={() => onDeleteQuestion(question.id)}
            >
              <Trash2 size={16} color={theme.colors.error} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.questionActionButton}>
              <GripVertical size={16} color={theme.colors.muted} />
            </TouchableOpacity>
          </View>
        </View>

        {isSelected && (
          <View style={[styles.questionEditor, { borderTopColor: theme.colors.border }]}>
            <QuestionEditor
              question={question}
              onUpdate={(updates) => onUpdateQuestion(question.id, updates)}
            />
          </View>
        )}
      </TouchableOpacity>
    );
  };

  const renderSection = (section: FormSection) => {
    const isExpanded = expandedSections.has(section.id);
    const isSelected = selectedSectionId === section.id;
    const isEditing = editingSectionId === section.id;

    return (
      <View
        key={section.id}
        style={[
          styles.sectionContainer,
          {
            backgroundColor: isSelected ? theme.colors.primary + '05' : theme.colors.card,
            borderColor: isSelected ? theme.colors.primary : theme.colors.border,
          },
        ]}
      >
        <TouchableOpacity
          style={styles.sectionHeader}
          onPress={() => {
            onSelectSection(section.id);
            toggleSection(section.id);
          }}
        >
          <TouchableOpacity
            style={styles.sectionToggle}
            onPress={() => toggleSection(section.id)}
          >
            {isExpanded ? (
              <ChevronDown size={20} color={theme.colors.text} />
            ) : (
              <ChevronRight size={20} color={theme.colors.text} />
            )}
          </TouchableOpacity>

          <View style={styles.sectionInfo}>
            {isEditing ? (
              <TextInput
                style={[styles.sectionTitleInput, { color: theme.colors.text, borderColor: theme.colors.border }]}
                value={section.title}
                onChangeText={(text) => onUpdateSection(section.id, { title: text })}
                onBlur={finishEditingSection}
                onSubmitEditing={finishEditingSection}
                autoFocus
              />
            ) : (
              <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
                {section.title}
              </Text>
            )}
            
            {section.description && !isEditing && (
              <Text style={[styles.sectionDescription, { color: theme.colors.muted }]}>
                {section.description}
              </Text>
            )}
            
            <Text style={[styles.sectionMeta, { color: theme.colors.muted }]}>
              {section.questions.length} question{section.questions.length !== 1 ? 's' : ''}
            </Text>
          </View>

          <View style={styles.sectionActions}>
            <TouchableOpacity
              style={styles.sectionActionButton}
              onPress={() => startEditingSection(section.id)}
            >
              <Settings size={16} color={theme.colors.muted} />
            </TouchableOpacity>
            {formSchema.sections.length > 1 && (
              <TouchableOpacity
                style={styles.sectionActionButton}
                onPress={() => deleteSection(section.id)}
              >
                <Trash2 size={16} color={theme.colors.error} />
              </TouchableOpacity>
            )}
          </View>
        </TouchableOpacity>

        {isExpanded && (
          <View style={styles.sectionContent}>
            {section.questions.length === 0 ? (
              <View style={[styles.emptySection, { backgroundColor: theme.colors.background }]}>
                <Text style={[styles.emptySectionText, { color: theme.colors.muted }]}>
                  No questions in this section
                </Text>
                <Text style={[styles.emptySectionSubtext, { color: theme.colors.muted }]}>
                  Drag question types from the palette to add them
                </Text>
              </View>
            ) : (
              section.questions.map(question => renderQuestion(question, section.id))
            )}
          </View>
        )}
      </View>
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <View style={[styles.header, { backgroundColor: theme.colors.card, borderBottomColor: theme.colors.border }]}>
        <Text style={[styles.title, { color: theme.colors.text }]}>Form Builder</Text>
        <TouchableOpacity
          style={[styles.addSectionButton, { backgroundColor: theme.colors.primary }]}
          onPress={onAddSection}
        >
          <Plus size={16} color="white" />
          <Text style={styles.addSectionButtonText}>Add Section</Text>
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {formSchema.sections.map(renderSection)}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
  },
  title: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
  },
  addSectionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    gap: 6,
  },
  addSectionButtonText: {
    color: 'white',
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  sectionContainer: {
    marginBottom: 16,
    borderRadius: 8,
    borderWidth: 1,
    overflow: 'hidden',
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  sectionToggle: {
    marginRight: 12,
  },
  sectionInfo: {
    flex: 1,
  },
  sectionTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 4,
  },
  sectionTitleInput: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    borderWidth: 1,
    borderRadius: 4,
    paddingHorizontal: 8,
    paddingVertical: 4,
    marginBottom: 4,
  },
  sectionDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    marginBottom: 4,
  },
  sectionMeta: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
  },
  sectionActions: {
    flexDirection: 'row',
    gap: 8,
  },
  sectionActionButton: {
    padding: 8,
  },
  sectionContent: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  emptySection: {
    padding: 24,
    borderRadius: 8,
    alignItems: 'center',
  },
  emptySectionText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    marginBottom: 4,
  },
  emptySectionSubtext: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
  },
  questionItem: {
    marginBottom: 12,
    borderRadius: 8,
    borderWidth: 1,
    overflow: 'hidden',
  },
  questionHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: 12,
  },
  questionInfo: {
    flex: 1,
  },
  questionLabel: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 4,
  },
  questionDescription: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    marginBottom: 4,
  },
  questionType: {
    fontSize: 10,
    fontFamily: 'Inter-Medium',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  questionActions: {
    flexDirection: 'row',
    gap: 4,
  },
  questionActionButton: {
    padding: 6,
  },
  questionEditor: {
    borderTopWidth: 1,
    padding: 12,
  },
});
