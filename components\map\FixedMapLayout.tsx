import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Modal,
  TextInput,
  Dimensions,
  Platform,
  Alert,
  SafeAreaView,
} from 'react-native';
import { useTheme } from '@/hooks/useTheme';
import * as Location from 'expo-location';
import {
  MapPin,
  Layers,
  Ruler,
  Search,
  Settings,
  Map as MapIcon,
  Satellite,
  Plus,
  Minus,
  Target,
  Route,
  Pentagon,
  Activity,
  Thermometer,
  Shield,
  Edit3,
  Eye,
  EyeOff,
  Filter,
  BarChart3,
  Zap,
  X,
  Menu,
  CheckCircle,
  XCircle,
  Save,
  Trash2,
} from 'lucide-react-native';

// Platform-specific map component import
const PlatformMap = Platform.OS === 'web' 
  ? require('./Map.web').default 
  : require('./Map.native').default;

interface Region {
  latitude: number;
  longitude: number;
  latitudeDelta: number;
  longitudeDelta: number;
}

interface DrawnFeature {
  id: string;
  type: 'point' | 'line' | 'polygon';
  coordinates: any;
  properties: {
    name: string;
    description?: string;
    color: string;
    created: number;
  };
}

interface FixedMapLayoutProps {
  initialRegion?: Region;
  onLocationSelect?: (location: { latitude: number; longitude: number }) => void;
  geoFeatures?: any[];
  showAnalysisTools?: boolean;
  enableDrawing?: boolean;
  enableMeasurement?: boolean;
  enableGeofencing?: boolean;
  enableRouting?: boolean;
  enableHeatmap?: boolean;
  enableClustering?: boolean;
  offlineMode?: boolean;
  // New props for enhanced functionality
  onFeatureCreated?: (feature: DrawnFeature) => void;
  onFeatureDeleted?: (featureId: string) => void;
  onExportFeatures?: () => void;
  onClearAllFeatures?: () => void;
  featureStats?: {
    total: number;
    points: number;
    lines: number;
    polygons: number;
  };
  maxFeatures?: number;
}

type DrawingMode = 'none' | 'point' | 'line' | 'polygon' | 'circle' | 'rectangle';
type MapLayer = 'standard' | 'satellite' | 'terrain' | 'hybrid';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

export default function FixedMapLayout({
  initialRegion,
  onLocationSelect,
  geoFeatures = [],
  showAnalysisTools = true,
  enableDrawing = true,
  enableMeasurement = true,
  enableGeofencing = true,
  enableRouting = true,
  enableHeatmap = true,
  enableClustering = true,
  offlineMode = false,
  // New props
  onFeatureCreated,
  onFeatureDeleted,
  onExportFeatures,
  onClearAllFeatures,
  featureStats,
  maxFeatures = 100,
}: FixedMapLayoutProps) {
  const { theme } = useTheme();
  
  // State management
  const [region] = useState<Region>(initialRegion || {
    latitude: 37.78825,
    longitude: -122.4324,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  });
  
  const [mapType, setMapType] = useState<MapLayer>('standard');
  const [drawingMode, setDrawingMode] = useState<DrawingMode>('none');
  const [measurementMode, setMeasurementMode] = useState(false);
  const [showHeatmap, setShowHeatmap] = useState(false);
  const [userLocation, setUserLocation] = useState<Location.LocationObject | null>(null);
  const [drawnFeatures, setDrawnFeatures] = useState<DrawnFeature[]>([]);
  const [isDrawing, setIsDrawing] = useState(false);
  const [currentDrawingPoints, setCurrentDrawingPoints] = useState<any[]>([]);
  
  // UI state
  const [showLayersPanel, setShowLayersPanel] = useState(false);
  const [showDrawingTools, setShowDrawingTools] = useState(false);
  const [showAnalysisPanel, setShowAnalysisPanel] = useState(false);
  const [showFeaturesList, setShowFeaturesList] = useState(false);
  const [showSearch, setShowSearch] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [uiExpanded, setUiExpanded] = useState(false);

  useEffect(() => {
    requestLocationPermission();
  }, []);

  const requestLocationPermission = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status === 'granted') {
        const location = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.High,
        });
        setUserLocation(location);
      }
    } catch (error) {
      console.error('Error getting location:', error);
    }
  };

  // Drawing functions
  const startDrawing = (mode: DrawingMode) => {
    setDrawingMode(mode);
    setIsDrawing(true);
    setCurrentDrawingPoints([]);
  };

  const addDrawingPoint = (point: any) => {
    setCurrentDrawingPoints(prev => [...prev, point]);
  };

  const finishDrawing = () => {
    if (currentDrawingPoints.length === 0) return;

    // Check feature limit
    if (drawnFeatures.length >= maxFeatures) {
      Alert.alert(
        'Feature Limit Reached',
        `Maximum of ${maxFeatures} features allowed. Please delete some features before adding new ones.`,
        [{ text: 'OK' }]
      );
      return;
    }

    let feature: DrawnFeature | null = null;

    switch (drawingMode) {
      case 'point':
        if (currentDrawingPoints.length >= 1) {
          feature = {
            id: `point_${Date.now()}`,
            type: 'point',
            coordinates: currentDrawingPoints[0],
            properties: {
              name: `Point ${drawnFeatures.length + 1}`,
              color: theme.colors.primary,
              created: Date.now(),
            },
          };
        }
        break;
        
      case 'line':
        if (currentDrawingPoints.length >= 2) {
          feature = {
            id: `line_${Date.now()}`,
            type: 'line',
            coordinates: currentDrawingPoints,
            properties: {
              name: `Line ${drawnFeatures.length + 1}`,
              color: theme.colors.primary,
              created: Date.now(),
            },
          };
        }
        break;
        
      case 'polygon':
        if (currentDrawingPoints.length >= 3) {
          feature = {
            id: `polygon_${Date.now()}`,
            type: 'polygon',
            coordinates: [...currentDrawingPoints, currentDrawingPoints[0]], // Close polygon
            properties: {
              name: `Polygon ${drawnFeatures.length + 1}`,
              color: theme.colors.primary,
              created: Date.now(),
            },
          };
        }
        break;
    }

    if (feature) {
      setDrawnFeatures(prev => [...prev, feature!]);
      
      // Notify parent component
      if (onFeatureCreated) {
        onFeatureCreated(feature);
      }
    }

    // Reset drawing state but keep the drawing mode active for multiple drawings
    setCurrentDrawingPoints([]);
    setIsDrawing(false);
    
    Alert.alert(
      'Drawing Complete',
      `${drawingMode.charAt(0).toUpperCase() + drawingMode.slice(1)} created successfully!`,
      [
        {
          text: 'Draw Another',
          onPress: () => setIsDrawing(true),
        },
        {
          text: 'Stop Drawing',
          onPress: () => {
            setDrawingMode('none');
            setIsDrawing(false);
          },
        },
      ]
    );
  };

  const cancelDrawing = () => {
    setCurrentDrawingPoints([]);
    setIsDrawing(false);
    setDrawingMode('none');
  };

  const clearAllFeatures = () => {
    if (onClearAllFeatures) {
      onClearAllFeatures();
    } else {
      Alert.alert(
        'Clear All Features',
        'Are you sure you want to clear all drawn features?',
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Clear All',
            style: 'destructive',
            onPress: () => setDrawnFeatures([]),
          },
        ]
      );
    }
  };

  const generateHeatmapData = () => {
    Alert.alert('Heatmap', 'Heatmap data generation demo');
    setShowHeatmap(true);
  };

  const renderCompactToolbar = () => (
    <View style={[styles.compactToolbar, { backgroundColor: theme.colors.card }]}>
      <TouchableOpacity
        style={styles.expandButton}
        onPress={() => setUiExpanded(!uiExpanded)}
      >
        <Menu size={20} color={theme.colors.text} />
      </TouchableOpacity>
      
      {uiExpanded && (
        <View style={styles.expandedTools}>
          <TouchableOpacity
            style={[styles.toolButton, { backgroundColor: showLayersPanel ? theme.colors.primary + '20' : 'transparent' }]}
            onPress={() => {
              setShowLayersPanel(true);
              setUiExpanded(false);
            }}
          >
            <Layers size={18} color={theme.colors.text} />
          </TouchableOpacity>
          
          {enableDrawing && (
            <TouchableOpacity
              style={[styles.toolButton, { backgroundColor: showDrawingTools ? theme.colors.primary + '20' : 'transparent' }]}
              onPress={() => {
                setShowDrawingTools(!showDrawingTools);
                setUiExpanded(false);
              }}
            >
              <Edit3 size={18} color={theme.colors.text} />
            </TouchableOpacity>
          )}
          
          {showAnalysisTools && (
            <TouchableOpacity
              style={[styles.toolButton, { backgroundColor: showAnalysisPanel ? theme.colors.primary + '20' : 'transparent' }]}
              onPress={() => {
                setShowAnalysisPanel(true);
                setUiExpanded(false);
              }}
            >
              <BarChart3 size={18} color={theme.colors.text} />
            </TouchableOpacity>
          )}
          
          <TouchableOpacity
            style={[styles.toolButton, { backgroundColor: showFeaturesList ? theme.colors.primary + '20' : 'transparent' }]}
            onPress={() => {
              setShowFeaturesList(true);
              setUiExpanded(false);
            }}
          >
            <Filter size={18} color={theme.colors.text} />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.toolButton, { backgroundColor: showSearch ? theme.colors.primary + '20' : 'transparent' }]}
            onPress={() => {
              setShowSearch(!showSearch);
              setUiExpanded(false);
            }}
          >
            <Search size={18} color={theme.colors.text} />
          </TouchableOpacity>
        </View>
      )}
    </View>
  );

  const renderMapLayers = () => (
    <Modal
      visible={showLayersPanel}
      transparent
      animationType="slide"
      onRequestClose={() => setShowLayersPanel(false)}
    >
      <TouchableOpacity 
        style={styles.modalOverlay}
        activeOpacity={1}
        onPress={() => setShowLayersPanel(false)}
      >
        <View style={[styles.layersPanel, { backgroundColor: theme.colors.card }]}>
          <View style={styles.panelHeader}>
            <Text style={[styles.panelTitle, { color: theme.colors.text }]}>
              Map Layers
            </Text>
            <TouchableOpacity onPress={() => setShowLayersPanel(false)}>
              <X size={24} color={theme.colors.text} />
            </TouchableOpacity>
          </View>
          
          <TouchableOpacity
            style={[styles.layerOption, mapType === 'standard' && styles.layerOptionActive]}
            onPress={() => {
              setMapType('standard');
              setShowLayersPanel(false);
            }}
          >
            <MapIcon size={24} color={mapType === 'standard' ? theme.colors.primary : theme.colors.text} />
            <Text style={[styles.layerText, { color: theme.colors.text }]}>Standard</Text>
            {mapType === 'standard' && <CheckCircle size={20} color={theme.colors.primary} />}
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.layerOption, mapType === 'satellite' && styles.layerOptionActive]}
            onPress={() => {
              setMapType('satellite');
              setShowLayersPanel(false);
            }}
          >
            <Satellite size={24} color={mapType === 'satellite' ? theme.colors.primary : theme.colors.text} />
            <Text style={[styles.layerText, { color: theme.colors.text }]}>Satellite</Text>
            {mapType === 'satellite' && <CheckCircle size={20} color={theme.colors.primary} />}
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.layerOption, mapType === 'terrain' && styles.layerOptionActive]}
            onPress={() => {
              setMapType('terrain');
              setShowLayersPanel(false);
            }}
          >
            <MapIcon size={24} color={mapType === 'terrain' ? theme.colors.primary : theme.colors.text} />
            <Text style={[styles.layerText, { color: theme.colors.text }]}>Terrain</Text>
            {mapType === 'terrain' && <CheckCircle size={20} color={theme.colors.primary} />}
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.layerOption, mapType === 'hybrid' && styles.layerOptionActive]}
            onPress={() => {
              setMapType('hybrid');
              setShowLayersPanel(false);
            }}
          >
            <Layers size={24} color={mapType === 'hybrid' ? theme.colors.primary : theme.colors.text} />
            <Text style={[styles.layerText, { color: theme.colors.text }]}>Hybrid</Text>
            {mapType === 'hybrid' && <CheckCircle size={20} color={theme.colors.primary} />}
          </TouchableOpacity>
          
          <View style={styles.panelDivider} />
          
          <Text style={[styles.panelSubtitle, { color: theme.colors.text }]}>
            Data Layers
          </Text>
          
          <TouchableOpacity
            style={[styles.layerOption, showHeatmap && styles.layerOptionActive]}
            onPress={() => {
              setShowHeatmap(!showHeatmap);
            }}
          >
            <Thermometer size={24} color={showHeatmap ? theme.colors.primary : theme.colors.text} />
            <Text style={[styles.layerText, { color: theme.colors.text }]}>Heatmap</Text>
            {showHeatmap && <CheckCircle size={20} color={theme.colors.primary} />}
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    </Modal>
  );

  const renderDrawingTools = () => (
    <View style={[styles.drawingToolbar, { backgroundColor: theme.colors.card }]}>
      <View style={styles.drawingHeader}>
        <View style={styles.drawingTitleContainer}>
          <Text style={[styles.drawingTitle, { color: theme.colors.text }]}>
            Drawing Tools
          </Text>
          {featureStats && (
            <Text style={[styles.featureStatsText, { color: theme.colors.muted }]}>
              {featureStats.total} features ({featureStats.points}P, {featureStats.lines}L, {featureStats.polygons}A)
            </Text>
          )}
        </View>
        <View style={styles.drawingHeaderButtons}>
          {onExportFeatures && drawnFeatures.length > 0 && (
            <TouchableOpacity
              style={[styles.headerActionButton, { backgroundColor: theme.colors.success }]}
              onPress={onExportFeatures}
            >
              <Save size={16} color="white" />
            </TouchableOpacity>
          )}
          {drawnFeatures.length > 0 && (
            <TouchableOpacity
              style={[styles.headerActionButton, { backgroundColor: theme.colors.error }]}
              onPress={clearAllFeatures}
            >
              <Trash2 size={16} color="white" />
            </TouchableOpacity>
          )}
          <TouchableOpacity onPress={() => setShowDrawingTools(false)}>
            <X size={20} color={theme.colors.text} />
          </TouchableOpacity>
        </View>
      </View>
      
      {/* Drawing Mode Selection */}
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.drawingModeScroll}>
        <TouchableOpacity
          style={[
            styles.drawingTool,
            drawingMode === 'point' && { backgroundColor: theme.colors.primary }
          ]}
          onPress={() => startDrawing('point')}
        >
          <MapPin size={18} color={drawingMode === 'point' ? 'white' : theme.colors.text} />
          <Text style={[styles.toolText, { color: drawingMode === 'point' ? 'white' : theme.colors.text }]}>
            Point
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.drawingTool,
            drawingMode === 'line' && { backgroundColor: theme.colors.primary }
          ]}
          onPress={() => startDrawing('line')}
        >
          <Route size={18} color={drawingMode === 'line' ? 'white' : theme.colors.text} />
          <Text style={[styles.toolText, { color: drawingMode === 'line' ? 'white' : theme.colors.text }]}>
            Line
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.drawingTool,
            drawingMode === 'polygon' && { backgroundColor: theme.colors.primary }
          ]}
          onPress={() => startDrawing('polygon')}
        >
          <Pentagon size={18} color={drawingMode === 'polygon' ? 'white' : theme.colors.text} />
          <Text style={[styles.toolText, { color: drawingMode === 'polygon' ? 'white' : theme.colors.text }]}>
            Polygon
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.drawingTool,
            measurementMode && { backgroundColor: theme.colors.secondary }
          ]}
          onPress={() => setMeasurementMode(!measurementMode)}
        >
          <Ruler size={18} color={measurementMode ? 'white' : theme.colors.text} />
          <Text style={[styles.toolText, { color: measurementMode ? 'white' : theme.colors.text }]}>
            Measure
          </Text>
        </TouchableOpacity>
      </ScrollView>
      
      {/* Drawing State Indicator */}
      {drawingMode !== 'none' && (
        <View style={[styles.drawingStatus, { backgroundColor: theme.colors.primary + '10' }]}>
          <Text style={[styles.drawingStatusText, { color: theme.colors.primary }]}>
            {isDrawing ? `Drawing ${drawingMode}... (${currentDrawingPoints.length} points)` : `${drawingMode} mode active - tap map to draw`}
          </Text>
          <View style={styles.drawingActions}>
            {currentDrawingPoints.length > 0 && (
              <TouchableOpacity
                style={[styles.actionButton, { backgroundColor: theme.colors.success }]}
                onPress={finishDrawing}
              >
                <CheckCircle size={16} color="white" />
                <Text style={styles.actionButtonText}>Finish</Text>
              </TouchableOpacity>
            )}
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: theme.colors.error }]}
              onPress={cancelDrawing}
            >
              <XCircle size={16} color="white" />
              <Text style={styles.actionButtonText}>Cancel</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}
    </View>
  );

  const renderRightControls = () => (
    <View style={styles.rightControlsContainer}>
      {/* Zoom Controls */}
      <View style={[styles.zoomControls, { backgroundColor: theme.colors.card }]}>
        <TouchableOpacity
          style={styles.zoomButton}
          onPress={() => Alert.alert('Zoom In', 'Zoom in functionality')}
        >
          <Plus size={18} color={theme.colors.text} />
        </TouchableOpacity>
        
        <View style={[styles.zoomDivider, { backgroundColor: theme.colors.border }]} />
        
        <TouchableOpacity
          style={styles.zoomButton}
          onPress={() => Alert.alert('Zoom Out', 'Zoom out functionality')}
        >
          <Minus size={18} color={theme.colors.text} />
        </TouchableOpacity>
      </View>
      
      {/* Location Button */}
      <TouchableOpacity
        style={[styles.locationButton, { backgroundColor: theme.colors.card }]}
        onPress={() => {
          if (userLocation) {
            Alert.alert('Location', `Current: ${userLocation.coords.latitude.toFixed(4)}, ${userLocation.coords.longitude.toFixed(4)}`);
          } else {
            requestLocationPermission();
          }
        }}
      >
        <Target size={20} color={theme.colors.primary} />
      </TouchableOpacity>
    </View>
  );

  const renderFeaturesList = () => (
    <Modal
      visible={showFeaturesList}
      transparent
      animationType="slide"
      onRequestClose={() => setShowFeaturesList(false)}
    >
      <TouchableOpacity 
        style={styles.modalOverlay}
        activeOpacity={1}
        onPress={() => setShowFeaturesList(false)}
      >
        <View style={[styles.featuresPanel, { backgroundColor: theme.colors.card }]}>
          <View style={styles.panelHeader}>
            <Text style={[styles.panelTitle, { color: theme.colors.text }]}>
              Features ({drawnFeatures.length})
            </Text>
            <TouchableOpacity onPress={() => setShowFeaturesList(false)}>
              <X size={24} color={theme.colors.text} />
            </TouchableOpacity>
          </View>
          
          <ScrollView>
            {drawnFeatures.length === 0 ? (
              <View style={styles.emptyFeatures}>
                <Text style={[styles.emptyText, { color: theme.colors.muted }]}>
                  No features created yet
                </Text>
                <Text style={[styles.emptySubtext, { color: theme.colors.muted }]}>
                  Use drawing tools to create features
                </Text>
              </View>
            ) : (
              drawnFeatures.map((feature, index) => (
                <View key={feature.id} style={[styles.featureItem, { backgroundColor: theme.colors.background }]}>
                  <View style={[styles.featureIcon, { backgroundColor: feature.properties.color + '20' }]}>
                    {feature.type === 'point' && <MapPin size={20} color={feature.properties.color} />}
                    {feature.type === 'line' && <Route size={20} color={feature.properties.color} />}
                    {feature.type === 'polygon' && <Pentagon size={20} color={feature.properties.color} />}
                  </View>
                  
                  <View style={styles.featureInfo}>
                    <Text style={[styles.featureName, { color: theme.colors.text }]}>
                      {feature.properties.name}
                    </Text>
                    <Text style={[styles.featureDetails, { color: theme.colors.muted }]}>
                      Type: {feature.type} • Created: {new Date(feature.properties.created).toLocaleTimeString()}
                    </Text>
                  </View>
                  
                  <TouchableOpacity
                    style={[styles.deleteFeatureButton, { backgroundColor: theme.colors.error }]}
                    onPress={() => {
                      Alert.alert(
                        'Delete Feature',
                        `Delete ${feature.properties.name}?`,
                        [
                          { text: 'Cancel', style: 'cancel' },
                          {
                            text: 'Delete',
                            style: 'destructive',
                            onPress: () => {
                              if (onFeatureDeleted) {
                                onFeatureDeleted(feature.id);
                              } else {
                                setDrawnFeatures(prev => prev.filter(f => f.id !== feature.id));
                              }
                            },
                          },
                        ]
                      );
                    }}
                  >
                    <Trash2 size={16} color="white" />
                  </TouchableOpacity>
                </View>
              ))
            )}
          </ScrollView>
        </View>
      </TouchableOpacity>
    </Modal>
  );

  return (
    <View style={styles.container}>
      {/* Platform-specific Map Component */}
      <PlatformMap
        initialRegion={region}
        mapType={mapType}
        drawingMode={drawingMode}
        measurementMode={measurementMode}
        showHeatmap={showHeatmap}
        userLocation={userLocation}
        geoFeatures={[...geoFeatures, ...drawnFeatures]}
        onLocationSelect={(location: any) => {
          if (drawingMode !== 'none' && isDrawing) {
            addDrawingPoint(location);
            if (drawingMode === 'point') {
              finishDrawing();
            }
          } else if (onLocationSelect) {
            onLocationSelect(location);
          }
        }}
      />
      
      {/* Compact Main Toolbar */}
      {renderCompactToolbar()}
      
      {/* Right Side Controls */}
      {renderRightControls()}
      
      {/* Search Bar - Only show when search is active */}
      {showSearch && (
        <View style={[styles.searchBar, { backgroundColor: theme.colors.card }]}>
          <Search size={18} color={theme.colors.muted} />
          <TextInput
            style={[styles.searchInput, { color: theme.colors.text }]}
            placeholder="Search location..."
            placeholderTextColor={theme.colors.placeholder}
            value={searchQuery}
            onChangeText={setSearchQuery}
            onSubmitEditing={() => {
              Alert.alert('Search', `Searching for: ${searchQuery}`);
              setShowSearch(false);
            }}
          />
          <TouchableOpacity onPress={() => setShowSearch(false)}>
            <X size={18} color={theme.colors.muted} />
          </TouchableOpacity>
        </View>
      )}
      
      {/* Drawing Tools - Slide up from bottom */}
      {showDrawingTools && renderDrawingTools()}
      
      {/* Offline Status - Only show when offline */}
      {offlineMode && (
        <View style={[styles.offlineStatus, { backgroundColor: theme.colors.info + '20' }]}>
          <Text style={[styles.offlineText, { color: theme.colors.info }]}>
            📱 Offline Mode
          </Text>
        </View>
      )}
      
      {/* Modals */}
      {renderMapLayers()}
      {renderFeaturesList()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  compactToolbar: {
    position: 'absolute',
    top: 60,
    left: 16,
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 25,
    paddingHorizontal: 8,
    paddingVertical: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
    zIndex: 1000,
    maxWidth: SCREEN_WIDTH - 32,
  },
  expandButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  expandedTools: {
    flexDirection: 'row',
    marginLeft: 8,
    gap: 4,
  },
  toolButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  rightControlsContainer: {
    position: 'absolute',
    right: 16,
    top: '50%',
    transform: [{ translateY: -60 }],
    alignItems: 'center',
    gap: 16,
    zIndex: 998,
  },
  zoomControls: {
    borderRadius: 25,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  zoomButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  zoomDivider: {
    height: 1,
    marginHorizontal: 8,
  },
  locationButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  searchBar: {
    position: 'absolute',
    top: 130,
    left: 16,
    right: 16,
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 25,
    paddingHorizontal: 16,
    paddingVertical: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
    gap: 12,
    zIndex: 999,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
  },
  drawingToolbar: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 16,
    paddingBottom: 34,
    paddingHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
    zIndex: 996,
  },
  drawingHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  drawingTitleContainer: {
    flex: 1,
  },
  drawingTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
  },
  featureStatsText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    marginTop: 2,
  },
  drawingHeaderButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  headerActionButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  drawingModeScroll: {
    marginBottom: 12,
  },
  drawingTool: {
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    marginHorizontal: 4,
    minWidth: 80,
    gap: 4,
  },
  toolText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },
  drawingStatus: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    borderRadius: 12,
    marginTop: 8,
  },
  drawingStatusText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    flex: 1,
  },
  drawingActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
    gap: 4,
  },
  actionButtonText: {
    color: 'white',
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
  },
  offlineStatus: {
    position: 'absolute',
    top: 130,
    right: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
    zIndex: 995,
  },
  offlineText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  layersPanel: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 20,
    paddingHorizontal: 16,
    paddingBottom: 32,
    maxHeight: '70%',
  },
  panelHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  panelTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
  },
  panelSubtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    marginVertical: 12,
  },
  layerOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 8,
    gap: 12,
  },
  layerOptionActive: {
    backgroundColor: 'rgba(0, 122, 255, 0.1)',
  },
  layerText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    flex: 1,
  },
  panelDivider: {
    height: 1,
    backgroundColor: '#E0E0E0',
    marginVertical: 16,
  },
  featuresPanel: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 20,
    paddingHorizontal: 16,
    paddingBottom: 32,
    maxHeight: '70%',
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 8,
    gap: 12,
  },
  featureIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  featureInfo: {
    flex: 1,
  },
  featureName: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    marginBottom: 2,
  },
  featureDetails: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
  },
  deleteFeatureButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyFeatures: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  emptyText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    marginBottom: 4,
  },
  emptySubtext: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
  },
});
