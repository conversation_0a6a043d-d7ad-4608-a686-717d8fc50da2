// Type definitions for enhanced map components

export interface Coordinate {
  latitude: number;
  longitude: number;
}

export interface Region extends Coordinate {
  latitudeDelta: number;
  longitudeDelta: number;
}

export interface FeatureGeometry {
  type: 'Point' | 'LineString' | 'Polygon' | 'MultiPoint' | 'MultiLineString' | 'MultiPolygon';
  coordinates: number[] | number[][] | number[][][];
}

export interface FeatureProperties {
  name: string;
  description?: string;
  color: string;
  created: number;
  updated?: number;
  category?: string;
  tags?: string[];
  measurements?: MeasurementData;
  metadata?: Record<string, any>;
}

export interface MeasurementData {
  area?: number;
  perimeter?: number;
  length?: number;
  radius?: number;
  bearing?: number;
  volume?: number;
  formattedArea?: string;
  formattedPerimeter?: string;
  formattedLength?: string;
}

export interface MapFeature {
  id: string;
  type: 'point' | 'line' | 'polygon' | 'circle' | 'rectangle';
  coordinates: any;
  geometry?: FeatureGeometry;
  properties: FeatureProperties;
  selected?: boolean;
  visible?: boolean;
  locked?: boolean;
}

export interface CustomLayer {
  id: string;
  name: string;
  type: 'geojson' | 'shapefile' | 'kml' | 'gpx' | 'wms' | 'wmts' | 'tile';
  data?: any;
  url?: string;
  visible: boolean;
  opacity?: number;
  style?: LayerStyle;
  attribution?: string;
  bounds?: [number, number, number, number];
  metadata?: Record<string, any>;
}

export interface LayerStyle {
  color?: string;
  fillColor?: string;
  fillOpacity?: number;
  weight?: number;
  opacity?: number;
  dashArray?: string;
  lineCap?: 'butt' | 'round' | 'square';
  lineJoin?: 'arcs' | 'bevel' | 'miter' | 'miter-clip' | 'round';
  radius?: number;
  icon?: IconStyle;
}

export interface IconStyle {
  iconUrl?: string;
  iconSize?: [number, number];
  iconAnchor?: [number, number];
  popupAnchor?: [number, number];
  shadowUrl?: string;
  shadowSize?: [number, number];
  shadowAnchor?: [number, number];
}

export interface MeasurementResult {
  id: string;
  type: 'distance' | 'area' | 'bearing' | 'perimeter' | 'radius';
  value: number;
  formattedValue: string;
  units: string;
  coordinates: Coordinate[];
  timestamp: string;
  precision?: number;
}

export interface AnalysisResult {
  id: string;
  type: 'buffer' | 'intersection' | 'union' | 'difference' | 'centroid' | 'convexHull' | 'nearestPoint' | 'cluster' | 'voronoi';
  geometry?: FeatureGeometry;
  properties?: Record<string, any>;
  inputFeatures: string[];
  parameters: Record<string, any>;
  executionTime: number;
  timestamp: string;
  metadata?: Record<string, any>;
}

export interface SpatialQuery {
  type: 'within' | 'intersects' | 'contains' | 'overlaps' | 'touches' | 'crosses' | 'disjoint';
  geometry: FeatureGeometry;
  buffer?: number;
  units?: 'meters' | 'kilometers' | 'miles' | 'feet';
}

export interface MapViewState {
  center: Coordinate;
  zoom: number;
  bearing?: number;
  pitch?: number;
  bounds?: [number, number, number, number];
}

export interface MapSettings {
  basemap: 'standard' | 'satellite' | 'terrain' | 'hybrid' | 'dark' | 'light';
  showScale?: boolean;
  showCompass?: boolean;
  showAttribution?: boolean;
  enableInteraction?: boolean;
  enableDrawing?: boolean;
  enableMeasurement?: boolean;
  enableAnalysis?: boolean;
  maxZoom?: number;
  minZoom?: number;
  projection?: string;
}

export interface DrawingOptions {
  mode: 'none' | 'point' | 'line' | 'polygon' | 'rectangle' | 'circle' | 'freehand';
  strokeColor?: string;
  fillColor?: string;
  strokeWidth?: number;
  strokeOpacity?: number;
  fillOpacity?: number;
  dashPattern?: number[];
  snapToGrid?: boolean;
  snapDistance?: number;
}

export interface MeasurementOptions {
  units: 'metric' | 'imperial' | 'nautical';
  precision: number;
  showLabels: boolean;
  showTooltips: boolean;
  continuous: boolean;
  snapToFeatures: boolean;
}

export interface AnalysisOptions {
  selectedOnly?: boolean;
  includeProperties?: boolean;
  outputProjection?: string;
  precision?: number;
  tolerance?: number;
  algorithm?: string;
  parameters?: Record<string, any>;
}

export interface ExportOptions {
  format: 'geojson' | 'shapefile' | 'kml' | 'gpx' | 'csv' | 'excel' | 'pdf';
  filename?: string;
  projection?: string;
  includeProperties?: boolean;
  includeMetadata?: boolean;
  compress?: boolean;
  quality?: number;
}

export interface ImportOptions {
  projection?: string;
  encoding?: string;
  separator?: string;
  headers?: string[];
  coordinateFields?: {
    latitude: string;
    longitude: string;
  };
  validate?: boolean;
  transform?: (feature: any) => any;
}

export interface MapEventHandlers {
  onMapLoad?: () => void;
  onMapError?: (error: Error) => void;
  onViewChange?: (viewState: MapViewState) => void;
  onFeatureClick?: (feature: MapFeature, event: any) => void;
  onFeatureDoubleClick?: (feature: MapFeature, event: any) => void;
  onFeatureHover?: (feature: MapFeature | null, event: any) => void;
  onFeatureSelect?: (features: MapFeature[]) => void;
  onFeatureCreate?: (feature: MapFeature) => void;
  onFeatureUpdate?: (feature: MapFeature, changes: Partial<MapFeature>) => void;
  onFeatureDelete?: (featureId: string) => void;
  onLayerAdd?: (layer: CustomLayer) => void;
  onLayerRemove?: (layerId: string) => void;
  onLayerToggle?: (layerId: string, visible: boolean) => void;
  onMeasurementStart?: () => void;
  onMeasurementEnd?: (result: MeasurementResult) => void;
  onAnalysisComplete?: (result: AnalysisResult) => void;
  onExportStart?: (options: ExportOptions) => void;
  onExportComplete?: (data: any, options: ExportOptions) => void;
  onImportStart?: (file: File, options: ImportOptions) => void;
  onImportComplete?: (features: MapFeature[], options: ImportOptions) => void;
}

export interface MapContextMenu {
  items: MenuItemConfig[];
  position: { x: number; y: number };
  feature?: MapFeature;
  coordinate?: Coordinate;
}

export interface MenuItemConfig {
  id: string;
  label: string;
  icon?: string;
  disabled?: boolean;
  separator?: boolean;
  action?: () => void;
  submenu?: MenuItemConfig[];
}

export interface ToolbarConfig {
  position: 'top' | 'bottom' | 'left' | 'right';
  orientation: 'horizontal' | 'vertical';
  tools: ToolConfig[];
  collapsible?: boolean;
  theme?: 'light' | 'dark' | 'auto';
}

export interface ToolConfig {
  id: string;
  type: 'button' | 'toggle' | 'dropdown' | 'separator' | 'group';
  icon?: string;
  label?: string;
  tooltip?: string;
  disabled?: boolean;
  active?: boolean;
  action?: () => void;
  options?: any[];
  tools?: ToolConfig[];
}

export interface PanelConfig {
  id: string;
  title: string;
  icon?: string;
  position: 'left' | 'right' | 'top' | 'bottom';
  width?: number;
  height?: number;
  collapsible?: boolean;
  resizable?: boolean;
  closable?: boolean;
  component: React.ComponentType<any>;
  props?: Record<string, any>;
}

export interface MapTheme {
  name: string;
  colors: {
    primary: string;
    secondary: string;
    success: string;
    warning: string;
    error: string;
    info: string;
    background: string;
    surface: string;
    text: string;
    textSecondary: string;
    border: string;
    disabled: string;
  };
  fonts: {
    primary: string;
    secondary: string;
    mono: string;
  };
  spacing: {
    xs: number;
    sm: number;
    md: number;
    lg: number;
    xl: number;
  };
  borderRadius: {
    sm: number;
    md: number;
    lg: number;
  };
  shadows: {
    sm: string;
    md: string;
    lg: string;
  };
}

export interface MapConfiguration {
  theme: MapTheme;
  settings: MapSettings;
  toolbar: ToolbarConfig;
  panels: PanelConfig[];
  contextMenu: MenuItemConfig[];
  keyboard: KeyboardShortcut[];
  plugins: PluginConfig[];
}

export interface KeyboardShortcut {
  key: string;
  modifiers?: ('ctrl' | 'shift' | 'alt' | 'meta')[];
  action: string | (() => void);
  description: string;
  context?: 'global' | 'map' | 'drawing' | 'measurement';
}

export interface PluginConfig {
  id: string;
  name: string;
  version: string;
  enabled: boolean;
  settings?: Record<string, any>;
  dependencies?: string[];
}

export interface Performance {
  renderTime: number;
  updateTime: number;
  featureCount: number;
  layerCount: number;
  memoryUsage?: number;
  frameRate?: number;
}

export interface MapState {
  view: MapViewState;
  features: Map<string, MapFeature>;
  layers: Map<string, CustomLayer>;
  selection: Set<string>;
  measurements: MeasurementResult[];
  analyses: AnalysisResult[];
  drawing: DrawingOptions;
  measuring: MeasurementOptions;
  settings: MapSettings;
  loading: boolean;
  error: Error | null;
  performance: Performance;
}

export interface MapActions {
  // View actions
  setView: (viewState: Partial<MapViewState>) => void;
  fitBounds: (bounds: [number, number, number, number], padding?: number) => void;
  
  // Feature actions
  addFeature: (feature: MapFeature) => void;
  updateFeature: (featureId: string, changes: Partial<MapFeature>) => void;
  deleteFeature: (featureId: string) => void;
  selectFeatures: (featureIds: string[]) => void;
  clearSelection: () => void;
  
  // Layer actions
  addLayer: (layer: CustomLayer) => void;
  removeLayer: (layerId: string) => void;
  toggleLayer: (layerId: string) => void;
  setLayerOpacity: (layerId: string, opacity: number) => void;
  
  // Drawing actions
  startDrawing: (mode: DrawingOptions['mode']) => void;
  stopDrawing: () => void;
  setDrawingOptions: (options: Partial<DrawingOptions>) => void;
  
  // Measurement actions
  startMeasuring: () => void;
  stopMeasuring: () => void;
  clearMeasurements: () => void;
  
  // Analysis actions
  runAnalysis: (type: string, options: AnalysisOptions) => Promise<AnalysisResult>;
  clearAnalyses: () => void;
  
  // Import/Export actions
  exportData: (options: ExportOptions) => Promise<void>;
  importData: (file: File, options: ImportOptions) => Promise<void>;
  
  // Settings actions
  updateSettings: (settings: Partial<MapSettings>) => void;
  resetSettings: () => void;
}

// Hook return types
export interface UseMapReturn {
  state: MapState;
  actions: MapActions;
  ref: React.RefObject<any>;
}

export interface UseDrawingReturn {
  isDrawing: boolean;
  currentTool: DrawingOptions['mode'];
  startDrawing: (mode: DrawingOptions['mode']) => void;
  stopDrawing: () => void;
  options: DrawingOptions;
  setOptions: (options: Partial<DrawingOptions>) => void;
}

export interface UseMeasurementReturn {
  isMeasuring: boolean;
  results: MeasurementResult[];
  start: () => void;
  stop: () => void;
  clear: () => void;
  options: MeasurementOptions;
  setOptions: (options: Partial<MeasurementOptions>) => void;
}

export interface UseLayersReturn {
  layers: CustomLayer[];
  addLayer: (layer: CustomLayer) => void;
  removeLayer: (layerId: string) => void;
  toggleLayer: (layerId: string) => void;
  setLayerStyle: (layerId: string, style: Partial<LayerStyle>) => void;
}

// Component prop types
export interface EnhancedMapProps {
  initialView?: Partial<MapViewState>;
  features?: MapFeature[];
  layers?: CustomLayer[];
  settings?: Partial<MapSettings>;
  theme?: Partial<MapTheme>;
  toolbar?: Partial<ToolbarConfig>;
  panels?: PanelConfig[];
  onLoad?: () => void;
  onError?: (error: Error) => void;
  children?: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
}

export interface MapToolbarProps {
  config: ToolbarConfig;
  onToolAction: (toolId: string, action?: any) => void;
  theme?: MapTheme;
  disabled?: boolean;
}

export interface LayersPanelProps {
  layers: CustomLayer[];
  onLayerToggle: (layerId: string) => void;
  onLayerRemove: (layerId: string) => void;
  onLayerAdd: () => void;
  onLayerStyleChange: (layerId: string, style: Partial<LayerStyle>) => void;
  theme?: MapTheme;
}

export interface FeaturesPanelProps {
  features: MapFeature[];
  selection: Set<string>;
  onFeatureSelect: (featureIds: string[]) => void;
  onFeatureEdit: (feature: MapFeature) => void;
  onFeatureDelete: (featureId: string) => void;
  onFeatureCreate: () => void;
  theme?: MapTheme;
}

export interface MeasurementPanelProps {
  results: MeasurementResult[];
  options: MeasurementOptions;
  onOptionsChange: (options: Partial<MeasurementOptions>) => void;
  onClear: () => void;
  onExport: () => void;
  theme?: MapTheme;
}

export interface AnalysisPanelProps {
  results: AnalysisResult[];
  features: MapFeature[];
  onRunAnalysis: (type: string, options: AnalysisOptions) => void;
  onClearResults: () => void;
  onExportResults: () => void;
  theme?: MapTheme;
}

// Utility types
export type FeatureType = MapFeature['type'];
export type LayerType = CustomLayer['type'];
export type AnalysisType = AnalysisResult['type'];
export type MeasurementType = MeasurementResult['type'];
export type DrawingMode = DrawingOptions['mode'];
export type BasemapType = MapSettings['basemap'];

// Error types
export interface MapError extends Error {
  code: string;
  context?: Record<string, any>;
  recoverable?: boolean;
}

export interface ValidationError extends MapError {
  field: string;
  value: any;
  constraints: string[];
}

export interface NetworkError extends MapError {
  status?: number;
  url?: string;
  retryable?: boolean;
}

// Event types
export interface MapEvent {
  type: string;
  target: any;
  originalEvent?: Event;
  coordinate?: Coordinate;
  feature?: MapFeature;
  layer?: CustomLayer;
}

export interface FeatureEvent extends MapEvent {
  feature: MapFeature;
  property?: string;
  oldValue?: any;
  newValue?: any;
}

export interface LayerEvent extends MapEvent {
  layer: CustomLayer;
}

export interface ViewEvent extends MapEvent {
  viewState: MapViewState;
  oldViewState: MapViewState;
}

// Plugin types
export interface MapPlugin {
  id: string;
  name: string;
  version: string;
  install: (map: any, config: PluginConfig) => void;
  uninstall: (map: any) => void;
  isCompatible: (version: string) => boolean;
}

export interface PluginContext {
  map: any;
  config: PluginConfig;
  api: {
    addTool: (tool: ToolConfig) => void;
    removeTool: (toolId: string) => void;
    addPanel: (panel: PanelConfig) => void;
    removePanel: (panelId: string) => void;
    registerCommand: (command: string, handler: Function) => void;
    unregisterCommand: (command: string) => void;
  };
}

// Constants
export const SUPPORTED_FORMATS = ['geojson', 'shapefile', 'kml', 'gpx', 'csv'] as const;
export const DRAWING_MODES = ['none', 'point', 'line', 'polygon', 'rectangle', 'circle', 'freehand'] as const;
export const ANALYSIS_TYPES = ['buffer', 'intersection', 'union', 'difference', 'centroid', 'convexHull', 'nearestPoint', 'cluster', 'voronoi'] as const;
export const MEASUREMENT_TYPES = ['distance', 'area', 'bearing', 'perimeter', 'radius'] as const;
export const BASEMAP_TYPES = ['standard', 'satellite', 'terrain', 'hybrid', 'dark', 'light'] as const;

export type SupportedFormat = typeof SUPPORTED_FORMATS[number];
export type SupportedDrawingMode = typeof DRAWING_MODES[number];
export type SupportedAnalysisType = typeof ANALYSIS_TYPES[number];
export type SupportedMeasurementType = typeof MEASUREMENT_TYPES[number];
export type SupportedBasemapType = typeof BASEMAP_TYPES[number];
