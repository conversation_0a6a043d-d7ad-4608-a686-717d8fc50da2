/**
 * Expo Updates Patch
 * 
 * This patch prevents Expo Updates from causing runtime errors
 * when updates are disabled or unavailable.
 */

// Only apply patch on mobile platforms
if (typeof global !== 'undefined' && global.expo) {
  const originalUpdates = global.expo.Updates;
  
  if (originalUpdates) {
    // Wrap all update methods to prevent errors
    const safeWrapper = (methodName, originalMethod) => {
      return async (...args) => {
        try {
          // Check if updates are disabled
          const isDisabled = process.env.EXPO_PUBLIC_DISABLE_UPDATES === 'true' ||
                           process.env.EXPO_PUBLIC_DEV_MODE === 'true';
          
          if (isDisabled) {
            console.log(`Updates disabled - skipping ${methodName}`);
            return { isAvailable: false, isDownloaded: false };
          }
          
          return await originalMethod.apply(this, args);
        } catch (error) {
          console.warn(`Update method ${methodName} failed:`, error);
          return { isAvailable: false, isDownloaded: false };
        }
      };
    };
    
    // Patch common update methods
    if (originalUpdates.checkForUpdateAsync) {
      originalUpdates.checkForUpdateAsync = safeWrapper(
        'checkForUpdateAsync',
        originalUpdates.checkForUpdateAsync
      );
    }
    
    if (originalUpdates.fetchUpdateAsync) {
      originalUpdates.fetchUpdateAsync = safeWrapper(
        'fetchUpdateAsync',
        originalUpdates.fetchUpdateAsync
      );
    }
    
    if (originalUpdates.reloadAsync) {
      originalUpdates.reloadAsync = safeWrapper(
        'reloadAsync',
        originalUpdates.reloadAsync
      );
    }
    
    console.log('Expo Updates patch applied successfully');
  }
}

// Export empty object for module compatibility
module.exports = {};
