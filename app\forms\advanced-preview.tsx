import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useLocalSearchParams, router } from 'expo-router';
import { useTheme } from '@/hooks/useTheme';
import { FormSchema, Submission } from '@/types';
import FormRenderer from '@/components/forms/FormRenderer';

export default function AdvancedPreviewScreen() {
  const { theme } = useTheme();
  const { formData } = useLocalSearchParams();
  const [submissionData, setSubmissionData] = useState<Record<string, any>>({});

  // Parse form data from params
  let parsedForm: any = null;
  try {
    parsedForm = JSON.parse(formData as string);
  } catch (error) {
    console.error('Error parsing form data:', error);
    Alert.alert('Error', 'Invalid form data', [
      { text: 'OK', onPress: () => router.back() }
    ]);
    return null;
  }

  // Convert to our form structure
  const form = {
    id: 'preview_form',
    name: parsedForm.name || 'Preview Form',
    description: parsedForm.description || '',
    schema: parsedForm.schema || { pages: [], logicRules: [] },
  };

  const handleSaveDraft = async (data: Record<string, any>, isDraft: boolean) => {
    setSubmissionData(data);
    
    if (Platform.OS === 'web') {
      // For web, we can store in localStorage for demo purposes
      const drafts = JSON.parse(localStorage.getItem('fieldsync_drafts') || '[]');
      const draft = {
        id: `draft_${Date.now()}`,
        formId: form.id,
        formName: form.name,
        data,
        savedAt: Date.now(),
        isDraft: true,
      };
      drafts.push(draft);
      localStorage.setItem('fieldsync_drafts', JSON.stringify(drafts));
    }

    console.log('Draft saved:', { data, isDraft });
  };

  const handleSubmit = async (submission: Partial<Submission>) => {
    console.log('Form submitted:', submission);
    
    // In a real app, this would submit to your backend
    if (Platform.OS === 'web') {
      const submissions = JSON.parse(localStorage.getItem('fieldsync_submissions') || '[]');
      const fullSubmission = {
        id: `submission_${Date.now()}`,
        formId: form.id,
        formName: form.name,
        projectId: 'preview_project',
        userId: 'preview_user',
        status: 'completed',
        startedAt: Date.now(),
        completedAt: Date.now(),
        data: submission.data || {},
        location: submission.location,
        media: [],
        ...submission,
      };
      submissions.push(fullSubmission);
      localStorage.setItem('fieldsync_submissions', JSON.stringify(submissions));
    }

    Alert.alert(
      'Form Submitted Successfully!',
      'This is a preview submission. In the actual app, this would be sent to your data collection system.',
      [
        { text: 'View Data', onPress: () => showSubmissionData(submission) },
        { text: 'Close Preview', onPress: () => router.back() },
      ]
    );
  };

  const showSubmissionData = (submission: Partial<Submission>) => {
    const dataString = JSON.stringify(submission.data, null, 2);
    Alert.alert(
      'Submitted Data',
      `Form: ${form.name}\n\nData:\n${dataString.substring(0, 500)}${dataString.length > 500 ? '...' : ''}`,
      [{ text: 'OK' }]
    );
  };

  if (!form.schema.pages || form.schema.pages.length === 0) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <View style={styles.errorContainer}>
          <Text style={[styles.errorTitle, { color: theme.colors.text }]}>
            No Form Content
          </Text>
          <Text style={[styles.errorMessage, { color: theme.colors.muted }]}>
            This form doesn't have any pages or sections to preview.
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <FormRenderer
        form={form}
        initialData={submissionData}
        onSave={handleSaveDraft}
        onSubmit={handleSubmit}
        showValidation={true}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  errorTitle: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    marginBottom: 8,
  },
  errorMessage: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
    lineHeight: 24,
  },
});
