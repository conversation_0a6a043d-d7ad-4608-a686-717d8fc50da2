#!/bin/bash

echo "🚀 Starting FieldSync Pro Development Environment"
echo "================================================"

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: package.json not found. Please run this from the project root directory."
    exit 1
fi

# Check if project name matches
if ! grep -q "fieldsync-pro" package.json; then
    echo "⚠️  Warning: This doesn't appear to be the FieldSync Pro project."
    read -p "Continue anyway? (y/N) " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

echo "✅ Project validation passed"

# Clean up any existing processes
echo "🧹 Cleaning up existing processes..."
pkill -f "expo start" 2>/dev/null || true
pkill -f "metro" 2>/dev/null || true

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
fi

# Clear Metro cache and start fresh
echo "🔄 Starting with cleared cache..."
npx expo start --clear

echo "🎉 Development server should be starting!"
echo ""
echo "📱 Scan the QR code with Expo Go app (mobile)"
echo "🌐 Press 'w' to open in web browser"
echo "💡 Press 'r' to reload"
echo "🔧 Press 'd' to open developer menu"
