/**
 * Advanced Offline Map Caching System for FieldSyncPro
 * 
 * Provides intelligent map tile caching, offline data synchronization,
 * and progressive enhancement for offline-first mapping experiences.
 * 
 * @version 2.0.0
 * <AUTHOR> Development Team
 */

import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  ProgressBar,
  Dimensions,
  Platform,
} from 'react-native';
import {
  Download,
  Wifi,
  WifiOff,
  HardDrive,
  Trash2,
  MapPin,
  Clock,
  CheckCircle,
  AlertCircle,
  Pause,
  Play,
  X,
  Settings,
  Map,
  Database,
  Zap,
} from 'lucide-react-native';
import { useTheme } from '@/hooks/useTheme';
import * as FileSystem from 'expo-file-system';
import * as NetInfo from '@react-native-community/netinfo';

interface CacheRegion {
  id: string;
  name: string;
  bounds: {
    north: number;
    south: number;
    east: number;
    west: number;
  };
  zoomLevels: {
    min: number;
    max: number;
  };
  tileCount: number;
  estimatedSize: number; // in bytes
  downloadedSize: number;
  progress: number;
  status: 'pending' | 'downloading' | 'completed' | 'paused' | 'error';
  lastUpdated?: string;
  expiryDate?: string;
  priority: 'high' | 'medium' | 'low';
}

interface CacheStats {
  totalSize: number;
  usedSize: number;
  tileCount: number;
  regionCount: number;
  lastCleanup: string;
  hitRate: number;
  networkSavings: number;
}

interface OfflineMapManagerProps {
  visible: boolean;
  onClose: () => void;
  mapRegion?: {
    latitude: number;
    longitude: number;
    latitudeDelta: number;
    longitudeDelta: number;
  };
  onRegionCached?: (regionId: string) => void;
  onRegionDeleted?: (regionId: string) => void;
}

const { width: SCREEN_WIDTH } = Dimensions.get('window');
const CACHE_DIRECTORY = `${FileSystem.documentDirectory}mapCache/`;
const MAX_CACHE_SIZE = 500 * 1024 * 1024; // 500MB default

export default function OfflineMapManager({
  visible,
  onClose,
  mapRegion,
  onRegionCached,
  onRegionDeleted,
}: OfflineMapManagerProps) {
  const { theme } = useTheme();
  const [selectedTab, setSelectedTab] = useState<'cache' | 'downloads' | 'settings'>('cache');
  const [isConnected, setIsConnected] = useState(true);
  const [cacheRegions, setCacheRegions] = useState<CacheRegion[]>([]);
  const [cacheStats, setCacheStats] = useState<CacheStats>({
    totalSize: MAX_CACHE_SIZE,
    usedSize: 0,
    tileCount: 0,
    regionCount: 0,
    lastCleanup: new Date().toISOString(),
    hitRate: 95,
    networkSavings: 45.2,
  });

  const [isDownloading, setIsDownloading] = useState(false);
  const [downloadQueue, setDownloadQueue] = useState<string[]>([]);
  const [autoCleanup, setAutoCleanup] = useState(true);
  const [maxCacheAge, setMaxCacheAge] = useState(30); // days
  const [downloadOnlyOnWifi, setDownloadOnlyOnWifi] = useState(true);

  const downloadInterval = useRef<NodeJS.Timeout>();

  // Monitor network connectivity
  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      setIsConnected(state.isConnected ?? false);
    });

    return () => unsubscribe();
  }, []);

  // Initialize cache directory
  useEffect(() => {
    initializeCacheDirectory();
    loadCacheRegions();
    updateCacheStats();
  }, []);

  // Download queue processor
  useEffect(() => {
    if (isDownloading && downloadQueue.length > 0) {
      downloadInterval.current = setInterval(() => {
        processDownloadQueue();
      }, 1000);
    } else {
      if (downloadInterval.current) {
        clearInterval(downloadInterval.current);
      }
    }

    return () => {
      if (downloadInterval.current) {
        clearInterval(downloadInterval.current);
      }
    };
  }, [isDownloading, downloadQueue]);

  /**
   * Initialize cache directory structure
   */
  const initializeCacheDirectory = useCallback(async () => {
    try {
      const dirInfo = await FileSystem.getInfoAsync(CACHE_DIRECTORY);
      if (!dirInfo.exists) {
        await FileSystem.makeDirectoryAsync(CACHE_DIRECTORY, { intermediates: true });
      }
    } catch (error) {
      console.error('Failed to initialize cache directory:', error);
    }
  }, []);

  /**
   * Load existing cache regions from storage
   */
  const loadCacheRegions = useCallback(async () => {
    try {
      // Load cache metadata from storage
      const metadataPath = `${CACHE_DIRECTORY}metadata.json`;
      const metadataInfo = await FileSystem.getInfoAsync(metadataPath);
      
      if (metadataInfo.exists) {
        const metadata = await FileSystem.readAsStringAsync(metadataPath);
        const regions = JSON.parse(metadata);
        setCacheRegions(regions);
      }
    } catch (error) {
      console.error('Failed to load cache regions:', error);
    }
  }, []);

  /**
   * Update cache statistics
   */
  const updateCacheStats = useCallback(async () => {
    try {
      const dirInfo = await FileSystem.getInfoAsync(CACHE_DIRECTORY);
      if (dirInfo.exists && dirInfo.isDirectory) {
        const files = await FileSystem.readDirectoryAsync(CACHE_DIRECTORY);
        let totalSize = 0;
        let tileCount = 0;

        for (const file of files) {
          const filePath = `${CACHE_DIRECTORY}${file}`;
          const fileInfo = await FileSystem.getInfoAsync(filePath);
          if (fileInfo.exists && !fileInfo.isDirectory) {
            totalSize += fileInfo.size || 0;
            if (file.endsWith('.png') || file.endsWith('.jpg')) {
              tileCount++;
            }
          }
        }

        setCacheStats(prev => ({
          ...prev,
          usedSize: totalSize,
          tileCount,
          regionCount: cacheRegions.length,
        }));
      }
    } catch (error) {
      console.error('Failed to update cache stats:', error);
    }
  }, [cacheRegions]);

  /**
   * Create a new cache region
   */
  const createCacheRegion = useCallback(async (regionData: Partial<CacheRegion>) => {
    const newRegion: CacheRegion = {
      id: `region_${Date.now()}`,
      name: regionData.name || 'New Region',
      bounds: regionData.bounds || {
        north: mapRegion ? mapRegion.latitude + mapRegion.latitudeDelta / 2 : 0,
        south: mapRegion ? mapRegion.latitude - mapRegion.latitudeDelta / 2 : 0,
        east: mapRegion ? mapRegion.longitude + mapRegion.longitudeDelta / 2 : 0,
        west: mapRegion ? mapRegion.longitude - mapRegion.longitudeDelta / 2 : 0,
      },
      zoomLevels: regionData.zoomLevels || { min: 1, max: 18 },
      tileCount: 0,
      estimatedSize: 0,
      downloadedSize: 0,
      progress: 0,
      status: 'pending',
      priority: regionData.priority || 'medium',
    };

    // Calculate estimated tile count and size
    const { tileCount, estimatedSize } = calculateRegionSize(newRegion);
    newRegion.tileCount = tileCount;
    newRegion.estimatedSize = estimatedSize;

    setCacheRegions(prev => [...prev, newRegion]);
    await saveCacheMetadata([...cacheRegions, newRegion]);

    return newRegion.id;
  }, [mapRegion, cacheRegions]);

  /**
   * Start downloading a cache region
   */
  const startRegionDownload = useCallback(async (regionId: string) => {
    setCacheRegions(prev => prev.map(region =>
      region.id === regionId
        ? { ...region, status: 'downloading' as const, progress: 0 }
        : region
    ));

    setDownloadQueue(prev => [...prev, regionId]);
    
    if (!isDownloading) {
      setIsDownloading(true);
    }
  }, [isDownloading]);

  /**
   * Pause/resume downloading
   */
  const toggleDownloading = useCallback(() => {
    setIsDownloading(!isDownloading);
    
    if (isDownloading) {
      // Pause all downloading regions
      setCacheRegions(prev => prev.map(region =>
        region.status === 'downloading'
          ? { ...region, status: 'paused' as const }
          : region
      ));
    } else {
      // Resume paused regions
      setCacheRegions(prev => prev.map(region =>
        region.status === 'paused'
          ? { ...region, status: 'downloading' as const }
          : region
      ));
    }
  }, [isDownloading]);

  /**
   * Process download queue
   */
  const processDownloadQueue = useCallback(async () => {
    if (downloadQueue.length === 0) {
      setIsDownloading(false);
      return;
    }

    const regionId = downloadQueue[0];
    const region = cacheRegions.find(r => r.id === regionId);
    
    if (!region || region.status !== 'downloading') {
      setDownloadQueue(prev => prev.slice(1));
      return;
    }

    // Check network conditions
    if (downloadOnlyOnWifi && !isConnected) {
      // Wait for connection
      return;
    }

    try {
      // Simulate tile download (in real app, this would fetch actual tiles)
      const progress = Math.min(region.progress + Math.random() * 5, 100);
      const downloadedSize = (progress / 100) * region.estimatedSize;

      setCacheRegions(prev => prev.map(r =>
        r.id === regionId
          ? { 
              ...r, 
              progress, 
              downloadedSize,
              status: progress >= 100 ? 'completed' : 'downloading',
              lastUpdated: progress >= 100 ? new Date().toISOString() : r.lastUpdated,
            }
          : r
      ));

      if (progress >= 100) {
        setDownloadQueue(prev => prev.slice(1));
        onRegionCached?.(regionId);
        
        // Set expiry date
        const expiryDate = new Date();
        expiryDate.setDate(expiryDate.getDate() + maxCacheAge);
        
        setCacheRegions(prev => prev.map(r =>
          r.id === regionId
            ? { ...r, expiryDate: expiryDate.toISOString() }
            : r
        ));
      }
    } catch (error) {
      console.error('Download error:', error);
      setCacheRegions(prev => prev.map(r =>
        r.id === regionId
          ? { ...r, status: 'error' as const }
          : r
      ));
      setDownloadQueue(prev => prev.slice(1));
    }
  }, [downloadQueue, cacheRegions, downloadOnlyOnWifi, isConnected, maxCacheAge, onRegionCached]);

  /**
   * Delete a cache region
   */
  const deleteRegion = useCallback(async (regionId: string) => {
    Alert.alert(
      'Delete Cache Region',
      'Are you sure you want to delete this cached region?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              // Remove files (simplified - in real app would remove actual tile files)
              setCacheRegions(prev => prev.filter(region => region.id !== regionId));
              await saveCacheMetadata(cacheRegions.filter(region => region.id !== regionId));
              
              onRegionDeleted?.(regionId);
              updateCacheStats();
            } catch (error) {
              console.error('Failed to delete region:', error);
            }
          },
        },
      ]
    );
  }, [cacheRegions, onRegionDeleted]);

  /**
   * Clear all cache
   */
  const clearAllCache = useCallback(async () => {
    Alert.alert(
      'Clear All Cache',
      'This will delete all cached map data. Are you sure?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear All',
          style: 'destructive',
          onPress: async () => {
            try {
              setCacheRegions([]);
              await saveCacheMetadata([]);
              updateCacheStats();
              
              Alert.alert('Success', 'All cache data has been cleared.');
            } catch (error) {
              console.error('Failed to clear cache:', error);
              Alert.alert('Error', 'Failed to clear cache data.');
            }
          },
        },
      ]
    );
  }, []);

  /**
   * Auto cleanup expired regions
   */
  const performAutoCleanup = useCallback(async () => {
    if (!autoCleanup) return;

    const now = new Date();
    const expiredRegions = cacheRegions.filter(region => 
      region.expiryDate && new Date(region.expiryDate) < now
    );

    if (expiredRegions.length > 0) {
      for (const region of expiredRegions) {
        await deleteRegion(region.id);
      }

      Alert.alert(
        'Auto Cleanup',
        `Removed ${expiredRegions.length} expired cache regions.`
      );
    }
  }, [autoCleanup, cacheRegions, deleteRegion]);

  /**
   * Save cache metadata
   */
  const saveCacheMetadata = useCallback(async (regions: CacheRegion[]) => {
    try {
      const metadataPath = `${CACHE_DIRECTORY}metadata.json`;
      await FileSystem.writeAsStringAsync(metadataPath, JSON.stringify(regions, null, 2));
    } catch (error) {
      console.error('Failed to save cache metadata:', error);
    }
  }, []);

  /**
   * Calculate region size estimation
   */
  const calculateRegionSize = useCallback((region: CacheRegion) => {
    const { bounds, zoomLevels } = region;
    const latDiff = bounds.north - bounds.south;
    const lngDiff = bounds.east - bounds.west;
    
    let totalTiles = 0;
    for (let zoom = zoomLevels.min; zoom <= zoomLevels.max; zoom++) {
      const tilesPerLat = Math.ceil(latDiff * Math.pow(2, zoom) / 360);
      const tilesPerLng = Math.ceil(lngDiff * Math.pow(2, zoom) / 360);
      totalTiles += tilesPerLat * tilesPerLng;
    }

    const avgTileSize = 20 * 1024; // 20KB average tile size
    const estimatedSize = totalTiles * avgTileSize;

    return { tileCount: totalTiles, estimatedSize };
  }, []);

  if (!visible) return null;

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.card, borderBottomColor: theme.colors.border }]}>
        <View style={styles.headerLeft}>
          <View style={styles.connectionStatus}>
            {isConnected ? (
              <Wifi size={20} color={theme.colors.success} />
            ) : (
              <WifiOff size={20} color={theme.colors.error} />
            )}
          </View>
          <Text style={[styles.title, { color: theme.colors.text }]}>Offline Maps</Text>
        </View>
        
        <View style={styles.headerRight}>
          <TouchableOpacity
            style={[styles.headerButton, { backgroundColor: theme.colors.primary }]}
            onPress={() => createCacheRegion({ name: 'Current View' })}
          >
            <Download size={16} color="white" />
            <Text style={styles.headerButtonText}>Cache Area</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <X size={20} color={theme.colors.text} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Stats Bar */}
      <View style={[styles.statsBar, { backgroundColor: theme.colors.card }]}>
        <View style={styles.statItem}>
          <HardDrive size={16} color={theme.colors.primary} />
          <Text style={[styles.statLabel, { color: theme.colors.text }]}>
            {(cacheStats.usedSize / (1024 * 1024)).toFixed(1)} MB
          </Text>
          <Text style={[styles.statSubtext, { color: theme.colors.muted }]}>
            /{(cacheStats.totalSize / (1024 * 1024)).toFixed(0)} MB
          </Text>
        </View>
        
        <View style={styles.statItem}>
          <Map size={16} color={theme.colors.primary} />
          <Text style={[styles.statLabel, { color: theme.colors.text }]}>
            {cacheStats.regionCount}
          </Text>
          <Text style={[styles.statSubtext, { color: theme.colors.muted }]}>regions</Text>
        </View>
        
        <View style={styles.statItem}>
          <Database size={16} color={theme.colors.primary} />
          <Text style={[styles.statLabel, { color: theme.colors.text }]}>
            {cacheStats.tileCount}
          </Text>
          <Text style={[styles.statSubtext, { color: theme.colors.muted }]}>tiles</Text>
        </View>
        
        <View style={styles.statItem}>
          <Zap size={16} color={theme.colors.success} />
          <Text style={[styles.statLabel, { color: theme.colors.text }]}>
            {cacheStats.hitRate}%
          </Text>
          <Text style={[styles.statSubtext, { color: theme.colors.muted }]}>hit rate</Text>
        </View>
      </View>

      {/* Tab Navigation */}
      <View style={[styles.tabContainer, { backgroundColor: theme.colors.card }]}>
        {['cache', 'downloads', 'settings'].map((tab) => (
          <TouchableOpacity
            key={tab}
            style={[
              styles.tab,
              selectedTab === tab && styles.activeTab,
              { backgroundColor: selectedTab === tab ? theme.colors.primary : 'transparent' }
            ]}
            onPress={() => setSelectedTab(tab as any)}
          >
            <Text style={[
              styles.tabText,
              { color: selectedTab === tab ? 'white' : theme.colors.text }
            ]}>
              {tab.charAt(0).toUpperCase() + tab.slice(1)}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Content */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {selectedTab === 'cache' && renderCacheTab()}
        {selectedTab === 'downloads' && renderDownloadsTab()}
        {selectedTab === 'settings' && renderSettingsTab()}
      </ScrollView>

      {/* Download Controls */}
      {downloadQueue.length > 0 && (
        <View style={[styles.downloadControls, { backgroundColor: theme.colors.card, borderTopColor: theme.colors.border }]}>
          <TouchableOpacity
            style={[styles.downloadControlButton, { backgroundColor: isDownloading ? theme.colors.error : theme.colors.success }]}
            onPress={toggleDownloading}
          >
            {isDownloading ? <Pause size={16} color="white" /> : <Play size={16} color="white" />}
            <Text style={styles.downloadControlText}>
              {isDownloading ? 'Pause' : 'Resume'} ({downloadQueue.length})
            </Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );

  function renderCacheTab() {
    return (
      <View style={styles.tabContent}>
        {cacheRegions.length === 0 ? (
          <View style={styles.emptyState}>
            <Download size={48} color={theme.colors.muted} />
            <Text style={[styles.emptyStateText, { color: theme.colors.text }]}>
              No cached regions
            </Text>
            <Text style={[styles.emptyStateSubtext, { color: theme.colors.muted }]}>
              Cache map areas for offline use
            </Text>
          </View>
        ) : (
          cacheRegions.map((region) => (
            <CacheRegionCard
              key={region.id}
              region={region}
              theme={theme}
              onDownload={() => startRegionDownload(region.id)}
              onDelete={() => deleteRegion(region.id)}
            />
          ))
        )}
      </View>
    );
  }

  function renderDownloadsTab() {
    const activeDownloads = cacheRegions.filter(r => r.status === 'downloading' || r.status === 'paused');
    
    return (
      <View style={styles.tabContent}>
        <View style={styles.downloadHeader}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Active Downloads ({activeDownloads.length})
          </Text>
          <View style={styles.downloadStats}>
            <Text style={[styles.downloadStatsText, { color: theme.colors.muted }]}>
              Queue: {downloadQueue.length} • Network: {isConnected ? 'Connected' : 'Offline'}
            </Text>
          </View>
        </View>

        {activeDownloads.length === 0 ? (
          <View style={styles.emptyState}>
            <CheckCircle size={48} color={theme.colors.success} />
            <Text style={[styles.emptyStateText, { color: theme.colors.text }]}>
              No active downloads
            </Text>
          </View>
        ) : (
          activeDownloads.map((region) => (
            <DownloadProgressCard
              key={region.id}
              region={region}
              theme={theme}
            />
          ))
        )}
      </View>
    );
  }

  function renderSettingsTab() {
    return (
      <View style={styles.tabContent}>
        <SettingSection title="Download Settings" icon={Download}>
          <SettingRow
            label="Download only on WiFi"
            value={downloadOnlyOnWifi}
            onToggle={setDownloadOnlyOnWifi}
            theme={theme}
          />
          <SettingRow
            label="Auto cleanup expired cache"
            value={autoCleanup}
            onToggle={setAutoCleanup}
            theme={theme}
          />
        </SettingSection>

        <SettingSection title="Cache Management" icon={HardDrive}>
          <View style={styles.settingItem}>
            <Text style={[styles.settingLabel, { color: theme.colors.text }]}>
              Max cache age: {maxCacheAge} days
            </Text>
            {/* Slider component would go here */}
          </View>
          
          <TouchableOpacity
            style={[styles.settingButton, { backgroundColor: theme.colors.warning }]}
            onPress={performAutoCleanup}
          >
            <Text style={styles.settingButtonText}>Run Cleanup Now</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.settingButton, { backgroundColor: theme.colors.error }]}
            onPress={clearAllCache}
          >
            <Text style={styles.settingButtonText}>Clear All Cache</Text>
          </TouchableOpacity>
        </SettingSection>

        <SettingSection title="Storage Usage" icon={Database}>
          <View style={styles.storageBar}>
            <View 
              style={[
                styles.storageUsed, 
                { 
                  width: `${(cacheStats.usedSize / cacheStats.totalSize) * 100}%`,
                  backgroundColor: theme.colors.primary 
                }
              ]} 
            />
          </View>
          <Text style={[styles.storageText, { color: theme.colors.muted }]}>
            {(cacheStats.usedSize / (1024 * 1024)).toFixed(1)} MB of {(cacheStats.totalSize / (1024 * 1024)).toFixed(0)} MB used
          </Text>
        </SettingSection>
      </View>
    );
  }

  function CacheRegionCard({ region, theme, onDownload, onDelete }: any) {
    const statusColor = region.status === 'completed' ? theme.colors.success :
                       region.status === 'downloading' ? theme.colors.primary :
                       region.status === 'error' ? theme.colors.error : theme.colors.muted;
    
    return (
      <View style={[styles.regionCard, { backgroundColor: theme.colors.card }]}>
        <View style={styles.regionHeader}>
          <Text style={[styles.regionName, { color: theme.colors.text }]}>{region.name}</Text>
          <View style={[styles.statusBadge, { backgroundColor: statusColor }]}>
            <Text style={styles.statusText}>{region.status.toUpperCase()}</Text>
          </View>
        </View>
        
        <View style={styles.regionInfo}>
          <View style={styles.regionStat}>
            <MapPin size={12} color={theme.colors.muted} />
            <Text style={[styles.regionStatText, { color: theme.colors.muted }]}>
              Zoom {region.zoomLevels.min}-{region.zoomLevels.max}
            </Text>
          </View>
          
          <View style={styles.regionStat}>
            <HardDrive size={12} color={theme.colors.muted} />
            <Text style={[styles.regionStatText, { color: theme.colors.muted }]}>
              {(region.estimatedSize / (1024 * 1024)).toFixed(1)} MB
            </Text>
          </View>
          
          {region.lastUpdated && (
            <View style={styles.regionStat}>
              <Clock size={12} color={theme.colors.muted} />
              <Text style={[styles.regionStatText, { color: theme.colors.muted }]}>
                {new Date(region.lastUpdated).toLocaleDateString()}
              </Text>
            </View>
          )}
        </View>

        {region.status === 'downloading' && (
          <View style={styles.progressContainer}>
            <View style={[styles.progressBar, { backgroundColor: theme.colors.border }]}>
              <View 
                style={[
                  styles.progressFill, 
                  { 
                    width: `${region.progress}%`,
                    backgroundColor: theme.colors.primary 
                  }
                ]} 
              />
            </View>
            <Text style={[styles.progressText, { color: theme.colors.text }]}>
              {region.progress.toFixed(1)}%
            </Text>
          </View>
        )}

        <View style={styles.regionActions}>
          {region.status === 'pending' && (
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: theme.colors.primary }]}
              onPress={onDownload}
            >
              <Download size={14} color="white" />
              <Text style={styles.actionButtonText}>Download</Text>
            </TouchableOpacity>
          )}
          
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: theme.colors.error }]}
            onPress={onDelete}
          >
            <Trash2 size={14} color="white" />
            <Text style={styles.actionButtonText}>Delete</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  function DownloadProgressCard({ region, theme }: any) {
    return (
      <View style={[styles.downloadCard, { backgroundColor: theme.colors.card }]}>
        <View style={styles.downloadCardHeader}>
          <Text style={[styles.downloadCardTitle, { color: theme.colors.text }]}>{region.name}</Text>
          <Text style={[styles.downloadCardProgress, { color: theme.colors.primary }]}>
            {region.progress.toFixed(1)}%
          </Text>
        </View>
        
        <View style={styles.progressContainer}>
          <View style={[styles.progressBar, { backgroundColor: theme.colors.border }]}>
            <View 
              style={[
                styles.progressFill, 
                { 
                  width: `${region.progress}%`,
                  backgroundColor: theme.colors.primary 
                }
              ]} 
            />
          </View>
        </View>
        
        <View style={styles.downloadCardStats}>
          <Text style={[styles.downloadCardStat, { color: theme.colors.muted }]}>
            {(region.downloadedSize / (1024 * 1024)).toFixed(1)} MB / {(region.estimatedSize / (1024 * 1024)).toFixed(1)} MB
          </Text>
          <Text style={[styles.downloadCardStat, { color: theme.colors.muted }]}>
            {region.tileCount} tiles
          </Text>
        </View>
      </View>
    );
  }

  function SettingSection({ title, icon: Icon, children }: any) {
    return (
      <View style={[styles.settingSection, { backgroundColor: theme.colors.card }]}>
        <View style={styles.settingSectionHeader}>
          <Icon size={20} color={theme.colors.primary} />
          <Text style={[styles.settingSectionTitle, { color: theme.colors.text }]}>{title}</Text>
        </View>
        {children}
      </View>
    );
  }

  function SettingRow({ label, value, onToggle, theme }: any) {
    return (
      <View style={styles.settingRow}>
        <Text style={[styles.settingLabel, { color: theme.colors.text }]}>{label}</Text>
        <TouchableOpacity
          style={[
            styles.toggle,
            { backgroundColor: value ? theme.colors.primary : theme.colors.muted }
          ]}
          onPress={() => onToggle(!value)}
        >
          <View style={[
            styles.toggleKnob,
            { 
              transform: [{ translateX: value ? 20 : 0 }],
              backgroundColor: 'white'
            }
          ]} />
        </TouchableOpacity>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  connectionStatus: {
    marginRight: 12,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  headerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    gap: 4,
  },
  headerButtonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '500',
  },
  closeButton: {
    padding: 8,
  },
  statsBar: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  statItem: {
    alignItems: 'center',
    gap: 2,
  },
  statLabel: {
    fontSize: 14,
    fontWeight: '600',
    marginTop: 4,
  },
  statSubtext: {
    fontSize: 10,
  },
  tabContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  tab: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 16,
    marginHorizontal: 2,
  },
  activeTab: {
    // Styles applied via backgroundColor prop
  },
  tabText: {
    fontSize: 12,
    fontWeight: '500',
  },
  content: {
    flex: 1,
  },
  tabContent: {
    padding: 16,
  },
  emptyState: {
    alignItems: 'center',
    padding: 40,
  },
  emptyStateText: {
    fontSize: 16,
    fontWeight: '500',
    marginTop: 12,
  },
  emptyStateSubtext: {
    fontSize: 14,
    marginTop: 4,
  },
  regionCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  regionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  regionName: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: 'white',
    fontSize: 10,
    fontWeight: '600',
  },
  regionInfo: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginBottom: 12,
  },
  regionStat: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  regionStatText: {
    fontSize: 12,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 12,
  },
  progressBar: {
    flex: 1,
    height: 4,
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
  progressText: {
    fontSize: 12,
    fontWeight: '500',
    minWidth: 40,
  },
  regionActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    gap: 4,
  },
  actionButtonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '500',
  },
  downloadHeader: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  downloadStats: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  downloadStatsText: {
    fontSize: 12,
  },
  downloadCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  downloadCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  downloadCardTitle: {
    fontSize: 14,
    fontWeight: '600',
    flex: 1,
  },
  downloadCardProgress: {
    fontSize: 14,
    fontWeight: '600',
  },
  downloadCardStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  downloadCardStat: {
    fontSize: 11,
  },
  settingSection: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  settingSectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  settingSectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
  settingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  settingLabel: {
    fontSize: 14,
    flex: 1,
  },
  toggle: {
    width: 44,
    height: 24,
    borderRadius: 12,
    padding: 2,
    justifyContent: 'center',
  },
  toggleKnob: {
    width: 20,
    height: 20,
    borderRadius: 10,
  },
  settingItem: {
    paddingVertical: 8,
  },
  settingButton: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 8,
  },
  settingButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
  },
  storageBar: {
    height: 8,
    backgroundColor: '#e0e0e0',
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: 8,
  },
  storageUsed: {
    height: '100%',
    borderRadius: 4,
  },
  storageText: {
    fontSize: 12,
    textAlign: 'center',
  },
  downloadControls: {
    flexDirection: 'row',
    padding: 16,
    borderTopWidth: 1,
  },
  downloadControlButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    gap: 6,
  },
  downloadControlText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
  },
});
