/**
 * Fallback Navigation Component
 * Provides basic navigation when react-native-screens fails
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '@/hooks/useTheme';
import { Home, Map, FileText, Settings, AlertTriangle } from 'lucide-react-native';

interface FallbackNavigationProps {
  onNavigate?: (route: string) => void;
  currentRoute?: string;
  error?: Error;
}

export const FallbackNavigation: React.FC<FallbackNavigationProps> = ({
  onNavigate,
  currentRoute = 'home',
  error,
}) => {
  const { theme } = useTheme();

  const navigationItems = [
    {
      id: 'home',
      label: 'Home',
      icon: Home,
      route: '/(tabs)/home',
    },
    {
      id: 'map',
      label: 'Map',
      icon: Map,
      route: '/(tabs)/map',
    },
    {
      id: 'forms',
      label: 'Forms',
      icon: FileText,
      route: '/(tabs)/forms',
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: Settings,
      route: '/(tabs)/settings',
    },
  ];

  const handleNavigation = (route: string) => {
    if (onNavigate) {
      onNavigate(route);
    } else {
      console.log('Navigate to:', route);
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.card }]}>
        <View style={styles.headerContent}>
          <AlertTriangle size={24} color={theme.colors.destructive} />
          <Text style={[styles.headerTitle, { color: theme.colors.foreground }]}>
            Navigation Fallback
          </Text>
        </View>
        
        {error && (
          <Text style={[styles.errorText, { color: theme.colors.muted }]}>
            Navigation system unavailable
          </Text>
        )}
      </View>

      {/* Content Area */}
      <ScrollView style={styles.content}>
        <View style={styles.messageContainer}>
          <Text style={[styles.messageTitle, { color: theme.colors.foreground }]}>
            Basic Navigation Mode
          </Text>
          <Text style={[styles.messageText, { color: theme.colors.muted }]}>
            The advanced navigation system is temporarily unavailable. 
            Use the buttons below to navigate between sections.
          </Text>
        </View>

        {/* Navigation Grid */}
        <View style={styles.navigationGrid}>
          {navigationItems.map((item) => {
            const IconComponent = item.icon;
            const isActive = currentRoute === item.id;
            
            return (
              <TouchableOpacity
                key={item.id}
                style={[
                  styles.navigationItem,
                  {
                    backgroundColor: isActive 
                      ? theme.colors.primary 
                      : theme.colors.card,
                    borderColor: theme.colors.border,
                  }
                ]}
                onPress={() => handleNavigation(item.route)}
              >
                <IconComponent 
                  size={32} 
                  color={isActive ? 'white' : theme.colors.foreground} 
                />
                <Text style={[
                  styles.navigationLabel,
                  { 
                    color: isActive ? 'white' : theme.colors.foreground,
                    fontWeight: isActive ? '600' : '400',
                  }
                ]}>
                  {item.label}
                </Text>
              </TouchableOpacity>
            );
          })}
        </View>

        {/* Debug Information */}
        {__DEV__ && error && (
          <View style={[styles.debugContainer, { backgroundColor: theme.colors.card }]}>
            <Text style={[styles.debugTitle, { color: theme.colors.foreground }]}>
              Debug Information
            </Text>
            <Text style={[styles.debugText, { color: theme.colors.muted }]}>
              Error: {error.message}
            </Text>
            <Text style={[styles.debugText, { color: theme.colors.muted }]}>
              Platform: {Platform.OS}
            </Text>
            <Text style={[styles.debugText, { color: theme.colors.muted }]}>
              Current Route: {currentRoute}
            </Text>
          </View>
        )}
      </ScrollView>

      {/* Footer */}
      <View style={[styles.footer, { backgroundColor: theme.colors.card }]}>
        <Text style={[styles.footerText, { color: theme.colors.muted }]}>
          FieldSyncPro - Fallback Mode
        </Text>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  errorText: {
    fontSize: 14,
    marginTop: 4,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  messageContainer: {
    marginBottom: 24,
  },
  messageTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 8,
  },
  messageText: {
    fontSize: 16,
    lineHeight: 24,
  },
  navigationGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
    justifyContent: 'space-between',
  },
  navigationItem: {
    width: '48%',
    aspectRatio: 1,
    borderRadius: 12,
    borderWidth: 1,
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
  },
  navigationLabel: {
    fontSize: 16,
    textAlign: 'center',
  },
  debugContainer: {
    marginTop: 24,
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  debugTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  debugText: {
    fontSize: 12,
    fontFamily: Platform.OS === 'ios' ? 'Menlo' : 'monospace',
    marginBottom: 4,
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    alignItems: 'center',
  },
  footerText: {
    fontSize: 14,
  },
});

export default FallbackNavigation;
