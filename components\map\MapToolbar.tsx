import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useTheme } from '@/hooks/useTheme';
import { MapPin, LineC<PERSON>, Shapes, CheckCircle, X } from 'lucide-react-native';

type MapToolbarProps = {
  activeDrawingMode: 'none' | 'point' | 'line' | 'polygon';
  onModeChange: (mode: 'none' | 'point' | 'line' | 'polygon') => void;
  onFinishDrawing: () => void;
  onCancelDrawing: () => void;
};

export default function MapToolbar({ 
  activeDrawingMode, 
  onModeChange, 
  onFinishDrawing, 
  onCancelDrawing 
}: MapToolbarProps) {
  const { theme } = useTheme();
  
  return (
    <View style={[styles.container, { backgroundColor: theme.colors.card }]}>
      {activeDrawingMode === 'none' ? (
        // Drawing tool options
        <View style={styles.toolsContainer}>
          <TouchableOpacity
            style={styles.toolButton}
            onPress={() => onModeChange('point')}
          >
            <MapPin size={24} color={theme.colors.text} />
            <Text style={[styles.toolText, { color: theme.colors.text }]}>Point</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.toolButton}
            onPress={() => onModeChange('line')}
          >
            <LineChart size={24} color={theme.colors.text} />
            <Text style={[styles.toolText, { color: theme.colors.text }]}>Line</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.toolButton}
            onPress={() => onModeChange('polygon')}
          >
            <Shapes size={24} color={theme.colors.text} />
            <Text style={[styles.toolText, { color: theme.colors.text }]}>Polygon</Text>
          </TouchableOpacity>
        </View>
      ) : (
        // Active drawing controls
        <View style={styles.drawingControlsContainer}>
          <Text style={[styles.drawingModeText, { color: theme.colors.primary }]}>
            Drawing: {activeDrawingMode.charAt(0).toUpperCase() + activeDrawingMode.slice(1)}
          </Text>
          
          <View style={styles.drawingActions}>
            <TouchableOpacity
              style={[styles.drawingActionButton, { backgroundColor: theme.colors.error }]}
              onPress={onCancelDrawing}
            >
              <X size={20} color="white" />
              <Text style={styles.drawingActionText}>Cancel</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.drawingActionButton, { backgroundColor: theme.colors.success }]}
              onPress={onFinishDrawing}
            >
              <CheckCircle size={20} color="white" />
              <Text style={styles.drawingActionText}>Finish</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 20,
    left: 16,
    right: 16,
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    zIndex: 1001,
  },
  toolsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  toolButton: {
    alignItems: 'center',
    padding: 8,
  },
  toolText: {
    marginTop: 4,
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },
  drawingControlsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  drawingModeText: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 16,
  },
  drawingActions: {
    flexDirection: 'row',
    gap: 8,
  },
  drawingActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    gap: 4,
  },
  drawingActionText: {
    color: 'white',
    fontFamily: 'Inter-Medium',
    fontSize: 14,
  },
});