# Advanced Map Quick Start Guide

This guide helps developers quickly get started with the Advanced Map feature in FieldSyncPro.

## 🚀 Quick Setup

### 1. Navigation Setup
The Advanced Map is already integrated into the tab navigation. Users can access it via the "Advanced Map" tab with the Globe icon.

### 2. Basic Usage
```typescript
import { useGISMap } from '@/hooks/useGISMap';
import { GISMapViewer } from '@/components/map/GISMapViewer';

const MyMapComponent = () => {
  const {
    mapRegion,
    layers,
    addLayer,
    showNotification,
  } = useGISMap({
    initialRegion: {
      latitude: 37.7749,
      longitude: -122.4194,
      latitudeDelta: 0.1,
      longitudeDelta: 0.1,
    },
  });

  return (
    <GISMapViewer
      region={mapRegion}
      layers={layers}
      onFeatureCreated={(feature) => {
        showNotification('success', 'Feature created!');
      }}
    />
  );
};
```

## 📋 Core Components Overview

### Main Components
- **AdvancedMapScreen**: Main screen with full GIS functionality
- **GISMapViewer**: Core map rendering component
- **LayerManagerModal**: Layer management interface
- **SpatialAnalysisModal**: Spatial analysis tools
- **MapSettingsModal**: Map configuration
- **StoryBuilderModal**: Interactive narrative creation
- **BookmarkManagerModal**: Location bookmarking

### Utility Components
- **CoordinateDisplay**: Multi-format coordinate display
- **MeasurementDisplay**: Measurement results management
- **MapErrorBoundary**: Error handling for map components

## 🔧 Common Development Tasks

### Adding a Custom Layer
```typescript
import { MapLayer } from '@/types/gis';

const customLayer: MapLayer = {
  id: 'my-custom-layer',
  name: 'Custom Data Layer',
  type: 'vector',
  source: 'upload',
  visible: true,
  opacity: 0.8,
  style: {
    fillColor: '#3B82F6',
    strokeColor: '#1E40AF',
    strokeWidth: 2,
  },
  metadata: {
    description: 'My custom dataset',
    source: 'Internal database',
    lastUpdated: new Date().toISOString(),
    properties: ['id', 'name', 'value'],
    geometryType: 'Polygon',
    featureCount: 150,
    extent: {
      minX: -180,
      minY: -90,
      maxX: 180,
      maxY: 90,
    },
  },
};

// Add to map
addLayer(customLayer);
```

### Creating Custom Analysis Tools
```typescript
import { createBuffer } from '@/components/map/spatial/SpatialAnalysisEngine';

const runCustomAnalysis = async (features: GISFeature[]) => {
  const result = await createBuffer(features, {
    distance: 1000,
    unit: 'meters',
    steps: 16,
    dissolve: true,
  });
  
  if (result.status === 'completed') {
    console.log('Analysis completed:', result.result);
  }
};
```

### Handling Map Events
```typescript
const handleMapPress = (event: MapPressEvent) => {
  const { coordinate } = event.nativeEvent;
  
  // Create a new point feature
  const feature: GISFeature = {
    id: `point-${Date.now()}`,
    type: 'point',
    coordinates: [coordinate],
    properties: {
      name: 'User Point',
      timestamp: new Date().toISOString(),
    },
    style: {
      fillColor: '#EF4444',
      strokeColor: '#DC2626',
    },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };
  
  createFeature(feature);
};
```

### Working with Demo Data
```typescript
import { DEMO_LAYERS, DEMO_FEATURES, getDemoData } from '@/data/demoData';

// Load demo layers
DEMO_LAYERS.forEach(layer => addLayer(layer));

// Get specific demo data
const demoBookmarks = getDemoData('bookmarks');
const demoStory = getDemoData('story');
```

## 🎨 Styling and Theming

### Custom Layer Styles
```typescript
const layerStyle: LayerStyle = {
  // For points
  iconType: 'circle',
  iconSize: 12,
  fillColor: '#10B981',
  strokeColor: '#059669',
  strokeWidth: 2,
  
  // For lines
  strokeColor: '#3B82F6',
  strokeWidth: 3,
  dashPattern: [10, 5],
  
  // For polygons
  fillColor: '#3B82F620',
  fillOpacity: 0.3,
  strokeColor: '#3B82F6',
  strokeWidth: 2,
  
  // Labels
  labelField: 'name',
  labelSize: 12,
  labelColor: '#1F2937',
};
```

### Theme Integration
```typescript
import { useTheme } from '@/hooks/useTheme';

const MapComponent = () => {
  const { theme } = useTheme();
  
  return (
    <View style={{ backgroundColor: theme.colors.background }}>
      <GISMapViewer
        // Use theme colors for map elements
        onFeatureCreated={(feature) => {
          feature.style = {
            fillColor: theme.colors.primary,
            strokeColor: theme.colors.primary,
          };
        }}
      />
    </View>
  );
};
```

## 📊 Performance Optimization

### Enable Performance Monitoring
```typescript
import { useMapPerformance } from '@/utils/mapPerformance';

const MapComponent = () => {
  const {
    startMonitoring,
    recordInteraction,
    getCurrentMetrics,
    getRecommendations,
  } = useMapPerformance();
  
  useEffect(() => {
    startMonitoring();
    return () => {
      const metrics = getCurrentMetrics();
      console.log('Final metrics:', metrics);
    };
  }, []);
  
  const handleUserAction = () => {
    recordInteraction('user-click');
    // ... handle action
  };
};
```

### Optimize Large Datasets
```typescript
// Enable clustering for point layers
const optimizedLayer: MapLayer = {
  // ... other properties
  metadata: {
    // ... other metadata
    featureCount: 50000, // Large dataset
  },
};

// Use performance settings
const mapSettings: MapSettings = {
  // ... other settings
  enableWebGL: true,
  clustering: true,
  maxFeatures: 25000,
  renderQuality: 'high',
};
```

## 🔄 State Management

### Using the GIS Hook
```typescript
const {
  // Map state
  mapRegion,
  layers,
  selectedFeatures,
  
  // Actions
  setMapRegion,
  addLayer,
  removeLayer,
  createFeature,
  
  // Measurements
  measurements,
  addMeasurement,
  
  // Bookmarks
  bookmarks,
  addBookmark,
  navigateToBookmark,
  
  // Notifications
  showNotification,
  
  // Utilities
  calculateDistance,
  calculateArea,
  exportLayer,
} = useGISMap();
```

### Persisting Map State
```typescript
const { saveState, loadState } = useGISMap();

// Save current state
await saveState();

// Load saved state
await loadState();
```

## 🛠️ Error Handling

### Using Error Boundaries
```typescript
import { MapErrorBoundary } from '@/components/map/MapErrorBoundary';

const App = () => (
  <MapErrorBoundary
    maxRetries={3}
    enablePerformanceMonitoring={true}
    onError={(error, errorInfo, errorId) => {
      console.log('Map error captured:', { error, errorInfo, errorId });
    }}
  >
    <AdvancedMapScreen />
  </MapErrorBoundary>
);
```

### Custom Error Handling
```typescript
const CustomErrorFallback = ({ error, resetError, retryCount }) => (
  <View style={styles.errorContainer}>
    <Text>Map Error: {error.message}</Text>
    <Text>Retries: {retryCount}</Text>
    <TouchableOpacity onPress={resetError}>
      <Text>Try Again</Text>
    </TouchableOpacity>
  </View>
);

<MapErrorBoundary fallbackComponent={CustomErrorFallback}>
  <MapComponent />
</MapErrorBoundary>
```

## 🧪 Testing

### Running Validation
```bash
# Run the validation script
node tests/validate-advanced-map.js

# Expected output:
# ✅ Advanced Map Screen
# ✅ GIS Map Viewer Component
# ✅ Layer Manager Modal
# ... (all components validated)
# 🎉 Advanced Map implementation validation PASSED!
```

### Performance Testing
```typescript
import { mapPerformanceMonitor } from '@/utils/mapPerformance';

// Start monitoring
mapPerformanceMonitor.startMonitoring();

// Perform operations
// ... map interactions, analysis, etc.

// Get performance report
const report = mapPerformanceMonitor.stopMonitoring();
console.log('Performance report:', report);
```

## 🔌 Integration Examples

### Custom Analysis Integration
```typescript
// Define custom analysis
const customAnalysis = {
  id: 'my-analysis',
  name: 'Custom Buffer Analysis',
  parameters: {
    distance: 500,
    unit: 'meters',
  },
  execute: async (features: GISFeature[]) => {
    return await createBuffer(features, {
      distance: 500,
      unit: 'meters',
      dissolve: true,
    });
  },
};

// Use in the analysis modal
onAnalysisStart(customAnalysis.id, customAnalysis.parameters);
```

### External Data Integration
```typescript
// Load data from external API
const loadExternalData = async () => {
  try {
    const response = await fetch('https://api.example.com/geodata');
    const geoJsonData = await response.json();
    
    const layer: MapLayer = {
      id: 'external-data',
      name: 'External Dataset',
      type: 'geojson',
      source: 'external',
      visible: true,
      opacity: 1,
      data: geoJsonData,
      metadata: {
        description: 'Data from external API',
        source: 'External API',
        lastUpdated: new Date().toISOString(),
        properties: Object.keys(geoJsonData.features[0]?.properties || {}),
        geometryType: 'Point',
        featureCount: geoJsonData.features.length,
        extent: calculateExtent(geoJsonData),
      },
    };
    
    addLayer(layer);
    showNotification('success', 'External data loaded');
  } catch (error) {
    showNotification('error', 'Failed to load external data');
  }
};
```

## 📱 Platform Considerations

### React Native Specific
```typescript
// Use appropriate map provider
import { PROVIDER_GOOGLE, PROVIDER_DEFAULT } from 'react-native-maps';

const mapProvider = Platform.OS === 'android' ? PROVIDER_GOOGLE : PROVIDER_DEFAULT;

<MapView provider={mapProvider} />
```

### Web Specific
```typescript
// Web-specific optimizations
const webOptimizations = {
  enableWebGL: true,
  renderQuality: 'high',
  clustering: true,
};
```

## 🚀 Deployment

### Environment Configuration
```typescript
// Configure map API keys
const MAP_CONFIG = {
  googleMapsApiKey: process.env.GOOGLE_MAPS_API_KEY,
  mapboxToken: process.env.MAPBOX_ACCESS_TOKEN,
  enableTelemetry: process.env.NODE_ENV !== 'production',
};
```

### Production Optimizations
```typescript
const productionSettings: MapSettings = {
  enableWebGL: true,
  renderQuality: 'high',
  maxFeatures: 50000,
  clustering: true,
  // Disable debug features
  requireAuthentication: true,
  allowDownload: false, // Restrict in production if needed
};
```

## 📚 Additional Resources

- [Implementation Guide](./ADVANCED_MAP_IMPLEMENTATION_GUIDE.md)
- [Type Definitions](./types/gis.ts)
- [Demo Data](./data/demoData.ts)
- [Performance Utilities](./utils/mapPerformance.ts)
- [Component Documentation](./components/map/)

## 🆘 Troubleshooting

### Common Issues

1. **Map not rendering**
   ```typescript
   // Check map permissions and API keys
   const checkMapSetup = () => {
     console.log('Google Maps API Key:', process.env.GOOGLE_MAPS_API_KEY);
     // Verify location permissions
   };
   ```

2. **Performance issues**
   ```typescript
   // Monitor and optimize
   const optimizePerformance = () => {
     const metrics = getCurrentMetrics();
     if (metrics.frameRate < 30) {
       // Reduce visible features
       // Enable clustering
       // Simplify geometries
     }
   };
   ```

3. **Memory leaks**
   ```typescript
   // Proper cleanup
   useEffect(() => {
     return () => {
       // Clear intervals
       // Remove event listeners
       // Reset performance monitor
     };
   }, []);
   ```

### Getting Help

1. Check the console for error messages
2. Use the performance monitor to identify bottlenecks
3. Enable error reporting for detailed error information
4. Review the implementation guide for best practices
5. Check the validation script results

---

This quick start guide covers the essential aspects of working with the Advanced Map feature. For more detailed information, refer to the comprehensive implementation guide and component documentation.
