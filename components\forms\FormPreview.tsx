import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Switch,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '@/hooks/useTheme';
import { FormSchema, FormQuestion } from '@/types';
import {
  X,
  ChevronLeft,
  ChevronRight,
  CheckSquare,
  Square,
  Circle,
  CheckCircle,
  Calendar,
  Clock,
  MapPin,
  Camera,
  Mic,
  Video,
  Edit3,
  BarChart3,
} from 'lucide-react-native';

interface FormPreviewProps {
  formSchema: FormSchema;
  formName: string;
  onClose: () => void;
}

export default function FormPreview({ formSchema, formName, onClose }: FormPreviewProps) {
  const { theme } = useTheme();
  const [currentSectionIndex, setCurrentSectionIndex] = useState(0);
  const [formData, setFormData] = useState<Record<string, any>>({});

  const currentSection = formSchema.sections[currentSectionIndex];
  const isFirstSection = currentSectionIndex === 0;
  const isLastSection = currentSectionIndex === formSchema.sections.length - 1;

  const updateFormData = (questionId: string, value: any) => {
    setFormData(prev => ({ ...prev, [questionId]: value }));
  };

  const goToNextSection = () => {
    if (!isLastSection) {
      setCurrentSectionIndex(prev => prev + 1);
    }
  };

  const goToPreviousSection = () => {
    if (!isFirstSection) {
      setCurrentSectionIndex(prev => prev - 1);
    }
  };

  const getQuestionIcon = (type: string) => {
    const iconProps = { size: 20, color: theme.colors.primary };
    
    switch (type) {
      case 'text': return <Edit3 {...iconProps} />;
      case 'number': return <Edit3 {...iconProps} />;
      case 'date': return <Calendar {...iconProps} />;
      case 'time': return <Clock {...iconProps} />;
      case 'datetime': return <Calendar {...iconProps} />;
      case 'select': return <Circle {...iconProps} />;
      case 'multiselect': return <Square {...iconProps} />;
      case 'location': return <MapPin {...iconProps} />;
      case 'photo': return <Camera {...iconProps} />;
      case 'audio': return <Mic {...iconProps} />;
      case 'video': return <Video {...iconProps} />;
      case 'signature': return <Edit3 {...iconProps} />;
      case 'drawing': return <Edit3 {...iconProps} />;
      case 'barcode': return <BarChart3 {...iconProps} />;
      default: return <Edit3 {...iconProps} />;
    }
  };

  const renderQuestion = (question: FormQuestion) => {
    // Add null check for question
    if (!question || !question.type) {
      console.warn('Invalid question object:', question);
      return null;
    }
    
    const value = formData[question.id];

    return (
      <View key={question.id} style={[styles.questionContainer, { backgroundColor: theme.colors.card, borderColor: theme.colors.border }]}>
        <View style={styles.questionHeader}>
          {getQuestionIcon(question.type)}
          <View style={styles.questionTitleContainer}>
            <Text style={[styles.questionLabel, { color: theme.colors.text }]}>
              {question.label}
              {question.required && <Text style={{ color: theme.colors.error }}> *</Text>}
            </Text>
            {question.description && (
              <Text style={[styles.questionDescription, { color: theme.colors.muted }]}>
                {question.description}
              </Text>
            )}
          </View>
        </View>

        <View style={styles.questionInput}>
          {renderQuestionInput(question, value)}
        </View>
      </View>
    );
  };

  const renderQuestionInput = (question: FormQuestion, value: any) => {
    switch (question.type) {
      case 'text':
        return (
          <TextInput
            style={[styles.textInput, { backgroundColor: theme.colors.background, color: theme.colors.text, borderColor: theme.colors.border }]}
            value={value || ''}
            onChangeText={(text) => updateFormData(question.id, text)}
            placeholder={question.placeholder || 'Enter text'}
            placeholderTextColor={theme.colors.placeholder}
          />
        );

      case 'number':
        return (
          <TextInput
            style={[styles.textInput, { backgroundColor: theme.colors.background, color: theme.colors.text, borderColor: theme.colors.border }]}
            value={value?.toString() || ''}
            onChangeText={(text) => updateFormData(question.id, parseFloat(text) || 0)}
            placeholder={question.placeholder || 'Enter number'}
            placeholderTextColor={theme.colors.placeholder}
            keyboardType="numeric"
          />
        );

      case 'select':
        return (
          <View style={styles.choicesContainer}>
            {(question.options || []).map((option, index) => (
              <TouchableOpacity
                key={index}
                style={styles.choiceOption}
                onPress={() => updateFormData(question.id, option.value)}
              >
                {value === option.value ? (
                  <CheckCircle size={20} color={theme.colors.primary} />
                ) : (
                  <Circle size={20} color={theme.colors.border} />
                )}
                <Text style={[styles.choiceText, { color: theme.colors.text }]}>
                  {option.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        );

      case 'multiselect':
        const multiValue = value || [];
        return (
          <View style={styles.choicesContainer}>
            {(question.options || []).map((option, index) => (
              <TouchableOpacity
                key={index}
                style={styles.choiceOption}
                onPress={() => {
                  const newValue = multiValue.includes(option.value)
                    ? multiValue.filter((v: any) => v !== option.value)
                    : [...multiValue, option.value];
                  updateFormData(question.id, newValue);
                }}
              >
                {multiValue.includes(option.value) ? (
                  <CheckSquare size={20} color={theme.colors.primary} />
                ) : (
                  <Square size={20} color={theme.colors.border} />
                )}
                <Text style={[styles.choiceText, { color: theme.colors.text }]}>
                  {option.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        );

      case 'date':
        return (
          <TouchableOpacity
            style={[styles.dateInput, { backgroundColor: theme.colors.background, borderColor: theme.colors.border }]}
            onPress={() => Alert.alert('Date Picker', 'Date picker would open here')}
          >
            <Calendar size={20} color={theme.colors.muted} />
            <Text style={[styles.dateText, { color: value ? theme.colors.text : theme.colors.placeholder }]}>
              {value || 'Select date'}
            </Text>
          </TouchableOpacity>
        );

      case 'time':
        return (
          <TouchableOpacity
            style={[styles.dateInput, { backgroundColor: theme.colors.background, borderColor: theme.colors.border }]}
            onPress={() => Alert.alert('Time Picker', 'Time picker would open here')}
          >
            <Clock size={20} color={theme.colors.muted} />
            <Text style={[styles.dateText, { color: value ? theme.colors.text : theme.colors.placeholder }]}>
              {value || 'Select time'}
            </Text>
          </TouchableOpacity>
        );

      case 'location':
        return (
          <TouchableOpacity
            style={[styles.locationInput, { backgroundColor: theme.colors.background, borderColor: theme.colors.border }]}
            onPress={() => Alert.alert('Location', 'GPS capture would happen here')}
          >
            <MapPin size={20} color={theme.colors.muted} />
            <Text style={[styles.dateText, { color: value ? theme.colors.text : theme.colors.placeholder }]}>
              {value ? `${value.lat}, ${value.lng}` : 'Capture location'}
            </Text>
          </TouchableOpacity>
        );

      case 'photo':
        return (
          <TouchableOpacity
            style={[styles.mediaInput, { backgroundColor: theme.colors.background, borderColor: theme.colors.border }]}
            onPress={() => Alert.alert('Camera', 'Camera would open here')}
          >
            <Camera size={24} color={theme.colors.muted} />
            <Text style={[styles.mediaText, { color: theme.colors.muted }]}>
              {value ? 'Photo captured' : 'Take photo'}
            </Text>
          </TouchableOpacity>
        );

      case 'audio':
        return (
          <TouchableOpacity
            style={[styles.mediaInput, { backgroundColor: theme.colors.background, borderColor: theme.colors.border }]}
            onPress={() => Alert.alert('Audio', 'Audio recorder would open here')}
          >
            <Mic size={24} color={theme.colors.muted} />
            <Text style={[styles.mediaText, { color: theme.colors.muted }]}>
              {value ? 'Audio recorded' : 'Record audio'}
            </Text>
          </TouchableOpacity>
        );

      case 'signature':
        return (
          <TouchableOpacity
            style={[styles.signatureInput, { backgroundColor: theme.colors.background, borderColor: theme.colors.border }]}
            onPress={() => Alert.alert('Signature', 'Signature pad would open here')}
          >
            <Edit3 size={24} color={theme.colors.muted} />
            <Text style={[styles.mediaText, { color: theme.colors.muted }]}>
              {value ? 'Signature captured' : 'Add signature'}
            </Text>
          </TouchableOpacity>
        );

      default:
        return (
          <Text style={[styles.previewPlaceholder, { color: theme.colors.muted }]}>
            {question.type} input preview
          </Text>
        );
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.card, borderBottomColor: theme.colors.border }]}>
        <TouchableOpacity onPress={onClose} style={styles.closeButton}>
          <X size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.colors.text }]}>Form Preview</Text>
        <View style={{ width: 40 }} />
      </View>

      {/* Form Title */}
      <View style={[styles.formHeader, { backgroundColor: theme.colors.card }]}>
        <Text style={[styles.formTitle, { color: theme.colors.text }]}>{formName}</Text>
        <Text style={[styles.sectionInfo, { color: theme.colors.muted }]}>
          Section {currentSectionIndex + 1} of {formSchema.sections.length}: {currentSection?.title}
        </Text>
      </View>

      {/* Section Content */}
      <ScrollView style={styles.content} contentContainerStyle={styles.contentContainer}>
        {currentSection && currentSection.questions && (
          <>
            <View style={styles.sectionHeader}>
              <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
                {currentSection.title}
              </Text>
              {currentSection.description && (
                <Text style={[styles.sectionDescription, { color: theme.colors.muted }]}>
                  {currentSection.description}
                </Text>
              )}
            </View>

            {currentSection.questions.map((question) => 
              question ? renderQuestion(question) : null
            )}
          </>
        )}
      </ScrollView>

      {/* Navigation */}
      <View style={[styles.navigation, { backgroundColor: theme.colors.card, borderTopColor: theme.colors.border }]}>
        <TouchableOpacity
          style={[
            styles.navButton,
            { 
              backgroundColor: isFirstSection ? theme.colors.border : theme.colors.secondary,
              opacity: isFirstSection ? 0.5 : 1,
            }
          ]}
          onPress={goToPreviousSection}
          disabled={isFirstSection}
        >
          <ChevronLeft size={20} color="white" />
          <Text style={styles.navButtonText}>Previous</Text>
        </TouchableOpacity>

        <View style={styles.progressIndicator}>
          {formSchema.sections.map((_, index) => (
            <View
              key={index}
              style={[
                styles.progressDot,
                {
                  backgroundColor: index === currentSectionIndex
                    ? theme.colors.primary
                    : theme.colors.border,
                },
              ]}
            />
          ))}
        </View>

        <TouchableOpacity
          style={[
            styles.navButton,
            { 
              backgroundColor: isLastSection ? theme.colors.primary : theme.colors.secondary,
            }
          ]}
          onPress={goToNextSection}
        >
          <Text style={styles.navButtonText}>
            {isLastSection ? 'Submit' : 'Next'}
          </Text>
          {!isLastSection && <ChevronRight size={20} color="white" />}
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  closeButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
  },
  formHeader: {
    padding: 16,
    alignItems: 'center',
  },
  formTitle: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  sectionInfo: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  sectionHeader: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    lineHeight: 24,
  },
  questionContainer: {
    marginBottom: 20,
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
  },
  questionHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  questionTitleContainer: {
    flex: 1,
    marginLeft: 12,
  },
  questionLabel: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    lineHeight: 24,
  },
  questionDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    marginTop: 4,
    lineHeight: 20,
  },
  questionInput: {
    marginTop: 8,
  },
  textInput: {
    borderWidth: 1,
    borderRadius: 6,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
  },
  choicesContainer: {
    gap: 12,
  },
  choiceOption: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  choiceText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    flex: 1,
  },
  dateInput: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 6,
    paddingHorizontal: 12,
    paddingVertical: 12,
    gap: 12,
  },
  dateText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
  },
  locationInput: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 6,
    paddingHorizontal: 12,
    paddingVertical: 12,
    gap: 12,
  },
  mediaInput: {
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderStyle: 'dashed',
    borderRadius: 8,
    paddingVertical: 32,
    gap: 8,
  },
  signatureInput: {
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderStyle: 'dashed',
    borderRadius: 8,
    paddingVertical: 48,
    gap: 8,
  },
  mediaText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
  },
  previewPlaceholder: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    fontStyle: 'italic',
    textAlign: 'center',
    paddingVertical: 20,
  },
  navigation: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
  },
  navButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 6,
    gap: 6,
    minWidth: 80,
  },
  navButtonText: {
    color: 'white',
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
  },
  progressIndicator: {
    flexDirection: 'row',
    gap: 6,
  },
  progressDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
});
