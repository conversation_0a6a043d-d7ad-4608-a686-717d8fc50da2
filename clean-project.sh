#!/bin/bash

echo "🧹 Cleaning FieldSync Pro Project..."

# Stop any running Metro processes
echo "Stopping Metro processes..."
pkill -f "expo start" || true
pkill -f "metro" || true

# Clear Metro cache
echo "Clearing Metro cache..."
npx expo start --clear

# Alternative commands for manual cleanup
echo ""
echo "📝 If issues persist, try these commands manually:"
echo "1. npm run reset-cache"
echo "2. expo start --clear"
echo "3. expo start --dev-client --clear"
echo "4. rm -rf node_modules && npm install"
echo ""
echo "✅ Project cleaned! Run 'npm run dev' to start fresh."
