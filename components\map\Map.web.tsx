import React, { useState, useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import { useTheme } from '@/hooks/useTheme';
import { useGeoFeatures } from '@/hooks/useGeoFeatures';
import { GeoFeature } from '@/types';
import * as Location from 'expo-location';
import LeafletMap from './LeafletMap.web';
import SpatialToolkit from './SpatialToolkit';

type DrawingMode = 'none' | 'point' | 'line' | 'polygon' | 'circle' | 'rectangle' | 'freehand';
type MapLayerType = 'standard' | 'satellite' | 'terrain' | 'hybrid';

interface Region {
  latitude: number;
  longitude: number;
  latitudeDelta: number;
  longitudeDelta: number;
}

interface MapProps {
  initialRegion?: Region;
  mapType?: MapLayerType;
  drawingMode?: DrawingMode;
  measurementMode?: boolean;
  showHeatmap?: boolean;
  showClustering?: boolean;
  userLocation?: Location.LocationObject | null;
  geoFeatures?: any[];
  onLocationSelect?: (location: { latitude: number; longitude: number }) => void;
}

// Enhanced Web Map with Leaflet Integration
export default function Map({
  initialRegion,
  mapType = 'standard',
  drawingMode = 'none',
  measurementMode = false,
  showHeatmap = false,
  showClustering = false,
  userLocation,
  geoFeatures = [],
  onLocationSelect,
}: MapProps) {
  const { theme } = useTheme();
  const { geoFeatures: localGeoFeatures } = useGeoFeatures();
  const [selectedFeature, setSelectedFeature] = useState<GeoFeature | null>(null);
  const [showSpatialTools, setShowSpatialTools] = useState(false);

  const handleFeatureSelect = (feature: GeoFeature | null) => {
    setSelectedFeature(feature);
  };

  const handleAnalysisResult = (result: any) => {
    console.log('Spatial analysis result:', result);
    // Handle analysis results (e.g., display notifications, save to database)
  };

  const handleMapClick = (location: { latitude: number; longitude: number }) => {
    if (onLocationSelect) {
      onLocationSelect(location);
    }
  };

  // Convert region to Leaflet center and zoom
  const getInitialCenter = (): [number, number] => {
    if (initialRegion) {
      return [initialRegion.latitude, initialRegion.longitude];
    }
    return [37.78825, -122.4324];
  };

  const getInitialZoom = (): number => {
    if (initialRegion) {
      // Approximate conversion from latitudeDelta to zoom level
      const delta = initialRegion.latitudeDelta;
      if (delta <= 0.01) return 16;
      if (delta <= 0.05) return 14;
      if (delta <= 0.1) return 12;
      if (delta <= 0.5) return 10;
      return 8;
    }
    return 12;
  };

  // Map drawing mode to Leaflet drawing tools
  const getDrawingOptions = () => {
    const options: any = {
      enableDrawing: drawingMode !== 'none',
      enableMeasurement: measurementMode,
    };

    if (drawingMode !== 'none') {
      options.defaultDrawingMode = drawingMode;
    }

    return options;
  };

  // Combine local and passed features
  const allFeatures = [...localGeoFeatures, ...geoFeatures];

  return (
    <View style={styles.container}>
      <LeafletMap
        onFeatureSelect={handleFeatureSelect}
        showSpatialTools={showSpatialTools}
        initialCenter={getInitialCenter()}
        initialZoom={getInitialZoom()}
        {...getDrawingOptions()}
        mapType={mapType}
        showHeatmap={showHeatmap}
        showClustering={showClustering}
        features={allFeatures}
        onMapClick={handleMapClick}
        userLocation={userLocation ? {
          lat: userLocation.coords.latitude,
          lng: userLocation.coords.longitude,
        } : undefined}
      />
      
      {showSpatialTools && (
        <SpatialToolkit
          visible={showSpatialTools}
          onClose={() => setShowSpatialTools(false)}
          features={allFeatures}
          selectedFeature={selectedFeature}
          onAnalysisResult={handleAnalysisResult}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
