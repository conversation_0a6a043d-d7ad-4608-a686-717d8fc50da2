import { Platform } from 'react-native';

// Storage interface for cross-platform compatibility
export interface IStorage {
  getItem(key: string): Promise<string | null>;
  setItem(key: string, value: string): Promise<void>;
  removeItem(key: string): Promise<void>;
}

// Web storage implementation using localStorage
class WebStorage implements IStorage {
  async getItem(key: string): Promise<string | null> {
    try {
      if (typeof window !== 'undefined' && window.localStorage) {
        return window.localStorage.getItem(key);
      }
      return null;
    } catch (error) {
      console.warn('WebStorage getItem error:', error);
      return null;
    }
  }

  async setItem(key: string, value: string): Promise<void> {
    try {
      if (typeof window !== 'undefined' && window.localStorage) {
        window.localStorage.setItem(key, value);
      }
    } catch (error) {
      console.warn('WebStorage setItem error:', error);
    }
  }

  async removeItem(key: string): Promise<void> {
    try {
      if (typeof window !== 'undefined' && window.localStorage) {
        window.localStorage.removeItem(key);
      }
    } catch (error) {
      console.warn('WebStorage removeItem error:', error);
    }
  }
}

// Mobile storage implementation using AsyncStorage
class MobileStorage implements IStorage {
  private asyncStorage: any;

  constructor() {
    try {
      // Dynamically import AsyncStorage for mobile platforms only
      this.asyncStorage = require('@react-native-async-storage/async-storage').default;
    } catch (error) {
      console.warn('AsyncStorage not available, falling back to memory storage');
      this.asyncStorage = null;
    }
  }

  async getItem(key: string): Promise<string | null> {
    try {
      if (this.asyncStorage) {
        return await this.asyncStorage.getItem(key);
      }
      return null;
    } catch (error) {
      console.warn('MobileStorage getItem error:', error);
      return null;
    }
  }

  async setItem(key: string, value: string): Promise<void> {
    try {
      if (this.asyncStorage) {
        await this.asyncStorage.setItem(key, value);
      }
    } catch (error) {
      console.warn('MobileStorage setItem error:', error);
    }
  }

  async removeItem(key: string): Promise<void> {
    try {
      if (this.asyncStorage) {
        await this.asyncStorage.removeItem(key);
      }
    } catch (error) {
      console.warn('MobileStorage removeItem error:', error);
    }
  }
}

// Memory storage fallback for development or when other storage is unavailable
class MemoryStorage implements IStorage {
  private storage: Map<string, string> = new Map();

  async getItem(key: string): Promise<string | null> {
    return this.storage.get(key) || null;
  }

  async setItem(key: string, value: string): Promise<void> {
    this.storage.set(key, value);
  }

  async removeItem(key: string): Promise<void> {
    this.storage.delete(key);
  }
}

// Create platform-appropriate storage instance
function createStorage(): IStorage {
  if (Platform.OS === 'web') {
    return new WebStorage();
  } else {
    return new MobileStorage();
  }
}

// Export singleton storage instance
export const Storage = createStorage();

// Export memory storage for testing or fallback
export const MemoryStorageInstance = new MemoryStorage();

// Utility functions for common storage operations
export const StorageUtils = {
  // Store JSON data
  async setJSON(key: string, data: any): Promise<void> {
    try {
      const jsonString = JSON.stringify(data);
      await Storage.setItem(key, jsonString);
    } catch (error) {
      console.error('StorageUtils setJSON error:', error);
      throw error;
    }
  },

  // Retrieve JSON data
  async getJSON<T>(key: string): Promise<T | null> {
    try {
      const jsonString = await Storage.getItem(key);
      if (jsonString) {
        return JSON.parse(jsonString) as T;
      }
      return null;
    } catch (error) {
      console.error('StorageUtils getJSON error:', error);
      return null;
    }
  },

  // Store with expiration
  async setWithExpiry(key: string, data: any, ttlMinutes: number): Promise<void> {
    try {
      const now = new Date();
      const item = {
        data,
        expiry: now.getTime() + (ttlMinutes * 60 * 1000),
      };
      await Storage.setItem(key, JSON.stringify(item));
    } catch (error) {
      console.error('StorageUtils setWithExpiry error:', error);
      throw error;
    }
  },

  // Get with expiration check
  async getWithExpiry<T>(key: string): Promise<T | null> {
    try {
      const itemStr = await Storage.getItem(key);
      if (!itemStr) return null;

      const item = JSON.parse(itemStr);
      const now = new Date();

      if (now.getTime() > item.expiry) {
        // Item has expired, remove it
        await Storage.removeItem(key);
        return null;
      }

      return item.data as T;
    } catch (error) {
      console.error('StorageUtils getWithExpiry error:', error);
      return null;
    }
  },

  // Clear all data with a specific prefix
  async clearPrefix(prefix: string): Promise<void> {
    try {
      if (Platform.OS === 'web' && typeof window !== 'undefined' && window.localStorage) {
        // For web, iterate through localStorage keys
        const keysToRemove: string[] = [];
        for (let i = 0; i < window.localStorage.length; i++) {
          const key = window.localStorage.key(i);
          if (key && key.startsWith(prefix)) {
            keysToRemove.push(key);
          }
        }
        for (const key of keysToRemove) {
          await Storage.removeItem(key);
        }
      } else {
        // For mobile, this would require getting all keys which AsyncStorage doesn't support directly
        // This is a limitation - mobile apps should track their own keys
        console.warn('clearPrefix not fully supported on mobile - track keys manually');
      }
    } catch (error) {
      console.error('StorageUtils clearPrefix error:', error);
    }
  },

  // Get storage info
  async getStorageInfo(): Promise<{ available: boolean; type: string; size?: number }> {
    if (Platform.OS === 'web') {
      if (typeof window !== 'undefined' && window.localStorage) {
        try {
          let size = 0;
          for (let key in window.localStorage) {
            if (window.localStorage.hasOwnProperty(key)) {
              size += window.localStorage[key].length + key.length;
            }
          }
          return { available: true, type: 'localStorage', size };
        } catch (error) {
          return { available: false, type: 'web-error' };
        }
      }
      return { available: false, type: 'web-unavailable' };
    } else {
      return { available: true, type: 'AsyncStorage' };
    }
  },
};

// Export storage constants
export const STORAGE_KEYS = {
  FEATURES: 'fieldsyncpro_enhanced_features',
  SETTINGS: 'fieldsyncpro_map_settings',
  OFFLINE_DATA: 'fieldsyncpro_offline_data',
  USER_PREFERENCES: 'fieldsyncpro_user_preferences',
} as const;

// Helper to generate project-specific keys
export const generateStorageKey = (key: string, projectId?: string): string => {
  return projectId ? `${key}_${projectId}` : key;
};
