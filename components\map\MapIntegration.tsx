import React, { useState, useEffect } from 'react';
import { View, Alert, StyleSheet } from 'react-native';
import { StorageUtils, STORAGE_KEYS, generateStorageKey } from '@/lib/storage';
import ProfessionalMapLayout from './ProfessionalMapLayout';
import { useTheme } from '@/hooks/useTheme';

interface Region {
  latitude: number;
  longitude: number;
  latitudeDelta: number;
  longitudeDelta: number;
}

interface StoredFeature {
  id: string;
  type: 'point' | 'line' | 'polygon';
  coordinates: any;
  properties: {
    name: string;
    description?: string;
    color: string;
    created: number;
  };
}

interface MapIntegrationProps {
  projectId?: string;
  initialRegion?: Region;
  onFeatureCreated?: (feature: StoredFeature) => void;
  onFeatureDeleted?: (featureId: string) => void;
  enableOfflineStorage?: boolean;
  maxFeatures?: number;
}

const FEATURES_STORAGE_KEY = STORAGE_KEYS.FEATURES;

export default function MapIntegration({
  projectId = 'default',
  initialRegion,
  onFeatureCreated,
  onFeatureDeleted,
  enableOfflineStorage = true,
  maxFeatures = 100,
}: MapIntegrationProps) {
  const { theme } = useTheme();
  const [features, setFeatures] = useState<StoredFeature[]>([]);
  const [loading, setLoading] = useState(true);
  const [region, setRegion] = useState<Region>(
    initialRegion || {
      latitude: 37.78825,
      longitude: -122.4324,
      latitudeDelta: 0.0922,
      longitudeDelta: 0.0421,
    }
  );

  // Load features from storage on mount
  useEffect(() => {
    loadFeatures();
  }, [projectId]);

  const loadFeatures = async () => {
    if (!enableOfflineStorage) {
      setLoading(false);
      return;
    }

    try {
      const storageKey = generateStorageKey(FEATURES_STORAGE_KEY, projectId);
      const parsedFeatures = await StorageUtils.getJSON<StoredFeature[]>(storageKey);
      
      if (parsedFeatures && Array.isArray(parsedFeatures)) {
        setFeatures(parsedFeatures);
      }
    } catch (error) {
      console.error('Error loading features:', error);
      Alert.alert(
        'Storage Error',
        'Failed to load saved features. Starting with empty map.'
      );
    } finally {
      setLoading(false);
    }
  };

  const saveFeatures = async (updatedFeatures: StoredFeature[]) => {
    if (!enableOfflineStorage) return;

    try {
      const storageKey = generateStorageKey(FEATURES_STORAGE_KEY, projectId);
      await StorageUtils.setJSON(storageKey, updatedFeatures);
    } catch (error) {
      console.error('Error saving features:', error);
      Alert.alert(
        'Storage Error',
        'Failed to save features. Changes may be lost.'
      );
    }
  };

  const handleFeatureCreated = (newFeature: any) => {
    // Check feature limit
    if (features.length >= maxFeatures) {
      Alert.alert(
        'Feature Limit Reached',
        `Maximum of ${maxFeatures} features allowed. Please delete some features before adding new ones.`,
        [{ text: 'OK' }]
      );
      return;
    }

    // Convert to stored feature format
    const storedFeature: StoredFeature = {
      id: newFeature.id || `feature_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: newFeature.type,
      coordinates: newFeature.coordinates,
      properties: {
        name: newFeature.properties?.name || `${newFeature.type.charAt(0).toUpperCase() + newFeature.type.slice(1)} ${features.length + 1}`,
        description: newFeature.properties?.description || '',
        color: newFeature.properties?.color || theme.colors.primary,
        created: Date.now(),
      },
    };

    const updatedFeatures = [...features, storedFeature];
    setFeatures(updatedFeatures);
    saveFeatures(updatedFeatures);

    // Notify parent component
    if (onFeatureCreated) {
      onFeatureCreated(storedFeature);
    }
  };

  const handleFeatureDeleted = (featureId: string) => {
    const updatedFeatures = features.filter(f => f.id !== featureId);
    setFeatures(updatedFeatures);
    saveFeatures(updatedFeatures);

    // Notify parent component
    if (onFeatureDeleted) {
      onFeatureDeleted(featureId);
    }
  };

  const handleLocationSelect = (location: { latitude: number; longitude: number }) => {
    // Update region to center on selected location
    setRegion({
      latitude: location.latitude,
      longitude: location.longitude,
      latitudeDelta: 0.01,
      longitudeDelta: 0.01,
    });
  };

  const exportFeatures = async () => {
    if (features.length === 0) {
      Alert.alert('No Features', 'No features to export.');
      return;
    }

    try {
      // Create GeoJSON format export
      const geoJSON = {
        type: 'FeatureCollection',
        features: features.map(feature => ({
          type: 'Feature',
          geometry: {
            type: feature.type === 'point' ? 'Point' : 
                  feature.type === 'line' ? 'LineString' : 'Polygon',
            coordinates: feature.coordinates,
          },
          properties: {
            ...feature.properties,
            id: feature.id,
          },
        })),
      };

      // In a real app, you would use a file sharing library
      // For now, we'll just copy to clipboard or show alert
      Alert.alert(
        'Export Features',
        `${features.length} features ready for export.\n\nIn a production app, this would save to files or share via email.`,
        [
          { text: 'OK' },
          { 
            text: 'View JSON', 
            onPress: () => {
              console.log('Exported GeoJSON:', JSON.stringify(geoJSON, null, 2));
              Alert.alert('Export Data', 'GeoJSON data logged to console for development.');
            }
          }
        ]
      );
    } catch (error) {
      Alert.alert('Export Error', 'Failed to export features.');
    }
  };

  const clearAllFeatures = () => {
    Alert.alert(
      'Clear All Features',
      'Are you sure you want to delete all features? This cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear All',
          style: 'destructive',
          onPress: () => {
            setFeatures([]);
            saveFeatures([]);
          },
        },
      ]
    );
  };

  const getFeatureStats = () => {
    const stats = {
      total: features.length,
      points: features.filter(f => f.type === 'point').length,
      lines: features.filter(f => f.type === 'line').length,
      polygons: features.filter(f => f.type === 'polygon').length,
    };
    return stats;
  };

  if (loading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: theme.colors.background }]}>
        {/* You could add a loading spinner here */}
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ProfessionalMapLayout
        initialRegion={region}
        onLocationSelect={handleLocationSelect}
        geoFeatures={features}
        showAnalysisTools={true}
        enableDrawing={true}
        enableMeasurement={true}
        enableGeofencing={false}
        enableRouting={false}
        enableHeatmap={true}
        enableClustering={true}
        offlineMode={!enableOfflineStorage}
        // Pass custom handlers
        onFeatureCreated={handleFeatureCreated}
        onFeatureDeleted={handleFeatureDeleted}
        onExportFeatures={exportFeatures}
        onClearAllFeatures={clearAllFeatures}
        featureStats={getFeatureStats()}
        maxFeatures={maxFeatures}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
