import { supabase, handleSupabaseError, isDevelopmentMode } from '../supabase';
import { ApiResponse, PaginatedResponse } from '@/types/database';

/**
 * Base API class providing common functionality for all services
 */
export abstract class BaseApi {
  protected tableName: string;

  constructor(tableName: string) {
    this.tableName = tableName;
  }

  /**
   * Handle API responses with consistent error handling
   */
  protected async handleResponse<T>(
    operation: Promise<any>,
    operationName: string
  ): Promise<ApiResponse<T>> {
    try {
      const { data, error, count } = await operation;
      
      if (error) {
        handleSupabaseError(error, operationName);
      }

      return {
        data,
        error: null,
        count
      };
    } catch (error) {
      console.error(`${operationName} failed:`, error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'An unknown error occurred'
      };
    }
  }

  /**
   * Handle paginated responses
   */
  protected async handlePaginatedResponse<T>(
    operation: Promise<any>,
    operationName: string,
    page: number = 1,
    pageSize: number = 20
  ): Promise<PaginatedResponse<T>> {
    try {
      const { data, error, count } = await operation;
      
      if (error) {
        handleSupabaseError(error, operationName);
      }

      const totalCount = count || 0;
      const totalPages = Math.ceil(totalCount / pageSize);

      return {
        data,
        error: null,
        count,
        page,
        pageSize,
        totalPages,
        totalCount
      };
    } catch (error) {
      console.error(`${operationName} failed:`, error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'An unknown error occurred',
        page,
        pageSize,
        totalPages: 0,
        totalCount: 0
      };
    }
  }

  /**
   * Check if we're in development mode and should use mock data
   */
  protected isDevelopment(): boolean {
    return isDevelopmentMode();
  }

  /**
   * Get current user ID
   */
  protected async getCurrentUserId(): Promise<string | null> {
    const { data: { user } } = await supabase.auth.getUser();
    return user?.id || null;
  }

  /**
   * Check if user has required role
   */
  protected async checkUserRole(requiredRoles: string[]): Promise<boolean> {
    const userId = await this.getCurrentUserId();
    if (!userId) return false;

    const { data } = await supabase
      .from('user_profiles')
      .select('role')
      .eq('id', userId)
      .single();

    return data ? requiredRoles.includes(data.role) : false;
  }

  /**
   * Generic create operation
   */
  protected async create<T, I>(data: I): Promise<ApiResponse<T>> {
    return this.handleResponse<T>(
      supabase
        .from(this.tableName)
        .insert(data)
        .select()
        .single(),
      `create ${this.tableName}`
    );
  }

  /**
   * Generic read operation with optional filters
   */
  protected async read<T>(
    filters: Record<string, any> = {},
    select: string = '*'
  ): Promise<ApiResponse<T[]>> {
    let query = supabase.from(this.tableName).select(select);

    // Apply filters
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        query = query.eq(key, value);
      }
    });

    return this.handleResponse<T[]>(
      query,
      `read ${this.tableName}`
    );
  }

  /**
   * Generic read with pagination
   */
  protected async readPaginated<T>(
    page: number = 1,
    pageSize: number = 20,
    filters: Record<string, any> = {},
    select: string = '*',
    orderBy: { column: string; ascending: boolean } = { column: 'created_at', ascending: false }
  ): Promise<PaginatedResponse<T>> {
    const from = (page - 1) * pageSize;
    const to = from + pageSize - 1;

    let query = supabase
      .from(this.tableName)
      .select(select, { count: 'exact' })
      .range(from, to)
      .order(orderBy.column, { ascending: orderBy.ascending });

    // Apply filters
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        query = query.eq(key, value);
      }
    });

    return this.handlePaginatedResponse<T>(
      query,
      `read paginated ${this.tableName}`,
      page,
      pageSize
    );
  }

  /**
   * Generic update operation
   */
  protected async update<T, U>(
    id: string,
    updates: U
  ): Promise<ApiResponse<T>> {
    return this.handleResponse<T>(
      supabase
        .from(this.tableName)
        .update(updates)
        .eq('id', id)
        .select()
        .single(),
      `update ${this.tableName}`
    );
  }

  /**
   * Generic delete operation
   */
  protected async delete(id: string): Promise<ApiResponse<null>> {
    return this.handleResponse<null>(
      supabase
        .from(this.tableName)
        .delete()
        .eq('id', id),
      `delete ${this.tableName}`
    );
  }

  /**
   * Generic soft delete operation
   */
  protected async softDelete(id: string): Promise<ApiResponse<any>> {
    return this.handleResponse(
      supabase
        .from(this.tableName)
        .update({ is_active: false })
        .eq('id', id)
        .select()
        .single(),
      `soft delete ${this.tableName}`
    );
  }

  /**
   * Get single record by ID
   */
  protected async getById<T>(
    id: string,
    select: string = '*'
  ): Promise<ApiResponse<T>> {
    return this.handleResponse<T>(
      supabase
        .from(this.tableName)
        .select(select)
        .eq('id', id)
        .single(),
      `get ${this.tableName} by id`
    );
  }

  /**
   * Search records with text query
   */
  protected async search<T>(
    searchTerm: string,
    searchColumns: string[],
    page: number = 1,
    pageSize: number = 20,
    additionalFilters: Record<string, any> = {}
  ): Promise<PaginatedResponse<T>> {
    const from = (page - 1) * pageSize;
    const to = from + pageSize - 1;

    let query = supabase
      .from(this.tableName)
      .select('*', { count: 'exact' })
      .range(from, to);

    // Add text search conditions
    if (searchTerm) {
      const searchCondition = searchColumns
        .map(col => `${col}.ilike.%${searchTerm}%`)
        .join(',');
      query = query.or(searchCondition);
    }

    // Apply additional filters
    Object.entries(additionalFilters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        query = query.eq(key, value);
      }
    });

    return this.handlePaginatedResponse<T>(
      query,
      `search ${this.tableName}`,
      page,
      pageSize
    );
  }

  /**
   * Count records with optional filters
   */
  protected async count(filters: Record<string, any> = {}): Promise<number> {
    let query = supabase
      .from(this.tableName)
      .select('*', { count: 'exact', head: true });

    // Apply filters
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        query = query.eq(key, value);
      }
    });

    const { count } = await query;
    return count || 0;
  }

  /**
   * Subscribe to real-time changes
   */
  protected subscribeToChanges<T>(
    callback: (payload: any) => void,
    filter?: string
  ) {
    const channel = supabase
      .channel(`${this.tableName}-changes`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: this.tableName,
          filter: filter
        },
        callback
      )
      .subscribe();

    return channel;
  }

  /**
   * Unsubscribe from real-time changes
   */
  protected unsubscribeFromChanges(channel: any) {
    return supabase.removeChannel(channel);
  }
}

/**
 * Utility functions for common operations
 */
export const ApiUtils = {
  /**
   * Format date for database queries
   */
  formatDate: (date: Date): string => {
    return date.toISOString();
  },

  /**
   * Parse database date
   */
  parseDate: (dateString: string): Date => {
    return new Date(dateString);
  },

  /**
   * Validate UUID format
   */
  isValidUUID: (uuid: string): boolean => {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
  },

  /**
   * Sanitize search term
   */
  sanitizeSearchTerm: (term: string): string => {
    return term.replace(/[%_]/g, '\\$&').trim();
  },

  /**
   * Build filter object from URL params
   */
  buildFiltersFromParams: (params: URLSearchParams): Record<string, any> => {
    const filters: Record<string, any> = {};
    
    for (const [key, value] of params.entries()) {
      if (value && value !== 'all' && value !== '') {
        filters[key] = value;
      }
    }
    
    return filters;
  },

  /**
   * Generate error message for UI display
   */
  getDisplayError: (error: string): string => {
    // Convert technical errors to user-friendly messages
    const errorMappings: Record<string, string> = {
      'JWT expired': 'Your session has expired. Please log in again.',
      'Invalid JWT': 'Authentication error. Please log in again.',
      'Row Level Security': 'You do not have permission to access this resource.',
      'duplicate key': 'This record already exists.',
      'foreign key': 'Cannot complete operation due to related data.',
      'not null': 'Required fields are missing.',
    };

    for (const [key, message] of Object.entries(errorMappings)) {
      if (error.includes(key)) {
        return message;
      }
    }

    return error;
  }
};
