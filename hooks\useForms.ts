import { useState, useEffect } from 'react';
import { Form } from '@/types';

// Simple ID generator for demo purposes
const generateId = () => Math.random().toString(36).substr(2, 9);

// Sample data for demo purposes
const SAMPLE_FORMS: Form[] = [
  {
    id: 'form1',
    projectId: '1',
    name: 'Tree Species Inventory',
    description: 'Document tree species, diameter, height, and health',
    version: 1,
    createdAt: Date.now() - 900000,
    updatedAt: Date.now() - 400000,
    createdBy: '1',
    status: 'published',
    schema: {
      sections: [
        {
          id: 'section1',
          title: 'Tree Identification',
          questions: [
            {
              id: 'species',
              type: 'text',
              label: 'Species Name',
              required: true,
              placeholder: 'e.g., Douglas Fir',
            },
            {
              id: 'diameter',
              type: 'number',
              label: 'Trunk Diameter (cm)',
              required: true,
              validation: [
                {
                  type: 'min',
                  value: 1,
                  message: 'Diameter must be positive'
                }
              ]
            },
            {
              id: 'height',
              type: 'number',
              label: 'Approximate Height (m)',
              required: false,
            },
          ]
        },
        {
          id: 'section2',
          title: 'Health Assessment',
          questions: [
            {
              id: 'health',
              type: 'select',
              label: 'Tree Health',
              required: true,
              options: [
                { label: 'Healthy', value: 'healthy' },
                { label: 'Minor Issues', value: 'minor_issues' },
                { label: 'Significant Problems', value: 'significant_problems' },
                { label: 'Dead/Dying', value: 'dead_dying' },
              ]
            },
            {
              id: 'notes',
              type: 'text',
              label: 'Notes',
              required: false,
              placeholder: 'Additional observations...',
            },
            {
              id: 'photo',
              type: 'photo',
              label: 'Tree Photo',
              required: true,
            }
          ]
        }
      ]
    }
  },
  {
    id: 'form2',
    projectId: '1',
    name: 'Wildlife Observation',
    description: 'Record animal sightings and behaviors',
    version: 1,
    createdAt: Date.now() - 800000,
    updatedAt: Date.now() - 300000,
    createdBy: '1',
    status: 'published',
    schema: {
      sections: [
        {
          id: 'section1',
          title: 'Species Information',
          questions: [
            {
              id: 'species',
              type: 'text',
              label: 'Species Name',
              required: true,
              placeholder: 'e.g., Black Bear',
            },
            {
              id: 'count',
              type: 'number',
              label: 'Number of Individuals',
              required: true,
              validation: [
                {
                  type: 'min',
                  value: 1,
                  message: 'Count must be at least 1'
                }
              ]
            },
          ]
        },
        {
          id: 'section2',
          title: 'Observation Details',
          questions: [
            {
              id: 'behavior',
              type: 'multiselect',
              label: 'Observed Behaviors',
              required: true,
              options: [
                { label: 'Feeding', value: 'feeding' },
                { label: 'Resting', value: 'resting' },
                { label: 'Traveling', value: 'traveling' },
                { label: 'Mating', value: 'mating' },
                { label: 'Nesting', value: 'nesting' },
              ]
            },
            {
              id: 'photo',
              type: 'photo',
              label: 'Photo Evidence',
              required: false,
            },
            {
              id: 'notes',
              type: 'text',
              label: 'Additional Notes',
              required: false,
            }
          ]
        }
      ]
    }
  },
  {
    id: 'form3',
    projectId: '2',
    name: 'Water Quality Sample',
    description: 'Collect water quality parameters and sample information',
    version: 1,
    createdAt: Date.now() - 700000,
    updatedAt: Date.now() - 200000,
    createdBy: '2',
    status: 'published',
    schema: {
      sections: [
        {
          id: 'section1',
          title: 'Sample Information',
          questions: [
            {
              id: 'sample_id',
              type: 'text',
              label: 'Sample ID',
              required: true,
              placeholder: 'e.g., WQ-2023-001',
            },
            {
              id: 'location',
              type: 'location',
              label: 'Sample Location',
              required: true,
            },
            {
              id: 'date_time',
              type: 'datetime',
              label: 'Collection Date & Time',
              required: true,
            },
          ]
        },
        {
          id: 'section2',
          title: 'Water Parameters',
          questions: [
            {
              id: 'temperature',
              type: 'number',
              label: 'Temperature (°C)',
              required: true,
            },
            {
              id: 'ph',
              type: 'number',
              label: 'pH',
              required: true,
              validation: [
                {
                  type: 'min',
                  value: 0,
                  message: 'pH must be between 0 and 14'
                },
                {
                  type: 'max',
                  value: 14,
                  message: 'pH must be between 0 and 14'
                }
              ]
            },
            {
              id: 'turbidity',
              type: 'number',
              label: 'Turbidity (NTU)',
              required: true,
            },
            {
              id: 'photo',
              type: 'photo',
              label: 'Sample Photo',
              required: true,
            }
          ]
        }
      ]
    }
  }
];

export function useForms() {
  const [forms, setForms] = useState<Form[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  
  const fetchForms = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // In a real app, this would be an API call or database query
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setForms(SAMPLE_FORMS);
    } catch (err) {
      console.error('Error fetching forms:', err);
      setError(err instanceof Error ? err : new Error('Unknown error occurred'));
    } finally {
      setLoading(false);
    }
  };
  
  const getFormById = (id: string) => {
    return forms.find(form => form.id === id) || null;
  };
  
  const getFormsByProject = (projectId: string) => {
    return forms.filter(form => form.projectId === projectId);
  };
  
  const createForm = async (form: Omit<Form, 'id' | 'createdAt' | 'updatedAt' | 'version'>) => {
    try {
      const newForm: Form = {
        ...form,
        id: generateId(),
        createdAt: Date.now(),
        updatedAt: Date.now(),
        version: 1,
      };
      
      // In a real app, this would be an API call or database insertion
      setForms(prev => [...prev, newForm]);
      return newForm;
    } catch (err) {
      console.error('Error creating form:', err);
      throw err;
    }
  };
  
  const updateForm = async (id: string, updates: Partial<Omit<Form, 'id' | 'createdAt' | 'createdBy' | 'version'>>) => {
    try {
      // In a real app, this would be an API call or database update
      setForms(prev => 
        prev.map(form => 
          form.id === id 
            ? { 
                ...form, 
                ...updates, 
                updatedAt: Date.now(),
                version: form.version + (updates.schema ? 1 : 0) // Increment version if schema changes
              }
            : form
        )
      );
    } catch (err) {
      console.error('Error updating form:', err);
      throw err;
    }
  };
  
  const deleteForm = async (id: string) => {
    try {
      // In a real app, this would be an API call or database deletion
      setForms(prev => prev.filter(form => form.id !== id));
    } catch (err) {
      console.error('Error deleting form:', err);
      throw err;
    }
  };
  
  // Load forms on mount
  useEffect(() => {
    fetchForms();
  }, []);
  
  return {
    forms,
    loading,
    error,
    fetchForms,
    getFormById,
    getFormsByProject,
    createForm,
    updateForm,
    deleteForm,
  };
}