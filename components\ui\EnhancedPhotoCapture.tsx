import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Image,
  ScrollView,
  Platform,
} from 'react-native';
import { useTheme } from '@/hooks/useTheme';
import { Camera, Trash2, Plus, Image as ImageIcon, Grid, X } from 'lucide-react-native';

interface EnhancedPhotoCaptureProps {
  value?: string | string[]; // Single URI or array of URIs
  onChange: (uri: string | string[] | null) => void;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  allowMultiple?: boolean;
  maxCount?: number;
  label?: string;
}

export default function EnhancedPhotoCapture({
  value,
  onChange,
  placeholder = 'Take photos',
  required = false,
  disabled = false,
  allowMultiple = false,
  maxCount = 5,
  label,
}: EnhancedPhotoCaptureProps) {
  const { theme } = useTheme();
  const [photos, setPhotos] = useState<string[]>([]);

  useEffect(() => {
    if (value) {
      if (typeof value === 'string') {
        setPhotos([value]);
      } else if (Array.isArray(value)) {
        setPhotos(value);
      }
    } else {
      setPhotos([]);
    }
  }, [value]);

  const updatePhotos = (newPhotos: string[]) => {
    setPhotos(newPhotos);
    if (allowMultiple) {
      onChange(newPhotos.length > 0 ? newPhotos : null);
    } else {
      onChange(newPhotos.length > 0 ? newPhotos[0] : null);
    }
  };

  const handlePhotoCapture = async () => {
    if (photos.length >= maxCount && allowMultiple) {
      Alert.alert('Maximum Photos', `You can only add up to ${maxCount} photos.`);
      return;
    }

    try {
      let result;
      
      if (Platform.OS === 'web') {
        // For web, create a file input
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = 'image/*';
        input.multiple = allowMultiple && photos.length < maxCount - 1;
        
        const promise = new Promise<string[]>((resolve) => {
          input.onchange = (event) => {
            const files = (event.target as HTMLInputElement).files;
            if (files) {
              const filePromises = Array.from(files).map(file => {
                return new Promise<string>((fileResolve) => {
                  const reader = new FileReader();
                  reader.onload = (e) => {
                    fileResolve(e.target?.result as string);
                  };
                  reader.readAsDataURL(file);
                });
              });
              
              Promise.all(filePromises).then(resolve);
            } else {
              resolve([]);
            }
          };
        });
        
        input.click();
        const newPhotoUris = await promise;
        
        if (newPhotoUris.length > 0) {
          const updatedPhotos = allowMultiple 
            ? [...photos, ...newPhotoUris].slice(0, maxCount)
            : [newPhotoUris[0]];
          updatePhotos(updatedPhotos);
        }
      } else {
        // For native platforms
        const { Camera } = await import('expo-camera');
        const ImagePicker = await import('expo-image-picker');
        
        const { status } = await ImagePicker.requestCameraPermissionsAsync();
        if (status !== 'granted') {
          Alert.alert('Permission needed', 'Camera permission is required to take photos');
          return;
        }

        Alert.alert(
          'Select Photo',
          'Choose how you want to add a photo',
          [
            { text: 'Cancel', style: 'cancel' },
            { 
              text: 'Take Photo', 
              onPress: async () => {
                try {
                  const result = await ImagePicker.launchCameraAsync({
                    mediaTypes: ImagePicker.MediaTypeOptions.Images,
                    allowsEditing: true,
                    aspect: [4, 3],
                    quality: 0.8,
                  });

                  if (!result.canceled && result.assets[0]) {
                    const newPhotoUri = result.assets[0].uri;
                    const updatedPhotos = allowMultiple 
                      ? [...photos, newPhotoUri]
                      : [newPhotoUri];
                    updatePhotos(updatedPhotos);
                  }
                } catch (error) {
                  Alert.alert('Error', 'Failed to take photo');
                }
              }
            },
            { 
              text: 'Choose from Library', 
              onPress: async () => {
                try {
                  const result = await ImagePicker.launchImageLibraryAsync({
                    mediaTypes: ImagePicker.MediaTypeOptions.Images,
                    allowsEditing: true,
                    aspect: [4, 3],
                    quality: 0.8,
                    allowsMultipleSelection: allowMultiple,
                    selectionLimit: allowMultiple ? maxCount - photos.length : 1,
                  });

                  if (!result.canceled && result.assets.length > 0) {
                    const newPhotoUris = result.assets.map(asset => asset.uri);
                    const updatedPhotos = allowMultiple 
                      ? [...photos, ...newPhotoUris].slice(0, maxCount)
                      : [newPhotoUris[0]];
                    updatePhotos(updatedPhotos);
                  }
                } catch (error) {
                  Alert.alert('Error', 'Failed to select photos');
                }
              }
            },
          ]
        );
      }
    } catch (error) {
      console.error('Photo capture error:', error);
      Alert.alert('Error', 'Failed to capture photo');
    }
  };

  const removePhoto = (index: number) => {
    Alert.alert(
      'Remove Photo',
      'Are you sure you want to remove this photo?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: () => {
            const updatedPhotos = photos.filter((_, i) => i !== index);
            updatePhotos(updatedPhotos);
          },
        },
      ]
    );
  };

  const clearAllPhotos = () => {
    Alert.alert(
      'Clear All Photos',
      'Are you sure you want to remove all photos?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear All',
          style: 'destructive',
          onPress: () => updatePhotos([]),
        },
      ]
    );
  };

  const renderPhoto = (uri: string, index: number) => (
    <View key={index} style={[styles.photoContainer, { borderColor: theme.colors.border }]}>
      <Image source={{ uri }} style={styles.photo} resizeMode="cover" />
      
      <TouchableOpacity
        style={[styles.removeButton, { backgroundColor: theme.colors.error }]}
        onPress={() => removePhoto(index)}
        disabled={disabled}
      >
        <X size={16} color="white" />
      </TouchableOpacity>
      
      {allowMultiple && (
        <View style={[styles.photoIndex, { backgroundColor: theme.colors.primary }]}>
          <Text style={styles.photoIndexText}>{index + 1}</Text>
        </View>
      )}
    </View>
  );

  const renderAddButton = () => {
    if (photos.length >= maxCount && allowMultiple) return null;

    return (
      <TouchableOpacity
        style={[
          styles.addButton,
          {
            backgroundColor: theme.colors.background,
            borderColor: theme.colors.border,
            opacity: disabled ? 0.5 : 1,
          },
        ]}
        onPress={handlePhotoCapture}
        disabled={disabled}
      >
        {photos.length === 0 ? (
          <>
            <Camera size={32} color={theme.colors.muted} />
            <Text style={[styles.addButtonText, { color: theme.colors.muted }]}>
              {placeholder}
            </Text>
            {required && (
              <Text style={[styles.requiredText, { color: theme.colors.error }]}>
                Required
              </Text>
            )}
          </>
        ) : (
          <>
            <Plus size={24} color={theme.colors.primary} />
            <Text style={[styles.addMoreText, { color: theme.colors.primary }]}>
              Add Photo
            </Text>
          </>
        )}
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      {label && (
        <Text style={[styles.label, { color: theme.colors.text }]}>
          {label}
          {required && <Text style={{ color: theme.colors.error }}> *</Text>}
        </Text>
      )}

      {allowMultiple && photos.length > 0 && (
        <View style={styles.header}>
          <View style={styles.photoCount}>
            <Grid size={16} color={theme.colors.muted} />
            <Text style={[styles.countText, { color: theme.colors.muted }]}>
              {photos.length} of {maxCount} photos
            </Text>
          </View>
          
          <TouchableOpacity
            style={styles.clearAllButton}
            onPress={clearAllPhotos}
            disabled={disabled}
          >
            <Trash2 size={16} color={theme.colors.error} />
            <Text style={[styles.clearAllText, { color: theme.colors.error }]}>
              Clear All
            </Text>
          </TouchableOpacity>
        </View>
      )}

      {allowMultiple ? (
        <ScrollView 
          horizontal={photos.length > 2}
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.photoGrid}
        >
          {photos.map(renderPhoto)}
          {renderAddButton()}
        </ScrollView>
      ) : (
        photos.length > 0 ? renderPhoto(photos[0], 0) : renderAddButton()
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
  },
  label: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    marginBottom: 6,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  photoCount: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  countText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
  },
  clearAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  clearAllText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },
  photoGrid: {
    flexDirection: 'row',
    gap: 12,
    paddingVertical: 4,
  },
  photoContainer: {
    position: 'relative',
    width: 120,
    height: 90,
    borderRadius: 8,
    borderWidth: 1,
    overflow: 'hidden',
  },
  photo: {
    width: '100%',
    height: '100%',
  },
  removeButton: {
    position: 'absolute',
    top: 4,
    right: 4,
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  photoIndex: {
    position: 'absolute',
    bottom: 4,
    left: 4,
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  photoIndexText: {
    color: 'white',
    fontSize: 10,
    fontFamily: 'Inter-Bold',
  },
  addButton: {
    width: 120,
    height: 90,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderStyle: 'dashed',
    borderRadius: 8,
    gap: 4,
  },
  addButtonText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    textAlign: 'center',
  },
  addMoreText: {
    fontSize: 11,
    fontFamily: 'Inter-SemiBold',
  },
  requiredText: {
    fontSize: 10,
    fontFamily: 'Inter-Medium',
  },
});
