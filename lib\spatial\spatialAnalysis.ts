/**
 * Advanced Spatial Analysis Library for FieldSyncPro
 * 
 * This module provides comprehensive spatial analysis capabilities
 * following software engineering excellence principles with:
 * - Type safety and validation
 * - Performance optimization
 * - Error handling
 * - Extensible architecture
 */

export interface Coordinate {
  latitude: number;
  longitude: number;
  elevation?: number;
}

export interface Geometry {
  type: 'Point' | 'LineString' | 'Polygon' | 'MultiPoint' | 'MultiLineString' | 'MultiPolygon';
  coordinates: number[] | number[][] | number[][][];
}

export interface Feature {
  id: string;
  type: 'Feature';
  geometry: Geometry;
  properties: Record<string, any>;
}

export interface FeatureCollection {
  type: 'FeatureCollection';
  features: Feature[];
}

export interface AnalysisOptions {
  units?: 'meters' | 'kilometers' | 'feet' | 'miles' | 'nautical';
  precision?: number;
  tolerance?: number;
  maxFeatures?: number;
}

export interface BufferOptions extends AnalysisOptions {
  distance: number;
  segments?: number;
  dissolve?: boolean;
  endCapStyle?: 'round' | 'square' | 'flat';
  joinStyle?: 'round' | 'mitre' | 'bevel';
}

export interface ClipOptions extends AnalysisOptions {
  preserveAttributes?: boolean;
  includePartial?: boolean;
}

export interface IntersectionOptions extends AnalysisOptions {
  outputType?: 'input' | 'overlay' | 'intersection';
  attributeJoin?: 'all' | 'input' | 'overlay';
}

export interface ProximityOptions extends AnalysisOptions {
  searchRadius?: number;
  maxResults?: number;
  includeDistance?: boolean;
  includeBearing?: boolean;
}

export interface GridOptions extends AnalysisOptions {
  cellSize: number;
  gridType?: 'square' | 'rectangle' | 'hexagon' | 'triangle';
  rotation?: number;
  extent?: {
    minX: number;
    minY: number;
    maxX: number;
    maxY: number;
  };
}

export interface SpatialJoinOptions extends AnalysisOptions {
  spatialRelation?: 'intersects' | 'contains' | 'within' | 'touches' | 'crosses' | 'overlaps';
  joinOperation?: 'one_to_one' | 'one_to_many';
  keepAll?: boolean;
  summaryStatistics?: string[];
}

// Constants for spatial calculations
export const EARTH_RADIUS_METERS = 6371000;
export const DEG_TO_RAD = Math.PI / 180;
export const RAD_TO_DEG = 180 / Math.PI;

// Unit conversion factors to meters
export const UNIT_FACTORS = {
  meters: 1,
  kilometers: 1000,
  feet: 0.3048,
  miles: 1609.344,
  nautical: 1852,
};

/**
 * Enhanced Spatial Analysis Engine
 * Provides professional-grade spatial operations with performance optimization
 */
export class EnhancedSpatialAnalysis {
  private maxFeatures: number;
  private defaultPrecision: number;

  constructor(maxFeatures = 25000, defaultPrecision = 6) {
    this.maxFeatures = maxFeatures;
    this.defaultPrecision = defaultPrecision;
  }

  /**
   * Calculate accurate distance between two coordinates using Haversine formula
   */
  calculateDistance(from: Coordinate, to: Coordinate, units: string = 'meters'): number {
    const φ1 = from.latitude * DEG_TO_RAD;
    const φ2 = to.latitude * DEG_TO_RAD;
    const Δφ = (to.latitude - from.latitude) * DEG_TO_RAD;
    const Δλ = (to.longitude - from.longitude) * DEG_TO_RAD;

    const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
            Math.cos(φ1) * Math.cos(φ2) *
            Math.sin(Δλ/2) * Math.sin(Δλ/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

    const distanceMeters = EARTH_RADIUS_METERS * c;
    return this.convertDistance(distanceMeters, 'meters', units);
  }

  /**
   * Calculate area of a polygon using Shoelace formula with spherical correction
   */
  calculateArea(coordinates: Coordinate[], units: string = 'meters'): number {
    if (coordinates.length < 3) return 0;

    let area = 0;
    const n = coordinates.length;
    
    for (let i = 0; i < n; i++) {
      const j = (i + 1) % n;
      const lat1 = coordinates[i].latitude * DEG_TO_RAD;
      const lat2 = coordinates[j].latitude * DEG_TO_RAD;
      const lng1 = coordinates[i].longitude * DEG_TO_RAD;
      const lng2 = coordinates[j].longitude * DEG_TO_RAD;
      
      area += (lng2 - lng1) * (2 + Math.sin(lat1) + Math.sin(lat2));
    }
    
    area = Math.abs(area * EARTH_RADIUS_METERS * EARTH_RADIUS_METERS / 2);
    
    const factor = UNIT_FACTORS[units as keyof typeof UNIT_FACTORS] || 1;
    return area / (factor * factor);
  }

  /**
   * Calculate bearing between two coordinates
   */
  calculateBearing(from: Coordinate, to: Coordinate): number {
    const φ1 = from.latitude * DEG_TO_RAD;
    const φ2 = to.latitude * DEG_TO_RAD;
    const Δλ = (to.longitude - from.longitude) * DEG_TO_RAD;

    const y = Math.sin(Δλ) * Math.cos(φ2);
    const x = Math.cos(φ1) * Math.sin(φ2) - 
              Math.sin(φ1) * Math.cos(φ2) * Math.cos(Δλ);

    let bearing = Math.atan2(y, x) * RAD_TO_DEG;
    return (bearing + 360) % 360;
  }

  /**
   * Create buffer around features
   */
  async createBuffer(
    features: Feature[], 
    options: BufferOptions
  ): Promise<{ results: Feature[]; metadata: any }> {
    try {
      this.validateFeatureCount(features);
      
      const results: Feature[] = [];
      const segments = options.segments || 16;
      const dissolve = options.dissolve || false;
      
      for (const feature of features) {
        const buffered = await this.bufferFeature(feature, options, segments);
        if (buffered) {
          results.push(buffered);
        }
      }

      // Dissolve overlapping buffers if requested
      const finalResults = dissolve ? await this.dissolveFeatures(results) : results;

      return {
        results: finalResults,
        metadata: {
          inputCount: features.length,
          outputCount: finalResults.length,
          bufferDistance: options.distance,
          units: options.units || 'meters',
          dissolved: dissolve,
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      throw new Error(`Buffer analysis failed: ${error}`);
    }
  }

  /**
   * Clip features by boundary
   */
  async clipFeatures(
    inputFeatures: Feature[],
    clipBoundary: Feature,
    options: ClipOptions = {}
  ): Promise<{ results: Feature[]; metadata: any }> {
    try {
      this.validateFeatureCount(inputFeatures);
      
      const results: Feature[] = [];
      
      for (const feature of inputFeatures) {
        const clipped = await this.clipFeature(feature, clipBoundary, options);
        if (clipped) {
          results.push(clipped);
        }
      }

      return {
        results,
        metadata: {
          inputCount: inputFeatures.length,
          outputCount: results.length,
          clipBoundaryType: clipBoundary.geometry.type,
          preserveAttributes: options.preserveAttributes !== false,
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      throw new Error(`Clip analysis failed: ${error}`);
    }
  }

  /**
   * Find intersections between two sets of features
   */
  async findIntersections(
    inputFeatures: Feature[],
    overlayFeatures: Feature[],
    options: IntersectionOptions = {}
  ): Promise<{ results: Feature[]; metadata: any }> {
    try {
      this.validateFeatureCount([...inputFeatures, ...overlayFeatures]);
      
      const results: Feature[] = [];
      
      for (const inputFeature of inputFeatures) {
        for (const overlayFeature of overlayFeatures) {
          const intersection = await this.intersectFeatures(inputFeature, overlayFeature, options);
          if (intersection) {
            results.push(intersection);
          }
        }
      }

      return {
        results,
        metadata: {
          inputCount: inputFeatures.length,
          overlayCount: overlayFeatures.length,
          intersectionCount: results.length,
          outputType: options.outputType || 'intersection',
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      throw new Error(`Intersection analysis failed: ${error}`);
    }
  }

  /**
   * Perform spatial join based on spatial relationship
   */
  async spatialJoin(
    targetFeatures: Feature[],
    joinFeatures: Feature[],
    options: SpatialJoinOptions = {}
  ): Promise<{ results: Feature[]; metadata: any }> {
    try {
      this.validateFeatureCount([...targetFeatures, ...joinFeatures]);
      
      const results: Feature[] = [];
      const relation = options.spatialRelation || 'intersects';
      
      for (const targetFeature of targetFeatures) {
        const matches = joinFeatures.filter(joinFeature => 
          this.checkSpatialRelation(targetFeature, joinFeature, relation)
        );
        
        if (matches.length > 0 || options.keepAll) {
          const joinedFeature = this.joinFeatureAttributes(targetFeature, matches, options);
          results.push(joinedFeature);
        }
      }

      return {
        results,
        metadata: {
          targetCount: targetFeatures.length,
          joinCount: joinFeatures.length,
          resultCount: results.length,
          spatialRelation: relation,
          joinOperation: options.joinOperation || 'one_to_one',
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      throw new Error(`Spatial join failed: ${error}`);
    }
  }

  /**
   * Find nearest neighbors
   */
  async findNearestNeighbors(
    sourceFeatures: Feature[],
    targetFeatures: Feature[],
    options: ProximityOptions = {}
  ): Promise<{ results: Feature[]; metadata: any }> {
    try {
      this.validateFeatureCount([...sourceFeatures, ...targetFeatures]);
      
      const results: Feature[] = [];
      const maxResults = options.maxResults || 1;
      const searchRadius = options.searchRadius;
      
      for (const sourceFeature of sourceFeatures) {
        const neighbors = await this.findNeighborsForFeature(
          sourceFeature,
          targetFeatures,
          options
        );
        
        // Limit results
        const limitedNeighbors = neighbors.slice(0, maxResults);
        
        // Filter by search radius if specified
        const filteredNeighbors = searchRadius 
          ? limitedNeighbors.filter(n => n.distance <= searchRadius)
          : limitedNeighbors;
        
        results.push(...filteredNeighbors);
      }

      return {
        results,
        metadata: {
          sourceCount: sourceFeatures.length,
          targetCount: targetFeatures.length,
          neighborCount: results.length,
          maxResults,
          searchRadius,
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      throw new Error(`Nearest neighbor analysis failed: ${error}`);
    }
  }

  /**
   * Create regular grid
   */
  async createGrid(options: GridOptions): Promise<{ results: Feature[]; metadata: any }> {
    try {
      const { cellSize, gridType = 'square', rotation = 0, extent } = options;
      
      if (!extent) {
        throw new Error('Grid extent is required');
      }
      
      const results: Feature[] = [];
      const { minX, minY, maxX, maxY } = extent;
      
      const cellSizeMeters = this.convertDistance(cellSize, options.units || 'meters', 'meters');
      const cellSizeDegrees = cellSizeMeters / (EARTH_RADIUS_METERS * DEG_TO_RAD);
      
      const cols = Math.ceil((maxX - minX) / cellSizeDegrees);
      const rows = Math.ceil((maxY - minY) / cellSizeDegrees);
      
      for (let row = 0; row < rows; row++) {
        for (let col = 0; col < cols; col++) {
          const cell = this.createGridCell(
            minX + col * cellSizeDegrees,
            minY + row * cellSizeDegrees,
            cellSizeDegrees,
            gridType,
            rotation,
            `${row}_${col}`
          );
          results.push(cell);
        }
      }

      return {
        results,
        metadata: {
          gridType,
          cellSize,
          units: options.units || 'meters',
          cellCount: results.length,
          rows,
          cols,
          extent,
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      throw new Error(`Grid creation failed: ${error}`);
    }
  }

  /**
   * Dissolve features based on attributes
   */
  async dissolveFeatures(
    features: Feature[],
    dissolveField?: string
  ): Promise<Feature[]> {
    try {
      if (!dissolveField) {
        // Simple union of all features
        return this.unionAllFeatures(features);
      }
      
      // Group by dissolve field
      const groups = new Map<string, Feature[]>();
      
      for (const feature of features) {
        const key = feature.properties[dissolveField]?.toString() || 'null';
        if (!groups.has(key)) {
          groups.set(key, []);
        }
        groups.get(key)!.push(feature);
      }
      
      const results: Feature[] = [];
      
      for (const [key, groupFeatures] of groups) {
        const dissolved = await this.unionFeatures(groupFeatures, key);
        if (dissolved) {
          results.push(dissolved);
        }
      }
      
      return results;
    } catch (error) {
      throw new Error(`Dissolve operation failed: ${error}`);
    }
  }

  // Private helper methods

  private validateFeatureCount(features: Feature[]): void {
    if (features.length > this.maxFeatures) {
      throw new Error(`Too many features: ${features.length}. Maximum allowed: ${this.maxFeatures}`);
    }
  }

  private convertDistance(value: number, fromUnit: string, toUnit: string): number {
    const fromFactor = UNIT_FACTORS[fromUnit as keyof typeof UNIT_FACTORS] || 1;
    const toFactor = UNIT_FACTORS[toUnit as keyof typeof UNIT_FACTORS] || 1;
    return (value * fromFactor) / toFactor;
  }

  private async bufferFeature(
    feature: Feature, 
    options: BufferOptions, 
    segments: number
  ): Promise<Feature | null> {
    // Simplified buffer implementation - in production, use a proper geometry library
    try {
      const geometry = feature.geometry;
      const distanceMeters = this.convertDistance(options.distance, options.units || 'meters', 'meters');
      const distanceDegrees = distanceMeters / (EARTH_RADIUS_METERS * DEG_TO_RAD);
      
      if (geometry.type === 'Point') {
        return this.bufferPoint(feature, distanceDegrees, segments);
      } else if (geometry.type === 'LineString') {
        return this.bufferLineString(feature, distanceDegrees, segments);
      } else if (geometry.type === 'Polygon') {
        return this.bufferPolygon(feature, distanceDegrees, segments);
      }
      
      return null;
    } catch (error) {
      console.warn(`Failed to buffer feature ${feature.id}:`, error);
      return null;
    }
  }

  private bufferPoint(feature: Feature, distance: number, segments: number): Feature {
    const [lng, lat] = feature.geometry.coordinates as number[];
    const coordinates: number[][] = [];
    
    for (let i = 0; i <= segments; i++) {
      const angle = (i * 2 * Math.PI) / segments;
      const bufferLng = lng + distance * Math.cos(angle);
      const bufferLat = lat + distance * Math.sin(angle);
      coordinates.push([bufferLng, bufferLat]);
    }
    
    return {
      ...feature,
      id: `${feature.id}_buffer`,
      geometry: {
        type: 'Polygon',
        coordinates: [coordinates],
      },
      properties: {
        ...feature.properties,
        buffer_distance: distance,
        original_feature_id: feature.id,
      },
    };
  }

  private bufferLineString(feature: Feature, distance: number, segments: number): Feature {
    // Simplified line buffer - creates rectangular buffer around line
    const coords = feature.geometry.coordinates as number[][];
    const bufferedCoords: number[][] = [];
    
    // This is a simplified implementation
    // In production, use a proper geometry library like Turf.js or JSTS
    for (const coord of coords) {
      bufferedCoords.push([coord[0] - distance, coord[1] - distance]);
      bufferedCoords.push([coord[0] + distance, coord[1] - distance]);
      bufferedCoords.push([coord[0] + distance, coord[1] + distance]);
      bufferedCoords.push([coord[0] - distance, coord[1] + distance]);
    }
    
    return {
      ...feature,
      id: `${feature.id}_buffer`,
      geometry: {
        type: 'Polygon',
        coordinates: [bufferedCoords],
      },
      properties: {
        ...feature.properties,
        buffer_distance: distance,
        original_feature_id: feature.id,
      },
    };
  }

  private bufferPolygon(feature: Feature, distance: number, segments: number): Feature {
    // Simplified polygon buffer - expands polygon outward
    const coords = feature.geometry.coordinates as number[][][];
    const bufferedCoords = coords.map(ring => 
      ring.map(coord => [coord[0] + distance, coord[1] + distance])
    );
    
    return {
      ...feature,
      id: `${feature.id}_buffer`,
      geometry: {
        type: 'Polygon',
        coordinates: bufferedCoords,
      },
      properties: {
        ...feature.properties,
        buffer_distance: distance,
        original_feature_id: feature.id,
      },
    };
  }

  private async clipFeature(
    feature: Feature,
    clipBoundary: Feature,
    options: ClipOptions
  ): Promise<Feature | null> {
    // Simplified clipping implementation
    try {
      // Check if feature intersects with clip boundary
      if (this.checkSpatialRelation(feature, clipBoundary, 'intersects')) {
        return {
          ...feature,
          id: `${feature.id}_clipped`,
          properties: options.preserveAttributes !== false 
            ? { ...feature.properties, clipped: true }
            : { clipped: true },
        };
      }
      return null;
    } catch (error) {
      console.warn(`Failed to clip feature ${feature.id}:`, error);
      return null;
    }
  }

  private async intersectFeatures(
    feature1: Feature,
    feature2: Feature,
    options: IntersectionOptions
  ): Promise<Feature | null> {
    // Simplified intersection implementation
    try {
      if (this.checkSpatialRelation(feature1, feature2, 'intersects')) {
        const outputType = options.outputType || 'intersection';
        
        let baseFeature: Feature;
        let attributes: Record<string, any> = {};
        
        switch (outputType) {
          case 'input':
            baseFeature = feature1;
            break;
          case 'overlay':
            baseFeature = feature2;
            break;
          default:
            baseFeature = feature1; // Use input as base for intersection
        }
        
        // Merge attributes based on join type
        switch (options.attributeJoin) {
          case 'input':
            attributes = feature1.properties;
            break;
          case 'overlay':
            attributes = feature2.properties;
            break;
          default:
            attributes = { ...feature1.properties, ...feature2.properties };
        }
        
        return {
          ...baseFeature,
          id: `${feature1.id}_${feature2.id}_intersect`,
          properties: attributes,
        };
      }
      return null;
    } catch (error) {
      console.warn(`Failed to intersect features ${feature1.id} and ${feature2.id}:`, error);
      return null;
    }
  }

  private checkSpatialRelation(
    feature1: Feature,
    feature2: Feature,
    relation: string
  ): boolean {
    // Simplified spatial relationship check
    // In production, use a proper geometry library
    
    const bounds1 = this.getFeatureBounds(feature1);
    const bounds2 = this.getFeatureBounds(feature2);
    
    switch (relation) {
      case 'intersects':
        return this.boundsIntersect(bounds1, bounds2);
      case 'contains':
        return this.boundsContains(bounds1, bounds2);
      case 'within':
        return this.boundsContains(bounds2, bounds1);
      default:
        return this.boundsIntersect(bounds1, bounds2);
    }
  }

  private getFeatureBounds(feature: Feature): { minX: number; minY: number; maxX: number; maxY: number } {
    const coords = this.getAllCoordinates(feature.geometry);
    
    let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
    
    for (const coord of coords) {
      minX = Math.min(minX, coord[0]);
      minY = Math.min(minY, coord[1]);
      maxX = Math.max(maxX, coord[0]);
      maxY = Math.max(maxY, coord[1]);
    }
    
    return { minX, minY, maxX, maxY };
  }

  private getAllCoordinates(geometry: Geometry): number[][] {
    switch (geometry.type) {
      case 'Point':
        return [geometry.coordinates as number[]];
      case 'LineString':
      case 'MultiPoint':
        return geometry.coordinates as number[][];
      case 'Polygon':
      case 'MultiLineString':
        return (geometry.coordinates as number[][][]).flat();
      case 'MultiPolygon':
        return (geometry.coordinates as number[][][][]).flat(2);
      default:
        return [];
    }
  }

  private boundsIntersect(
    bounds1: { minX: number; minY: number; maxX: number; maxY: number },
    bounds2: { minX: number; minY: number; maxX: number; maxY: number }
  ): boolean {
    return !(bounds1.maxX < bounds2.minX || bounds1.minX > bounds2.maxX ||
             bounds1.maxY < bounds2.minY || bounds1.minY > bounds2.maxY);
  }

  private boundsContains(
    container: { minX: number; minY: number; maxX: number; maxY: number },
    contained: { minX: number; minY: number; maxX: number; maxY: number }
  ): boolean {
    return container.minX <= contained.minX && container.minY <= contained.minY &&
           container.maxX >= contained.maxX && container.maxY >= contained.maxY;
  }

  private joinFeatureAttributes(
    targetFeature: Feature,
    joinFeatures: Feature[],
    options: SpatialJoinOptions
  ): Feature {
    let joinedProperties = { ...targetFeature.properties };
    
    if (joinFeatures.length > 0) {
      if (options.joinOperation === 'one_to_many') {
        // Add array of all matching feature properties
        joinedProperties.joined_features = joinFeatures.map(f => f.properties);
      } else {
        // Take first match for one-to-one
        joinedProperties = { ...joinedProperties, ...joinFeatures[0].properties };
      }
      
      // Add summary statistics if requested
      if (options.summaryStatistics) {
        for (const field of options.summaryStatistics) {
          const values = joinFeatures
            .map(f => f.properties[field])
            .filter(v => typeof v === 'number');
          
          if (values.length > 0) {
            joinedProperties[`${field}_count`] = values.length;
            joinedProperties[`${field}_sum`] = values.reduce((a, b) => a + b, 0);
            joinedProperties[`${field}_avg`] = values.reduce((a, b) => a + b, 0) / values.length;
            joinedProperties[`${field}_min`] = Math.min(...values);
            joinedProperties[`${field}_max`] = Math.max(...values);
          }
        }
      }
    }
    
    return {
      ...targetFeature,
      id: `${targetFeature.id}_joined`,
      properties: joinedProperties,
    };
  }

  private async findNeighborsForFeature(
    sourceFeature: Feature,
    targetFeatures: Feature[],
    options: ProximityOptions
  ): Promise<Feature[]> {
    const sourceCentroid = this.getFeatureCentroid(sourceFeature);
    const neighbors: Array<Feature & { distance: number; bearing?: number }> = [];
    
    for (const targetFeature of targetFeatures) {
      if (sourceFeature.id === targetFeature.id) continue;
      
      const targetCentroid = this.getFeatureCentroid(targetFeature);
      const distance = this.calculateDistance(sourceCentroid, targetCentroid, options.units || 'meters');
      
      let bearing: number | undefined;
      if (options.includeBearing) {
        bearing = this.calculateBearing(sourceCentroid, targetCentroid);
      }
      
      const neighborFeature = {
        ...targetFeature,
        distance,
        bearing,
        properties: {
          ...targetFeature.properties,
          distance,
          ...(bearing !== undefined && { bearing }),
          source_feature_id: sourceFeature.id,
        },
      };
      
      neighbors.push(neighborFeature);
    }
    
    // Sort by distance
    neighbors.sort((a, b) => a.distance - b.distance);
    
    return neighbors;
  }

  private getFeatureCentroid(feature: Feature): Coordinate {
    const bounds = this.getFeatureBounds(feature);
    return {
      latitude: (bounds.minY + bounds.maxY) / 2,
      longitude: (bounds.minX + bounds.maxX) / 2,
    };
  }

  private createGridCell(
    x: number,
    y: number,
    size: number,
    gridType: string,
    rotation: number,
    id: string
  ): Feature {
    let coordinates: number[][];
    
    switch (gridType) {
      case 'square':
      case 'rectangle':
        coordinates = [
          [x, y],
          [x + size, y],
          [x + size, y + size],
          [x, y + size],
          [x, y], // Close the polygon
        ];
        break;
      case 'hexagon':
        coordinates = this.createHexagonCoordinates(x + size/2, y + size/2, size/2);
        break;
      case 'triangle':
        coordinates = [
          [x, y],
          [x + size, y],
          [x + size/2, y + size],
          [x, y], // Close the polygon
        ];
        break;
      default:
        coordinates = [
          [x, y],
          [x + size, y],
          [x + size, y + size],
          [x, y + size],
          [x, y],
        ];
    }
    
    // Apply rotation if needed
    if (rotation !== 0) {
      const centerX = x + size/2;
      const centerY = y + size/2;
      coordinates = coordinates.map(coord => 
        this.rotatePoint(coord[0], coord[1], centerX, centerY, rotation)
      );
    }
    
    return {
      id: `grid_cell_${id}`,
      type: 'Feature',
      geometry: {
        type: 'Polygon',
        coordinates: [coordinates],
      },
      properties: {
        grid_id: id,
        grid_type: gridType,
        cell_size: size,
        center_x: x + size/2,
        center_y: y + size/2,
      },
    };
  }

  private createHexagonCoordinates(centerX: number, centerY: number, radius: number): number[][] {
    const coordinates: number[][] = [];
    for (let i = 0; i < 6; i++) {
      const angle = (i * Math.PI) / 3;
      const x = centerX + radius * Math.cos(angle);
      const y = centerY + radius * Math.sin(angle);
      coordinates.push([x, y]);
    }
    coordinates.push(coordinates[0]); // Close the polygon
    return coordinates;
  }

  private rotatePoint(x: number, y: number, centerX: number, centerY: number, angle: number): number[] {
    const radians = angle * DEG_TO_RAD;
    const cos = Math.cos(radians);
    const sin = Math.sin(radians);
    
    const dx = x - centerX;
    const dy = y - centerY;
    
    return [
      centerX + dx * cos - dy * sin,
      centerY + dx * sin + dy * cos,
    ];
  }

  private async unionAllFeatures(features: Feature[]): Promise<Feature[]> {
    // Simplified union implementation
    if (features.length === 0) return [];
    if (features.length === 1) return features;
    
    // In a real implementation, use a proper geometry library
    const unionFeature: Feature = {
      id: `union_${Date.now()}`,
      type: 'Feature',
      geometry: features[0].geometry, // Simplified - just use first geometry
      properties: {
        union_of: features.map(f => f.id),
        feature_count: features.length,
        union_created: new Date().toISOString(),
      },
    };
    
    return [unionFeature];
  }

  private async unionFeatures(features: Feature[], groupKey: string): Promise<Feature | null> {
    if (features.length === 0) return null;
    
    const union = await this.unionAllFeatures(features);
    if (union.length > 0) {
      union[0].id = `dissolved_${groupKey}`;
      union[0].properties.dissolve_field_value = groupKey;
    }
    
    return union[0] || null;
  }
}

/**
 * Factory function to create a spatial analysis instance
 */
export function createSpatialAnalysis(maxFeatures?: number, precision?: number): EnhancedSpatialAnalysis {
  return new EnhancedSpatialAnalysis(maxFeatures, precision);
}

/**
 * Utility functions for common spatial operations
 */
export const SpatialUtils = {
  /**
   * Convert coordinates between different formats
   */
  convertCoordinates: (
    coords: Coordinate,
    fromFormat: 'decimal' | 'dms',
    toFormat: 'decimal' | 'dms'
  ) => {
    if (fromFormat === toFormat) return coords;
    
    if (fromFormat === 'decimal' && toFormat === 'dms') {
      return {
        latitude: SpatialUtils.decimalToDMS(coords.latitude, 'lat'),
        longitude: SpatialUtils.decimalToDMS(coords.longitude, 'lng'),
      };
    } else if (fromFormat === 'dms' && toFormat === 'decimal') {
      return {
        latitude: SpatialUtils.dmsToDecimal(coords.latitude as any),
        longitude: SpatialUtils.dmsToDecimal(coords.longitude as any),
      };
    }
    
    return coords;
  },

  /**
   * Convert decimal degrees to DMS format
   */
  decimalToDMS: (decimal: number, type: 'lat' | 'lng') => {
    const abs = Math.abs(decimal);
    const degrees = Math.floor(abs);
    const minutes = Math.floor((abs - degrees) * 60);
    const seconds = ((abs - degrees) * 60 - minutes) * 60;
    
    const direction = type === 'lat' 
      ? (decimal >= 0 ? 'N' : 'S')
      : (decimal >= 0 ? 'E' : 'W');
    
    return `${degrees}°${minutes}'${seconds.toFixed(2)}"${direction}`;
  },

  /**
   * Convert DMS format to decimal degrees
   */
  dmsToDecimal: (dms: string) => {
    const matches = dms.match(/(\d+)°(\d+)'([\d.]+)"([NSEW])/);
    if (!matches) return 0;
    
    const [, degrees, minutes, seconds, direction] = matches;
    let decimal = parseInt(degrees) + parseInt(minutes) / 60 + parseFloat(seconds) / 3600;
    
    if (direction === 'S' || direction === 'W') {
      decimal = -decimal;
    }
    
    return decimal;
  },

  /**
   * Validate coordinate bounds
   */
  isValidCoordinate: (coord: Coordinate) => {
    return coord.latitude >= -90 && coord.latitude <= 90 &&
           coord.longitude >= -180 && coord.longitude <= 180;
  },

  /**
   * Calculate bounding box for a set of coordinates
   */
  calculateBounds: (coordinates: Coordinate[]) => {
    if (coordinates.length === 0) return null;
    
    let minLat = Infinity, maxLat = -Infinity;
    let minLng = Infinity, maxLng = -Infinity;
    
    for (const coord of coordinates) {
      minLat = Math.min(minLat, coord.latitude);
      maxLat = Math.max(maxLat, coord.latitude);
      minLng = Math.min(minLng, coord.longitude);
      maxLng = Math.max(maxLng, coord.longitude);
    }
    
    return {
      southwest: { latitude: minLat, longitude: minLng },
      northeast: { latitude: maxLat, longitude: maxLng },
      center: { 
        latitude: (minLat + maxLat) / 2, 
        longitude: (minLng + maxLng) / 2 
      },
    };
  },
};
