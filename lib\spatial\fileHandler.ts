/**
 * Enhanced File Handling System for FieldSyncPro
 * 
 * Provides comprehensive file import/export capabilities for GIS data
 * with support for multiple formats, validation, and error handling.
 */

import { Feature, FeatureCollection } from './spatialAnalysis';

export interface FileImportOptions {
  format?: 'auto' | 'geojson' | 'kml' | 'gpx' | 'csv' | 'shapefile';
  encoding?: 'utf-8' | 'latin1' | 'ascii';
  coordinateFields?: {
    lat: string;
    lng: string;
    elevation?: string;
  };
  delimiter?: string;
  skipHeader?: boolean;
  maxFileSize?: number; // in bytes
  validateGeometry?: boolean;
  reprojectTo?: string; // EPSG code
}

export interface FileExportOptions {
  format: 'geojson' | 'kml' | 'gpx' | 'csv' | 'shapefile';
  includeProperties?: boolean;
  coordinateFormat?: 'decimal' | 'dms';
  precision?: number;
  compression?: boolean;
  fileName?: string;
}

export interface ImportResult {
  success: boolean;
  features: Feature[];
  metadata: {
    format: string;
    featureCount: number;
    geometryTypes: string[];
    properties: string[];
    coordinateSystem?: string;
    bounds?: {
      minX: number;
      minY: number;
      maxX: number;
      maxY: number;
    };
    warnings: string[];
    errors: string[];
  };
  processingTime: number;
}

export interface ExportResult {
  success: boolean;
  data?: string | Uint8Array;
  fileName: string;
  mimeType: string;
  size: number;
  metadata: {
    format: string;
    featureCount: number;
    processingTime: number;
    compression?: boolean;
  };
  error?: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: Array<{
    type: 'geometry' | 'properties' | 'coordinate' | 'format';
    message: string;
    featureId?: string;
    severity: 'error' | 'warning';
  }>;
  warnings: string[];
  statistics: {
    totalFeatures: number;
    validFeatures: number;
    geometryTypes: Record<string, number>;
  };
}

const SUPPORTED_FORMATS = {
  geojson: {
    extensions: ['.geojson', '.json'],
    mimeType: 'application/geo+json',
    description: 'GeoJSON format',
  },
  kml: {
    extensions: ['.kml'],
    mimeType: 'application/vnd.google-earth.kml+xml',
    description: 'Keyhole Markup Language',
  },
  gpx: {
    extensions: ['.gpx'],
    mimeType: 'application/gpx+xml',
    description: 'GPS Exchange Format',
  },
  csv: {
    extensions: ['.csv'],
    mimeType: 'text/csv',
    description: 'Comma Separated Values',
  },
  shapefile: {
    extensions: ['.shp', '.zip'],
    mimeType: 'application/zip',
    description: 'ESRI Shapefile',
  },
};

const DEFAULT_IMPORT_OPTIONS: FileImportOptions = {
  format: 'auto',
  encoding: 'utf-8',
  maxFileSize: 50 * 1024 * 1024, // 50MB
  validateGeometry: true,
  delimiter: ',',
  skipHeader: false,
};

const DEFAULT_EXPORT_OPTIONS: Partial<FileExportOptions> = {
  includeProperties: true,
  coordinateFormat: 'decimal',
  precision: 6,
  compression: false,
};

/**
 * Enhanced File Handler for GIS data
 */
export class EnhancedFileHandler {
  private maxFileSize: number;
  private supportedFormats: Set<string>;

  constructor(maxFileSize = 50 * 1024 * 1024) {
    this.maxFileSize = maxFileSize;
    this.supportedFormats = new Set(Object.keys(SUPPORTED_FORMATS));
  }

  /**
   * Import file and convert to GeoJSON features
   */
  async importFile(
    file: File | string, 
    options: FileImportOptions = {}
  ): Promise<ImportResult> {
    const startTime = Date.now();
    const opts = { ...DEFAULT_IMPORT_OPTIONS, ...options };
    
    try {
      // Validate file
      if (file instanceof File) {
        await this.validateFile(file, opts);
      }

      // Detect format
      const format = opts.format === 'auto' 
        ? this.detectFormat(file)
        : opts.format!;

      if (!this.supportedFormats.has(format)) {
        throw new Error(`Unsupported format: ${format}`);
      }

      // Read file content
      const content = await this.readFileContent(file, opts.encoding!);

      // Parse based on format
      let features: Feature[] = [];
      let metadata: any = {};

      switch (format) {
        case 'geojson':
          ({ features, metadata } = await this.parseGeoJSON(content, opts));
          break;
        case 'kml':
          ({ features, metadata } = await this.parseKML(content, opts));
          break;
        case 'gpx':
          ({ features, metadata } = await this.parseGPX(content, opts));
          break;
        case 'csv':
          ({ features, metadata } = await this.parseCSV(content, opts));
          break;
        case 'shapefile':
          ({ features, metadata } = await this.parseShapefile(file as File, opts));
          break;
        default:
          throw new Error(`Parser not implemented for format: ${format}`);
      }

      // Validate geometry if requested
      if (opts.validateGeometry) {
        const validation = this.validateFeatures(features);
        metadata.warnings = validation.warnings;
        metadata.errors = validation.errors.map(e => e.message);
        
        // Filter out invalid features if any
        features = features.filter(f => this.isValidFeature(f));
      }

      // Calculate bounds
      metadata.bounds = this.calculateBounds(features);

      // Gather statistics
      metadata.featureCount = features.length;
      metadata.geometryTypes = [...new Set(features.map(f => f.geometry.type))];
      metadata.properties = this.extractPropertyNames(features);
      metadata.format = format;

      return {
        success: true,
        features,
        metadata,
        processingTime: Date.now() - startTime,
      };

    } catch (error) {
      return {
        success: false,
        features: [],
        metadata: {
          format: 'unknown',
          featureCount: 0,
          geometryTypes: [],
          properties: [],
          warnings: [],
          errors: [error instanceof Error ? error.message : 'Unknown error'],
        },
        processingTime: Date.now() - startTime,
      };
    }
  }

  /**
   * Export features to specified format
   */
  async exportFeatures(
    features: Feature[],
    options: FileExportOptions
  ): Promise<ExportResult> {
    const startTime = Date.now();
    const opts = { ...DEFAULT_EXPORT_OPTIONS, ...options };
    
    try {
      if (!features || features.length === 0) {
        throw new Error('No features to export');
      }

      if (!this.supportedFormats.has(options.format)) {
        throw new Error(`Unsupported export format: ${options.format}`);
      }

      let data: string | Uint8Array;
      let mimeType: string;
      let fileName: string;

      switch (options.format) {
        case 'geojson':
          data = this.exportToGeoJSON(features, opts);
          mimeType = SUPPORTED_FORMATS.geojson.mimeType;
          fileName = opts.fileName || `export_${Date.now()}.geojson`;
          break;
        
        case 'kml':
          data = this.exportToKML(features, opts);
          mimeType = SUPPORTED_FORMATS.kml.mimeType;
          fileName = opts.fileName || `export_${Date.now()}.kml`;
          break;
        
        case 'gpx':
          data = this.exportToGPX(features, opts);
          mimeType = SUPPORTED_FORMATS.gpx.mimeType;
          fileName = opts.fileName || `export_${Date.now()}.gpx`;
          break;
        
        case 'csv':
          data = this.exportToCSV(features, opts);
          mimeType = SUPPORTED_FORMATS.csv.mimeType;
          fileName = opts.fileName || `export_${Date.now()}.csv`;
          break;
        
        case 'shapefile':
          data = await this.exportToShapefile(features, opts);
          mimeType = SUPPORTED_FORMATS.shapefile.mimeType;
          fileName = opts.fileName || `export_${Date.now()}.zip`;
          break;
        
        default:
          throw new Error(`Export not implemented for format: ${options.format}`);
      }

      return {
        success: true,
        data,
        fileName,
        mimeType,
        size: data instanceof Uint8Array ? data.length : new Blob([data]).size,
        metadata: {
          format: options.format,
          featureCount: features.length,
          processingTime: Date.now() - startTime,
          compression: opts.compression,
        },
      };

    } catch (error) {
      return {
        success: false,
        fileName: '',
        mimeType: '',
        size: 0,
        metadata: {
          format: options.format,
          featureCount: 0,
          processingTime: Date.now() - startTime,
        },
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Validate features for consistency and correctness
   */
  validateFeatures(features: Feature[]): ValidationResult {
    const errors: ValidationResult['errors'] = [];
    const warnings: string[] = [];
    const geometryTypes: Record<string, number> = {};
    let validFeatures = 0;

    for (const feature of features) {
      let isFeatureValid = true;

      // Validate feature structure
      if (!feature.id) {
        warnings.push(`Feature missing ID: ${JSON.stringify(feature).substring(0, 100)}...`);
      }

      if (!feature.geometry) {
        errors.push({
          type: 'geometry',
          message: 'Feature missing geometry',
          featureId: feature.id,
          severity: 'error',
        });
        isFeatureValid = false;
      }

      if (!feature.properties) {
        warnings.push(`Feature missing properties: ${feature.id}`);
      }

      // Validate geometry
      if (feature.geometry) {
        const geometryValidation = this.validateGeometry(feature.geometry);
        if (!geometryValidation.isValid) {
          errors.push({
            type: 'geometry',
            message: geometryValidation.error || 'Invalid geometry',
            featureId: feature.id,
            severity: 'error',
          });
          isFeatureValid = false;
        }

        // Count geometry types
        const geoType = feature.geometry.type;
        geometryTypes[geoType] = (geometryTypes[geoType] || 0) + 1;
      }

      // Validate coordinates
      if (feature.geometry && feature.geometry.coordinates) {
        const coordValidation = this.validateCoordinates(feature.geometry.coordinates);
        if (!coordValidation.isValid) {
          errors.push({
            type: 'coordinate',
            message: coordValidation.error || 'Invalid coordinates',
            featureId: feature.id,
            severity: 'error',
          });
          isFeatureValid = false;
        }
      }

      if (isFeatureValid) {
        validFeatures++;
      }
    }

    return {
      isValid: errors.filter(e => e.severity === 'error').length === 0,
      errors,
      warnings,
      statistics: {
        totalFeatures: features.length,
        validFeatures,
        geometryTypes,
      },
    };
  }

  // Private helper methods

  private async validateFile(file: File, options: FileImportOptions): Promise<void> {
    if (file.size > (options.maxFileSize || this.maxFileSize)) {
      throw new Error(`File too large: ${file.size} bytes. Maximum: ${options.maxFileSize || this.maxFileSize} bytes`);
    }

    const extension = this.getFileExtension(file.name);
    const format = options.format === 'auto' ? this.detectFormatFromExtension(extension) : options.format;
    
    if (!format || !this.supportedFormats.has(format)) {
      throw new Error(`Unsupported file format: ${extension}`);
    }
  }

  private detectFormat(file: File | string): string {
    if (typeof file === 'string') {
      // Try to detect from content
      const trimmed = file.trim();
      if (trimmed.startsWith('{') || trimmed.startsWith('[')) {
        return 'geojson';
      } else if (trimmed.startsWith('<?xml') && trimmed.includes('kml')) {
        return 'kml';
      } else if (trimmed.startsWith('<?xml') && trimmed.includes('gpx')) {
        return 'gpx';
      } else {
        return 'csv';
      }
    } else {
      const extension = this.getFileExtension(file.name);
      return this.detectFormatFromExtension(extension) || 'geojson';
    }
  }

  private detectFormatFromExtension(extension: string): string | null {
    for (const [format, config] of Object.entries(SUPPORTED_FORMATS)) {
      if (config.extensions.includes(extension.toLowerCase())) {
        return format;
      }
    }
    return null;
  }

  private getFileExtension(fileName: string): string {
    return fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
  }

  private async readFileContent(file: File | string, encoding: string): Promise<string> {
    if (typeof file === 'string') {
      return file;
    }

    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsText(file, encoding);
    });
  }

  private async parseGeoJSON(content: string, options: FileImportOptions): Promise<{ features: Feature[]; metadata: any }> {
    try {
      const data = JSON.parse(content);
      
      let features: Feature[] = [];
      
      if (data.type === 'FeatureCollection') {
        features = data.features || [];
      } else if (data.type === 'Feature') {
        features = [data];
      } else if (data.type && data.coordinates) {
        // Single geometry
        features = [{
          id: `feature_${Date.now()}`,
          type: 'Feature',
          geometry: data,
          properties: {},
        }];
      }

      // Ensure features have IDs
      features = features.map((feature, index) => ({
        ...feature,
        id: feature.id || `feature_${index}`,
      }));

      return {
        features,
        metadata: {
          originalType: data.type,
          crs: data.crs,
        },
      };
    } catch (error) {
      throw new Error(`Invalid GeoJSON: ${error}`);
    }
  }

  private async parseKML(content: string, options: FileImportOptions): Promise<{ features: Feature[]; metadata: any }> {
    // Simplified KML parser - in production, use a proper XML parser
    const features: Feature[] = [];
    
    try {
      // Basic regex-based parsing (simplified)
      const placemarkRegex = /<Placemark[^>]*>(.*?)<\/Placemark>/gs;
      const nameRegex = /<name[^>]*>(.*?)<\/name>/s;
      const coordinatesRegex = /<coordinates[^>]*>(.*?)<\/coordinates>/s;
      
      let match;
      let index = 0;
      
      while ((match = placemarkRegex.exec(content)) !== null) {
        const placemarkContent = match[1];
        const nameMatch = nameRegex.exec(placemarkContent);
        const coordMatch = coordinatesRegex.exec(placemarkContent);
        
        if (coordMatch) {
          const coords = coordMatch[1].trim().split(/\s+/).map(coord => {
            const parts = coord.split(',').map(Number);
            return [parts[0], parts[1], parts[2] || 0]; // lng, lat, elevation
          });
          
          const feature: Feature = {
            id: `kml_feature_${index++}`,
            type: 'Feature',
            geometry: {
              type: coords.length === 1 ? 'Point' : 'LineString',
              coordinates: coords.length === 1 ? coords[0] : coords,
            },
            properties: {
              name: nameMatch ? nameMatch[1] : `Feature ${index}`,
            },
          };
          
          features.push(feature);
        }
      }

      return {
        features,
        metadata: {
          format: 'kml',
          parser: 'simplified',
        },
      };
    } catch (error) {
      throw new Error(`Invalid KML: ${error}`);
    }
  }

  private async parseGPX(content: string, options: FileImportOptions): Promise<{ features: Feature[]; metadata: any }> {
    // Simplified GPX parser
    const features: Feature[] = [];
    
    try {
      // Parse waypoints
      const waypointRegex = /<wpt[^>]*lat="([^"]*)"[^>]*lon="([^"]*)"[^>]*>(.*?)<\/wpt>/gs;
      let match;
      let index = 0;
      
      while ((match = waypointRegex.exec(content)) !== null) {
        const lat = parseFloat(match[1]);
        const lon = parseFloat(match[2]);
        const waypointContent = match[3];
        
        const nameMatch = /<name[^>]*>(.*?)<\/name>/.exec(waypointContent);
        
        const feature: Feature = {
          id: `gpx_waypoint_${index++}`,
          type: 'Feature',
          geometry: {
            type: 'Point',
            coordinates: [lon, lat],
          },
          properties: {
            name: nameMatch ? nameMatch[1] : `Waypoint ${index}`,
            type: 'waypoint',
          },
        };
        
        features.push(feature);
      }

      // Parse tracks (simplified)
      const trackRegex = /<trk[^>]*>(.*?)<\/trk>/gs;
      while ((match = trackRegex.exec(content)) !== null) {
        const trackContent = match[1];
        const trackPointRegex = /<trkpt[^>]*lat="([^"]*)"[^>]*lon="([^"]*)"[^>]*>/g;
        const coordinates: number[][] = [];
        
        let trkptMatch;
        while ((trkptMatch = trackPointRegex.exec(trackContent)) !== null) {
          const lat = parseFloat(trkptMatch[1]);
          const lon = parseFloat(trkptMatch[2]);
          coordinates.push([lon, lat]);
        }
        
        if (coordinates.length > 0) {
          const feature: Feature = {
            id: `gpx_track_${index++}`,
            type: 'Feature',
            geometry: {
              type: 'LineString',
              coordinates,
            },
            properties: {
              name: `Track ${index}`,
              type: 'track',
            },
          };
          
          features.push(feature);
        }
      }

      return {
        features,
        metadata: {
          format: 'gpx',
          parser: 'simplified',
        },
      };
    } catch (error) {
      throw new Error(`Invalid GPX: ${error}`);
    }
  }

  private async parseCSV(content: string, options: FileImportOptions): Promise<{ features: Feature[]; metadata: any }> {
    try {
      const lines = content.split('\n').filter(line => line.trim());
      if (lines.length === 0) {
        throw new Error('Empty CSV file');
      }

      const delimiter = options.delimiter || ',';
      const headers = lines[0].split(delimiter).map(h => h.trim().replace(/"/g, ''));
      const dataLines = options.skipHeader ? lines.slice(1) : lines;

      // Auto-detect coordinate fields
      const coordFields = options.coordinateFields || this.detectCoordinateFields(headers);
      
      if (!coordFields.lat || !coordFields.lng) {
        throw new Error('Could not detect latitude/longitude columns');
      }

      const latIndex = headers.indexOf(coordFields.lat);
      const lngIndex = headers.indexOf(coordFields.lng);
      const elevIndex = coordFields.elevation ? headers.indexOf(coordFields.elevation) : -1;

      if (latIndex === -1 || lngIndex === -1) {
        throw new Error(`Coordinate columns not found: ${coordFields.lat}, ${coordFields.lng}`);
      }

      const features: Feature[] = [];

      for (let i = 0; i < dataLines.length; i++) {
        const values = this.parseCSVLine(dataLines[i], delimiter);
        
        if (values.length < Math.max(latIndex, lngIndex) + 1) {
          continue; // Skip incomplete rows
        }

        const lat = parseFloat(values[latIndex]);
        const lng = parseFloat(values[lngIndex]);
        
        if (isNaN(lat) || isNaN(lng) || lat < -90 || lat > 90 || lng < -180 || lng > 180) {
          continue; // Skip invalid coordinates
        }

        const coordinates: number[] = [lng, lat];
        if (elevIndex !== -1 && values[elevIndex]) {
          const elevation = parseFloat(values[elevIndex]);
          if (!isNaN(elevation)) {
            coordinates.push(elevation);
          }
        }

        const properties: Record<string, any> = {};
        headers.forEach((header, index) => {
          if (index !== latIndex && index !== lngIndex && index !== elevIndex) {
            const value = values[index]?.replace(/"/g, '') || '';
            // Try to parse as number
            const numValue = parseFloat(value);
            properties[header] = isNaN(numValue) ? value : numValue;
          }
        });

        const feature: Feature = {
          id: `csv_feature_${i}`,
          type: 'Feature',
          geometry: {
            type: 'Point',
            coordinates,
          },
          properties,
        };

        features.push(feature);
      }

      return {
        features,
        metadata: {
          format: 'csv',
          headers,
          coordinateFields: coordFields,
          totalRows: lines.length,
          validRows: features.length,
        },
      };
    } catch (error) {
      throw new Error(`Invalid CSV: ${error}`);
    }
  }

  private async parseShapefile(file: File, options: FileImportOptions): Promise<{ features: Feature[]; metadata: any }> {
    // Simplified shapefile parser - in production, use a proper shapefile library
    throw new Error('Shapefile import requires a specialized library (e.g., shapefile.js)');
  }

  private detectCoordinateFields(headers: string[]): { lat: string; lng: string; elevation?: string } {
    const latPatterns = ['lat', 'latitude', 'y', 'lat_dd', 'lat_deg'];
    const lngPatterns = ['lng', 'lon', 'longitude', 'x', 'lng_dd', 'lon_deg', 'long'];
    const elevPatterns = ['elevation', 'elev', 'altitude', 'alt', 'z', 'height'];

    const latField = headers.find(h => 
      latPatterns.some(p => h.toLowerCase().includes(p.toLowerCase()))
    );
    
    const lngField = headers.find(h => 
      lngPatterns.some(p => h.toLowerCase().includes(p.toLowerCase()))
    );
    
    const elevField = headers.find(h => 
      elevPatterns.some(p => h.toLowerCase().includes(p.toLowerCase()))
    );

    return {
      lat: latField || '',
      lng: lngField || '',
      elevation: elevField,
    };
  }

  private parseCSVLine(line: string, delimiter: string): string[] {
    const result: string[] = [];
    let current = '';
    let inQuotes = false;
    
    for (let i = 0; i < line.length; i++) {
      const char = line[i];
      
      if (char === '"') {
        inQuotes = !inQuotes;
      } else if (char === delimiter && !inQuotes) {
        result.push(current);
        current = '';
      } else {
        current += char;
      }
    }
    
    result.push(current);
    return result;
  }

  private exportToGeoJSON(features: Feature[], options: Partial<FileExportOptions>): string {
    const featureCollection: FeatureCollection = {
      type: 'FeatureCollection',
      features: options.includeProperties 
        ? features 
        : features.map(f => ({ ...f, properties: {} })),
    };

    return JSON.stringify(featureCollection, null, options.precision ? 2 : undefined);
  }

  private exportToKML(features: Feature[], options: Partial<FileExportOptions>): string {
    let kml = `<?xml version="1.0" encoding="UTF-8"?>
<kml xmlns="http://www.opengis.net/kml/2.2">
  <Document>
    <name>FieldSyncPro Export</name>
`;

    for (const feature of features) {
      kml += `    <Placemark>
      <name>${feature.properties?.name || feature.id}</name>
`;
      
      if (feature.geometry.type === 'Point') {
        const coords = feature.geometry.coordinates as number[];
        kml += `      <Point>
        <coordinates>${coords[0]},${coords[1]},${coords[2] || 0}</coordinates>
      </Point>
`;
      } else if (feature.geometry.type === 'LineString') {
        const coords = (feature.geometry.coordinates as number[][])
          .map(c => `${c[0]},${c[1]},${c[2] || 0}`)
          .join(' ');
        kml += `      <LineString>
        <coordinates>${coords}</coordinates>
      </LineString>
`;
      }
      
      kml += `    </Placemark>
`;
    }

    kml += `  </Document>
</kml>`;

    return kml;
  }

  private exportToGPX(features: Feature[], options: Partial<FileExportOptions>): string {
    let gpx = `<?xml version="1.0" encoding="UTF-8"?>
<gpx version="1.1" creator="FieldSyncPro">
`;

    for (const feature of features) {
      if (feature.geometry.type === 'Point') {
        const coords = feature.geometry.coordinates as number[];
        gpx += `  <wpt lat="${coords[1]}" lon="${coords[0]}">
    <name>${feature.properties?.name || feature.id}</name>
    ${coords[2] ? `<ele>${coords[2]}</ele>` : ''}
  </wpt>
`;
      }
    }

    gpx += `</gpx>`;
    return gpx;
  }

  private exportToCSV(features: Feature[], options: Partial<FileExportOptions>): string {
    if (features.length === 0) return '';

    // Collect all unique property keys
    const allProperties = new Set<string>();
    features.forEach(f => {
      if (options.includeProperties && f.properties) {
        Object.keys(f.properties).forEach(key => allProperties.add(key));
      }
    });

    const headers = ['id', 'geometry_type', 'latitude', 'longitude', 'elevation', ...Array.from(allProperties)];
    const rows = [headers];

    for (const feature of features) {
      const centroid = this.getGeometryCentroid(feature.geometry);
      const row: string[] = [
        feature.id,
        feature.geometry.type,
        centroid.lat.toString(),
        centroid.lng.toString(),
        centroid.elevation?.toString() || '',
      ];

      // Add property values
      for (const prop of allProperties) {
        const value = feature.properties?.[prop];
        row.push(value?.toString() || '');
      }

      rows.push(row);
    }

    return rows.map(row => row.map(cell => `"${cell}"`).join(',')).join('\n');
  }

  private async exportToShapefile(features: Feature[], options: Partial<FileExportOptions>): Promise<Uint8Array> {
    // Simplified shapefile export - in production, use a proper shapefile library
    throw new Error('Shapefile export requires a specialized library (e.g., shpjs)');
  }

  private validateGeometry(geometry: any): { isValid: boolean; error?: string } {
    try {
      if (!geometry || !geometry.type || !geometry.coordinates) {
        return { isValid: false, error: 'Missing geometry type or coordinates' };
      }

      const validTypes = ['Point', 'LineString', 'Polygon', 'MultiPoint', 'MultiLineString', 'MultiPolygon'];
      if (!validTypes.includes(geometry.type)) {
        return { isValid: false, error: `Invalid geometry type: ${geometry.type}` };
      }

      // Basic coordinate validation
      if (!Array.isArray(geometry.coordinates)) {
        return { isValid: false, error: 'Coordinates must be an array' };
      }

      return { isValid: true };
    } catch (error) {
      return { isValid: false, error: `Geometry validation error: ${error}` };
    }
  }

  private validateCoordinates(coordinates: any): { isValid: boolean; error?: string } {
    try {
      if (!Array.isArray(coordinates)) {
        return { isValid: false, error: 'Coordinates must be an array' };
      }

      const validateCoord = (coord: any): boolean => {
        if (!Array.isArray(coord) || coord.length < 2) return false;
        const [lng, lat] = coord;
        return typeof lng === 'number' && typeof lat === 'number' &&
               lng >= -180 && lng <= 180 && lat >= -90 && lat <= 90;
      };

      const validateCoordArray = (coords: any): boolean => {
        if (!Array.isArray(coords)) return false;
        return coords.every(c => Array.isArray(c) ? validateCoordArray(c) : validateCoord(c));
      };

      if (!validateCoordArray(coordinates)) {
        return { isValid: false, error: 'Invalid coordinate values' };
      }

      return { isValid: true };
    } catch (error) {
      return { isValid: false, error: `Coordinate validation error: ${error}` };
    }
  }

  private isValidFeature(feature: Feature): boolean {
    return !!(feature.id && feature.geometry && feature.geometry.type && feature.geometry.coordinates);
  }

  private calculateBounds(features: Feature[]): { minX: number; minY: number; maxX: number; maxY: number } | null {
    if (features.length === 0) return null;

    let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;

    for (const feature of features) {
      const coords = this.extractAllCoordinates(feature.geometry);
      for (const coord of coords) {
        minX = Math.min(minX, coord[0]);
        minY = Math.min(minY, coord[1]);
        maxX = Math.max(maxX, coord[0]);
        maxY = Math.max(maxY, coord[1]);
      }
    }

    return { minX, minY, maxX, maxY };
  }

  private extractAllCoordinates(geometry: any): number[][] {
    switch (geometry.type) {
      case 'Point':
        return [geometry.coordinates];
      case 'LineString':
      case 'MultiPoint':
        return geometry.coordinates;
      case 'Polygon':
      case 'MultiLineString':
        return geometry.coordinates.flat();
      case 'MultiPolygon':
        return geometry.coordinates.flat(2);
      default:
        return [];
    }
  }

  private extractPropertyNames(features: Feature[]): string[] {
    const properties = new Set<string>();
    for (const feature of features) {
      if (feature.properties) {
        Object.keys(feature.properties).forEach(key => properties.add(key));
      }
    }
    return Array.from(properties);
  }

  private getGeometryCentroid(geometry: any): { lat: number; lng: number; elevation?: number } {
    const coords = this.extractAllCoordinates(geometry);
    if (coords.length === 0) return { lat: 0, lng: 0 };

    const sum = coords.reduce(
      (acc, coord) => ({
        lng: acc.lng + coord[0],
        lat: acc.lat + coord[1],
        elevation: acc.elevation + (coord[2] || 0),
      }),
      { lng: 0, lat: 0, elevation: 0 }
    );

    return {
      lng: sum.lng / coords.length,
      lat: sum.lat / coords.length,
      elevation: coords.some(c => c[2] !== undefined) ? sum.elevation / coords.length : undefined,
    };
  }
}

/**
 * Utility functions for file operations
 */
export const FileUtils = {
  /**
   * Download data as a file
   */
  downloadFile: (data: string | Uint8Array, fileName: string, mimeType: string) => {
    const blob = new Blob([data], { type: mimeType });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    URL.revokeObjectURL(url);
  },

  /**
   * Format file size for display
   */
  formatFileSize: (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },

  /**
   * Get supported file extensions
   */
  getSupportedExtensions: (): string[] => {
    return Object.values(SUPPORTED_FORMATS).flatMap(format => format.extensions);
  },

  /**
   * Check if file is supported
   */
  isSupportedFile: (fileName: string): boolean => {
    const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
    return FileUtils.getSupportedExtensions().includes(extension);
  },
};

/**
 * Create a file handler instance
 */
export function createFileHandler(maxFileSize?: number): EnhancedFileHandler {
  return new EnhancedFileHandler(maxFileSize);
}
