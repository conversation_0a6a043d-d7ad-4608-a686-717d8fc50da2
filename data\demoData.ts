import type { MapLayer, GISFeature, MapBookmark, StorySlide, MapSettings } from '@/types/gis';

/**
 * Demo data for Advanced Map feature
 * Provides sample layers, features, bookmarks, and stories for testing and demonstration
 */

// Sample map layers representing common GIS data types
export const DEMO_LAYERS: MapLayer[] = [
  {
    id: 'demo-cities',
    name: 'Major Cities',
    type: 'vector',
    source: 'catalog',
    visible: true,
    opacity: 1.0,
    style: {
      iconType: 'circle',
      iconSize: 8,
      fillColor: '#EF4444',
      strokeColor: '#DC2626',
      strokeWidth: 2,
      labelField: 'name',
      labelSize: 12,
      labelColor: '#1F2937',
    },
    metadata: {
      description: 'Major cities around the world with population data',
      source: 'Natural Earth',
      lastUpdated: '2024-01-15T10:30:00Z',
      properties: ['name', 'country', 'population', 'capital'],
      geometryType: 'Point',
      featureCount: 45,
      extent: {
        minX: -180,
        minY: -85,
        maxX: 180,
        maxY: 85,
      },
    },
  },
  {
    id: 'demo-countries',
    name: 'World Countries',
    type: 'vector',
    source: 'catalog',
    visible: false,
    opacity: 0.7,
    style: {
      strokeColor: '#374151',
      strokeWidth: 1,
      fillColor: '#F3F4F640',
      fillOpacity: 0.4,
      labelField: 'name',
      labelSize: 10,
      labelColor: '#1F2937',
    },
    metadata: {
      description: 'Country boundaries with demographic and economic data',
      source: 'Natural Earth',
      lastUpdated: '2024-01-10T14:20:00Z',
      properties: ['name', 'iso_a3', 'pop_est', 'gdp_md_est', 'continent'],
      geometryType: 'Polygon',
      featureCount: 195,
      extent: {
        minX: -180,
        minY: -90,
        maxX: 180,
        maxY: 90,
      },
    },
  },
  {
    id: 'demo-rivers',
    name: 'Major Rivers',
    type: 'vector',
    source: 'catalog',
    visible: false,
    opacity: 0.8,
    style: {
      strokeColor: '#06B6D4',
      strokeWidth: 2,
      labelField: 'name',
      labelSize: 10,
      labelColor: '#0891B2',
    },
    metadata: {
      description: 'Major river systems worldwide',
      source: 'Natural Earth',
      lastUpdated: '2024-01-08T09:45:00Z',
      properties: ['name', 'name_en', 'length_km', 'discharge'],
      geometryType: 'LineString',
      featureCount: 127,
      extent: {
        minX: -180,
        minY: -85,
        maxX: 180,
        maxY: 85,
      },
    },
  },
  {
    id: 'demo-national-parks',
    name: 'National Parks',
    type: 'vector',
    source: 'catalog',
    visible: false,
    opacity: 0.6,
    style: {
      strokeColor: '#16A34A',
      strokeWidth: 2,
      fillColor: '#16A34A30',
      fillOpacity: 0.3,
      labelField: 'name',
      labelSize: 9,
      labelColor: '#15803D',
    },
    metadata: {
      description: 'Protected areas and national parks',
      source: 'World Database on Protected Areas',
      lastUpdated: '2024-01-12T16:15:00Z',
      properties: ['name', 'country', 'area_km2', 'established', 'type'],
      geometryType: 'Polygon',
      featureCount: 89,
      extent: {
        minX: -180,
        minY: -85,
        maxX: 180,
        maxY: 85,
      },
    },
  },
  {
    id: 'demo-earthquakes',
    name: 'Recent Earthquakes',
    type: 'vector',
    source: 'catalog',
    visible: false,
    opacity: 0.9,
    style: {
      iconType: 'circle',
      iconSize: 6,
      fillColor: '#F59E0B',
      strokeColor: '#D97706',
      strokeWidth: 1,
    },
    metadata: {
      description: 'Earthquake events from the last 30 days (magnitude > 4.0)',
      source: 'USGS',
      lastUpdated: '2024-01-20T12:00:00Z',
      properties: ['magnitude', 'depth', 'place', 'time', 'alert'],
      geometryType: 'Point',
      featureCount: 234,
      extent: {
        minX: -180,
        minY: -85,
        maxX: 180,
        maxY: 85,
      },
    },
  },
];

// Sample GIS features for demonstration
export const DEMO_FEATURES: GISFeature[] = [
  {
    id: 'feature-sf',
    type: 'point',
    coordinates: [{ latitude: 37.7749, longitude: -122.4194 }],
    properties: {
      name: 'San Francisco',
      country: 'United States',
      population: 873965,
      capital: false,
      type: 'Major City',
    },
    style: {
      fillColor: '#EF4444',
      strokeColor: '#DC2626',
      strokeWidth: 2,
    },
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
    author: 'Demo Data',
  },
  {
    id: 'feature-london',
    type: 'point',
    coordinates: [{ latitude: 51.5074, longitude: -0.1278 }],
    properties: {
      name: 'London',
      country: 'United Kingdom',
      population: 8982000,
      capital: true,
      type: 'Capital City',
    },
    style: {
      fillColor: '#EF4444',
      strokeColor: '#DC2626',
      strokeWidth: 2,
    },
    createdAt: '2024-01-15T10:01:00Z',
    updatedAt: '2024-01-15T10:01:00Z',
    author: 'Demo Data',
  },
  {
    id: 'feature-tokyo',
    type: 'point',
    coordinates: [{ latitude: 35.6762, longitude: 139.6503 }],
    properties: {
      name: 'Tokyo',
      country: 'Japan',
      population: 13929286,
      capital: true,
      type: 'Capital City',
    },
    style: {
      fillColor: '#EF4444',
      strokeColor: '#DC2626',
      strokeWidth: 2,
    },
    createdAt: '2024-01-15T10:02:00Z',
    updatedAt: '2024-01-15T10:02:00Z',
    author: 'Demo Data',
  },
  {
    id: 'feature-flight-path',
    type: 'line',
    coordinates: [
      { latitude: 37.7749, longitude: -122.4194 }, // San Francisco
      { latitude: 40.7128, longitude: -74.0060 },  // New York
      { latitude: 51.5074, longitude: -0.1278 },   // London
    ],
    properties: {
      name: 'Trans-Atlantic Flight Route',
      type: 'Flight Path',
      distance_km: 8633,
      duration_hours: 11.5,
    },
    style: {
      strokeColor: '#3B82F6',
      strokeWidth: 3,
      dashPattern: [10, 5],
    },
    createdAt: '2024-01-15T10:03:00Z',
    updatedAt: '2024-01-15T10:03:00Z',
    author: 'Demo Data',
  },
  {
    id: 'feature-study-area',
    type: 'polygon',
    coordinates: [
      { latitude: 37.8, longitude: -122.5 },
      { latitude: 37.8, longitude: -122.3 },
      { latitude: 37.7, longitude: -122.3 },
      { latitude: 37.7, longitude: -122.5 },
      { latitude: 37.8, longitude: -122.5 }, // Close the polygon
    ],
    properties: {
      name: 'San Francisco Bay Study Area',
      type: 'Research Zone',
      area_km2: 156.7,
      established: '2024-01-01',
      purpose: 'Environmental monitoring',
    },
    style: {
      strokeColor: '#10B981',
      strokeWidth: 2,
      fillColor: '#10B98130',
      fillOpacity: 0.3,
    },
    createdAt: '2024-01-15T10:04:00Z',
    updatedAt: '2024-01-15T10:04:00Z',
    author: 'Demo Data',
  },
];

// Sample bookmarks for quick navigation
export const DEMO_BOOKMARKS: MapBookmark[] = [
  {
    id: 'bookmark-sf-bay',
    name: 'San Francisco Bay Area',
    description: 'Overview of the San Francisco Bay Area including the city, Oakland, and surrounding regions',
    extent: {
      latitude: 37.7749,
      longitude: -122.4194,
      latitudeDelta: 0.5,
      longitudeDelta: 0.5,
    },
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
    author: 'Demo User',
    tags: ['california', 'bay area', 'urban'],
    isPublic: true,
    isFavorite: true,
    visitCount: 12,
    lastVisited: '2024-01-20T14:30:00Z',
    category: 'project',
    metadata: {
      zoom: 10,
      visibleLayers: ['demo-cities', 'demo-countries'],
      notes: 'Primary study area for urban planning project',
    },
  },
  {
    id: 'bookmark-europe',
    name: 'Western Europe',
    description: 'Western European countries for demographic analysis',
    extent: {
      latitude: 50.0,
      longitude: 10.0,
      latitudeDelta: 15.0,
      longitudeDelta: 20.0,
    },
    createdAt: '2024-01-14T09:00:00Z',
    updatedAt: '2024-01-14T09:00:00Z',
    author: 'Demo User',
    tags: ['europe', 'demographics', 'analysis'],
    isPublic: false,
    isFavorite: false,
    visitCount: 5,
    lastVisited: '2024-01-18T11:15:00Z',
    category: 'personal',
    metadata: {
      zoom: 5,
      visibleLayers: ['demo-countries', 'demo-cities'],
    },
  },
  {
    id: 'bookmark-pacific',
    name: 'Pacific Ring of Fire',
    description: 'Seismically active region around the Pacific Ocean',
    extent: {
      latitude: 0.0,
      longitude: -150.0,
      latitudeDelta: 80.0,
      longitudeDelta: 120.0,
    },
    createdAt: '2024-01-13T16:45:00Z',
    updatedAt: '2024-01-13T16:45:00Z',
    author: 'Demo User',
    tags: ['seismic', 'pacific', 'geology'],
    isPublic: true,
    isFavorite: true,
    visitCount: 8,
    lastVisited: '2024-01-19T08:20:00Z',
    category: 'shared',
    metadata: {
      zoom: 3,
      visibleLayers: ['demo-earthquakes', 'demo-countries'],
      notes: 'Earthquake monitoring and analysis region',
    },
  },
];

// Sample story slides for interactive narratives
export const DEMO_STORY_SLIDES: StorySlide[] = [
  {
    id: 'slide-intro',
    title: 'Global Cities Analysis',
    description: 'An exploration of major cities and their characteristics',
    content: 'Welcome to our interactive map story exploring major cities around the world. We\'ll examine population patterns, geographic distribution, and urban characteristics across different continents.',
    duration: 30,
    extent: {
      latitude: 20.0,
      longitude: 0.0,
      latitudeDelta: 120.0,
      longitudeDelta: 180.0,
    },
    visibleLayers: ['demo-countries', 'demo-cities'],
    baseMap: 'satellite',
    interactive: true,
    autoAdvance: false,
    transition: {
      type: 'fade',
      duration: 1,
    },
  },
  {
    id: 'slide-europe',
    title: 'European Cities',
    description: 'Major urban centers in Europe',
    content: 'Europe hosts some of the world\'s most historic and influential cities. From London\'s financial district to Paris\'s cultural heritage, European cities showcase diverse urban development patterns shaped by centuries of history.',
    duration: 45,
    extent: {
      latitude: 54.0,
      longitude: 15.0,
      latitudeDelta: 20.0,
      longitudeDelta: 30.0,
    },
    visibleLayers: ['demo-countries', 'demo-cities'],
    baseMap: 'terrain',
    interactive: true,
    autoAdvance: false,
    transition: {
      type: 'zoom',
      duration: 2,
    },
  },
  {
    id: 'slide-asia',
    title: 'Asian Megacities',
    description: 'The rise of Asian urban centers',
    content: 'Asia is home to some of the world\'s largest and fastest-growing cities. Tokyo, Delhi, Shanghai, and Mumbai represent the new centers of global economic activity, with populations exceeding 10 million people each.',
    duration: 40,
    extent: {
      latitude: 25.0,
      longitude: 110.0,
      latitudeDelta: 40.0,
      longitudeDelta: 60.0,
    },
    visibleLayers: ['demo-countries', 'demo-cities'],
    baseMap: 'street',
    interactive: true,
    autoAdvance: false,
    transition: {
      type: 'slide',
      duration: 1.5,
    },
  },
  {
    id: 'slide-conclusion',
    title: 'Global Urban Future',
    description: 'Trends and projections for urban development',
    content: 'As we\'ve seen, cities are the engines of global growth and innovation. Understanding their distribution, characteristics, and growth patterns is essential for planning sustainable urban futures and addressing global challenges.',
    duration: 35,
    extent: {
      latitude: 0.0,
      longitude: 0.0,
      latitudeDelta: 120.0,
      longitudeDelta: 180.0,
    },
    visibleLayers: ['demo-countries', 'demo-cities'],
    baseMap: 'dark',
    interactive: false,
    autoAdvance: false,
    transition: {
      type: 'fade',
      duration: 2,
    },
  },
];

// Sample map settings for different use cases
export const DEMO_MAP_SETTINGS: MapSettings = {
  title: 'Global Cities Analysis Map',
  description: 'Interactive map for exploring major cities and urban patterns worldwide',
  author: 'FieldSyncPro Demo',
  tags: ['cities', 'urban', 'demographics', 'global'],
  
  defaultExtent: {
    latitude: 20.0,
    longitude: 0.0,
    latitudeDelta: 120.0,
    longitudeDelta: 180.0,
  },
  minZoom: 2,
  maxZoom: 18,
  restrictPanning: false,
  
  coordinateSystem: 'EPSG:3857',
  displayCoordinates: true,
  coordinateFormat: 'decimal',
  coordinatePrecision: 4,
  
  baseMapType: 'satellite',
  baseMapOpacity: 1.0,
  
  showScale: true,
  showNorthArrow: true,
  showZoomControls: true,
  showLayerPanel: true,
  showToolbar: true,
  showMeasurementTools: true,
  
  primaryColor: '#3B82F6',
  secondaryColor: '#1E40AF',
  backgroundColor: '#F8FAFC',
  textColor: '#1E293B',
  fontFamily: 'Inter-Regular',
  fontSize: 14,
  
  logoPosition: 'bottom-right',
  logoSize: 80,
  
  enableWebGL: true,
  enable3D: false,
  renderQuality: 'high',
  maxFeatures: 25000,
  clustering: true,
  
  requireAuthentication: false,
  allowDownload: true,
  allowPrint: true,
  allowShare: true,
};

// Sample coordinate reference systems
export const DEMO_COORDINATE_SYSTEMS = [
  {
    code: 'EPSG:4326',
    name: 'WGS 84',
    description: 'World Geodetic System 1984 - Global geographic coordinate system',
    unit: 'degree',
    type: 'Geographic',
  },
  {
    code: 'EPSG:3857',
    name: 'Web Mercator',
    description: 'Pseudo-Mercator projection used by Google Maps and OpenStreetMap',
    unit: 'meter',
    type: 'Projected',
  },
  {
    code: 'EPSG:4269',
    name: 'NAD83',
    description: 'North American Datum 1983',
    unit: 'degree',
    type: 'Geographic',
  },
  {
    code: 'EPSG:32633',
    name: 'UTM Zone 33N',
    description: 'Universal Transverse Mercator Zone 33 North',
    unit: 'meter',
    type: 'Projected',
  },
];

// Sample base map configurations
export const DEMO_BASE_MAPS = [
  {
    id: 'satellite',
    name: 'Satellite',
    description: 'High-resolution satellite imagery',
    type: 'raster',
    attribution: 'Imagery © Providers',
  },
  {
    id: 'terrain',
    name: 'Terrain',
    description: 'Topographic map with elevation shading',
    type: 'raster',
    attribution: 'Map data © Contributors',
  },
  {
    id: 'street',
    name: 'Street Map',
    description: 'Detailed street and road network',
    type: 'vector',
    attribution: 'Map data © OpenStreetMap contributors',
  },
  {
    id: 'dark',
    name: 'Dark Theme',
    description: 'Dark mode optimized for night viewing',
    type: 'vector',
    attribution: 'Map data © Contributors',
  },
];

// Utility function to get demo data by type
export const getDemoData = (type: 'layers' | 'features' | 'bookmarks' | 'story' | 'settings') => {
  switch (type) {
    case 'layers':
      return DEMO_LAYERS;
    case 'features':
      return DEMO_FEATURES;
    case 'bookmarks':
      return DEMO_BOOKMARKS;
    case 'story':
      return DEMO_STORY_SLIDES;
    case 'settings':
      return DEMO_MAP_SETTINGS;
    default:
      return null;
  }
};

// Utility function to generate random demo features
export const generateRandomFeatures = (count: number, type: 'point' | 'line' | 'polygon' = 'point') => {
  const features: GISFeature[] = [];
  
  for (let i = 0; i < count; i++) {
    const lat = (Math.random() - 0.5) * 180; // -90 to 90
    const lng = (Math.random() - 0.5) * 360; // -180 to 180
    
    let coordinates;
    let featureType;
    
    switch (type) {
      case 'point':
        coordinates = [{ latitude: lat, longitude: lng }];
        featureType = 'point';
        break;
      case 'line':
        coordinates = [
          { latitude: lat, longitude: lng },
          { latitude: lat + (Math.random() - 0.5) * 2, longitude: lng + (Math.random() - 0.5) * 2 },
        ];
        featureType = 'line';
        break;
      case 'polygon':
        const size = 0.1;
        coordinates = [
          { latitude: lat, longitude: lng },
          { latitude: lat + size, longitude: lng },
          { latitude: lat + size, longitude: lng + size },
          { latitude: lat, longitude: lng + size },
          { latitude: lat, longitude: lng }, // Close polygon
        ];
        featureType = 'polygon';
        break;
    }
    
    features.push({
      id: `random-${type}-${i}`,
      type: featureType as any,
      coordinates,
      properties: {
        name: `Random ${type} ${i + 1}`,
        value: Math.round(Math.random() * 100),
        category: ['A', 'B', 'C'][Math.floor(Math.random() * 3)],
      },
      style: {
        fillColor: `#${Math.floor(Math.random()*16777215).toString(16)}`,
        strokeColor: '#000000',
        strokeWidth: 1,
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      author: 'Demo Generator',
    });
  }
  
  return features;
};

export default {
  DEMO_LAYERS,
  DEMO_FEATURES,
  DEMO_BOOKMARKS,
  DEMO_STORY_SLIDES,
  DEMO_MAP_SETTINGS,
  DEMO_COORDINATE_SYSTEMS,
  DEMO_BASE_MAPS,
  getDemoData,
  generateRandomFeatures,
};
