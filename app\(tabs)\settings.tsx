import React, { useState, useCallback, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Switch,
  TouchableOpacity,
  ScrollView,
  Alert,
  Platform,
  TextInput,
  Modal,
  Dimensions
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '@/providers/ThemeProvider';
import { useSettings, AppSettings, UnitUtils } from '@/providers/SettingsProvider';
import { useAuth } from '@/hooks/useAuth';
import { router } from 'expo-router';
import { Picker } from '@react-native-picker/picker';
import {
  Settings as SettingsIcon,
  Moon,
  Sun,
  LogOut,
  User,
  Map,
  Database,
  BarChart3,
  Globe,
  Shield,
  Bell,
  Palette,
  Languages,
  Download,
  Upload,
  Wifi,
  WifiOff,
  Search,
  RotateCcw,
  ChevronRight,
  Info,
  HelpCircle,
  CheckCircle,
  XCircle,
  AlertTriangle,
  X,
  Smartphone,
  Monitor,
  Navigation,
  Layers,
  Target,
  Clock,
  Save,
  Trash2,
  <PERSON>,
  <PERSON>O<PERSON>,
  <PERSON>,
  Key,
  FileText,
  Zap,
  Volume2,
  VolumeX,
  Vibrate
} from 'lucide-react-native';

// Types for settings (imported from SettingsProvider)

interface SettingsItemProps {
  title: string;
  description?: string;
  icon: React.ReactElement;
  action?: React.ReactElement;
  onPress?: () => void;
  showChevron?: boolean;
  disabled?: boolean;
}

interface SettingsSectionProps {
  title: string;
  description?: string;
  children: React.ReactNode;
  collapsible?: boolean;
}

interface NotificationState {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  message: string;
  timestamp: string;
}

// Settings Item Component
function SettingsItem({
  title,
  description,
  icon,
  action,
  onPress,
  showChevron = false,
  disabled = false
}: SettingsItemProps) {
  const { theme } = useTheme();

  return (
    <TouchableOpacity
      style={[
        styles.settingsItem,
        {
          backgroundColor: theme.colors.card,
          opacity: disabled ? 0.6 : 1
        }
      ]}
      onPress={onPress}
      disabled={!onPress || disabled}
      activeOpacity={0.7}
    >
      <View style={styles.settingsItemContent}>
        <View style={styles.settingsItemLeft}>
          <View style={[styles.iconContainer, { backgroundColor: theme.colors.background }]}>
            {icon}
          </View>
          <View style={styles.settingsItemText}>
            <Text style={[styles.settingsItemTitle, { color: theme.colors.text }]}>
              {title}
            </Text>
            {description && (
              <Text style={[styles.settingsItemDescription, { color: theme.colors.muted }]}>
                {description}
              </Text>
            )}
          </View>
        </View>
        <View style={styles.settingsItemRight}>
          {action}
          {showChevron && (
            <ChevronRight
              size={16}
              color={theme.colors.muted}
              style={{ marginLeft: 8 }}
            />
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
}

// Settings Section Component
function SettingsSection({ title, description, children, collapsible = false }: SettingsSectionProps) {
  const { theme } = useTheme();
  const [collapsed, setCollapsed] = useState(false);

  return (
    <View style={styles.section}>
      <TouchableOpacity
        style={styles.sectionHeader}
        onPress={collapsible ? () => setCollapsed(!collapsed) : undefined}
        disabled={!collapsible}
      >
        <View>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            {title}
          </Text>
          {description && (
            <Text style={[styles.sectionDescription, { color: theme.colors.muted }]}>
              {description}
            </Text>
          )}
        </View>
        {collapsible && (
          <ChevronRight
            size={20}
            color={theme.colors.muted}
            style={{
              transform: [{ rotate: collapsed ? '0deg' : '90deg' }],
              marginLeft: 8
            }}
          />
        )}
      </TouchableOpacity>
      {!collapsed && (
        <View style={styles.sectionContent}>
          {children}
        </View>
      )}
    </View>
  );
}

export default function SettingsScreen() {
  try {
    const { theme, setTheme, isDark } = useTheme();
    const {
      settings,
      updateSetting,
      updateSettings,
      resetSettings,
      resetSection,
      isLoading: settingsLoading,
      error: settingsError,
      unitUtils
    } = useSettings();

    const [searchQuery, setSearchQuery] = useState('');
    const [notifications, setNotifications] = useState<NotificationState[]>([]);
    const [showLanguageModal, setShowLanguageModal] = useState(false);
    const [showCoordinateModal, setShowCoordinateModal] = useState(false);
    const [showBaseMapModal, setShowBaseMapModal] = useState(false);
    const [showSessionTimeoutModal, setShowSessionTimeoutModal] = useState(false);
    const [showUnitsModal, setShowUnitsModal] = useState(false);

    // Available options
    const languages = [
      { code: 'en', name: 'English', flag: '🇺🇸' },
      { code: 'fr', name: 'Français', flag: '🇫🇷' },
      { code: 'es', name: 'Español', flag: '🇪🇸' },
      { code: 'ar', name: 'العربية', flag: '🇸🇦' },
    ];

    const coordinateSystems = [
      { code: 'EPSG:4326', name: 'WGS84 (EPSG:4326)', description: 'World Geodetic System 1984' },
      { code: 'EPSG:3857', name: 'Web Mercator (EPSG:3857)', description: 'Web Mercator projection' },
      { code: 'EPSG:4269', name: 'NAD83 (EPSG:4269)', description: 'North American Datum 1983' },
      { code: 'EPSG:2154', name: 'RGF93 (EPSG:2154)', description: 'French Lambert-93' },
    ];

    const baseMaps = [
      { id: 'satellite', name: 'Satellite', description: 'High-resolution satellite imagery' },
      { id: 'street', name: 'Street Map', description: 'Detailed street and road information' },
      { id: 'topographic', name: 'Topographic', description: 'Terrain and elevation data' },
      { id: 'hybrid', name: 'Hybrid', description: 'Satellite with street overlays' },
    ];

    const sessionTimeouts = [15, 30, 60, 120, 240]; // minutes

    // Unit options
    const unitOptions = [
      {
        id: 'metric',
        name: 'Metric',
        description: 'Meters, kilometers, square meters, hectares',
        distance: 'meters',
        area: 'square meters'
      },
      {
        id: 'imperial',
        name: 'Imperial',
        description: 'Feet, miles, square feet, acres',
        distance: 'feet',
        area: 'square feet'
      },
    ];

    // Add a loading state for theme
    if (!theme || !theme.colors) {
      return (
        <SafeAreaView style={styles.container}>
          <Text>Loading theme...</Text>
        </SafeAreaView>
      );
    }

    let authData;
    try {
      authData = useAuth();
    } catch (authError) {
      console.error('Auth context error:', authError);
      return (
        <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
          <View style={styles.header}>
            <Text style={[styles.title, { color: theme.colors.text }]}>Settings</Text>
            <Text style={[styles.subtitle, { color: theme.colors.muted }]}>
              ⚙️ Configure your app preferences
            </Text>
          </View>
          <View style={styles.scrollContent}>
            <Text style={[styles.title, { color: theme.colors.text }]}>
              Authentication service is loading...
            </Text>
          </View>
        </SafeAreaView>
      );
    }

    const { user, logout } = authData;

    // Use user data directly
    const displayName = user?.name || user?.email?.split('@')[0] || 'User';
    const displayEmail = user?.email || '<EMAIL>';
    const displayRole = user?.role || 'user';

    // Notification system
    const showNotification = useCallback((
      type: 'success' | 'error' | 'warning' | 'info',
      message: string
    ) => {
      const notification: NotificationState = {
        id: `notification-${Date.now()}`,
        type,
        message,
        timestamp: new Date().toISOString(),
      };

      setNotifications(prev => [notification, ...prev.slice(0, 4)]);

      // Auto-dismiss after 3 seconds
      setTimeout(() => {
        setNotifications(prev => prev.filter(n => n.id !== notification.id));
      }, 3000);
    }, []);

    // Settings handlers
    const handleThemeChange = useCallback(async (newTheme: 'light' | 'dark' | 'auto') => {
      try {
        await updateSetting('theme', newTheme);
        setTheme(newTheme);
        showNotification('success', `Theme changed to ${newTheme}`);
      } catch (error) {
        console.error('Error changing theme:', error);
        showNotification('error', 'Failed to change theme');
      }
    }, [updateSetting, setTheme, showNotification]);

    const handleSettingChange = useCallback(async (key: keyof AppSettings, value: any) => {
      try {
        await updateSetting(key, value);

        // Special handling for certain settings
        if (key === 'language') {
          const selectedLang = languages.find(lang => lang.code === value);
          showNotification('success', `Language changed to ${selectedLang?.name || value}`);
        } else if (key === 'coordinateSystem') {
          const selectedCoord = coordinateSystems.find(coord => coord.code === value);
          showNotification('success', `Coordinate system changed to ${selectedCoord?.name || value}`);
        } else if (key === 'baseMap') {
          const selectedMap = baseMaps.find(map => map.id === value);
          showNotification('success', `Base map changed to ${selectedMap?.name || value}`);
        } else if (key === 'units') {
          const selectedUnit = unitOptions.find(unit => unit.id === value);
          showNotification('success', `Units changed to ${selectedUnit?.name || value}`);
        } else {
          showNotification('info', 'Setting updated successfully');
        }
      } catch (error) {
        console.error('Error updating setting:', error);
        showNotification('error', 'Failed to update setting');
      }
    }, [updateSetting, showNotification, languages, coordinateSystems, baseMaps, unitOptions]);

    const handleLanguageSelect = useCallback(async (languageCode: string) => {
      await handleSettingChange('language', languageCode);
      setShowLanguageModal(false);
    }, [handleSettingChange]);

    const handleCoordinateSystemSelect = useCallback(async (coordSystem: string) => {
      await handleSettingChange('coordinateSystem', coordSystem);
      setShowCoordinateModal(false);
    }, [handleSettingChange]);

    const handleBaseMapSelect = useCallback(async (baseMapId: string) => {
      await handleSettingChange('baseMap', baseMapId);
      setShowBaseMapModal(false);
    }, [handleSettingChange]);

    const handleSessionTimeoutSelect = useCallback(async (timeout: number) => {
      await handleSettingChange('sessionTimeout', timeout);
      setShowSessionTimeoutModal(false);
    }, [handleSettingChange]);

    const handleUnitsSelect = useCallback(async (units: 'metric' | 'imperial') => {
      await handleSettingChange('units', units);
      setShowUnitsModal(false);
    }, [handleSettingChange]);

    const handleResetSection = useCallback(async (section: string) => {
      const performReset = async () => {
        try {
          if (section === 'all') {
            await resetSettings();
            showNotification('success', 'All settings reset to defaults');
          } else {
            await resetSection(section);
            showNotification('success', `${section} settings reset to defaults`);
          }
        } catch (error) {
          console.error('Error resetting settings:', error);
          showNotification('error', 'Failed to reset settings');
        }
      };

      if (Platform.OS === 'web') {
        if (confirm(`Reset all ${section} settings to defaults?`)) {
          await performReset();
        }
      } else {
        Alert.alert(
          'Reset Settings',
          `Reset all ${section} settings to defaults?`,
          [
            { text: 'Cancel', style: 'cancel' },
            {
              text: 'Reset',
              style: 'destructive',
              onPress: performReset
            },
          ]
        );
      }
    }, [resetSettings, resetSection, showNotification]);

    const handleExportSettings = useCallback(async () => {
      try {
        const exportData = {
          settings,
          exportDate: new Date().toISOString(),
          version: '1.0.0',
          appName: 'FieldSync Pro'
        };

        if (Platform.OS === 'web') {
          // For web, create a download link
          const dataStr = JSON.stringify(exportData, null, 2);
          const dataBlob = new Blob([dataStr], { type: 'application/json' });
          const url = URL.createObjectURL(dataBlob);
          const link = document.createElement('a');
          link.href = url;
          link.download = `fieldsync-settings-${new Date().toISOString().split('T')[0]}.json`;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          URL.revokeObjectURL(url);
        } else {
          // For mobile, you could use react-native-fs or similar
          // For now, just copy to clipboard or show the data
          console.log('Export data:', exportData);
        }

        showNotification('success', 'Settings exported successfully');
      } catch (error) {
        console.error('Error exporting settings:', error);
        showNotification('error', 'Failed to export settings');
      }
    }, [settings, showNotification]);

    const handleLogout = async () => {
      const performLogout = async () => {
        try {
          await logout();
          // Navigation will be handled by the AuthProvider
        } catch (error) {
          console.error('Logout error:', error);
          const errorMessage = error instanceof Error ? error.message : 'Failed to log out. Please try again.';
          if (Platform.OS === 'web') {
            alert(errorMessage);
          } else {
            Alert.alert('Error', errorMessage);
          }
        }
      };

      if (Platform.OS === 'web') {
        if (confirm('Are you sure you want to log out?')) {
          await performLogout();
        }
      } else {
        Alert.alert(
          'Confirm Logout',
          'Are you sure you want to log out?',
          [
            { text: 'Cancel', style: 'cancel' },
            {
              text: 'Log Out',
              style: 'destructive',
              onPress: performLogout
            },
          ]
        );
      }
    };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: theme.colors.text }]}>Settings</Text>
        <Text style={[styles.subtitle, { color: theme.colors.muted }]}>
          ⚙️ Configure your app preferences
        </Text>
      </View>

      {/* Search Bar */}
      <View style={[styles.searchContainer, { backgroundColor: theme.colors.card }]}>
        <Search size={20} color={theme.colors.muted} />
        <TextInput
          style={[styles.searchInput, { color: theme.colors.text }]}
          placeholder="Search settings..."
          placeholderTextColor={theme.colors.muted}
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>

      <ScrollView contentContainerStyle={styles.scrollContent} showsVerticalScrollIndicator={false}>
        {/* User Profile Section */}
        {user && (
          <View style={[styles.profileSection, { backgroundColor: theme.colors.card }]}>
            <View style={styles.profileInfo}>
              <View style={[styles.profileAvatar, { backgroundColor: theme.colors.primary }]}>
                <User size={24} color="white" />
              </View>
              <View style={styles.profileDetails}>
                <Text style={[styles.profileName, { color: theme.colors.text }]}>{displayName}</Text>
                <Text style={[styles.profileEmail, { color: theme.colors.muted }]}>{displayEmail}</Text>
                <View style={[styles.roleBadge, { backgroundColor: theme.colors.info + '20' }]}>
                  <Text style={[styles.roleText, { color: theme.colors.info }]}>
                    {displayRole.charAt(0).toUpperCase() + displayRole.slice(1)}
                  </Text>
                </View>
              </View>
            </View>
          </View>
        )}

        {/* Map & Display Settings */}
        <SettingsSection
          title="Map & Display"
          description="Configure map appearance and coordinate systems"
          collapsible={true}
        >
          <SettingsItem
            title="Coordinate System"
            description={`Current: ${settings.coordinateSystem}`}
            icon={<Globe size={20} color={theme.colors.primary} />}
            action={
              <Text style={[styles.valueText, { color: theme.colors.muted }]}>
                {settings.coordinateSystem}
              </Text>
            }
            showChevron={true}
            onPress={() => setShowCoordinateModal(true)}
          />

          <SettingsItem
            title="Base Map"
            description={`Current: ${settings.baseMap}`}
            icon={<Map size={20} color={theme.colors.primary} />}
            action={
              <Text style={[styles.valueText, { color: theme.colors.muted }]}>
                {settings.baseMap}
              </Text>
            }
            showChevron={true}
            onPress={() => setShowBaseMapModal(true)}
          />

          <SettingsItem
            title="Units"
            description={`Current: ${unitOptions.find(unit => unit.id === settings.units)?.name || 'Metric'}`}
            icon={<Target size={20} color={theme.colors.primary} />}
            action={
              <Text style={[styles.valueText, { color: theme.colors.muted }]}>
                {unitOptions.find(unit => unit.id === settings.units)?.name || 'Metric'}
              </Text>
            }
            showChevron={true}
            onPress={() => setShowUnitsModal(true)}
          />

          <SettingsItem
            title="Show Scale Bar"
            description="Display scale information on map"
            icon={<Layers size={20} color={theme.colors.primary} />}
            action={
              <Switch
                value={settings.showScale}
                onValueChange={(value) => handleSettingChange('showScale', value)}
                trackColor={{ false: '#767577', true: theme.colors.primary + '40' }}
                thumbColor={settings.showScale ? theme.colors.primary : '#f4f3f4'}
              />
            }
          />

          <SettingsItem
            title="Show Coordinates"
            description="Display current coordinates on map"
            icon={<Navigation size={20} color={theme.colors.primary} />}
            action={
              <Switch
                value={settings.showCoordinates}
                onValueChange={(value) => handleSettingChange('showCoordinates', value)}
                trackColor={{ false: '#767577', true: theme.colors.primary + '40' }}
                thumbColor={settings.showCoordinates ? theme.colors.primary : '#f4f3f4'}
              />
            }
          />
        </SettingsSection>

        {/* Data & Sync Settings */}
        <SettingsSection
          title="Data & Sync"
          description="Configure data synchronization and storage"
          collapsible={true}
        >
          <SettingsItem
            title="Auto Sync"
            description="Automatically sync data when connected"
            icon={<Wifi size={20} color={theme.colors.primary} />}
            action={
              <Switch
                value={settings.autoSync}
                onValueChange={(value) => handleSettingChange('autoSync', value)}
                trackColor={{ false: '#767577', true: theme.colors.primary + '40' }}
                thumbColor={settings.autoSync ? theme.colors.primary : '#f4f3f4'}
              />
            }
          />

          <SettingsItem
            title="Offline Storage"
            description="Store data locally for offline access"
            icon={<Database size={20} color={theme.colors.primary} />}
            action={
              <Switch
                value={settings.offlineStorage}
                onValueChange={(value) => handleSettingChange('offlineStorage', value)}
                trackColor={{ false: '#767577', true: theme.colors.primary + '40' }}
                thumbColor={settings.offlineStorage ? theme.colors.primary : '#f4f3f4'}
              />
            }
          />

          <SettingsItem
            title="Cloud Backup"
            description="Backup data to cloud storage"
            icon={<Upload size={20} color={theme.colors.primary} />}
            action={
              <Switch
                value={settings.cloudBackup}
                onValueChange={(value) => handleSettingChange('cloudBackup', value)}
                trackColor={{ false: '#767577', true: theme.colors.primary + '40' }}
                thumbColor={settings.cloudBackup ? theme.colors.primary : '#f4f3f4'}
              />
            }
          />
        </SettingsSection>

        {/* UI & Accessibility */}
        <SettingsSection
          title="Interface & Accessibility"
          description="Customize appearance and accessibility options"
          collapsible={true}
        >
          <SettingsItem
            title="Theme"
            description="Choose your preferred theme"
            icon={isDark ? <Moon size={20} color={theme.colors.primary} /> : <Sun size={20} color={theme.colors.warning} />}
            action={
              <Switch
                value={isDark}
                onValueChange={(value) => handleThemeChange(value ? 'dark' : 'light')}
                trackColor={{ false: '#767577', true: theme.colors.primary + '40' }}
                thumbColor={isDark ? theme.colors.primary : '#f4f3f4'}
              />
            }
          />

          <SettingsItem
            title="Language"
            description={`Current: ${languages.find(lang => lang.code === settings.language)?.name || 'English'}`}
            icon={<Languages size={20} color={theme.colors.primary} />}
            action={
              <Text style={[styles.valueText, { color: theme.colors.muted }]}>
                {languages.find(lang => lang.code === settings.language)?.flag || '🇺🇸'} {languages.find(lang => lang.code === settings.language)?.name || 'English'}
              </Text>
            }
            showChevron={true}
            onPress={() => setShowLanguageModal(true)}
          />

          <SettingsItem
            title="High Contrast"
            description="Increase contrast for better visibility"
            icon={<Eye size={20} color={theme.colors.primary} />}
            action={
              <Switch
                value={settings.highContrast}
                onValueChange={(value) => handleSettingChange('highContrast', value)}
                trackColor={{ false: '#767577', true: theme.colors.primary + '40' }}
                thumbColor={settings.highContrast ? theme.colors.primary : '#f4f3f4'}
              />
            }
          />
        </SettingsSection>

        {/* Notifications */}
        <SettingsSection
          title="Notifications"
          description="Configure notification preferences"
          collapsible={true}
        >
          <SettingsItem
            title="Push Notifications"
            description="Receive push notifications"
            icon={<Bell size={20} color={theme.colors.primary} />}
            action={
              <Switch
                value={settings.pushNotifications}
                onValueChange={(value) => handleSettingChange('pushNotifications', value)}
                trackColor={{ false: '#767577', true: theme.colors.primary + '40' }}
                thumbColor={settings.pushNotifications ? theme.colors.primary : '#f4f3f4'}
              />
            }
          />

          <SettingsItem
            title="Sound"
            description="Play notification sounds"
            icon={<Volume2 size={20} color={theme.colors.primary} />}
            action={
              <Switch
                value={settings.soundEnabled}
                onValueChange={(value) => handleSettingChange('soundEnabled', value)}
                trackColor={{ false: '#767577', true: theme.colors.primary + '40' }}
                thumbColor={settings.soundEnabled ? theme.colors.primary : '#f4f3f4'}
              />
            }
          />

          <SettingsItem
            title="Vibration"
            description="Vibrate for notifications"
            icon={<Vibrate size={20} color={theme.colors.primary} />}
            action={
              <Switch
                value={settings.vibrationEnabled}
                onValueChange={(value) => handleSettingChange('vibrationEnabled', value)}
                trackColor={{ false: '#767577', true: theme.colors.primary + '40' }}
                thumbColor={settings.vibrationEnabled ? theme.colors.primary : '#f4f3f4'}
              />
            }
          />
        </SettingsSection>

        {/* Security */}
        <SettingsSection
          title="Security & Privacy"
          description="Manage security and privacy settings"
          collapsible={true}
        >
          <SettingsItem
            title="Data Encryption"
            description="Encrypt sensitive data"
            icon={<Lock size={20} color={theme.colors.primary} />}
            action={
              <Switch
                value={settings.dataEncryption}
                onValueChange={(value) => handleSettingChange('dataEncryption', value)}
                trackColor={{ false: '#767577', true: theme.colors.primary + '40' }}
                thumbColor={settings.dataEncryption ? theme.colors.primary : '#f4f3f4'}
              />
            }
          />

          <SettingsItem
            title="Session Timeout"
            description={`Auto logout after ${settings.sessionTimeout} minutes`}
            icon={<Clock size={20} color={theme.colors.primary} />}
            action={
              <Text style={[styles.valueText, { color: theme.colors.muted }]}>
                {settings.sessionTimeout}m
              </Text>
            }
            showChevron={true}
            onPress={() => setShowSessionTimeoutModal(true)}
          />
        </SettingsSection>

        {/* Reset & Actions */}
        <SettingsSection title="Actions">
          <SettingsItem
            title="Reset All Settings"
            description="Reset all settings to default values"
            icon={<RotateCcw size={20} color={theme.colors.warning} />}
            showChevron={true}
            onPress={() => handleResetSection('all')}
          />

          <SettingsItem
            title="Export Settings"
            description="Export current settings configuration"
            icon={<Download size={20} color={theme.colors.info} />}
            showChevron={true}
            onPress={handleExportSettings}
          />

          <SettingsItem
            title="Help & Support"
            description="Get help and contact support"
            icon={<HelpCircle size={20} color={theme.colors.info} />}
            showChevron={true}
            onPress={() => showNotification('info', 'Help center opened')}
          />
        </SettingsSection>

        {/* Logout Button */}
        {user && (
          <TouchableOpacity
            style={[styles.logoutButton, { backgroundColor: theme.colors.error + '20' }]}
            onPress={handleLogout}
          >
            <LogOut size={20} color={theme.colors.error} />
            <Text style={[styles.logoutText, { color: theme.colors.error }]}>Log Out</Text>
          </TouchableOpacity>
        )}

        {/* App Version */}
        <Text style={[styles.versionText, { color: theme.colors.muted }]}>
          FieldSync Pro v1.0.0
        </Text>

        {/* Notifications Display */}
        <View style={styles.notificationsContainer}>
          {notifications.map(notification => (
            <View
              key={notification.id}
              style={[
                styles.notification,
                {
                  backgroundColor:
                    notification.type === 'success' ? theme.colors.success :
                    notification.type === 'error' ? theme.colors.error :
                    notification.type === 'warning' ? theme.colors.warning :
                    theme.colors.info,
                }
              ]}
            >
              <View style={styles.notificationContent}>
                {notification.type === 'success' && <CheckCircle size={16} color="white" />}
                {notification.type === 'error' && <XCircle size={16} color="white" />}
                {notification.type === 'warning' && <AlertTriangle size={16} color="white" />}
                {notification.type === 'info' && <Info size={16} color="white" />}
                <Text style={styles.notificationText}>{notification.message}</Text>
              </View>
            </View>
          ))}
        </View>
      </ScrollView>

      {/* Language Selection Modal */}
      <Modal
        visible={showLanguageModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowLanguageModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: theme.colors.card }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: theme.colors.text }]}>Select Language</Text>
              <TouchableOpacity onPress={() => setShowLanguageModal(false)}>
                <X size={24} color={theme.colors.muted} />
              </TouchableOpacity>
            </View>
            <ScrollView style={styles.modalList}>
              {languages.map((language) => (
                <TouchableOpacity
                  key={language.code}
                  style={[
                    styles.modalItem,
                    settings.language === language.code && { backgroundColor: theme.colors.primary + '20' }
                  ]}
                  onPress={() => handleLanguageSelect(language.code)}
                >
                  <Text style={[styles.modalItemFlag, { color: theme.colors.text }]}>
                    {language.flag}
                  </Text>
                  <Text style={[styles.modalItemText, { color: theme.colors.text }]}>
                    {language.name}
                  </Text>
                  {settings.language === language.code && (
                    <CheckCircle size={20} color={theme.colors.primary} />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </View>
      </Modal>

      {/* Coordinate System Selection Modal */}
      <Modal
        visible={showCoordinateModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowCoordinateModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: theme.colors.card }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: theme.colors.text }]}>Select Coordinate System</Text>
              <TouchableOpacity onPress={() => setShowCoordinateModal(false)}>
                <X size={24} color={theme.colors.muted} />
              </TouchableOpacity>
            </View>
            <ScrollView style={styles.modalList}>
              {coordinateSystems.map((coord) => (
                <TouchableOpacity
                  key={coord.code}
                  style={[
                    styles.modalItem,
                    settings.coordinateSystem === coord.code && { backgroundColor: theme.colors.primary + '20' }
                  ]}
                  onPress={() => handleCoordinateSystemSelect(coord.code)}
                >
                  <View style={styles.modalItemContent}>
                    <Text style={[styles.modalItemText, { color: theme.colors.text }]}>
                      {coord.name}
                    </Text>
                    <Text style={[styles.modalItemDescription, { color: theme.colors.muted }]}>
                      {coord.description}
                    </Text>
                  </View>
                  {settings.coordinateSystem === coord.code && (
                    <CheckCircle size={20} color={theme.colors.primary} />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </View>
      </Modal>

      {/* Base Map Selection Modal */}
      <Modal
        visible={showBaseMapModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowBaseMapModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: theme.colors.card }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: theme.colors.text }]}>Select Base Map</Text>
              <TouchableOpacity onPress={() => setShowBaseMapModal(false)}>
                <X size={24} color={theme.colors.muted} />
              </TouchableOpacity>
            </View>
            <ScrollView style={styles.modalList}>
              {baseMaps.map((baseMap) => (
                <TouchableOpacity
                  key={baseMap.id}
                  style={[
                    styles.modalItem,
                    settings.baseMap === baseMap.id && { backgroundColor: theme.colors.primary + '20' }
                  ]}
                  onPress={() => handleBaseMapSelect(baseMap.id)}
                >
                  <View style={styles.modalItemContent}>
                    <Text style={[styles.modalItemText, { color: theme.colors.text }]}>
                      {baseMap.name}
                    </Text>
                    <Text style={[styles.modalItemDescription, { color: theme.colors.muted }]}>
                      {baseMap.description}
                    </Text>
                  </View>
                  {settings.baseMap === baseMap.id && (
                    <CheckCircle size={20} color={theme.colors.primary} />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </View>
      </Modal>

      {/* Session Timeout Selection Modal */}
      <Modal
        visible={showSessionTimeoutModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowSessionTimeoutModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: theme.colors.card }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: theme.colors.text }]}>Select Session Timeout</Text>
              <TouchableOpacity onPress={() => setShowSessionTimeoutModal(false)}>
                <X size={24} color={theme.colors.muted} />
              </TouchableOpacity>
            </View>
            <ScrollView style={styles.modalList}>
              {sessionTimeouts.map((timeout) => (
                <TouchableOpacity
                  key={timeout}
                  style={[
                    styles.modalItem,
                    settings.sessionTimeout === timeout && { backgroundColor: theme.colors.primary + '20' }
                  ]}
                  onPress={() => handleSessionTimeoutSelect(timeout)}
                >
                  <Text style={[styles.modalItemText, { color: theme.colors.text }]}>
                    {timeout} minutes
                  </Text>
                  {settings.sessionTimeout === timeout && (
                    <CheckCircle size={20} color={theme.colors.primary} />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </View>
      </Modal>

      {/* Units Selection Modal */}
      <Modal
        visible={showUnitsModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowUnitsModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: theme.colors.card }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: theme.colors.text }]}>Select Units</Text>
              <TouchableOpacity onPress={() => setShowUnitsModal(false)}>
                <X size={24} color={theme.colors.muted} />
              </TouchableOpacity>
            </View>
            <ScrollView style={styles.modalList}>
              {unitOptions.map((unit) => (
                <TouchableOpacity
                  key={unit.id}
                  style={[
                    styles.modalItem,
                    settings.units === unit.id && { backgroundColor: theme.colors.primary + '20' }
                  ]}
                  onPress={() => handleUnitsSelect(unit.id as 'metric' | 'imperial')}
                >
                  <View style={styles.modalItemContent}>
                    <Text style={[styles.modalItemText, { color: theme.colors.text }]}>
                      {unit.name}
                    </Text>
                    <Text style={[styles.modalItemDescription, { color: theme.colors.muted }]}>
                      {unit.description}
                    </Text>
                  </View>
                  {settings.units === unit.id && (
                    <CheckCircle size={20} color={theme.colors.primary} />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
  } catch (error) {
    console.error('Settings screen error:', error);
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Settings</Text>
          <Text style={styles.subtitle}>Something went wrong. Please try again.</Text>
        </View>
      </SafeAreaView>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 16,
    paddingTop: 8,
    paddingBottom: 12,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  subtitle: {
    fontSize: 14,
    marginTop: 4,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 16,
    marginBottom: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    gap: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    paddingVertical: 0,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 32,
  },
  profileSection: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  profileInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  profileAvatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  profileDetails: {
    flex: 1,
  },
  profileName: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
  },
  profileEmail: {
    fontSize: 14,
    marginBottom: 8,
  },
  roleBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  roleText: {
    fontSize: 12,
    fontWeight: '500',
  },
  section: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  sectionDescription: {
    fontSize: 14,
    marginTop: 2,
    lineHeight: 18,
  },
  sectionContent: {
    gap: 8,
  },
  settingsItem: {
    borderRadius: 12,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  settingsItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
  },
  settingsItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  settingsItemText: {
    flex: 1,
  },
  settingsItemTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 2,
  },
  settingsItemDescription: {
    fontSize: 14,
    lineHeight: 18,
  },
  settingsItemRight: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 16,
  },
  settingsItemAction: {
    marginLeft: 16,
  },
  valueText: {
    fontSize: 14,
    fontWeight: '500',
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    marginTop: 24,
    marginBottom: 16,
  },
  logoutText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  versionText: {
    textAlign: 'center',
    fontSize: 12,
    marginBottom: 16,
  },
  notificationsContainer: {
    position: 'absolute',
    top: 100,
    right: 16,
    left: 16,
    gap: 8,
    zIndex: 1000,
    pointerEvents: 'none',
  },
  notification: {
    borderRadius: 8,
    padding: 12,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  notificationContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  notificationText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
    flex: 1,
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
    paddingBottom: Platform.OS === 'ios' ? 34 : 20,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  modalList: {
    maxHeight: 400,
  },
  modalItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.05)',
  },
  modalItemContent: {
    flex: 1,
  },
  modalItemText: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 2,
  },
  modalItemDescription: {
    fontSize: 14,
    lineHeight: 18,
  },
  modalItemFlag: {
    fontSize: 20,
    marginRight: 12,
  },
});
