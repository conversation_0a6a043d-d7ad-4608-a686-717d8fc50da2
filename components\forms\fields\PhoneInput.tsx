import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import { useTheme } from '@/hooks/useTheme';
import { Phone, CheckCircle, XCircle, Globe } from 'lucide-react-native';

interface PhoneInputProps {
  value?: string;
  onChange: (value: string) => void;
  placeholder?: string;
  required?: boolean;
  error?: string;
  disabled?: boolean;
  enableValidation?: boolean;
}

export default function PhoneInput({
  value = '',
  onChange,
  placeholder = 'Enter phone number',
  required = false,
  error,
  disabled = false,
  enableValidation = true,
}: PhoneInputProps) {
  const { theme } = useTheme();
  const [isValidating, setIsValidating] = useState(false);
  const [validationResult, setValidationResult] = useState<{
    isValid: boolean;
    formatted?: string;
    country?: string;
    type?: string;
  } | null>(null);

  const validatePhoneNumber = async (phoneNumber: string) => {
    if (!enableValidation || !phoneNumber.trim()) {
      setValidationResult(null);
      return;
    }

    setIsValidating(true);
    
    try {
      // Simulate phone number validation
      // In a real implementation, this would use a service like Google's libphonenumber
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const cleaned = phoneNumber.replace(/\D/g, '');
      const isValid = cleaned.length >= 10 && cleaned.length <= 15;
      
      if (isValid) {
        const formatted = formatPhoneNumber(cleaned);
        setValidationResult({
          isValid: true,
          formatted,
          country: 'United States',
          type: 'Mobile',
        });
      } else {
        setValidationResult({
          isValid: false,
        });
      }
    } catch (error) {
      setValidationResult({ isValid: false });
    } finally {
      setIsValidating(false);
    }
  };

  const formatPhoneNumber = (phoneNumber: string) => {
    const cleaned = phoneNumber.replace(/\D/g, '');
    
    if (cleaned.length === 10) {
      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
    } else if (cleaned.length === 11 && cleaned[0] === '1') {
      return `+1 (${cleaned.slice(1, 4)}) ${cleaned.slice(4, 7)}-${cleaned.slice(7)}`;
    }
    
    return phoneNumber;
  };

  const handleTextChange = (text: string) => {
    onChange(text);
    
    // Debounced validation
    setTimeout(() => {
      validatePhoneNumber(text);
    }, 1000);
  };

  const renderValidationIndicator = () => {
    if (isValidating) {
      return (
        <View style={styles.validationIndicator}>
          <Text style={[styles.validationText, { color: theme.colors.muted }]}>
            Validating...
          </Text>
        </View>
      );
    }

    if (validationResult) {
      return (
        <View style={styles.validationIndicator}>
          {validationResult.isValid ? (
            <>
              <CheckCircle size={16} color={theme.colors.success} />
              <View style={styles.validationDetails}>
                <Text style={[styles.validationText, { color: theme.colors.success }]}>
                  Valid phone number
                </Text>
                {validationResult.formatted && (
                  <Text style={[styles.formattedText, { color: theme.colors.muted }]}>
                    {validationResult.formatted}
                  </Text>
                )}
                {validationResult.country && (
                  <Text style={[styles.countryText, { color: theme.colors.muted }]}>
                    {validationResult.country} • {validationResult.type}
                  </Text>
                )}
              </View>
            </>
          ) : (
            <>
              <XCircle size={16} color={theme.colors.error} />
              <Text style={[styles.validationText, { color: theme.colors.error }]}>
                Invalid phone number format
              </Text>
            </>
          )}
        </View>
      );
    }

    return null;
  };

  return (
    <View style={styles.container}>
      <View style={[
        styles.inputContainer,
        {
          borderColor: error ? theme.colors.error : 
                      validationResult?.isValid ? theme.colors.success :
                      theme.colors.border,
          backgroundColor: disabled ? theme.colors.muted + '20' : theme.colors.card,
        }
      ]}>
        <View style={styles.inputIcon}>
          <Phone size={20} color={theme.colors.muted} />
        </View>

        <TextInput
          style={[
            styles.input,
            {
              color: disabled ? theme.colors.muted : theme.colors.text,
            }
          ]}
          value={value}
          onChangeText={handleTextChange}
          placeholder={placeholder}
          placeholderTextColor={theme.colors.placeholder}
          editable={!disabled}
          keyboardType="phone-pad"
          autoCapitalize="none"
          autoCorrect={false}
        />

        {enableValidation && (
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: theme.colors.secondary }]}
            onPress={() => validatePhoneNumber(value)}
            disabled={disabled || isValidating}
          >
            <Globe size={18} color="white" />
          </TouchableOpacity>
        )}
      </View>

      {renderValidationIndicator()}

      {error && (
        <Text style={[styles.errorText, { color: theme.colors.error }]}>
          {error}
        </Text>
      )}

      {required && !value && (
        <Text style={[styles.requiredText, { color: theme.colors.muted }]}>
          * Required field
        </Text>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 4,
    gap: 8,
  },
  inputIcon: {
    paddingVertical: 8,
  },
  input: {
    flex: 1,
    paddingVertical: 12,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
  },
  actionButton: {
    padding: 8,
    borderRadius: 6,
  },
  validationIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    gap: 8,
  },
  validationDetails: {
    flex: 1,
  },
  validationText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },
  formattedText: {
    fontSize: 11,
    fontFamily: 'Inter-Regular',
    marginTop: 2,
  },
  countryText: {
    fontSize: 11,
    fontFamily: 'Inter-Regular',
    marginTop: 1,
  },
  errorText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    marginTop: 4,
  },
  requiredText: {
    fontSize: 11,
    fontFamily: 'Inter-Regular',
    marginTop: 4,
  },
});
