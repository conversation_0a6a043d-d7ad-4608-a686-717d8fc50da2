import { useState, useEffect, useCallback } from 'react';
import { projectsApi } from '@/lib/api/projects';
import { ApiHelpers } from '@/lib/api/base';
import { Project, ProjectWithStats, ProjectInsert, ProjectUpdate } from '@/types/database';
import { isDevelopmentMode } from '@/lib/supabase';

// Mock data for development
const MOCK_PROJECTS: ProjectWithStats[] = [
  {
    id: '1',
    name: 'Forest Biodiversity Survey',
    description: 'Monitoring plant and animal species in the Pacific Northwest',
    status: 'active',
    created_by: 'user-1',
    organization: 'FieldSync Pro',
    created_at: new Date(Date.now() - 1000000).toISOString(),
    updated_at: new Date(Date.now() - 500000).toISOString(),
    start_date: null,
    end_date: null,
    region: {
      type: 'Point',
      coordinates: [-122.6784, 45.5152]
    },
    settings: {},
    metadata: {},
    stats: {
      project_id: '1',
      total_forms: 2,
      total_submissions: 15,
      completed_submissions: 12,
      team_count: 1,
      completion_rate: 80
    }
  },
  {
    id: '2',
    name: 'Urban Water Quality Assessment',
    description: 'Collecting water samples from metropolitan area rivers and streams',
    status: 'active',
    created_by: 'user-1',
    organization: 'FieldSync Pro',
    created_at: new Date(Date.now() - 2000000).toISOString(),
    updated_at: new Date(Date.now() - 1000000).toISOString(),
    start_date: null,
    end_date: null,
    region: {
      type: 'Point',
      coordinates: [-74.0060, 40.7128]
    },
    settings: {},
    metadata: {},
    stats: {
      project_id: '2',
      total_forms: 1,
      total_submissions: 8,
      completed_submissions: 8,
      team_count: 1,
      completion_rate: 100
    }
  },
  {
    id: '3',
    name: 'Agricultural Soil Analysis',
    description: 'Monitoring soil health in farming regions',
    status: 'draft',
    created_by: 'user-2',
    organization: 'FieldSync Pro',
    created_at: new Date(Date.now() - 3000000).toISOString(),
    updated_at: new Date(Date.now() - 1500000).toISOString(),
    start_date: null,
    end_date: null,
    region: {
      type: 'Point',
      coordinates: [-98.5795, 39.8283]
    },
    settings: {},
    metadata: {},
    stats: {
      project_id: '3',
      total_forms: 0,
      total_submissions: 0,
      completed_submissions: 0,
      team_count: 2,
      completion_rate: 0
    }
  }
];

export interface ProjectFilters {
  status?: 'draft' | 'active' | 'completed' | 'archived';
  search?: string;
  teamId?: string;
  userId?: string;
}

export interface CreateProjectData {
  name: string;
  description?: string;
  teams?: string[];
  region?: {
    latitude: number;
    longitude: number;
    latitudeDelta?: number;
    longitudeDelta?: number;
  };
  settings?: Record<string, any>;
}

export function useProjects() {
  const [projects, setProjects] = useState<ProjectWithStats[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isDev] = useState(isDevelopmentMode());

  // Mock functions for development
  const mockFetchProjects = useCallback(async (filters?: ProjectFilters) => {
    await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate network delay
    
    let filteredProjects = [...MOCK_PROJECTS];
    
    if (filters?.status) {
      filteredProjects = filteredProjects.filter(p => p.status === filters.status);
    }
    
    if (filters?.search) {
      const searchLower = filters.search.toLowerCase();
      filteredProjects = filteredProjects.filter(p =>
        p.name.toLowerCase().includes(searchLower) ||
        (p.description && p.description.toLowerCase().includes(searchLower))
      );
    }
    
    return filteredProjects;
  }, []);

  const mockCreateProject = useCallback(async (projectData: CreateProjectData) => {
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const newProject: ProjectWithStats = {
      id: `project-${Date.now()}`,
      name: projectData.name,
      description: projectData.description || null,
      status: 'draft',
      created_by: 'user-1',
      organization: 'FieldSync Pro',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      start_date: null,
      end_date: null,
      region: projectData.region ? {
        type: 'Point',
        coordinates: [projectData.region.longitude, projectData.region.latitude]
      } : null,
      settings: projectData.settings || {},
      metadata: {},
      stats: {
        project_id: `project-${Date.now()}`,
        total_forms: 0,
        total_submissions: 0,
        completed_submissions: 0,
        team_count: projectData.teams?.length || 0,
        completion_rate: 0
      }
    };
    
    MOCK_PROJECTS.unshift(newProject);
    return newProject;
  }, []);

  const mockUpdateProject = useCallback(async (projectId: string, updates: Partial<ProjectUpdate>) => {
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const projectIndex = MOCK_PROJECTS.findIndex(p => p.id === projectId);
    if (projectIndex === -1) {
      throw new Error('Project not found');
    }
    
    MOCK_PROJECTS[projectIndex] = {
      ...MOCK_PROJECTS[projectIndex],
      ...updates,
      updated_at: new Date().toISOString()
    };
    
    return MOCK_PROJECTS[projectIndex];
  }, []);

  const mockDeleteProject = useCallback(async (projectId: string) => {
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const projectIndex = MOCK_PROJECTS.findIndex(p => p.id === projectId);
    if (projectIndex === -1) {
      throw new Error('Project not found');
    }
    
    MOCK_PROJECTS[projectIndex] = {
      ...MOCK_PROJECTS[projectIndex],
      status: 'archived',
      updated_at: new Date().toISOString()
    };
  }, []);

  // Real API functions
  const realFetchProjects = useCallback(async (filters?: ProjectFilters) => {
    const response = await projectsApi.getProjects(1, 100, {
      status: filters?.status,
      search: filters?.search,
      teamId: filters?.teamId,
      createdBy: filters?.userId
    });
    
    const formattedResponse = ApiHelpers.formatApiResponse(response);
    
    if (!formattedResponse.success) {
      throw new Error(formattedResponse.error || 'Failed to fetch projects');
    }
    
    return formattedResponse.data || [];
  }, []);

  const realCreateProject = useCallback(async (projectData: CreateProjectData) => {
    const insertData: Omit<ProjectInsert, 'id' | 'created_at' | 'updated_at' | 'created_by'> = {
      name: projectData.name,
      description: projectData.description,
      status: 'draft',
      organization: null,
      start_date: null,
      end_date: null,
      region: projectData.region ? {
        type: 'Point',
        coordinates: [projectData.region.longitude, projectData.region.latitude]
      } : null,
      settings: projectData.settings || {},
      metadata: {}
    };

    const response = await projectsApi.createProject(insertData);
    const formattedResponse = ApiHelpers.formatApiResponse(response);
    
    if (!formattedResponse.success) {
      throw new Error(formattedResponse.error || 'Failed to create project');
    }

    const newProject = formattedResponse.data;

    // Assign teams if provided
    if (newProject && projectData.teams && projectData.teams.length > 0) {
      for (const teamId of projectData.teams) {
        try {
          await projectsApi.assignTeamToProject(newProject.id, teamId);
        } catch (error) {
          console.warn('Failed to assign team to project:', error);
        }
      }
    }
    
    return newProject;
  }, []);

  const realUpdateProject = useCallback(async (projectId: string, updates: Partial<ProjectUpdate>) => {
    const response = await projectsApi.updateProject(projectId, updates);
    const formattedResponse = ApiHelpers.formatApiResponse(response);
    
    if (!formattedResponse.success) {
      throw new Error(formattedResponse.error || 'Failed to update project');
    }
    
    return formattedResponse.data;
  }, []);

  const realDeleteProject = useCallback(async (projectId: string) => {
    const response = await projectsApi.deleteProject(projectId);
    const formattedResponse = ApiHelpers.formatApiResponse(response);
    
    if (!formattedResponse.success) {
      throw new Error(formattedResponse.error || 'Failed to delete project');
    }
  }, []);

  // Unified functions that choose between mock and real implementations
  const fetchProjects = useCallback(async (filters?: ProjectFilters) => {
    setLoading(true);
    setError(null);
    
    try {
      const projectsData = isDev 
        ? await mockFetchProjects(filters)
        : await realFetchProjects(filters);
      
      setProjects(projectsData);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Error fetching projects:', err);
    } finally {
      setLoading(false);
    }
  }, [isDev, mockFetchProjects, realFetchProjects]);

  const createProject = useCallback(async (projectData: CreateProjectData) => {
    setLoading(true);
    setError(null);
    
    try {
      const newProject = isDev
        ? await mockCreateProject(projectData)
        : await realCreateProject(projectData);
      
      // Add to local state
      setProjects(prev => [newProject as ProjectWithStats, ...prev]);
      
      return { success: true, data: newProject };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create project';
      setError(errorMessage);
      console.error('Error creating project:', err);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [isDev, mockCreateProject, realCreateProject]);

  const updateProject = useCallback(async (projectId: string, updates: Partial<ProjectUpdate>) => {
    setLoading(true);
    setError(null);
    
    try {
      const updatedProject = isDev
        ? await mockUpdateProject(projectId, updates)
        : await realUpdateProject(projectId, updates);
      
      // Update local state
      setProjects(prev => 
        prev.map(p => p.id === projectId ? { ...p, ...updatedProject } : p)
      );
      
      return { success: true, data: updatedProject };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update project';
      setError(errorMessage);
      console.error('Error updating project:', err);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [isDev, mockUpdateProject, realUpdateProject]);

  const deleteProject = useCallback(async (projectId: string) => {
    setLoading(true);
    setError(null);
    
    try {
      await (isDev ? mockDeleteProject(projectId) : realDeleteProject(projectId));
      
      // Update local state
      setProjects(prev => 
        prev.map(p => p.id === projectId ? { ...p, status: 'archived' as const } : p)
      );
      
      return { success: true };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete project';
      setError(errorMessage);
      console.error('Error deleting project:', err);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [isDev, mockDeleteProject, realDeleteProject]);

  const getProjectById = useCallback(async (projectId: string) => {
    if (isDev) {
      return MOCK_PROJECTS.find(p => p.id === projectId) || null;
    }
    
    try {
      const response = await projectsApi.getProjectById(projectId);
      const formattedResponse = ApiHelpers.formatApiResponse(response);
      
      if (formattedResponse.success) {
        return formattedResponse.data;
      }
      
      return null;
    } catch (error) {
      console.error('Error getting project by ID:', error);
      return null;
    }
  }, [isDev]);

  const searchProjects = useCallback(async (searchTerm: string, filters?: Omit<ProjectFilters, 'search'>) => {
    setLoading(true);
    setError(null);
    
    try {
      let projectsData: ProjectWithStats[];
      
      if (isDev) {
        projectsData = await mockFetchProjects({ ...filters, search: searchTerm });
      } else {
        const response = await projectsApi.searchProjects(searchTerm, 1, 100, filters);
        const formattedResponse = ApiHelpers.formatApiResponse(response);
        
        if (!formattedResponse.success) {
          throw new Error(formattedResponse.error || 'Search failed');
        }
        
        projectsData = formattedResponse.data || [];
      }
      
      setProjects(projectsData);
      return { success: true, data: projectsData };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Search failed';
      setError(errorMessage);
      console.error('Error searching projects:', err);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [isDev, mockFetchProjects]);

  const getProjectAnalytics = useCallback(async (projectId: string, dateFrom?: string, dateTo?: string) => {
    if (isDev) {
      // Return mock analytics data
      return {
        submissionsByDate: Array.from({ length: 7 }, (_, i) => ({
          date: new Date(Date.now() - (6 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          count: Math.floor(Math.random() * 10) + 1
        })),
        completionRates: { total: 85, thisWeek: 92, lastWeek: 78 },
        teamPerformance: [
          { teamName: 'Team Alpha', submissions: 35, completionRate: 88 },
          { teamName: 'Team Beta', submissions: 28, completionRate: 92 },
          { teamName: 'Team Gamma', submissions: 22, completionRate: 75 }
        ],
        geographicDistribution: {}
      };
    }

    try {
      const response = await projectsApi.getProjectAnalytics(projectId, dateFrom, dateTo);
      const formattedResponse = ApiHelpers.formatApiResponse(response);
      
      if (formattedResponse.success) {
        return formattedResponse.data;
      }
      
      return null;
    } catch (error) {
      console.error('Error getting project analytics:', error);
      return null;
    }
  }, [isDev]);

  const refreshProjects = useCallback(() => {
    fetchProjects();
  }, [fetchProjects]);

  // Load projects on mount
  useEffect(() => {
    fetchProjects();
  }, [fetchProjects]);

  return {
    projects,
    loading,
    error,
    fetchProjects,
    createProject,
    updateProject,
    deleteProject,
    getProjectById,
    searchProjects,
    getProjectAnalytics,
    refreshProjects,
    clearError: () => setError(null),
    isDevelopment: isDev
  };
}

export default useProjects;
