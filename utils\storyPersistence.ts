import AsyncStorage from '@react-native-async-storage/async-storage';

interface StorySlide {
  id: string;
  title: string;
  description: string;
  content: string;
  duration: number;
  visibleLayers: string[];
  mapExtent?: {
    latitude: number;
    longitude: number;
    latitudeDelta: number;
    longitudeDelta: number;
  };
  baseMap: string;
  interactive: boolean;
  autoAdvance: boolean;
}

interface MapStory {
  id: string;
  title: string;
  description: string;
  author: string;
  createdAt: string;
  updatedAt: string;
  tags: string[];
  slides: StorySlide[];
  settings: {
    showProgress: boolean;
    showControls: boolean;
    allowSkipping: boolean;
    loop: boolean;
    theme: 'light' | 'dark' | 'auto';
    layout: 'sidebar' | 'overlay' | 'bottom';
  };
  sharing: {
    isPublic: boolean;
    allowComments: boolean;
    allowEmbedding: boolean;
    password?: string;
  };
  thumbnail?: string;
  viewCount?: number;
  likeCount?: number;
}

const STORAGE_KEYS = {
  STORY_PROJECTS: '@story_projects',
  STORY_SETTINGS: '@story_settings',
  STORY_TEMPLATES: '@story_templates',
};

export class StoryPersistence {
  // Save story projects to storage
  static async saveStoryProjects(stories: MapStory[]): Promise<void> {
    try {
      const serialized = JSON.stringify(stories);
      await AsyncStorage.setItem(STORAGE_KEYS.STORY_PROJECTS, serialized);
    } catch (error) {
      console.error('Failed to save story projects:', error);
      throw new Error('Failed to save story projects');
    }
  }

  // Load story projects from storage
  static async loadStoryProjects(): Promise<MapStory[]> {
    try {
      const serialized = await AsyncStorage.getItem(STORAGE_KEYS.STORY_PROJECTS);
      if (!serialized) return [];
      
      const stories = JSON.parse(serialized) as MapStory[];
      return stories;
    } catch (error) {
      console.error('Failed to load story projects:', error);
      return [];
    }
  }

  // Save a single story project
  static async saveStoryProject(story: MapStory): Promise<void> {
    try {
      const stories = await this.loadStoryProjects();
      const existingIndex = stories.findIndex(s => s.id === story.id);
      
      if (existingIndex >= 0) {
        stories[existingIndex] = { ...story, updatedAt: new Date().toISOString() };
      } else {
        stories.push(story);
      }
      
      await this.saveStoryProjects(stories);
    } catch (error) {
      console.error('Failed to save story project:', error);
      throw new Error('Failed to save story project');
    }
  }

  // Delete a story project
  static async deleteStoryProject(storyId: string): Promise<void> {
    try {
      const stories = await this.loadStoryProjects();
      const filteredStories = stories.filter(s => s.id !== storyId);
      await this.saveStoryProjects(filteredStories);
    } catch (error) {
      console.error('Failed to delete story project:', error);
      throw new Error('Failed to delete story project');
    }
  }

  // Export story to JSON
  static async exportStory(story: MapStory): Promise<string> {
    try {
      const exportData = {
        version: '1.0',
        exportedAt: new Date().toISOString(),
        story: story,
      };
      
      return JSON.stringify(exportData, null, 2);
    } catch (error) {
      console.error('Failed to export story:', error);
      throw new Error('Failed to export story');
    }
  }

  // Import story from JSON
  static async importStory(jsonData: string): Promise<MapStory> {
    try {
      const importData = JSON.parse(jsonData);
      
      if (!importData.story) {
        throw new Error('Invalid story format');
      }
      
      const story = importData.story as MapStory;
      
      // Generate new ID to avoid conflicts
      const importedStory: MapStory = {
        ...story,
        id: `story-${Date.now()}`,
        title: `${story.title} (Imported)`,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      
      await this.saveStoryProject(importedStory);
      return importedStory;
    } catch (error) {
      console.error('Failed to import story:', error);
      throw new Error('Failed to import story');
    }
  }

  // Create story template
  static async saveStoryTemplate(story: MapStory, templateName: string): Promise<void> {
    try {
      const templates = await this.loadStoryTemplates();
      const template = {
        id: `template-${Date.now()}`,
        name: templateName,
        story: {
          ...story,
          id: '',
          title: '',
          description: '',
          createdAt: '',
          updatedAt: '',
        },
        createdAt: new Date().toISOString(),
      };
      
      templates.push(template);
      await AsyncStorage.setItem(STORAGE_KEYS.STORY_TEMPLATES, JSON.stringify(templates));
    } catch (error) {
      console.error('Failed to save story template:', error);
      throw new Error('Failed to save story template');
    }
  }

  // Load story templates
  static async loadStoryTemplates(): Promise<Array<{
    id: string;
    name: string;
    story: Partial<MapStory>;
    createdAt: string;
  }>> {
    try {
      const serialized = await AsyncStorage.getItem(STORAGE_KEYS.STORY_TEMPLATES);
      if (!serialized) return [];
      
      return JSON.parse(serialized);
    } catch (error) {
      console.error('Failed to load story templates:', error);
      return [];
    }
  }

  // Create story from template
  static async createStoryFromTemplate(templateId: string, title: string, description: string): Promise<MapStory> {
    try {
      const templates = await this.loadStoryTemplates();
      const template = templates.find(t => t.id === templateId);
      
      if (!template) {
        throw new Error('Template not found');
      }
      
      const story: MapStory = {
        ...template.story,
        id: `story-${Date.now()}`,
        title,
        description,
        author: 'User',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        tags: [],
        slides: template.story.slides || [],
        settings: template.story.settings || {
          showProgress: true,
          showControls: true,
          allowSkipping: true,
          loop: false,
          theme: 'auto',
          layout: 'sidebar',
        },
        sharing: template.story.sharing || {
          isPublic: false,
          allowComments: false,
          allowEmbedding: false,
        },
      } as MapStory;
      
      await this.saveStoryProject(story);
      return story;
    } catch (error) {
      console.error('Failed to create story from template:', error);
      throw new Error('Failed to create story from template');
    }
  }

  // Get story statistics
  static async getStoryStatistics(): Promise<{
    totalStories: number;
    totalSlides: number;
    averageSlidesPerStory: number;
    totalDuration: number; // in seconds
    lastUpdated: string | null;
  }> {
    try {
      const stories = await this.loadStoryProjects();
      
      const totalStories = stories.length;
      const totalSlides = stories.reduce((sum, story) => sum + story.slides.length, 0);
      const totalDuration = stories.reduce((sum, story) => 
        sum + story.slides.reduce((slideSum, slide) => slideSum + slide.duration, 0), 0
      );
      
      const lastUpdated = stories.length > 0 
        ? stories.reduce((latest, story) => 
            story.updatedAt > latest ? story.updatedAt : latest, stories[0].updatedAt
          )
        : null;
      
      return {
        totalStories,
        totalSlides,
        averageSlidesPerStory: totalStories > 0 ? totalSlides / totalStories : 0,
        totalDuration,
        lastUpdated,
      };
    } catch (error) {
      console.error('Failed to get story statistics:', error);
      return {
        totalStories: 0,
        totalSlides: 0,
        averageSlidesPerStory: 0,
        totalDuration: 0,
        lastUpdated: null,
      };
    }
  }

  // Clear all story data (for testing/reset)
  static async clearAllStoryData(): Promise<void> {
    try {
      await AsyncStorage.multiRemove([
        STORAGE_KEYS.STORY_PROJECTS,
        STORAGE_KEYS.STORY_SETTINGS,
        STORAGE_KEYS.STORY_TEMPLATES,
      ]);
    } catch (error) {
      console.error('Failed to clear story data:', error);
      throw new Error('Failed to clear story data');
    }
  }
}
