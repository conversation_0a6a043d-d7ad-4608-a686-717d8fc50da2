import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Alert,
  Platform,
} from 'react-native';
import { useTheme } from '@/hooks/useTheme';
import { 
  Video, 
  Camera, 
  Play, 
  Pause, 
  Square, 
  RotateCcw, 
  Trash2,
  Upload,
  Download,
} from 'lucide-react-native';

interface VideoRecorderProps {
  value?: string; // Video URI
  onChange: (uri: string) => void;
  placeholder?: string;
  required?: boolean;
  label?: string;
  maxDuration?: number; // in seconds
  quality?: 'low' | 'medium' | 'high';
  compress?: boolean;
}

export default function VideoRecorder({
  value,
  onChange,
  placeholder = 'Record video',
  required,
  label,
  maxDuration = 600, // 10 minutes default
  quality = 'medium',
  compress = true,
}: VideoRecorderProps) {
  const { theme } = useTheme();
  const [showRecorder, setShowRecorder] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [recordingDuration, setRecordingDuration] = useState(0);
  const [showPreview, setShowPreview] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const recordingTimer = useRef<NodeJS.Timeout | null>(null);

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const startRecording = () => {
    setIsRecording(true);
    setRecordingDuration(0);
    
    // Start timer
    recordingTimer.current = setInterval(() => {
      setRecordingDuration(prev => {
        const newDuration = prev + 1;
        if (newDuration >= maxDuration) {
          stopRecording();
        }
        return newDuration;
      });
    }, 1000);

    // In a real implementation, start camera recording here
    console.log('Starting video recording...');
  };

  const stopRecording = () => {
    setIsRecording(false);
    
    if (recordingTimer.current) {
      clearInterval(recordingTimer.current);
      recordingTimer.current = null;
    }

    // Simulate successful recording
    const videoUri = `video_${Date.now()}.mp4`;
    onChange(videoUri);
    setShowRecorder(false);
    
    console.log('Video recording stopped:', videoUri);
    Alert.alert('Recording Complete', `Video recorded successfully (${formatDuration(recordingDuration)})`);
  };

  const cancelRecording = () => {
    if (isRecording) {
      setIsRecording(false);
      if (recordingTimer.current) {
        clearInterval(recordingTimer.current);
        recordingTimer.current = null;
      }
    }
    setRecordingDuration(0);
    setShowRecorder(false);
  };

  const deleteVideo = () => {
    Alert.alert(
      'Delete Video',
      'Are you sure you want to delete this video?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => onChange(''),
        },
      ]
    );
  };

  const handlePreview = () => {
    if (value) {
      setShowPreview(true);
    }
  };

  const handleFileUpload = () => {
    // In a real implementation, this would open file picker
    Alert.alert(
      'Upload Video',
      'File picker would open here to select a video file',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Simulate Upload',
          onPress: () => {
            const uploadedUri = `uploaded_video_${Date.now()}.mp4`;
            onChange(uploadedUri);
            Alert.alert('Upload Complete', 'Video uploaded successfully');
          },
        },
      ]
    );
  };

  const renderRecorderModal = () => (
    <Modal
      visible={showRecorder}
      animationType="slide"
      onRequestClose={cancelRecording}
    >
      <View style={[styles.recorderContainer, { backgroundColor: '#000' }]}>
        {/* Recording Header */}
        <View style={styles.recorderHeader}>
          <TouchableOpacity
            style={styles.cancelButton}
            onPress={cancelRecording}
          >
            <Text style={styles.cancelButtonText}>Cancel</Text>
          </TouchableOpacity>
          
          <View style={styles.recordingInfo}>
            <View style={[
              styles.recordingIndicator,
              { backgroundColor: isRecording ? '#FF4444' : '#666' }
            ]}>
              {isRecording && <View style={styles.recordingDot} />}
            </View>
            <Text style={styles.durationText}>
              {formatDuration(recordingDuration)} / {formatDuration(maxDuration)}
            </Text>
          </View>
          
          <View style={styles.headerSpacer} />
        </View>

        {/* Camera View Placeholder */}
        <View style={styles.cameraView}>
          <View style={styles.cameraPlaceholder}>
            <Camera size={64} color="white" />
            <Text style={styles.cameraText}>
              Camera view would appear here
            </Text>
            <Text style={styles.cameraNote}>
              Use expo-camera or react-native-vision-camera
            </Text>
          </View>
        </View>

        {/* Recording Controls */}
        <View style={styles.recordingControls}>
          <View style={styles.controlsRow}>
            {!isRecording ? (
              <TouchableOpacity
                style={[styles.recordButton, { backgroundColor: '#FF4444' }]}
                onPress={startRecording}
              >
                <Video size={32} color="white" />
              </TouchableOpacity>
            ) : (
              <TouchableOpacity
                style={[styles.recordButton, { backgroundColor: '#666' }]}
                onPress={stopRecording}
              >
                <Square size={24} color="white" />
              </TouchableOpacity>
            )}
          </View>
          
          <Text style={styles.recordInstructions}>
            {isRecording ? 'Tap to stop recording' : 'Tap to start recording'}
          </Text>
          
          <View style={styles.qualityInfo}>
            <Text style={styles.qualityText}>
              Quality: {quality.charAt(0).toUpperCase() + quality.slice(1)}
            </Text>
            {compress && (
              <Text style={styles.qualityText}>• Compression: On</Text>
            )}
          </View>
        </View>
      </View>
    </Modal>
  );

  const renderPreviewModal = () => (
    <Modal
      visible={showPreview}
      animationType="slide"
      onRequestClose={() => setShowPreview(false)}
    >
      <View style={[styles.previewContainer, { backgroundColor: '#000' }]}>
        <View style={styles.previewHeader}>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={() => setShowPreview(false)}
          >
            <Text style={styles.closeButtonText}>Close</Text>
          </TouchableOpacity>
          <Text style={styles.previewTitle}>Video Preview</Text>
          <View style={styles.headerSpacer} />
        </View>

        {/* Video Player Placeholder */}
        <View style={styles.videoPlayer}>
          <View style={styles.videoPlaceholder}>
            <Video size={64} color="white" />
            <Text style={styles.videoText}>
              Video player would appear here
            </Text>
            <Text style={styles.videoNote}>
              {value}
            </Text>
          </View>
        </View>

        {/* Player Controls */}
        <View style={styles.playerControls}>
          <TouchableOpacity
            style={styles.playButton}
            onPress={() => setIsPlaying(!isPlaying)}
          >
            {isPlaying ? (
              <Pause size={24} color="white" />
            ) : (
              <Play size={24} color="white" />
            )}
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );

  return (
    <View style={styles.container}>
      {label && (
        <Text style={[styles.label, { color: theme.colors.text }]}>
          {label}
          {required && <Text style={{ color: theme.colors.error }}> *</Text>}
        </Text>
      )}

      {value ? (
        <View style={[
          styles.videoContainer,
          {
            backgroundColor: theme.colors.background,
            borderColor: theme.colors.border,
          },
        ]}>
          <View style={styles.videoInfo}>
            <Video size={24} color={theme.colors.primary} />
            <View style={styles.videoDetails}>
              <Text style={[styles.videoName, { color: theme.colors.text }]}>
                Video recorded
              </Text>
              <Text style={[styles.videoMeta, { color: theme.colors.muted }]}>
                Duration: {formatDuration(recordingDuration || 30)} • Quality: {quality}
              </Text>
            </View>
          </View>
          
          <View style={styles.videoActions}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={handlePreview}
            >
              <Play size={16} color={theme.colors.primary} />
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => setShowRecorder(true)}
            >
              <RotateCcw size={16} color={theme.colors.muted} />
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={deleteVideo}
            >
              <Trash2 size={16} color={theme.colors.error} />
            </TouchableOpacity>
          </View>
        </View>
      ) : (
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={[
              styles.recordActionButton,
              { backgroundColor: theme.colors.primary },
            ]}
            onPress={() => setShowRecorder(true)}
          >
            <Camera size={20} color="white" />
            <Text style={styles.recordActionText}>Record Video</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.uploadButton,
              {
                backgroundColor: theme.colors.background,
                borderColor: theme.colors.border,
              },
            ]}
            onPress={handleFileUpload}
          >
            <Upload size={18} color={theme.colors.text} />
          </TouchableOpacity>
        </View>
      )}

      {renderRecorderModal()}
      {renderPreviewModal()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
  },
  label: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    marginBottom: 6,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  recordActionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    borderRadius: 8,
    gap: 8,
  },
  recordActionText: {
    color: 'white',
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
  },
  uploadButton: {
    paddingVertical: 14,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  videoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 14,
    borderRadius: 8,
    borderWidth: 1,
    gap: 12,
  },
  videoInfo: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  videoDetails: {
    flex: 1,
  },
  videoName: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 2,
  },
  videoMeta: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
  },
  videoActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    padding: 8,
  },
  recorderContainer: {
    flex: 1,
  },
  recorderHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 50,
    paddingBottom: 20,
  },
  cancelButton: {
    padding: 8,
  },
  cancelButtonText: {
    color: 'white',
    fontSize: 16,
    fontFamily: 'Inter-Medium',
  },
  recordingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  recordingIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    justifyContent: 'center',
    alignItems: 'center',
  },
  recordingDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'white',
  },
  durationText: {
    color: 'white',
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
  },
  headerSpacer: {
    width: 60,
  },
  cameraView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cameraPlaceholder: {
    alignItems: 'center',
    gap: 12,
  },
  cameraText: {
    color: 'white',
    fontSize: 18,
    fontFamily: 'Inter-Medium',
  },
  cameraNote: {
    color: '#999',
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
  },
  recordingControls: {
    alignItems: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  controlsRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 20,
  },
  recordButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  recordInstructions: {
    color: 'white',
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
    marginBottom: 12,
  },
  qualityInfo: {
    flexDirection: 'row',
    gap: 8,
  },
  qualityText: {
    color: '#999',
    fontSize: 12,
    fontFamily: 'Inter-Regular',
  },
  previewContainer: {
    flex: 1,
  },
  previewHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 50,
    paddingBottom: 20,
  },
  closeButton: {
    padding: 8,
  },
  closeButtonText: {
    color: 'white',
    fontSize: 16,
    fontFamily: 'Inter-Medium',
  },
  previewTitle: {
    color: 'white',
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
  },
  videoPlayer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  videoPlaceholder: {
    alignItems: 'center',
    gap: 12,
  },
  videoText: {
    color: 'white',
    fontSize: 18,
    fontFamily: 'Inter-Medium',
  },
  videoNote: {
    color: '#999',
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
  },
  playerControls: {
    alignItems: 'center',
    paddingVertical: 30,
  },
  playButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
});
