import React from 'react';
import { View } from 'react-native';

// Mock requireNativeComponent for web
export default function requireNativeComponent(componentName, componentInterface) {
  return React.forwardRef((props, ref) => (
    <View ref={ref} {...props} />
  ));
}

// Mock additional exports that might be needed
export const requireNativeViewManager = () => View;
export const Platform = {
  select: (obj) => obj.web || obj.default,
};
