/**
 * Global Error Handler
 * 
 * Handles runtime errors gracefully, particularly those related to
 * Expo Updates and other platform-specific issues.
 */

import { Platform } from 'react-native';

interface ErrorInfo {
  error: Error;
  errorInfo?: any;
  timestamp: Date;
  platform: string;
  isUpdateRelated: boolean;
}

class GlobalErrorHandler {
  private errorLog: ErrorInfo[] = [];
  private maxLogSize = 50;

  constructor() {
    this.setupGlobalErrorHandlers();
  }

  private setupGlobalErrorHandlers() {
    // Handle unhandled promise rejections
    if (typeof global !== 'undefined' && global.HermesInternal) {
      // Hermes-specific error handling
      this.setupHermesErrorHandling();
    }

    // Handle React Native errors
    if (Platform.OS !== 'web') {
      this.setupNativeErrorHandling();
    } else {
      this.setupWebErrorHandling();
    }
  }

  private setupHermesErrorHandling() {
    // Set up error handling for Hermes engine
    const originalConsoleError = console.error;
    console.error = (...args) => {
      this.handleConsoleError(args);
      originalConsoleError.apply(console, args);
    };
  }

  private setupNativeErrorHandling() {
    // Handle native platform errors
    if (typeof ErrorUtils !== 'undefined') {
      const originalHandler = ErrorUtils.getGlobalHandler();
      
      ErrorUtils.setGlobalHandler((error, isFatal) => {
        this.handleGlobalError(error, { isFatal });
        
        // Call original handler if it exists
        if (originalHandler) {
          originalHandler(error, isFatal);
        }
      });
    }
  }

  private setupWebErrorHandling() {
    // Handle web platform errors
    window.addEventListener('error', (event) => {
      this.handleGlobalError(new Error(event.message), {
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
      });
    });

    window.addEventListener('unhandledrejection', (event) => {
      this.handleGlobalError(
        new Error(`Unhandled Promise Rejection: ${event.reason}`),
        { type: 'unhandledrejection' }
      );
    });
  }

  private handleConsoleError(args: any[]) {
    const message = args.join(' ');
    
    // Check if this is an update-related error
    if (this.isUpdateRelatedError(message)) {
      console.warn('Suppressing update-related error:', message);
      return; // Suppress update-related console errors
    }
  }

  private handleGlobalError(error: Error, errorInfo?: any) {
    const isUpdateRelated = this.isUpdateRelatedError(error.message);
    
    const errorData: ErrorInfo = {
      error,
      errorInfo,
      timestamp: new Date(),
      platform: Platform.OS,
      isUpdateRelated,
    };

    // Log the error
    this.logError(errorData);

    // Handle update-related errors gracefully
    if (isUpdateRelated) {
      this.handleUpdateError(errorData);
      return; // Don't propagate update errors
    }

    // Handle other errors based on severity
    this.handleOtherError(errorData);
  }

  private isUpdateRelatedError(message: string): boolean {
    const updateErrorPatterns = [
      'Failed to download remote update',
      'expo-updates',
      'Update check failed',
      'Update download failed',
      'Updates module',
      'EXPO_UPDATES',
      'updateAsync',
      'checkForUpdateAsync',
      'fetchUpdateAsync',
      'reloadAsync',
      'IOException: Failed to download',
      'Update manifest',
      'Update bundle',
    ];

    return updateErrorPatterns.some(pattern => 
      message.toLowerCase().includes(pattern.toLowerCase())
    );
  }

  private handleUpdateError(errorData: ErrorInfo) {
    console.warn('Update-related error handled gracefully:', errorData.error.message);
    
    // Optionally notify user about update issues
    if (Platform.OS !== 'web') {
      // Could show a non-intrusive notification about update issues
      // For now, just log it
      console.log('Update functionality temporarily unavailable');
    }
  }

  private handleOtherError(errorData: ErrorInfo) {
    // Handle non-update errors normally
    console.error('Application error:', errorData.error);
    
    // Could implement crash reporting here
    // For now, just log it
  }

  private logError(errorData: ErrorInfo) {
    // Add to error log
    this.errorLog.push(errorData);
    
    // Keep log size manageable
    if (this.errorLog.length > this.maxLogSize) {
      this.errorLog = this.errorLog.slice(-this.maxLogSize);
    }
  }

  public getErrorLog(): ErrorInfo[] {
    return [...this.errorLog];
  }

  public getUpdateErrors(): ErrorInfo[] {
    return this.errorLog.filter(error => error.isUpdateRelated);
  }

  public clearErrorLog() {
    this.errorLog = [];
  }

  public hasUpdateErrors(): boolean {
    return this.errorLog.some(error => error.isUpdateRelated);
  }
}

// Create and export singleton instance
export const globalErrorHandler = new GlobalErrorHandler();

// Export hook for React components
export function useErrorHandler() {
  return {
    getErrorLog: globalErrorHandler.getErrorLog.bind(globalErrorHandler),
    getUpdateErrors: globalErrorHandler.getUpdateErrors.bind(globalErrorHandler),
    clearErrorLog: globalErrorHandler.clearErrorLog.bind(globalErrorHandler),
    hasUpdateErrors: globalErrorHandler.hasUpdateErrors.bind(globalErrorHandler),
  };
}

// Export types
export type { ErrorInfo };
