import { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  FlatList, 
  TouchableOpacity, 
  ActivityIndicator,
  ScrollView,
  RefreshControl 
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '@/hooks/useTheme';
import { useConnection } from '@/hooks/useConnection';
import { useProjects } from '@/hooks/useProjects';
import { router } from 'expo-router';
import { Project } from '@/types';
import { ProjectWithStats } from '@/types/database';
import { 
  ClipboardList, 
  CheckCircle, 
  Clock, 
  FileInput,
  Plus,
  Filter,
  FolderPlus
} from 'lucide-react-native';

// Simplified Project Card Component
function SimpleProjectCard({ project, onPress }: { project: ProjectWithStats; onPress: () => void }) {
  const { theme } = useTheme();
  
  const getStatusColor = () => {
    switch (project.status) {
      case 'active': return theme.colors.success;
      case 'completed': return theme.colors.primary;
      case 'draft': return theme.colors.warning;
      default: return theme.colors.muted;
    }
  };

  const getStatusIcon = () => {
    switch (project.status) {
      case 'active': return <CheckCircle size={16} color={getStatusColor()} />;
      case 'completed': return <CheckCircle size={16} color={getStatusColor()} />;
      case 'draft': return <Clock size={16} color={getStatusColor()} />;
      default: return <ClipboardList size={16} color={getStatusColor()} />;
    }
  };

  return (
    <TouchableOpacity 
      style={[styles.projectCard, { backgroundColor: theme.colors.card }]}
      onPress={onPress}
    >
      <View style={styles.projectHeader}>
        <Text style={[styles.projectName, { color: theme.colors.text }]} numberOfLines={1}>
          {project.name}
        </Text>
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor() + '20' }]}>
          {getStatusIcon()}
          <Text style={[styles.statusText, { color: getStatusColor() }]}>
            {project.status.charAt(0).toUpperCase() + project.status.slice(1)}
          </Text>
        </View>
      </View>
      
      {project.description && (
        <Text style={[styles.projectDescription, { color: theme.colors.muted }]} numberOfLines={2}>
          {project.description}
        </Text>
      )}
      
      <View style={styles.projectStats}>
        <View style={styles.stat}>
          <FileInput size={14} color={theme.colors.primary} />
          <Text style={[styles.statText, { color: theme.colors.text }]}>
            {project.stats?.total_forms || project.forms?.length || 0} forms
          </Text>
        </View>
        <View style={styles.stat}>
          <Text style={[styles.statText, { color: theme.colors.muted }]}>
            Created {new Date(project.created_at).toLocaleDateString()}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );
}

// Simple Empty State Component
function SimpleEmptyState({ icon, title, description }: { icon: JSX.Element; title: string; description: string }) {
  const { theme } = useTheme();
  
  return (
    <View style={styles.emptyState}>
      {icon}
      <Text style={[styles.emptyTitle, { color: theme.colors.text }]}>{title}</Text>
      <Text style={[styles.emptyDescription, { color: theme.colors.muted }]}>{description}</Text>
    </View>
  );
}

export default function CollectScreen() {
  const { theme } = useTheme();
  const { isConnected } = useConnection();
  const { projects, loading: projectsLoading, fetchProjects } = useProjects();
  
  const [activeTab, setActiveTab] = useState<'projects' | 'forms' | 'drafts'>('projects');
  const [statusFilter, setStatusFilter] = useState<ProjectWithStats['status'] | 'all'>('all');
  const [refreshing, setRefreshing] = useState(false);
  
  const filteredProjects = projects.filter(project => {
    const matchesStatus = statusFilter === 'all' || project.status === statusFilter;
    return matchesStatus;
  });

  const onRefresh = async () => {
    setRefreshing(true);
    try {
      await fetchProjects();
    } catch (error) {
      console.error('Error refreshing:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const renderTab = (tab: typeof activeTab, label: string, icon: JSX.Element) => (
    <TouchableOpacity
      style={[
        styles.tab,
        { borderBottomColor: activeTab === tab ? theme.colors.primary : 'transparent' }
      ]}
      onPress={() => setActiveTab(tab)}
    >
      <View style={styles.tabContent}>
        {icon}
        <Text
          style={[
            styles.tabText,
            { color: activeTab === tab ? theme.colors.primary : theme.colors.text }
          ]}
        >
          {label}
        </Text>
      </View>
    </TouchableOpacity>
  );

  const renderStatusFilterButton = (status: ProjectWithStats['status'] | 'all', label: string) => (
    <TouchableOpacity
      style={[
        styles.filterButton,
        { backgroundColor: statusFilter === status ? theme.colors.primary : theme.colors.card }
      ]}
      onPress={() => setStatusFilter(status)}
    >
      <Text
        style={[
          styles.filterButtonText,
          { color: statusFilter === status ? 'white' : theme.colors.text }
        ]}
      >
        {label}
      </Text>
    </TouchableOpacity>
  );

  const getTabCount = () => {
    switch (activeTab) {
      case 'projects':
        return filteredProjects.length;
      case 'forms':
        return 0; // Simplified for now
      case 'drafts':
        return 0; // Simplified for now
      default:
        return 0;
    }
  };

  if (projectsLoading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={[styles.loadingText, { color: theme.colors.text }]}>
            Loading projects...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]} edges={['bottom']}>
      <View style={styles.header}>
        <View>
          <Text style={[styles.title, { color: theme.colors.text }]}>Projects & Data</Text>
          <Text style={[styles.subtitle, { color: theme.colors.muted }]}>
            📋 Manage projects and collect field data
          </Text>
        </View>
        {activeTab === 'projects' && (
          <TouchableOpacity
            style={[styles.addButton, { backgroundColor: theme.colors.primary }]}
            onPress={() => console.log('Create project')}
          >
            <Plus size={20} color="white" />
          </TouchableOpacity>
        )}
      </View>
      
      <View style={[styles.tabBar, { borderBottomColor: theme.colors.border }]}>
        {renderTab('projects', 'Projects', <FolderPlus size={16} color={activeTab === 'projects' ? theme.colors.primary : theme.colors.text} />)}
        {renderTab('forms', 'Forms', <FileInput size={16} color={activeTab === 'forms' ? theme.colors.primary : theme.colors.text} />)}
        {renderTab('drafts', 'Drafts', <Clock size={16} color={activeTab === 'drafts' ? theme.colors.primary : theme.colors.text} />)}
      </View>

      {/* Project Filters */}
      {activeTab === 'projects' && (
        <View style={styles.filterContainer}>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.filtersScrollContent}>
            {renderStatusFilterButton('all', 'All')}
            {renderStatusFilterButton('active', 'Active')}
            {renderStatusFilterButton('draft', 'Drafts')}
            {renderStatusFilterButton('completed', 'Completed')}
          </ScrollView>
        </View>
      )}

      <View style={styles.countContainer}>
        <Text style={[styles.countText, { color: theme.colors.muted }]}>
          {getTabCount()} {activeTab} found
        </Text>
      </View>
      
      {/* Projects Tab */}
      {activeTab === 'projects' && (
        <FlatList
          data={filteredProjects}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => (
            <SimpleProjectCard
              project={item}
              onPress={() => console.log('Navigate to project:', item.id)}
            />
          )}
          contentContainerStyle={styles.listContent}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[theme.colors.primary]}
              tintColor={theme.colors.primary}
            />
          }
          ListEmptyComponent={
            <SimpleEmptyState
              icon={<ClipboardList size={64} color={theme.colors.muted} />}
              title="No projects found"
              description="Tap the + button to create your first project"
            />
          }
        />
      )}

      {/* Forms Tab */}
      {activeTab === 'forms' && (
        <View style={styles.formsTabContent}>
          <View style={styles.formsQuickActions}>
            <TouchableOpacity
              style={[styles.quickActionCard, { backgroundColor: theme.colors.primary }]}
              onPress={() => router.push('/enhanced-form-demo')}
            >
              <Plus size={24} color="white" />
              <Text style={styles.quickActionText}>Enhanced Demo</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.quickActionCard, { backgroundColor: theme.colors.secondary }]}
              onPress={() => router.push('/forms')}
            >
              <FileInput size={24} color="white" />
              <Text style={styles.quickActionText}>Manage Forms</Text>
            </TouchableOpacity>
          </View>
          <SimpleEmptyState
            icon={<FileInput size={64} color={theme.colors.muted} />}
            title="Enhanced Form Builder"
            description="Create custom data collection forms with new field types (phone, QR/barcode, video, audio, photos)"
          />
        </View>
      )}
      
      {/* Drafts Tab */}
      {activeTab === 'drafts' && (
        <SimpleEmptyState
          icon={<Clock size={64} color={theme.colors.muted} />}
          title="No draft submissions"
          description="You don't have any draft submissions"
        />
      )}
      
      {/* Connection Status */}
      <View style={styles.connectionStatus}>
        <View 
          style={[
            styles.connectionIndicator, 
            { backgroundColor: isConnected ? theme.colors.success : theme.colors.warning }
          ]} 
        />
        <Text style={[styles.connectionText, { color: 'white' }]}>
          {isConnected ? 'Online' : 'Offline'}
        </Text>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    fontWeight: '500',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingHorizontal: 16,
    paddingTop: 8,
    paddingBottom: 12,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  subtitle: {
    fontSize: 14,
    marginTop: 4,
  },
  addButton: {
    padding: 12,
    borderRadius: 8,
  },
  tabBar: {
    flexDirection: 'row',
    borderBottomWidth: 1,
  },
  tab: {
    flex: 1,
    paddingVertical: 16,
    borderBottomWidth: 2,
  },
  tabContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 6,
  },
  tabText: {
    fontWeight: '500',
    fontSize: 13,
  },
  filterContainer: {
    paddingVertical: 8,
  },
  filtersScrollContent: {
    paddingHorizontal: 16,
    gap: 8,
  },
  filterButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  filterButtonText: {
    fontWeight: '500',
    fontSize: 12,
  },
  countContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  countText: {
    fontSize: 12,
    fontWeight: '500',
  },
  listContent: {
    padding: 16,
    paddingBottom: 100,
  },
  projectCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  projectHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  projectName: {
    fontSize: 16,
    fontWeight: 'bold',
    flex: 1,
    marginRight: 8,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  statusText: {
    fontSize: 11,
    fontWeight: '600',
  },
  projectDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 12,
  },
  projectStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  stat: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  statText: {
    fontSize: 12,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 48,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
  connectionStatus: {
    position: 'absolute',
    bottom: 24,
    left: 24,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
  },
  connectionIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  connectionText: {
    fontSize: 12,
    fontWeight: '500',
  },
  formsTabContent: {
    flex: 1,
    padding: 16,
  },
  formsQuickActions: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 24,
  },
  quickActionCard: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    gap: 8,
  },
  quickActionText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
});
