import { useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { router, usePathname } from 'expo-router';

export function AuthRedirect() {
  const { user, loading } = useAuth();
  const pathname = usePathname();

  useEffect(() => {
    if (loading) return; // Wait for auth to initialize
    
    const isAuthRoute = pathname?.startsWith('/(auth)') || pathname?.includes('/login') || pathname?.includes('/register');
    
    if (user && isAuthRoute) {
      // User is logged in but on auth page, redirect to tabs
      router.replace('/(tabs)');
    } else if (!user && !isAuthRoute) {
      // User is not logged in but not on auth page, redirect to login
      router.replace('/(auth)/login');
    }
  }, [user, loading, pathname]);

  return null; // This component doesn't render anything
}
