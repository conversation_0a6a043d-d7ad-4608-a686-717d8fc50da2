import { supabase, TABLES } from '../supabase';
import { BaseApi } from './base';
import { 
  Submission, 
  SubmissionInsert, 
  SubmissionUpdate,
  SubmissionWithDetails,
  MediaAttachment,
  MediaAttachmentInsert,
  ApiResponse,
  PaginatedResponse
} from '@/types/database';

export interface SubmissionFilters {
  formId?: string;
  projectId?: string;
  userId?: string;
  status?: 'draft' | 'completed' | 'synced' | 'rejected';
  dateFrom?: string;
  dateTo?: string;
  hasLocation?: boolean;
}

export interface LocationData {
  latitude: number;
  longitude: number;
  altitude?: number;
  accuracy?: number;
  heading?: number;
  speed?: number;
  timestamp: number;
  provider?: string;
}

export interface MediaUploadData {
  fieldId: string;
  fileName: string;
  fileType: string;
  fileSize: number;
  localUri: string;
  metadata?: Record<string, any>;
}

export interface SubmissionData {
  formId: string;
  projectId: string;
  data: Record<string, any>;
  location?: LocationData;
  media?: MediaUploadData[];
  deviceInfo?: {
    platform: string;
    version: string;
    model?: string;
    batteryLevel?: number;
    networkType?: string;
  };
  metadata?: Record<string, any>;
}

export interface SubmissionStats {
  total: number;
  completed: number;
  synced: number;
  pending: number;
  rejected: number;
  completionRate: number;
}

/**
 * Submissions API Service
 * Handles form submissions, data collection, media uploads, and offline sync
 */
export class SubmissionsApi extends BaseApi {
  constructor() {
    super(TABLES.SUBMISSIONS);
  }

  /**
   * Create a new submission
   */
  async createSubmission(submissionData: SubmissionData): Promise<ApiResponse<Submission>> {
    const userId = await this.getCurrentUserId();
    if (!userId) {
      return {
        data: null,
        error: 'User not authenticated'
      };
    }

    const newSubmission: SubmissionInsert = {
      form_id: submissionData.formId,
      project_id: submissionData.projectId,
      user_id: userId,
      data: submissionData.data,
      location: submissionData.location as any,
      device_info: submissionData.deviceInfo as any,
      metadata: submissionData.metadata as any,
      status: 'draft',
      started_at: new Date().toISOString()
    };

    const response = await this.handleResponse<Submission>(
      supabase
        .from(TABLES.SUBMISSIONS)
        .insert(newSubmission)
        .select(`
          *,
          forms (
            id,
            name,
            version,
            schema
          ),
          projects (
            id,
            name,
            status
          ),
          user_profiles (
            id,
            full_name,
            email
          )
        `)
        .single(),
      'create submission'
    );

    // Handle media attachments if provided
    if (response.data && submissionData.media && submissionData.media.length > 0) {
      await this.addMediaAttachments(response.data.id, submissionData.media);
    }

    return response;
  }

  /**
   * Get submissions with pagination and filters
   */
  async getSubmissions(
    page: number = 1,
    pageSize: number = 20,
    filters: SubmissionFilters = {}
  ): Promise<PaginatedResponse<SubmissionWithDetails>> {
    const from = (page - 1) * pageSize;
    const to = from + pageSize - 1;

    let query = supabase
      .from(TABLES.SUBMISSIONS)
      .select(`
        *,
        forms (
          id,
          name,
          version,
          schema,
          projects (
            id,
            name
          )
        ),
        projects (
          id,
          name,
          status
        ),
        user_profiles (
          id,
          full_name,
          email,
          avatar_url
        ),
        media_attachments (
          id,
          field_id,
          file_name,
          file_type,
          storage_path,
          upload_status
        )
      `, { count: 'exact' })
      .range(from, to)
      .order('started_at', { ascending: false });

    // Apply filters
    if (filters.formId) {
      query = query.eq('form_id', filters.formId);
    }
    if (filters.projectId) {
      query = query.eq('project_id', filters.projectId);
    }
    if (filters.userId) {
      query = query.eq('user_id', filters.userId);
    }
    if (filters.status) {
      query = query.eq('status', filters.status);
    }
    if (filters.dateFrom) {
      query = query.gte('started_at', filters.dateFrom);
    }
    if (filters.dateTo) {
      query = query.lte('started_at', filters.dateTo);
    }
    if (filters.hasLocation !== undefined) {
      if (filters.hasLocation) {
        query = query.not('location', 'is', null);
      } else {
        query = query.is('location', null);
      }
    }

    return this.handlePaginatedResponse<SubmissionWithDetails>(
      query,
      'get submissions',
      page,
      pageSize
    );
  }

  /**
   * Get submission by ID
   */
  async getSubmissionById(submissionId: string): Promise<ApiResponse<SubmissionWithDetails>> {
    return this.handleResponse<SubmissionWithDetails>(
      supabase
        .from(TABLES.SUBMISSIONS)
        .select(`
          *,
          forms (
            id,
            name,
            version,
            schema,
            settings,
            projects (
              id,
              name,
              status
            )
          ),
          projects (
            id,
            name,
            status,
            description
          ),
          user_profiles (
            id,
            full_name,
            email,
            avatar_url,
            role
          ),
          media_attachments (
            id,
            field_id,
            file_name,
            file_type,
            file_size,
            storage_path,
            thumbnail_path,
            upload_status,
            created_at,
            metadata
          )
        `)
        .eq('id', submissionId)
        .single(),
      'get submission by id'
    );
  }

  /**
   * Update submission
   */
  async updateSubmission(
    submissionId: string,
    updates: {
      data?: Record<string, any>;
      location?: LocationData;
      status?: 'draft' | 'completed' | 'synced' | 'rejected';
      metadata?: Record<string, any>;
    }
  ): Promise<ApiResponse<Submission>> {
    const submissionUpdate: SubmissionUpdate = {
      ...updates,
      location: updates.location as any,
      metadata: updates.metadata as any
    };

    // Set completion timestamp if status is being changed to completed
    if (updates.status === 'completed') {
      submissionUpdate.completed_at = new Date().toISOString();
    }

    // Set sync timestamp if status is being changed to synced
    if (updates.status === 'synced') {
      submissionUpdate.synced_at = new Date().toISOString();
    }

    return this.handleResponse<Submission>(
      supabase
        .from(TABLES.SUBMISSIONS)
        .update(submissionUpdate)
        .eq('id', submissionId)
        .select(`
          *,
          forms (
            id,
            name,
            version
          ),
          projects (
            id,
            name
          ),
          user_profiles (
            id,
            full_name,
            email
          )
        `)
        .single(),
      'update submission'
    );
  }

  /**
   * Complete submission
   */
  async completeSubmission(submissionId: string): Promise<ApiResponse<Submission>> {
    return this.updateSubmission(submissionId, {
      status: 'completed'
    });
  }

  /**
   * Sync submission
   */
  async syncSubmission(submissionId: string): Promise<ApiResponse<Submission>> {
    return this.updateSubmission(submissionId, {
      status: 'synced'
    });
  }

  /**
   * Delete submission
   */
  async deleteSubmission(submissionId: string): Promise<ApiResponse<null>> {
    // First delete associated media attachments
    await supabase
      .from(TABLES.MEDIA_ATTACHMENTS)
      .delete()
      .eq('submission_id', submissionId);

    return this.handleResponse<null>(
      supabase
        .from(TABLES.SUBMISSIONS)
        .delete()
        .eq('id', submissionId),
      'delete submission'
    );
  }

  /**
   * Add media attachments to submission
   */
  async addMediaAttachments(
    submissionId: string,
    mediaList: MediaUploadData[]
  ): Promise<ApiResponse<MediaAttachment[]>> {
    try {
      const mediaInserts: MediaAttachmentInsert[] = mediaList.map(media => ({
        submission_id: submissionId,
        field_id: media.fieldId,
        file_name: media.fileName,
        file_type: media.fileType,
        file_size: media.fileSize,
        storage_path: media.localUri, // Will be updated when uploaded to storage
        upload_status: 'pending',
        metadata: media.metadata as any
      }));

      const { data, error } = await supabase
        .from(TABLES.MEDIA_ATTACHMENTS)
        .insert(mediaInserts)
        .select('*');

      if (error) {
        throw error;
      }

      return {
        data: data || [],
        error: null
      };
    } catch (error) {
      console.error('Add media attachments failed:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to add media attachments'
      };
    }
  }

  /**
   * Upload media file to storage
   */
  async uploadMediaFile(
    attachmentId: string,
    fileData: Blob | File,
    fileName: string
  ): Promise<ApiResponse<string>> {
    try {
      // Update upload status to uploading
      await supabase
        .from(TABLES.MEDIA_ATTACHMENTS)
        .update({ upload_status: 'uploading' })
        .eq('id', attachmentId);

      // Upload to Supabase storage
      const storagePath = `submissions/${attachmentId}/${fileName}`;
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('media-attachments')
        .upload(storagePath, fileData);

      if (uploadError) {
        // Update upload status to failed
        await supabase
          .from(TABLES.MEDIA_ATTACHMENTS)
          .update({ upload_status: 'failed' })
          .eq('id', attachmentId);
        
        throw uploadError;
      }

      // Update media attachment with storage path and completed status
      await supabase
        .from(TABLES.MEDIA_ATTACHMENTS)
        .update({
          storage_path: uploadData.path,
          upload_status: 'completed'
        })
        .eq('id', attachmentId);

      return {
        data: uploadData.path,
        error: null
      };
    } catch (error) {
      console.error('Upload media file failed:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to upload media file'
      };
    }
  }

  /**
   * Get media file URL
   */
  async getMediaFileUrl(storagePath: string): Promise<ApiResponse<string>> {
    try {
      const { data } = supabase.storage
        .from('media-attachments')
        .getPublicUrl(storagePath);

      return {
        data: data.publicUrl,
        error: null
      };
    } catch (error) {
      console.error('Get media file URL failed:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to get media file URL'
      };
    }
  }

  /**
   * Get user's submissions
   */
  async getUserSubmissions(
    userId?: string,
    page: number = 1,
    pageSize: number = 20,
    filters: Omit<SubmissionFilters, 'userId'> = {}
  ): Promise<PaginatedResponse<SubmissionWithDetails>> {
    const currentUserId = userId || await this.getCurrentUserId();
    if (!currentUserId) {
      return {
        data: null,
        error: 'User not authenticated',
        page,
        pageSize,
        totalPages: 0,
        totalCount: 0
      };
    }

    return this.getSubmissions(page, pageSize, {
      ...filters,
      userId: currentUserId
    });
  }

  /**
   * Get submission statistics
   */
  async getSubmissionStats(filters: SubmissionFilters = {}): Promise<ApiResponse<SubmissionStats>> {
    try {
      let query = supabase
        .from(TABLES.SUBMISSIONS)
        .select('status', { count: 'exact' });

      // Apply filters
      if (filters.formId) {
        query = query.eq('form_id', filters.formId);
      }
      if (filters.projectId) {
        query = query.eq('project_id', filters.projectId);
      }
      if (filters.userId) {
        query = query.eq('user_id', filters.userId);
      }
      if (filters.dateFrom) {
        query = query.gte('started_at', filters.dateFrom);
      }
      if (filters.dateTo) {
        query = query.lte('started_at', filters.dateTo);
      }

      const { data, error, count } = await query;

      if (error) {
        throw error;
      }

      const total = count || 0;
      const completed = data?.filter(s => s.status === 'completed').length || 0;
      const synced = data?.filter(s => s.status === 'synced').length || 0;
      const pending = data?.filter(s => s.status === 'draft').length || 0;
      const rejected = data?.filter(s => s.status === 'rejected').length || 0;
      const completionRate = total > 0 ? ((completed + synced) / total) * 100 : 0;

      const stats: SubmissionStats = {
        total,
        completed,
        synced,
        pending,
        rejected,
        completionRate: Math.round(completionRate * 100) / 100
      };

      return {
        data: stats,
        error: null
      };
    } catch (error) {
      console.error('Get submission stats failed:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to get submission statistics'
      };
    }
  }

  /**
   * Export submissions data
   */
  async exportSubmissions(
    filters: SubmissionFilters = {},
    format: 'json' | 'csv' | 'xlsx' = 'json'
  ): Promise<ApiResponse<any>> {
    try {
      // Get all submissions matching filters (no pagination for export)
      const response = await this.getSubmissions(1, 10000, filters);
      
      if (!response.data) {
        return {
          data: null,
          error: response.error || 'Failed to get submissions data'
        };
      }

      const submissions = response.data;

      if (format === 'json') {
        const exportData = {
          submissions,
          exported_at: new Date().toISOString(),
          exported_by: await this.getCurrentUserId(),
          filters,
          total_records: submissions.length
        };

        return {
          data: exportData,
          error: null
        };
      }

      if (format === 'csv') {
        // Convert to CSV format
        const csvData = this.convertSubmissionsToCSV(submissions);
        return {
          data: csvData,
          error: null
        };
      }

      // TODO: Implement XLSX export
      return {
        data: null,
        error: 'XLSX export not yet implemented'
      };
    } catch (error) {
      console.error('Export submissions failed:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to export submissions'
      };
    }
  }

  /**
   * Convert submissions to CSV format
   */
  private convertSubmissionsToCSV(submissions: SubmissionWithDetails[]): string {
    if (submissions.length === 0) {
      return 'No data to export';
    }

    // Get all unique field names from submissions
    const allFields = new Set<string>();
    submissions.forEach(submission => {
      Object.keys(submission.data || {}).forEach(field => allFields.add(field));
    });

    // Create header
    const baseHeaders = [
      'submission_id',
      'form_name',
      'project_name',
      'user_name',
      'status',
      'started_at',
      'completed_at',
      'latitude',
      'longitude'
    ];
    
    const fieldHeaders = Array.from(allFields);
    const headers = [...baseHeaders, ...fieldHeaders];

    // Create rows
    const rows = submissions.map(submission => {
      const location = submission.location as any;
      const baseData = [
        submission.id,
        submission.form?.name || '',
        submission.project?.name || '',
        submission.user_profile?.full_name || '',
        submission.status,
        submission.started_at,
        submission.completed_at || '',
        location?.latitude || '',
        location?.longitude || ''
      ];

      const fieldData = fieldHeaders.map(field => {
        const value = submission.data?.[field];
        return value !== undefined ? String(value) : '';
      });

      return [...baseData, ...fieldData];
    });

    // Convert to CSV string
    const csvContent = [
      headers.join(','),
      ...rows.map(row => row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(','))
    ].join('\n');

    return csvContent;
  }

  /**
   * Search submissions
   */
  async searchSubmissions(
    searchTerm: string,
    page: number = 1,
    pageSize: number = 20,
    filters: SubmissionFilters = {}
  ): Promise<PaginatedResponse<SubmissionWithDetails>> {
    const from = (page - 1) * pageSize;
    const to = from + pageSize - 1;

    let query = supabase
      .from(TABLES.SUBMISSIONS)
      .select(`
        *,
        forms (
          id,
          name,
          version,
          projects (
            id,
            name
          )
        ),
        projects (
          id,
          name
        ),
        user_profiles (
          id,
          full_name,
          email
        )
      `, { count: 'exact' })
      .range(from, to)
      .order('started_at', { ascending: false });

    // Apply text search on submission data (JSONB search)
    if (searchTerm) {
      query = query.or(`data->>text.ilike.%${searchTerm}%,forms.name.ilike.%${searchTerm}%,projects.name.ilike.%${searchTerm}%`);
    }

    // Apply other filters
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        const columnMap: Record<string, string> = {
          formId: 'form_id',
          projectId: 'project_id',
          userId: 'user_id',
          dateFrom: 'started_at',
          dateTo: 'started_at'
        };
        
        const column = columnMap[key] || key;
        
        if (key === 'dateFrom') {
          query = query.gte(column, value);
        } else if (key === 'dateTo') {
          query = query.lte(column, value);
        } else {
          query = query.eq(column, value);
        }
      }
    });

    return this.handlePaginatedResponse<SubmissionWithDetails>(
      query,
      'search submissions',
      page,
      pageSize
    );
  }

  /**
   * Get offline submissions (for sync)
   */
  async getOfflineSubmissions(): Promise<ApiResponse<Submission[]>> {
    const userId = await this.getCurrentUserId();
    if (!userId) {
      return {
        data: null,
        error: 'User not authenticated'
      };
    }

    return this.handleResponse<Submission[]>(
      supabase
        .from(TABLES.SUBMISSIONS)
        .select('*')
        .eq('user_id', userId)
        .in('status', ['draft', 'completed'])
        .order('started_at', { ascending: true }),
      'get offline submissions'
    );
  }

  /**
   * Bulk sync submissions
   */
  async bulkSyncSubmissions(submissionIds: string[]): Promise<ApiResponse<Submission[]>> {
    try {
      const { data, error } = await supabase
        .from(TABLES.SUBMISSIONS)
        .update({
          status: 'synced',
          synced_at: new Date().toISOString()
        })
        .in('id', submissionIds)
        .select('*');

      if (error) {
        throw error;
      }

      return {
        data: data || [],
        error: null
      };
    } catch (error) {
      console.error('Bulk sync submissions failed:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to sync submissions'
      };
    }
  }

  /**
   * Subscribe to submission changes
   */
  subscribeToSubmissionChanges(formId: string, callback: (payload: any) => void) {
    return this.subscribeToChanges(callback, `form_id=eq.${formId}`);
  }

  /**
   * Subscribe to user's submission changes
   */
  subscribeToUserSubmissions(userId: string, callback: (payload: any) => void) {
    return this.subscribeToChanges(callback, `user_id=eq.${userId}`);
  }
}

// Export singleton instance
export const submissionsApi = new SubmissionsApi();
export default submissionsApi;
