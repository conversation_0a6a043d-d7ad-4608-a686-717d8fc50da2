        <View style={styles.notifications}>
          {notifications.map(notification => (
            <View
              key={notification.id}
              style={[
                styles.notification,
                {
                  backgroundColor: 
                    notification.type === 'success' ? theme.colors.success :
                    notification.type === 'error' ? theme.colors.error :
                    notification.type === 'warning' ? theme.colors.warning :
                    theme.colors.primary,
                }
              ]}
            >
              <Text style={[styles.notificationText, { color: 'white' }]}>
                {notification.message}
              </Text>
            </View>
          ))}
        </View>

        {/* Modals */}
        {modals.fileUpload && (
          <FileUploadModal
            isVisible={modals.fileUpload}
            onClose={() => setModals(prev => ({ ...prev, fileUpload: false }))}
            onFileUploaded={handleFileUploaded}
          />
        )}

        {modals.attributeTable && attributeTableLayer && (
          <AttributeTableModal
            isVisible={modals.attributeTable}
            layer={attributeTableLayer}
            onClose={handleCloseAttributeTable}
          />
        )}

        {renderAnalysisConfigModal()}

        {/* Bookmarks Modal */}
        <Modal
          visible={modals.bookmarks}
          animationType="slide"
          transparent={true}
          onRequestClose={() => setModals(prev => ({ ...prev, bookmarks: false }))}
        >
          <View style={styles.modalOverlay}>
            <View style={[styles.modalContent, { backgroundColor: theme.colors.card }]}>
              <View style={[styles.modalHeader, { borderBottomColor: theme.colors.border }]}>
                <Text style={[styles.modalTitle, { color: theme.colors.text }]}>Bookmarks</Text>
                <TouchableOpacity
                  onPress={() => setModals(prev => ({ ...prev, bookmarks: false }))}
                >
                  <X size={24} color={theme.colors.muted} />
                </TouchableOpacity>
              </View>

              <ScrollView style={styles.modalBody}>
                <TouchableOpacity
                  style={[styles.bookmarkItem, { backgroundColor: theme.colors.background }]}
                  onPress={handleBookmarkSave}
                >
                  <Bookmark size={20} color={theme.colors.primary} />
                  <Text style={[styles.bookmarkText, { color: theme.colors.text }]}>
                    Save Current View
                  </Text>
                </TouchableOpacity>

                {bookmarks.map(bookmark => (
                  <TouchableOpacity
                    key={bookmark.id}
                    style={[styles.bookmarkItem, { backgroundColor: theme.colors.background }]}
                    onPress={() => {
                      handleBookmarkLoad(bookmark);
                      setModals(prev => ({ ...prev, bookmarks: false }));
                    }}
                  >
                    <MapPin size={20} color={theme.colors.text} />
                    <View style={styles.bookmarkInfo}>
                      <Text style={[styles.bookmarkName, { color: theme.colors.text }]}>
                        {bookmark.name}
                      </Text>
                      {bookmark.description && (
                        <Text style={[styles.bookmarkDescription, { color: theme.colors.muted }]}>
                          {bookmark.description}
                        </Text>
                      )}
                    </View>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>
          </View>
        </Modal>
      </SafeAreaView>
    </ErrorBoundary>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingText: {
    fontSize: 18,
    textAlign: 'center',
    marginTop: 100,
  },
  toolbar: {
    height: 60,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    borderBottomWidth: 1,
  },
  toolbarScroll: {
    flex: 1,
  },
  toolButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    borderRadius: 6,
    minWidth: 80,
  },
  toolButtonText: {
    marginLeft: 8,
    fontSize: 14,
    fontWeight: '500',
  },
  mainContent: {
    flex: 1,
    flexDirection: 'row',
  },
  sidebar: {
    borderRightWidth: 1,
    overflow: 'hidden',
  },
  sidebarTabs: {
    flexDirection: 'row',
    borderBottomWidth: 1,
  },
  sidebarTab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 8,
  },
  sidebarTabText: {
    marginLeft: 6,
    fontSize: 12,
    fontWeight: '500',
  },
  sidebarContent: {
    flex: 1,
  },
  mapContainer: {
    flex: 1,
    position: 'relative',
  },
  sidebarToggle: {
    position: 'absolute',
    top: 16,
    left: 16,
    zIndex: 1000,
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  statusBar: {
    height: 32,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    borderTopWidth: 1,
  },
  statusText: {
    fontSize: 12,
    marginRight: 16,
  },
  notifications: {
    position: 'absolute',
    top: 80,
    right: 16,
    zIndex: 1000,
  },
  notification: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    marginBottom: 8,
    maxWidth: 300,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  notificationText: {
    fontSize: 14,
    fontWeight: '500',
  },
  // Panel styles
  panel: {
    flex: 1,
    padding: 16,
  },
  panelHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  panelTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  panelSubtitle: {
    fontSize: 12,
    marginTop: 2,
  },
  panelHeaderActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerButton: {
    padding: 8,
    borderRadius: 6,
    marginRight: 8,
  },
  // Layer panel styles
  layerItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  layerItemLeft: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  layerInfo: {
    marginLeft: 12,
    flex: 1,
  },
  layerName: {
    fontSize: 14,
    fontWeight: '500',
  },
  layerSource: {
    fontSize: 12,
    marginTop: 2,
  },
  layerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  layerActionButton: {
    padding: 8,
    marginLeft: 4,
  },
  emptyStateContainer: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  emptyState: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 16,
  },
  uploadButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  uploadButtonText: {
    marginLeft: 8,
    fontSize: 14,
    fontWeight: '500',
  },
  emptyStateSubtext: {
    fontSize: 12,
    textAlign: 'center',
  },
  // Catalog panel styles
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    marginBottom: 16,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 14,
  },
  catalogItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  catalogItemContent: {
    flex: 1,
  },
  catalogItemName: {
    fontSize: 14,
    fontWeight: '500',
  },
  catalogItemDesc: {
    fontSize: 12,
    marginTop: 2,
  },
  catalogItemMeta: {
    fontSize: 11,
    marginTop: 4,
  },
  // Analysis panel styles
  analysisContainer: {
    flex: 1,
  },
  analysisCategory: {
    marginBottom: 24,
  },
  categoryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    paddingLeft: 4,
    borderLeftWidth: 4,
  },
  categoryIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 12,
  },
  categoryInfo: {
    flex: 1,
  },
  categoryTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  categoryDescription: {
    fontSize: 12,
    marginTop: 2,
  },
  toolsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -4,
  },
  toolCard: {
    width: '48%',
    margin: 4,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
  },
  toolIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  toolInfo: {
    flex: 1,
  },
  toolLabel: {
    fontSize: 12,
    fontWeight: '600',
    marginBottom: 4,
  },
  toolDescription: {
    fontSize: 10,
    lineHeight: 14,
  },
  progressContainer: {
    alignItems: 'center',
  },
  progressText: {
    fontSize: 12,
    fontWeight: '600',
  },
  analysisResults: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
  },
  resultsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  resultsTitle: {
    fontSize: 14,
    fontWeight: '600',
  },
  resultsCount: {
    fontSize: 12,
  },
  resultItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    marginBottom: 4,
  },
  resultInfo: {
    flex: 1,
  },
  resultName: {
    fontSize: 12,
    fontWeight: '500',
  },
  resultTime: {
    fontSize: 10,
    marginTop: 2,
  },
  resultStatus: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    maxWidth: 500,
    maxHeight: '80%',
    borderRadius: 12,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  modalBody: {
    padding: 16,
    maxHeight: 400,
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    padding: 16,
    borderTopWidth: 1,
  },
  modalButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    marginLeft: 8,
  },
  modalButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  cancelButton: {
    // Style for cancel button
  },
  runButton: {
    // Style for run button
  },
  configSection: {
    marginBottom: 16,
  },
  configLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  configInput: {
    borderWidth: 1,
    borderRadius: 6,
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 14,
  },
  pickerContainer: {
    borderWidth: 1,
    borderRadius: 6,
  },
  picker: {
    height: 40,
  },
  // Bookmark styles
  bookmarkItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  bookmarkText: {
    marginLeft: 12,
    fontSize: 14,
    fontWeight: '500',
  },
  bookmarkInfo: {
    marginLeft: 12,
    flex: 1,
  },
  bookmarkName: {
    fontSize: 14,
    fontWeight: '500',
  },
  bookmarkDescription: {
    fontSize: 12,
    marginTop: 2,
  },
});
