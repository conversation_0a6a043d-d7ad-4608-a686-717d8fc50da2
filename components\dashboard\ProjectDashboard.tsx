import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  ActivityIndicator,
  Dimensions,
  Animated,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { useTheme } from '@/hooks/useTheme';
import { useProjects } from '@/hooks/useProjects';
import { useSubmissions } from '@/hooks/useSubmissions';
import { useTeams } from '@/hooks/useTeams';
import { useConnection } from '@/hooks/useConnection';
import { Project, Submission, User } from '@/types';
import { ProjectWithStats } from '@/types/database';
import {
  MapPin,
  Users,
  Activity,
  Clock,
  CheckCircle,
  AlertTriangle,
  TrendingUp,
  Calendar,
  FileText,
  RotateCw, // Using RotateCw instead of Sync as it's the correct export
  Plus,
  Settings,
  Bell,
  Wifi,
  WifiOff,
  Download,
  Upload,
  Search,
  Filter,
  BarChart3,
  Zap,
  Target,
  Award,
  Globe,
  Send,
  BookOpen,
  Briefcase,
  Camera,
  PieChart,
} from 'lucide-react-native';

const { width: screenWidth } = Dimensions.get('window');

// Quick Action Item Component
interface QuickActionProps {
  title: string;
  description: string;
  icon: React.ReactElement;
  color: string;
  onPress: () => void;
  badge?: string;
}

function QuickActionCard({ title, description, icon, color, onPress, badge }: QuickActionProps) {
  const { theme } = useTheme();
  const scaleAnim = new Animated.Value(1);

  const handlePressIn = () => {
    Animated.spring(scaleAnim, {
      toValue: 0.95,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      useNativeDriver: true,
    }).start();
  };

  return (
    <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
      <TouchableOpacity
        style={[styles.quickActionCard, { backgroundColor: theme.colors.card }]}
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        activeOpacity={0.9}
      >
        <View style={[styles.quickActionIconContainer, { backgroundColor: color + '15' }]}>
          {React.cloneElement(icon, { color, size: 24 })}
          {badge && (
            <View style={[styles.quickActionBadge, { backgroundColor: theme.colors.error }]}>
              <Text style={styles.quickActionBadgeText}>{badge}</Text>
            </View>
          )}
        </View>
        <View style={styles.quickActionContent}>
          <Text style={[styles.quickActionTitle, { color: theme.colors.text }]}>{title}</Text>
          <Text style={[styles.quickActionDescription, { color: theme.colors.muted }]}>{description}</Text>
        </View>
        <View style={[styles.quickActionArrow, { borderLeftColor: color }]} />
      </TouchableOpacity>
    </Animated.View>
  );
}

// Enhanced KPI Card Component
interface KPICardProps {
  title: string;
  value: string;
  trend: string;
  trendDirection: 'up' | 'down' | 'neutral';
  icon: React.ReactElement;
  color: string;
  backgroundColor: string;
}

function KPICard({ title, value, trend, trendDirection, icon, color, backgroundColor }: KPICardProps) {
  const { theme } = useTheme();
  
  const getTrendIcon = () => {
    if (trendDirection === 'up') return <TrendingUp size={12} color={theme.colors.success} />;
    if (trendDirection === 'down') return <TrendingUp size={12} color={theme.colors.error} style={{ transform: [{ rotate: '180deg' }] }} />;
    return <Activity size={12} color={theme.colors.muted} />;
  };

  const getTrendColor = () => {
    if (trendDirection === 'up') return theme.colors.success;
    if (trendDirection === 'down') return theme.colors.error;
    return theme.colors.muted;
  };

  return (
    <View style={[styles.kpiCard, { backgroundColor: theme.colors.card }]}>
      <View style={styles.kpiHeader}>
        <View style={[styles.kpiIconContainer, { backgroundColor }]}>
          {React.cloneElement(icon, { color, size: 20 })}
        </View>
        <View style={styles.kpiTrend}>
          {getTrendIcon()}
          <Text style={[styles.kpiTrendText, { color: getTrendColor() }]}>{trend}</Text>
        </View>
      </View>
      <Text style={[styles.kpiValue, { color: theme.colors.text }]}>{value}</Text>
      <Text style={[styles.kpiTitle, { color: theme.colors.muted }]}>{title}</Text>
    </View>
  );
}
// Enhanced Project Card Interface
interface ProjectCardProps {
  project: ProjectWithStats;
  submissions: Submission[];
  onPress: () => void;
}

function ProjectCard({ project, submissions, onPress }: ProjectCardProps) {
  const { theme } = useTheme();
  
  const completedSubmissions = submissions.filter(s => 
    s.status === 'synced' || s.status === 'completed'
  ).length;
  
  const progress = submissions.length > 0 ? (completedSubmissions / submissions.length) * 100 : 0;
  
  const getStatusColor = (status: ProjectWithStats['status']) => {
    switch (status) {
      case 'active':
        return theme.colors.success;
      case 'completed':
        return theme.colors.primary;
      case 'draft':
        return theme.colors.warning;
      case 'archived':
        return theme.colors.muted;
      default:
        return theme.colors.muted;
    }
  };

  const getStatusIcon = (status: ProjectWithStats['status']) => {
    switch (status) {
      case 'active':
        return <Zap size={16} color={theme.colors.success} />;
      case 'completed':
        return <Award size={16} color={theme.colors.primary} />;
      case 'draft':
        return <Clock size={16} color={theme.colors.warning} />;
      case 'archived':
        return <FileText size={16} color={theme.colors.muted} />;
      default:
        return <AlertTriangle size={16} color={theme.colors.muted} />;
    }
  };

  return (
    <TouchableOpacity
      style={[styles.modernProjectCard, { backgroundColor: theme.colors.card }]}
      onPress={onPress}
      activeOpacity={0.95}
    >
      {/* Header with Status */}
      <View style={styles.modernProjectHeader}>
        <View style={styles.modernProjectInfo}>
          <Text style={[styles.modernProjectName, { color: theme.colors.text }]}>
            {project.name}
          </Text>
          <View style={[styles.modernStatusBadge, { backgroundColor: getStatusColor(project.status) + '20' }]}>
            {getStatusIcon(project.status)}
            <Text style={[styles.modernStatusText, { color: getStatusColor(project.status) }]}>
              {project.status.charAt(0).toUpperCase() + project.status.slice(1)}
            </Text>
          </View>
        </View>
        
        {/* Progress Ring */}
        <View style={styles.modernProgressContainer}>
          <View style={[styles.modernProgressRing, { borderColor: theme.colors.border }]}>
            <View style={[
              styles.modernProgressFill,
              { 
                borderColor: getStatusColor(project.status),
                transform: [{ rotate: `${(progress * 3.6)}deg` }]
              }
            ]} />
            <Text style={[styles.modernProgressText, { color: theme.colors.text }]}>
              {Math.round(progress)}%
            </Text>
          </View>
        </View>
      </View>
      
      {/* Description */}
      <Text style={[styles.modernProjectDescription, { color: theme.colors.muted }]}>
        {project.description || 'No description available'}
      </Text>
      
      {/* Enhanced Stats Row */}
      <View style={styles.modernStatsContainer}>
        <View style={styles.modernStatItem}>
          <View style={[styles.modernStatIcon, { backgroundColor: theme.colors.primary + '15' }]}>
            <FileText size={14} color={theme.colors.primary} />
          </View>
          <View>
            <Text style={[styles.modernStatValue, { color: theme.colors.text }]}>
              {project.stats?.total_forms || project.forms?.length || 0}
            </Text>
            <Text style={[styles.modernStatLabel, { color: theme.colors.muted }]}>Forms</Text>
          </View>
        </View>
        
        <View style={styles.modernStatItem}>
          <View style={[styles.modernStatIcon, { backgroundColor: theme.colors.success + '15' }]}>
            <CheckCircle size={14} color={theme.colors.success} />
          </View>
          <View>
            <Text style={[styles.modernStatValue, { color: theme.colors.text }]}>
              {submissions.length}
            </Text>
            <Text style={[styles.modernStatLabel, { color: theme.colors.muted }]}>Submissions</Text>
          </View>
        </View>
        
        <View style={styles.modernStatItem}>
          <View style={[styles.modernStatIcon, { backgroundColor: theme.colors.secondary + '15' }]}>
            <Users size={14} color={theme.colors.secondary} />
          </View>
          <View>
            <Text style={[styles.modernStatValue, { color: theme.colors.text }]}>
              {project.teams?.length || 0}
            </Text>
            <Text style={[styles.modernStatLabel, { color: theme.colors.muted }]}>Teams</Text>
          </View>
        </View>
      </View>
      
      {/* Location Info */}
      {project.region && (
        <View style={styles.modernLocationContainer}>
          <MapPin size={12} color={theme.colors.muted} />
          <Text style={[styles.modernLocationText, { color: theme.colors.muted }]}>
            {typeof project.region === 'object' && project.region && 'coordinates' in project.region && Array.isArray(project.region.coordinates)
              ? `${project.region.coordinates[1]?.toFixed(3)}, ${project.region.coordinates[0]?.toFixed(3)}`
              : 'Location available'
            }
          </Text>
        </View>
      )}
    </TouchableOpacity>
  );
}

interface TeamStatusCardProps {
  team: any;
  members: User[];
}

function TeamStatusCard({ team, members }: TeamStatusCardProps) {
  const { theme } = useTheme();
  
  const onlineMembers = members.filter(member => 
    Date.now() - (member.lastSyncTimestamp || 0) < 5 * 60 * 1000
  );

  return (
    <View style={[styles.teamCard, { backgroundColor: theme.colors.card }]}>
      <View style={styles.teamHeader}>
        <Text style={[styles.teamName, { color: theme.colors.text }]}>
          {team.name}
        </Text>
        <View style={styles.teamStats}>
          <Text style={[styles.teamOnline, { color: theme.colors.success }]}>
            {onlineMembers.length}
          </Text>
          <Text style={[styles.teamTotal, { color: theme.colors.muted }]}>
            /{members.length}
          </Text>
        </View>
      </View>
      <View style={styles.memberAvatars}>
        {members.slice(0, 4).map((member, index) => {
          const isOnline = Date.now() - (member.lastSyncTimestamp || 0) < 5 * 60 * 1000;
          return (
            <View
              key={member.id}
              style={[
                styles.memberAvatar,
                {
                  backgroundColor: isOnline ? theme.colors.success : theme.colors.muted,
                  marginLeft: index > 0 ? -8 : 0,
                },
              ]}
            >
              <Text style={styles.memberInitial}>
                {member.name.charAt(0).toUpperCase()}
              </Text>
            </View>
          );
        })}
        {members.length > 4 && (
          <View
            style={[
              styles.memberAvatar,
              { backgroundColor: theme.colors.border, marginLeft: -8 },
            ]}
          >
            <Text style={[styles.memberInitial, { color: theme.colors.text }]}>
              +{members.length - 4}
            </Text>
          </View>
        )}
      </View>
    </View>
  );
}

interface ActivityItemProps {
  activity: {
    id: string;
    type: 'submission' | 'sync' | 'project_created' | 'team_update';
    message: string;
    timestamp: number;
    user?: string;
  };
}

function ActivityItem({ activity }: ActivityItemProps) {
  const { theme } = useTheme();
  
  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'submission':
        return <FileText size={16} color={theme.colors.primary} />;
      case 'sync':
        return <RotateCw size={16} color={theme.colors.success} />;
      case 'project_created':
        return <Plus size={16} color={theme.colors.secondary} />;
      case 'team_update':
        return <Users size={16} color={theme.colors.warning} />;
      default:
        return <Activity size={16} color={theme.colors.muted} />;
    }
  };

  const timeAgo = (timestamp: number) => {
    const now = Date.now();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);
    
    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  };

  return (
    <View style={styles.activityItem}>
      <View style={[styles.activityIcon, { backgroundColor: theme.colors.primaryLight }]}>
        {getActivityIcon(activity.type)}
      </View>
      <View style={styles.activityContent}>
        <Text style={[styles.activityMessage, { color: theme.colors.text }]}>
          {activity.message}
        </Text>
        <Text style={[styles.activityTime, { color: theme.colors.muted }]}>
          {timeAgo(activity.timestamp)}
        </Text>
      </View>
    </View>
  );
}

export default function ProjectDashboard() {
  const { theme } = useTheme();
  const { isConnected } = useConnection();
  const { projects, loading: projectsLoading, fetchProjects } = useProjects();
  const { submissions, loading: submissionsLoading } = useSubmissions();
  const { teams, teamMembers, loading: teamsLoading, getTeamMembers } = useTeams();
  
  const [refreshing, setRefreshing] = useState(false);
  const [lastSyncTime, setLastSyncTime] = useState<Date | null>(null);
  const [searchVisible, setSearchVisible] = useState(false);

  const loading = projectsLoading || submissionsLoading || teamsLoading;

  // Calculate stats first
  const activeProjects = projects.filter(p => p.status === 'active');
  const totalSubmissions = submissions.length;
  const completedSubmissions = submissions.filter(s => 
    s.status === 'synced' || s.status === 'completed'
  ).length;
  const onlineMembers = teamMembers.filter(member => 
    Date.now() - (member.lastSyncTimestamp || 0) < 5 * 60 * 1000
  );

  // Quick Actions Data
  const quickActions = [
    {
      title: 'New Project',
      description: 'Create a new field project',
      icon: <Plus />,
      color: theme.colors.primary,
      onPress: () => {
        Alert.alert('Quick Action', 'Navigate to Create Project');
        router.push('/(tabs)');
      }
    },
    {
      title: 'Form Builder',
      description: 'Design custom forms',
      icon: <BookOpen />,
      color: theme.colors.secondary,
      onPress: () => {
        router.push('/forms/builder');
      }
    },
    {
      title: 'Export Data',
      description: 'Download project data',
      icon: <Download />,
      color: theme.colors.warning,
      onPress: () => {
        Alert.alert('Export Data', 'Preparing data export...');
      },
      badge: '3'
    },
    {
      title: 'Team Invite',
      description: 'Add team members',
      icon: <Send />,
      color: theme.colors.accent,
      onPress: () => {
        Alert.alert('Quick Action', 'Navigate to Team Management');
        router.push('/team');
      }
    },
    {
      title: 'Analytics',
      description: 'View detailed reports',
      icon: <BarChart3 />,
      color: theme.colors.info,
      onPress: () => {
        Alert.alert('Quick Action', 'Navigate to Analytics');
      }
    },
    {
      title: 'Map View',
      description: 'Explore project locations',
      icon: <Globe />,
      color: theme.colors.success,
      onPress: () => {
        Alert.alert('Quick Action', 'Navigate to Map View');
        router.push('/(tabs)/map');
      }
    }
  ];

  // Enhanced KPI Data
  const kpiData = [
    {
      title: 'Active Projects',
      value: projects.filter(p => p.status === 'active').length.toString(),
      trend: '+12%',
      trendDirection: 'up' as const,
      icon: <Briefcase />,
      color: theme.colors.primary,
      backgroundColor: theme.colors.primary + '15'
    },
    {
      title: 'Completion Rate',
      value: `${Math.round((completedSubmissions / Math.max(totalSubmissions, 1)) * 100)}%`,
      trend: '+8%',
      trendDirection: 'up' as const,
      icon: <Target />,
      color: theme.colors.success,
      backgroundColor: theme.colors.success + '15'
    },
    {
      title: 'Teams Online',
      value: onlineMembers.length.toString(),
      trend: '+5%',
      trendDirection: 'up' as const,
      icon: <Users />,
      color: theme.colors.secondary,
      backgroundColor: theme.colors.secondary + '15'
    },
    {
      title: 'Data Synced',
      value: `${Math.round(Math.random() * 100)}%`,
      trend: '+2%',
      trendDirection: 'up' as const,
      icon: <RotateCw />,
      color: theme.colors.info,
      backgroundColor: theme.colors.info + '15'
    }
  ];
  const recentActivity = [
    {
      id: '1',
      type: 'submission' as const,
      message: 'New forest survey data collected by Sarah Chen',
      timestamp: Date.now() - 300000, // 5 minutes ago
      user: 'Sarah Chen',
    },
    {
      id: '2',
      type: 'sync' as const,
      message: 'Data synchronized successfully across all devices',
      timestamp: Date.now() - 900000, // 15 minutes ago
    },
    {
      id: '3',
      type: 'project_created' as const,
      message: 'New project "Coastal Erosion Study" created',
      timestamp: Date.now() - 1800000, // 30 minutes ago
    },
    {
      id: '4',
      type: 'team_update' as const,
      message: 'Mike Rodriguez joined Field Research Alpha team',
      timestamp: Date.now() - 3600000, // 1 hour ago
    },
  ];

  const onRefresh = async () => {
    setRefreshing(true);
    try {
      await Promise.all([fetchProjects()]);
      setLastSyncTime(new Date());
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setRefreshing(false);
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={[styles.loadingText, { color: theme.colors.text }]}>
            Loading dashboard...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Enhanced Header */}
      <View style={styles.modernHeader}>
        <View style={styles.modernHeaderContent}>
          <View>
            <Text style={[styles.modernTitle, { color: theme.colors.text }]}>Project Command Center</Text>
            <Text style={[styles.modernSubtitle, { color: theme.colors.muted }]}>
              🚀 Real-time oversight & intelligent coordination
            </Text>
          </View>
          <View style={styles.modernHeaderActions}>
            <TouchableOpacity 
              style={[styles.modernHeaderButton, { backgroundColor: theme.colors.card }]}
              onPress={() => setSearchVisible(!searchVisible)}
            >
              <Search size={20} color={theme.colors.text} />
            </TouchableOpacity>
            <TouchableOpacity style={[styles.modernHeaderButton, { backgroundColor: theme.colors.card }]}>
              <Bell size={20} color={theme.colors.text} />
              <View style={[styles.notificationBadge, { backgroundColor: theme.colors.error }]}>
                <Text style={styles.notificationBadgeText}>3</Text>
              </View>
            </TouchableOpacity>
            <TouchableOpacity 
              style={[styles.modernHeaderButton, { backgroundColor: theme.colors.card }]}
              onPress={() => router.push('/team')}
            >
              <Users size={20} color={theme.colors.text} />
            </TouchableOpacity>
          </View>
        </View>
        
        {/* Quick Search Bar */}
        {searchVisible && (
          <View style={[styles.quickSearchContainer, { backgroundColor: theme.colors.card }]}>
            <Search size={16} color={theme.colors.muted} />
            <Text style={[styles.quickSearchPlaceholder, { color: theme.colors.muted }]}>
              Search projects, forms, or team members...
            </Text>
          </View>
        )}
      </View>

      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Quick Actions Section */}
        <View style={styles.quickActionsSection}>
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
              ⚡ Quick Actions
            </Text>
            <TouchableOpacity>
              <Text style={[styles.viewAllLink, { color: theme.colors.primary }]}>
                Customize
              </Text>
            </TouchableOpacity>
          </View>
          
          <View style={styles.quickActionsGrid}>
            {quickActions.map((action, index) => (
              <QuickActionCard
                key={index}
                title={action.title}
                description={action.description}
                icon={action.icon}
                color={action.color}
                onPress={action.onPress}
                badge={action.badge}
              />
            ))}
          </View>
        </View>

        {/* Enhanced KPIs */}
        <View style={styles.kpiSection}>
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
              📊 Performance Metrics
            </Text>
            <TouchableOpacity>
              <Filter size={16} color={theme.colors.primary} />
            </TouchableOpacity>
          </View>
          
          <View style={styles.kpiGrid}>
            {kpiData.map((kpi, index) => (
              <KPICard
                key={index}
                title={kpi.title}
                value={kpi.value}
                trend={kpi.trend}
                trendDirection={kpi.trendDirection}
                icon={kpi.icon}
                color={kpi.color}
                backgroundColor={kpi.backgroundColor}
              />
            ))}
          </View>
        </View>

        {/* Sync Status */}
        <View style={[styles.syncCard, { backgroundColor: theme.colors.card }]}>
          <View style={styles.syncHeader}>
            <View style={styles.syncInfo}>
              <View style={styles.syncStatus}>
                {isConnected ? (
                  <Wifi size={20} color={theme.colors.success} />
                ) : (
                  <WifiOff size={20} color={theme.colors.warning} />
                )}
                <Text style={[styles.syncText, { color: theme.colors.text }]}>
                  {isConnected ? 'Connected' : 'Offline Mode'}
                </Text>
              </View>
              {lastSyncTime && (
                <Text style={[styles.lastSync, { color: theme.colors.muted }]}>
                  Last sync: {lastSyncTime.toLocaleTimeString()}
                </Text>
              )}
            </View>
            <TouchableOpacity
              style={[styles.syncButton, { backgroundColor: theme.colors.primary }]}
              onPress={onRefresh}
            >
              <RotateCw size={16} color="white" />
              <Text style={styles.syncButtonText}>Sync Now</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Active Projects */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
              Active Projects
            </Text>
            <TouchableOpacity onPress={() => router.push('/(tabs)')}>
              <Text style={[styles.viewAll, { color: theme.colors.primary }]}>
                View All
              </Text>
            </TouchableOpacity>
          </View>
          
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View style={styles.projectsList}>
              {activeProjects.slice(0, 5).map((project) => (
                <ProjectCard
                  key={project.id}
                  project={project}
                  submissions={submissions.filter(s => s.projectId === project.id)}
                  onPress={() => router.push(`/projects/${project.id}`)}
                />
              ))}
              
              <TouchableOpacity
                style={[styles.addProjectCard, { backgroundColor: theme.colors.card }]}
                onPress={() => router.push('/(tabs)')}
              >
                <Plus size={32} color={theme.colors.primary} />
                <Text style={[styles.addProjectText, { color: theme.colors.primary }]}>
                  New Project
                </Text>
              </TouchableOpacity>
            </View>
          </ScrollView>
        </View>

        {/* Team Status */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
              Team Status & Availability
            </Text>
            <TouchableOpacity onPress={() => router.push('/team')}>
              <Text style={[styles.viewAll, { color: theme.colors.primary }]}>
                Manage
              </Text>
            </TouchableOpacity>
          </View>
          
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View style={styles.teamsList}>
              {teams.map((team) => (
                <TeamStatusCard
                  key={team.id}
                  team={team}
                  members={getTeamMembers(team.id)}
                />
              ))}
            </View>
          </ScrollView>
        </View>

        {/* Recent Activity */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
              Recent Activity
            </Text>
            <TouchableOpacity>
              <Text style={[styles.viewAll, { color: theme.colors.primary }]}>
                View All
              </Text>
            </TouchableOpacity>
          </View>
          
          <View style={[styles.activityFeed, { backgroundColor: theme.colors.card }]}>
            {recentActivity.map((activity) => (
              <ActivityItem key={activity.id} activity={activity} />
            ))}
          </View>
        </View>

        <View style={styles.bottomSpace} />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    fontWeight: '500',
  },
  
  // Modern Header Styles
  modernHeader: {
    paddingHorizontal: 20,
    paddingTop: 12,
    paddingBottom: 16,
  },
  modernHeaderContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  modernTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    letterSpacing: -0.5,
  },
  modernSubtitle: {
    fontSize: 15,
    marginTop: 6,
    lineHeight: 20,
  },
  modernHeaderActions: {
    flexDirection: 'row',
    gap: 12,
  },
  modernHeaderButton: {
    padding: 12,
    borderRadius: 12,
    position: 'relative',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  notificationBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 8,
    height: 8,
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  notificationBadgeText: {
    color: 'white',
    fontSize: 8,
    fontWeight: 'bold',
  },
  quickSearchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    gap: 12,
  },
  quickSearchPlaceholder: {
    fontSize: 14,
  },
  
  // Quick Actions Styles
  quickActionsSection: {
    marginBottom: 24,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    paddingHorizontal: 20,
  },
  quickActionCard: {
    flex: 1,
    minWidth: (screenWidth - 64) / 2,
    maxWidth: (screenWidth - 64) / 2,
    padding: 16,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  quickActionIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
    position: 'relative',
  },
  quickActionBadge: {
    position: 'absolute',
    top: -2,
    right: -2,
    width: 16,
    height: 16,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  quickActionBadgeText: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
  },
  quickActionContent: {
    marginBottom: 8,
  },
  quickActionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  quickActionDescription: {
    fontSize: 13,
    lineHeight: 18,
  },
  quickActionArrow: {
    position: 'absolute',
    right: 16,
    top: '50%',
    width: 0,
    height: 0,
    borderLeftWidth: 6,
    borderTopWidth: 4,
    borderBottomWidth: 4,
    borderTopColor: 'transparent',
    borderBottomColor: 'transparent',
    transform: [{ translateY: -4 }],
  },
  
  // Enhanced KPI Styles
  kpiSection: {
    marginBottom: 24,
  },
  kpiGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    paddingHorizontal: 20,
  },
  kpiCard: {
    flex: 1,
    minWidth: (screenWidth - 64) / 2,
    maxWidth: (screenWidth - 64) / 2,
    padding: 20,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 4,
  },
  kpiHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  kpiIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  kpiTrend: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  kpiTrendText: {
    fontSize: 12,
    fontWeight: '600',
  },
  kpiValue: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 4,
    letterSpacing: -0.5,
  },
  kpiTitle: {
    fontSize: 13,
    fontWeight: '500',
  },
  
  // Modern Project Card Styles
  modernProjectCard: {
    width: screenWidth * 0.85,
    padding: 20,
    borderRadius: 20,
    marginRight: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 6,
  },
  modernProjectHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  modernProjectInfo: {
    flex: 1,
  },
  modernProjectName: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
    letterSpacing: -0.3,
  },
  modernStatusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    gap: 6,
    alignSelf: 'flex-start',
  },
  modernStatusText: {
    fontSize: 12,
    fontWeight: '600',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  modernProgressContainer: {
    marginLeft: 16,
  },
  modernProgressRing: {
    width: 60,
    height: 60,
    borderRadius: 30,
    borderWidth: 3,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  modernProgressFill: {
    position: 'absolute',
    width: 54,
    height: 54,
    borderRadius: 27,
    borderWidth: 3,
    borderColor: 'transparent',
  },
  modernProgressText: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  modernProjectDescription: {
    fontSize: 15,
    lineHeight: 22,
    marginBottom: 20,
  },
  modernStatsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  modernStatItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  modernStatIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modernStatValue: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  modernStatLabel: {
    fontSize: 11,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  modernLocationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    marginTop: 8,
  },
  modernLocationText: {
    fontSize: 12,
    fontFamily: 'monospace',
  },
  
  // Enhanced Common Styles
  content: {
    flex: 1,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    letterSpacing: -0.3,
  },
  viewAllLink: {
    fontSize: 14,
    fontWeight: '600',
  },
  viewAll: {
    fontSize: 14,
    fontWeight: '600',
  },
  
  // Sync Card Styles (enhanced)
  syncCard: {
    marginHorizontal: 20,
    marginBottom: 24,
    padding: 20,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  syncHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  syncInfo: {
    flex: 1,
  },
  syncStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    marginBottom: 6,
  },
  syncText: {
    fontSize: 18,
    fontWeight: '600',
  },
  lastSync: {
    fontSize: 13,
  },
  syncButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 12,
    gap: 8,
  },
  syncButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  
  // Legacy styles for compatibility
  section: {
    marginBottom: 28,
  },
  projectsList: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    gap: 16,
  },
  addProjectCard: {
    width: screenWidth * 0.65,
    height: 180,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderStyle: 'dashed',
    borderColor: '#ddd',
  },
  addProjectText: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 12,
  },
  teamsList: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    gap: 12,
  },
  teamCard: {
    width: screenWidth * 0.6,
    padding: 20,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 4,
  },
  teamHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  teamName: {
    fontSize: 17,
    fontWeight: 'bold',
    flex: 1,
  },
  teamStats: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  teamOnline: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  teamTotal: {
    fontSize: 15,
  },
  memberAvatars: {
    flexDirection: 'row',
  },
  memberAvatar: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'white',
  },
  memberInitial: {
    color: 'white',
    fontSize: 13,
    fontWeight: 'bold',
  },
  activityFeed: {
    marginHorizontal: 20,
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 4,
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 20,
  },
  activityIcon: {
    padding: 10,
    borderRadius: 10,
    marginRight: 16,
  },
  activityContent: {
    flex: 1,
  },
  activityMessage: {
    fontSize: 15,
    lineHeight: 22,
    marginBottom: 6,
  },
  activityTime: {
    fontSize: 12,
  },
  bottomSpace: {
    height: 120,
  },
});
