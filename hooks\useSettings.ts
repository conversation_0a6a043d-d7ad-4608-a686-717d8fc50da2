import { useState, useEffect, useContext } from 'react';
import { AppSettings } from '@/types';
import { Alert, Platform } from 'react-native';
import { DatabaseContext } from '@/providers/DatabaseProvider';

const DEFAULT_SETTINGS: AppSettings = {
  offlineMapRadius: 10, // in kilometers
  syncOnCellularData: false,
  syncInterval: 30, // in minutes
  language: 'en',
  theme: 'system',
  locationAccuracyThreshold: 10, // in meters
  automaticBackup: true,
  backupInterval: 7, // in days
};

export function useSettings() {
  const [settings, setSettings] = useState<AppSettings>(DEFAULT_SETTINGS);
  const [loading, setLoading] = useState(false);
  const { db, webStorage, initialized } = useContext(DatabaseContext);
  
  const loadSettings = async () => {
    if (!initialized) return;
    
    try {
      if (Platform.OS === 'web' && webStorage) {
        const stored = webStorage.getItem('fieldsync_settings');
        if (stored) {
          const parsedSettings = JSON.parse(stored);
          setSettings({
            offlineMapRadius: parsedSettings.offlineMapRadius || DEFAULT_SETTINGS.offlineMapRadius,
            syncOnCellularData: !!parsedSettings.syncOnCellularData,
            syncInterval: parsedSettings.syncInterval || DEFAULT_SETTINGS.syncInterval,
            language: parsedSettings.language || DEFAULT_SETTINGS.language,
            theme: parsedSettings.theme || DEFAULT_SETTINGS.theme,
            locationAccuracyThreshold: parsedSettings.locationAccuracyThreshold || DEFAULT_SETTINGS.locationAccuracyThreshold,
            automaticBackup: !!parsedSettings.automaticBackup,
            backupInterval: parsedSettings.backupInterval || DEFAULT_SETTINGS.backupInterval,
          });
        }
      } else if (db) {
        const stored = await db.getFirstAsync<any>('SELECT * FROM settings WHERE id = 1');
        if (stored) {
          setSettings({
            offlineMapRadius: stored.offline_map_radius,
            syncOnCellularData: !!stored.sync_on_cellular_data,
            syncInterval: stored.sync_interval,
            language: stored.language,
            theme: stored.theme,
            locationAccuracyThreshold: stored.location_accuracy_threshold,
            automaticBackup: !!stored.automatic_backup,
            backupInterval: stored.backup_interval,
          });
        }
      }
    } catch (error) {
      console.error('Error loading settings:', error);
    }
  };
  
  const updateSettings = async (newSettings: AppSettings) => {
    setLoading(true);
    
    try {
      if (Platform.OS === 'web' && webStorage) {
        webStorage.setItem('fieldsync_settings', JSON.stringify({
          id: 1,
          offlineMapRadius: newSettings.offlineMapRadius,
          syncOnCellularData: newSettings.syncOnCellularData,
          syncInterval: newSettings.syncInterval,
          language: newSettings.language,
          theme: newSettings.theme,
          locationAccuracyThreshold: newSettings.locationAccuracyThreshold,
          automaticBackup: newSettings.automaticBackup,
          backupInterval: newSettings.backupInterval,
        }));
      } else if (db) {
        await db.runAsync(`
          UPDATE settings SET
            offline_map_radius = ?,
            sync_on_cellular_data = ?,
            sync_interval = ?,
            language = ?,
            theme = ?,
            location_accuracy_threshold = ?,
            automatic_backup = ?,
            backup_interval = ?
          WHERE id = 1
        `, [
          newSettings.offlineMapRadius,
          newSettings.syncOnCellularData ? 1 : 0,
          newSettings.syncInterval,
          newSettings.language,
          newSettings.theme,
          newSettings.locationAccuracyThreshold,
          newSettings.automaticBackup ? 1 : 0,
          newSettings.backupInterval,
        ]);
      }
      
      setSettings(newSettings);
    } catch (error) {
      console.error('Error updating settings:', error);
    } finally {
      setLoading(false);
    }
  };
  
  const syncData = async () => {
    setLoading(true);
    
    try {
      // Simulate syncing
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Show success message
      if (Platform.OS === 'web') {
        alert('Data synchronized successfully');
      } else {
        Alert.alert('Success', 'Data synchronized successfully');
      }
    } catch (error) {
      console.error('Error syncing data:', error);
      
      // Show error message
      if (Platform.OS === 'web') {
        alert('Failed to synchronize data');
      } else {
        Alert.alert('Error', 'Failed to synchronize data');
      }
    } finally {
      setLoading(false);
    }
  };
  
  const clearLocalData = async () => {
    setLoading(true);
    
    try {
      // Simulate clearing data
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Show success message
      if (Platform.OS === 'web') {
        alert('Local data cleared successfully');
      } else {
        Alert.alert('Success', 'Local data cleared successfully');
      }
    } catch (error) {
      console.error('Error clearing data:', error);
      
      // Show error message
      if (Platform.OS === 'web') {
        alert('Failed to clear local data');
      } else {
        Alert.alert('Error', 'Failed to clear local data');
      }
    } finally {
      setLoading(false);
    }
  };
  
  const exportData = async () => {
    setLoading(true);
    
    try {
      // Simulate exporting data
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Show success message
      if (Platform.OS === 'web') {
        alert('Data exported successfully');
      } else {
        Alert.alert('Success', 'Data exported successfully');
      }
    } catch (error) {
      console.error('Error exporting data:', error);
      
      // Show error message
      if (Platform.OS === 'web') {
        alert('Failed to export data');
      } else {
        Alert.alert('Error', 'Failed to export data');
      }
    } finally {
      setLoading(false);
    }
  };
  
  // Load settings when database is initialized
  useEffect(() => {
    if (initialized) {
      loadSettings();
    }
  }, [initialized]);
  
  return {
    settings,
    loading,
    updateSettings,
    syncData,
    clearLocalData,
    exportData,
  };
}