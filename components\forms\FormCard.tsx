import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Form } from '@/types';
import { useTheme } from '@/hooks/useTheme';
import { FileInput, Clock, CheckCircle, Archive, Calendar } from 'lucide-react-native';

type FormCardProps = {
  form: Form;
  onPress: () => void;
};

export default function FormCard({ form, onPress }: FormCardProps) {
  const { theme } = useTheme();
  
  const getStatusIcon = () => {
    switch (form.status) {
      case 'published':
        return <CheckCircle size={16} color={theme.colors.success} />;
      case 'draft':
        return <Clock size={16} color={theme.colors.warning} />;
      case 'archived':
        return <Archive size={16} color={theme.colors.muted} />;
      default:
        return <FileInput size={16} color={theme.colors.text} />;
    }
  };
  
  const getStatusText = () => {
    switch (form.status) {
      case 'published':
        return 'Published';
      case 'draft':
        return 'Draft';
      case 'archived':
        return 'Archived';
      default:
        return 'Unknown';
    }
  };
  
  const getStatusColor = () => {
    switch (form.status) {
      case 'published':
        return theme.colors.success;
      case 'draft':
        return theme.colors.warning;
      case 'archived':
        return theme.colors.muted;
      default:
        return theme.colors.text;
    }
  };
  
  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp);
    return date.toLocaleDateString();
  };
  
  const questionCount = form.schema.sections.reduce(
    (count, section) => count + section.questions.length, 
    0
  );

  return (
    <TouchableOpacity 
      style={[styles.container, { backgroundColor: theme.colors.card, borderColor: theme.colors.border }]} 
      onPress={onPress}
      disabled={form.status !== 'published'}
    >
      <View style={styles.content}>
        <View style={styles.header}>
          <Text style={[styles.title, { color: theme.colors.text }]}>{form.name}</Text>
          <View style={[styles.statusBadge, { backgroundColor: `${getStatusColor()}20` }]}>
            {getStatusIcon()}
            <Text style={[styles.statusText, { color: getStatusColor() }]}>{getStatusText()}</Text>
          </View>
        </View>
        
        {form.description && (
          <Text 
            style={[styles.description, { color: theme.colors.text }]}
            numberOfLines={2}
          >
            {form.description}
          </Text>
        )}
        
        <View style={styles.metadata}>
          <View style={styles.metadataItem}>
            <Calendar size={14} color={theme.colors.muted} />
            <Text style={[styles.metadataText, { color: theme.colors.muted }]}>
              Updated: {formatDate(form.updatedAt)}
            </Text>
          </View>
          
          <View style={styles.metadataItem}>
            <FileInput size={14} color={theme.colors.muted} />
            <Text style={[styles.metadataText, { color: theme.colors.muted }]}>
              Questions: {questionCount}
            </Text>
          </View>
        </View>
        
        {form.status !== 'published' && (
          <View style={[styles.disabledOverlay, { backgroundColor: theme.colors.card + 'E6' }]}>
            <Text style={[styles.disabledText, { color: theme.colors.muted }]}>
              {form.status === 'draft' ? 'This form is still in draft' : 'This form is archived'}
            </Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    borderWidth: 1,
    overflow: 'hidden',
    marginBottom: 16,
    position: 'relative',
  },
  content: {
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  title: {
    flex: 1,
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    marginRight: 8,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    gap: 4,
  },
  statusText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },
  description: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    marginBottom: 16,
  },
  metadata: {
    flexDirection: 'row',
    gap: 16,
  },
  metadataItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  metadataText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },
  disabledOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  disabledText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    textAlign: 'center',
    padding: 16,
  },
});