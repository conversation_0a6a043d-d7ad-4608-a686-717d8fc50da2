import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Alert,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { useTheme } from '@/hooks/useTheme';
import { FormSchema, FormPage, FormSection, FormQuestion } from '@/types';
import EnhancedFormBuilder from '@/components/forms/EnhancedFormBuilder';
import {
  ArrowLeft,
  Save,
  Eye,
  Settings,
  Info,
} from 'lucide-react-native';

export default function AdvancedFormBuilderScreen() {
  const { theme } = useTheme();
  const [formName, setFormName] = useState('');
  const [formDescription, setFormDescription] = useState('');
  const [showSettings, setShowSettings] = useState(true);
  
  // Initialize with a default form structure
  const [formSchema, setFormSchema] = useState<FormSchema>({
    pages: [
      {
        id: `page_${Date.now()}`,
        title: 'Page 1',
        description: 'First page of the form',
        order: 0,
        sections: [
          {
            id: `section_${Date.now()}`,
            title: 'General Information',
            description: 'Basic information collection',
            order: 0,
            questions: [],
          },
        ],
      },
    ],
    logicRules: [],
  });

  const updateFormSchema = useCallback((newSchema: FormSchema) => {
    setFormSchema(newSchema);
  }, []);

  const handleSave = async () => {
    if (!formName.trim()) {
      Alert.alert('Error', 'Please enter a form name');
      return;
    }

    if (formSchema.pages.length === 0) {
      Alert.alert('Error', 'Please add at least one page');
      return;
    }

    // Validate form structure
    for (const page of formSchema.pages) {
      if (!page.title.trim()) {
        Alert.alert('Error', 'All pages must have a title');
        return;
      }
      
      if (page.sections.length === 0) {
        Alert.alert('Error', `Page "${page.title}" must have at least one section`);
        return;
      }

      for (const section of page.sections) {
        if (!section.title.trim()) {
          Alert.alert('Error', 'All sections must have a title');
          return;
        }
        
        for (const question of section.questions) {
          if (!question.label.trim()) {
            Alert.alert('Error', 'All questions must have a label');
            return;
          }
        }
      }
    }

    const form = {
      id: `form_${Date.now()}`,
      projectId: 'current-project', // Would come from context
      name: formName,
      description: formDescription,
      version: 1,
      createdAt: Date.now(),
      updatedAt: Date.now(),
      createdBy: 'current-user', // Would come from auth context
      status: 'draft' as const,
      schema: formSchema,
    };

    try {
      console.log('Saving enhanced form:', form);
      
      if (Platform.OS === 'web') {
        const existingForms = JSON.parse(localStorage.getItem('fieldsync_forms') || '[]');
        existingForms.push(form);
        localStorage.setItem('fieldsync_forms', JSON.stringify(existingForms));
      }

      Alert.alert('Success', 'Enhanced form created successfully!', [
        { text: 'OK', onPress: () => router.back() }
      ]);
    } catch (error) {
      console.error('Error saving form:', error);
      Alert.alert('Error', 'Failed to save form. Please try again.');
    }
  };

  const handlePreview = () => {
    if (!formName.trim()) {
      Alert.alert('Error', 'Please enter a form name before previewing');
      return;
    }

    const formData = {
      name: formName,
      description: formDescription,
      schema: formSchema,
    };

    router.push({
      pathname: '/forms/advanced-preview',
      params: { formData: JSON.stringify(formData) }
    });
  };

  const renderFormSettings = () => {
    if (!showSettings) return null;

    return (
      <View style={[styles.settingsPanel, { backgroundColor: theme.colors.card, borderBottomColor: theme.colors.border }]}>
        <View style={styles.settingsHeader}>
          <View style={styles.settingsHeaderLeft}>
            <Settings size={20} color={theme.colors.primary} />
            <Text style={[styles.settingsTitle, { color: theme.colors.text }]}>
              Form Settings
            </Text>
          </View>
          <TouchableOpacity
            onPress={() => setShowSettings(false)}
            style={styles.settingsToggle}
          >
            <Text style={[styles.settingsToggleText, { color: theme.colors.muted }]}>
              Hide
            </Text>
          </TouchableOpacity>
        </View>

        <View style={styles.settingsContent}>
          <View style={styles.inputGroup}>
            <Text style={[styles.inputLabel, { color: theme.colors.text }]}>
              Form Name *
            </Text>
            <TextInput
              style={[
                styles.input,
                {
                  backgroundColor: theme.colors.background,
                  borderColor: theme.colors.border,
                  color: theme.colors.text,
                },
              ]}
              value={formName}
              onChangeText={setFormName}
              placeholder="Enter form name"
              placeholderTextColor={theme.colors.placeholder}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={[styles.inputLabel, { color: theme.colors.text }]}>
              Description
            </Text>
            <TextInput
              style={[
                styles.textArea,
                {
                  backgroundColor: theme.colors.background,
                  borderColor: theme.colors.border,
                  color: theme.colors.text,
                },
              ]}
              value={formDescription}
              onChangeText={setFormDescription}
              placeholder="Describe the purpose of this form"
              placeholderTextColor={theme.colors.placeholder}
              multiline
              numberOfLines={3}
            />
          </View>

          <View style={styles.formStats}>
            <View style={styles.statItem}>
              <Text style={[styles.statLabel, { color: theme.colors.muted }]}>
                Pages:
              </Text>
              <Text style={[styles.statValue, { color: theme.colors.text }]}>
                {formSchema.pages.length}
              </Text>
            </View>
            <View style={styles.statItem}>
              <Text style={[styles.statLabel, { color: theme.colors.muted }]}>
                Sections:
              </Text>
              <Text style={[styles.statValue, { color: theme.colors.text }]}>
                {formSchema.pages.reduce((total, page) => total + page.sections.length, 0)}
              </Text>
            </View>
            <View style={styles.statItem}>
              <Text style={[styles.statLabel, { color: theme.colors.muted }]}>
                Questions:
              </Text>
              <Text style={[styles.statValue, { color: theme.colors.text }]}>
                {formSchema.pages.reduce((total, page) => 
                  total + page.sections.reduce((sectionTotal, section) => 
                    sectionTotal + section.questions.length, 0), 0)}
              </Text>
            </View>
          </View>
        </View>
      </View>
    );
  };

  const renderCollapsedSettings = () => {
    if (showSettings) return null;

    return (
      <TouchableOpacity
        style={[styles.collapsedSettings, { backgroundColor: theme.colors.card }]}
        onPress={() => setShowSettings(true)}
      >
        <Info size={16} color={theme.colors.muted} />
        <Text style={[styles.collapsedSettingsText, { color: theme.colors.muted }]}>
          {formName || 'Untitled Form'} • {formSchema.pages.length} page{formSchema.pages.length !== 1 ? 's' : ''}
        </Text>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.card, borderBottomColor: theme.colors.border }]}>
        <View style={styles.headerLeft}>
          <TouchableOpacity
            style={[styles.headerButton, { backgroundColor: theme.colors.background }]}
            onPress={() => router.back()}
          >
            <ArrowLeft size={20} color={theme.colors.text} />
          </TouchableOpacity>
          <View>
            <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
              Enhanced Form Builder
            </Text>
            <Text style={[styles.headerSubtitle, { color: theme.colors.muted }]}>
              🎯 Build advanced multi-page forms with sections
            </Text>
          </View>
        </View>
        
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={[styles.headerButton, { backgroundColor: theme.colors.background }]}
            onPress={handlePreview}
          >
            <Eye size={20} color={theme.colors.text} />
            <Text style={[styles.headerButtonText, { color: theme.colors.text }]}>
              Preview
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.saveButton, { backgroundColor: theme.colors.primary }]}
            onPress={handleSave}
          >
            <Save size={20} color="white" />
            <Text style={styles.saveButtonText}>Save Form</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Form Settings */}
      {renderFormSettings()}
      {renderCollapsedSettings()}

      {/* Enhanced Form Builder */}
      <View style={styles.builderContainer}>
        <EnhancedFormBuilder
          formSchema={formSchema}
          onUpdateSchema={updateFormSchema}
          onSave={handleSave}
          onPreview={handlePreview}
        />
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    flex: 1,
  },
  headerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    gap: 6,
  },
  headerButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
  },
  headerTitle: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    marginBottom: 2,
  },
  headerSubtitle: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8,
  },
  saveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
    gap: 6,
  },
  saveButtonText: {
    color: 'white',
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
  },
  settingsPanel: {
    borderBottomWidth: 1,
  },
  settingsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  settingsHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  settingsTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
  },
  settingsToggle: {
    padding: 4,
  },
  settingsToggleText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },
  settingsContent: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 6,
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
  },
  textArea: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    height: 80,
    textAlignVertical: 'top',
  },
  formStats: {
    flexDirection: 'row',
    gap: 24,
    marginTop: 8,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  statLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
  },
  statValue: {
    fontSize: 12,
    fontFamily: 'Inter-Bold',
  },
  collapsedSettings: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    gap: 8,
  },
  collapsedSettingsText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },
  builderContainer: {
    flex: 1,
  },
});
