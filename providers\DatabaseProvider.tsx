import { createContext, useEffect, useState, ReactNode } from 'react';
import { Platform } from 'react-native';

type DatabaseContextType = {
  db: any | null; // Changed from SQLite.SQLiteDatabase to any for web compatibility
  initialized: boolean;
  error: Error | null;
  webStorage: Storage | null;
};

export const DatabaseContext = createContext<DatabaseContextType>({
  db: null,
  initialized: false,
  error: null,
  webStorage: null
});

// Web storage fallback for web platform
const createWebStorage = () => {
  if (typeof window !== 'undefined' && window.localStorage) {
    return window.localStorage;
  }
  return null;
};

export function DatabaseProvider({ children }: { children: ReactNode }) {
  const [db, setDb] = useState<any | null>(null); // Changed type for web compatibility
  const [initialized, setInitialized] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [webStorage, setWebStorage] = useState<Storage | null>(null);

  useEffect(() => {
    // Always use localStorage on web platform
    if (Platform.OS === 'web') {
      console.log('Initializing web storage for database operations');
      const storage = createWebStorage();
      setWebStorage(storage);
      
      // Initialize default settings in localStorage if not exist
      if (storage && !storage.getItem('fieldsync_settings')) {
        const defaultSettings = {
          id: 1,
          offlineMapRadius: 10,
          syncOnCellularData: false,
          syncInterval: 30,
          language: 'en',
          theme: 'system',
          locationAccuracyThreshold: 10,
          automaticBackup: true,
          backupInterval: 7
        };
        storage.setItem('fieldsync_settings', JSON.stringify(defaultSettings));
      }
      
      setInitialized(true);
      return;
    }

    // For native platforms, try to initialize SQLite
    const initDatabase = async () => {
      try {
        // For now, skip native SQLite to get web working
        console.log('Native SQLite initialization skipped for testing');
        setError(new Error('SQLite initialization skipped - web fallback only'));
        setInitialized(true);
      } catch (err) {
        console.error('Database initialization error:', err);
        setError(err instanceof Error ? err : new Error('Unknown database error'));
        setInitialized(true);
      }
    };

    initDatabase();
  }, []);

  return (
    <DatabaseContext.Provider value={{ db, initialized, error, webStorage }}>
      {children}
    </DatabaseContext.Provider>
  );
}