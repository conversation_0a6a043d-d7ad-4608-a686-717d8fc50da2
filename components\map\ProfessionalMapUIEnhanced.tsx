import React, { useState, useRef, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Platform,
  Dimensions,
  SafeAreaView,
  ScrollView,
  Alert,
  TextInput,
  Modal,
} from 'react-native';
import { useTheme } from '@/hooks/useTheme';
import * as Location from 'expo-location';
import * as DocumentPicker from 'expo-document-picker';
import {
  MapPin,
  Layers,
  Edit3,
  Ruler,
  BarChart3,
  Filter,
  Settings,
  Target,
  Plus, 
  Minus,
  Route,
  Pentagon,
  Square,
  Circle,
  Navigation,
  Eye,
  EyeOff,
  Save,
  Trash2,
  X,
  CheckCircle,
  Info,
  Menu,
  Maximize2,
  Download,
  Upload,
  FileText,
  Zap,
  GitMerge,
  Scissors,
  MousePointer,
  Move,
  RotateCcw,
  Play,
  Pause,
  RefreshCw,
} from 'lucide-react-native';

// Import the enhanced map component
import EnhancedMapWithFixes, { type MapRef } from './EnhancedMapWithFixes';

interface Region {
  latitude: number;
  longitude: number;
  latitudeDelta: number;
  longitudeDelta: number;
}

interface DrawnFeature {
  id: string;
  type: 'point' | 'line' | 'polygon' | 'circle' | 'rectangle';
  coordinates: any;
  properties: {
    name: string;
    description?: string;
    color: string;
    created: number;
    measurements?: {
      area?: number;
      perimeter?: number;
      length?: number;
      radius?: number;
    };
  };
  selected?: boolean;
}

interface CustomLayer {
  id: string;
  name: string;
  type: 'geojson' | 'shapefile' | 'kml' | 'gpx';
  data: any;
  visible: boolean;
  style?: any;
}

interface MeasurementResult {
  id: string;
  type: 'line' | 'polygon';
  points: number;
  totalDistance: number;
  area?: number;
  formattedDistance: string;
  formattedArea?: string;
  timestamp: string;
}

interface AnalysisResult {
  id: string;
  type: string;
  result: any;
  inputFeatures: number;
  timestamp: string;
  options?: any;
}

interface ProfessionalMapUIEnhancedProps {
  initialRegion?: Region;
  geoFeatures?: any[];
  onFeatureCreated?: (feature: DrawnFeature) => void;
  onFeatureDeleted?: (featureId: string) => void;
  onLocationSelect?: (location: { latitude: number; longitude: number }) => void;
  enableDrawing?: boolean;
  enableMeasurement?: boolean;
  enableAnalysis?: boolean;
  enableLayerImport?: boolean;
  maxFeatures?: number;
}

type DrawingTool = 'none' | 'point' | 'line' | 'polygon' | 'rectangle' | 'circle';
type MapLayer = 'standard' | 'satellite' | 'terrain' | 'hybrid';
type ActiveTool = 'none' | 'layers' | 'drawing' | 'measurement' | 'analysis' | 'features' | 'import';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');
const TOOLBAR_HEIGHT = 60;
const SIDEBAR_WIDTH = 320;

export default function ProfessionalMapUIEnhanced({
  initialRegion,
  geoFeatures = [],
  onFeatureCreated,
  onFeatureDeleted,
  onLocationSelect,
  enableDrawing = true,
  enableMeasurement = true,
  enableAnalysis = true,
  enableLayerImport = true,
  maxFeatures = 100,
}: ProfessionalMapUIEnhancedProps) {
  const { theme } = useTheme();
  
  // State management
  const [region] = useState<Region>(initialRegion || {
    latitude: 37.78825,
    longitude: -122.4324,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  });
  
  const [activeTool, setActiveTool] = useState<ActiveTool>('none');
  const [drawingTool, setDrawingTool] = useState<DrawingTool>('none');
  const [mapLayer, setMapLayer] = useState<MapLayer>('standard');
  const [userLocation, setUserLocation] = useState<Location.LocationObject | null>(null);
  const [drawnFeatures, setDrawnFeatures] = useState<DrawnFeature[]>([]);
  const [customLayers, setCustomLayers] = useState<CustomLayer[]>([]);
  const [selectedFeature, setSelectedFeature] = useState<DrawnFeature | null>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [measurementMode, setMeasurementMode] = useState(false);
  const [measurementResults, setMeasurementResults] = useState<MeasurementResult[]>([]);
  const [analysisResults, setAnalysisResults] = useState<AnalysisResult[]>([]);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(true);
  const [showControls, setShowControls] = useState(true);
  const [exportInProgress, setExportInProgress] = useState(false);
  const [analysisInProgress, setAnalysisInProgress] = useState(false);
  const [importModalVisible, setImportModalVisible] = useState(false);
  const [featureEditModalVisible, setFeatureEditModalVisible] = useState(false);
  const [editingFeature, setEditingFeature] = useState<DrawnFeature | null>(null);

  // Refs
  const mapRef = useRef<MapRef>(null);

  useEffect(() => {
    requestLocationPermission();
  }, []);

  const requestLocationPermission = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status === 'granted') {
        const location = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.High,
        });
        setUserLocation(location);
      }
    } catch (error) {
      console.error('Error getting location:', error);
    }
  };

  // Feature management
  const handleFeatureCreated = useCallback((feature: DrawnFeature) => {
    setDrawnFeatures(prev => [...prev, feature]);
    if (onFeatureCreated) {
      onFeatureCreated(feature);
    }
  }, [onFeatureCreated]);

  const handleFeatureDeleted = useCallback((featureId: string) => {
    setDrawnFeatures(prev => prev.filter(f => f.id !== featureId));
    if (selectedFeature?.id === featureId) {
      setSelectedFeature(null);
    }
    if (onFeatureDeleted) {
      onFeatureDeleted(featureId);
    }
  }, [selectedFeature, onFeatureDeleted]);

  const handleFeatureSelected = useCallback((feature: DrawnFeature | null) => {
    setSelectedFeature(feature);
    if (feature) {
      // Center map on selected feature
      mapRef.current?.centerOnFeature(feature.id);
    }
  }, []);

  // Drawing tool handlers
  const selectDrawingTool = (tool: DrawingTool) => {
    setDrawingTool(tool);
    setActiveTool('drawing');
    setIsDrawing(tool !== 'none');
    
    if (tool !== 'none') {
      setSidebarCollapsed(true); // Hide sidebar when drawing
      setMeasurementMode(false); // Disable measurement when drawing
    }
  };

  const cancelDrawing = () => {
    setDrawingTool('none');
    setIsDrawing(false);
    setActiveTool('none');
  };

  // Measurement handlers
  const startMeasurement = () => {
    setMeasurementMode(true);
    setActiveTool('measurement');
    setDrawingTool('none'); // Disable drawing when measuring
    setIsDrawing(false);
    mapRef.current?.startMeasurement();
  };

  const stopMeasurement = () => {
    setMeasurementMode(false);
    mapRef.current?.stopMeasurement();
  };

  const handleMeasurementResult = useCallback((result: MeasurementResult) => {
    setMeasurementResults(prev => [...prev, result]);
  }, []);

  // Export handlers
  const handleExportGeoJSON = async () => {
    if (!mapRef.current) return;
    
    setExportInProgress(true);
    try {
      await mapRef.current.exportGeoJSON();
    } catch (error) {
      console.error('Export error:', error);
      Alert.alert('Export Error', 'Failed to export GeoJSON');
    } finally {
      setExportInProgress(false);
    }
  };

  const handleExportShapefile = async () => {
    if (!mapRef.current) return;
    
    setExportInProgress(true);
    try {
      await mapRef.current.exportShapefile();
    } catch (error) {
      console.error('Export error:', error);
      Alert.alert('Export Error', 'Failed to export Shapefile');
    } finally {
      setExportInProgress(false);
    }
  };

  // Import handlers
  const handleImportLayer = async () => {
    try {
      if (Platform.OS === 'web') {
        // Web file picker
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.geojson,.json,.kml,.gpx,.zip,.shp';
        input.onchange = async (e: any) => {
          const file = e.target.files[0];
          if (file && mapRef.current) {
            try {
              await mapRef.current.importLayer(file);
              Alert.alert('Import Success', `Layer "${file.name}" imported successfully`);
            } catch (error: any) {
              console.error('Import error:', error);
              Alert.alert('Import Error', `Failed to import layer: ${(error as Error).message}`);
            }
          }
        };
        input.click();
      } else {
        // Mobile document picker
        const result = await DocumentPicker.getDocumentAsync({
          type: ['application/json', 'application/zip'],
          copyToCacheDirectory: true,
        });
        
        if (!result.canceled && result.assets[0]) {
          // Handle mobile file import
          Alert.alert('Import', 'File import on mobile requires additional implementation');
        }
      }
    } catch (error) {
      console.error('Import error:', error);
      Alert.alert('Import Error', 'Failed to import layer');
    }
  };

  // Analysis handlers
  const runSpatialAnalysis = async (analysisType: string, options: any = {}) => {
    if (!mapRef.current) return;
    
    setAnalysisInProgress(true);
    try {
      const result = await mapRef.current.runAnalysis(analysisType, options);
      if (result) {
        const analysisResult: AnalysisResult = {
          id: `analysis_${Date.now()}`,
          type: analysisType,
          result,
          inputFeatures: drawnFeatures.length,
          timestamp: new Date().toISOString(),
          options,
        };
        
        setAnalysisResults(prev => [...prev, analysisResult]);
        
        Alert.alert(
          'Analysis Complete',
          `${analysisType} analysis completed successfully`
        );
      }
    } catch (error) {
      console.error('Analysis error:', error);
      Alert.alert('Analysis Error', `Failed to run ${analysisType} analysis`);
    } finally {
      setAnalysisInProgress(false);
    }
  };

  const clearAnalysisResults = () => {
    setAnalysisResults([]);
    Alert.alert('Cleared', 'All analysis results cleared');
  };

  // Feature editing
  const editFeature = (feature: DrawnFeature) => {
    setEditingFeature(feature);
    setFeatureEditModalVisible(true);
  };

  const saveFeatureEdit = (updatedFeature: DrawnFeature) => {
    setDrawnFeatures(prev => prev.map(f => 
      f.id === updatedFeature.id ? updatedFeature : f
    ));
    setFeatureEditModalVisible(false);
    setEditingFeature(null);
  };

  // Control handlers
  const toggleTool = (tool: ActiveTool) => {
    if (activeTool === tool) {
      setActiveTool('none');
      setSidebarCollapsed(true);
      if (tool === 'measurement') {
        stopMeasurement();
      }
    } else {
      setActiveTool(tool);
      setSidebarCollapsed(false);
      if (tool === 'measurement') {
        startMeasurement();
      }
    }
  };

  const centerOnUserLocation = () => {
    if (userLocation && mapRef.current) {
      mapRef.current.centerOnLocation(
        userLocation.coords.latitude,
        userLocation.coords.longitude
      );
    } else {
      requestLocationPermission();
    }
  };

  const clearAllFeatures = () => {
    Alert.alert(
      'Clear All Features',
      'Are you sure you want to delete all features?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear All',
          style: 'destructive',
          onPress: () => {
            mapRef.current?.clearAllFeatures();
            setDrawnFeatures([]);
            setSelectedFeature(null);
            setAnalysisResults([]);
            setMeasurementResults([]);
          },
        },
      ]
    );
  };

  // Render main toolbar
  const renderMainToolbar = () => (
    <View style={[styles.mainToolbar, { backgroundColor: theme.colors.card, borderBottomColor: theme.colors.border }]}>
      <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.toolbarContent}>
        
        {/* Layer Control */}
        <TouchableOpacity
          style={[styles.toolbarButton, activeTool === 'layers' && styles.toolbarButtonActive]}
          onPress={() => toggleTool('layers')}
        >
          <Layers size={20} color={activeTool === 'layers' ? theme.colors.primary : theme.colors.text} />
          <Text style={[styles.toolbarButtonText, { color: theme.colors.text }]}>Layers</Text>
        </TouchableOpacity>

        {/* Drawing Tools */}
        {enableDrawing && (
          <TouchableOpacity
            style={[styles.toolbarButton, activeTool === 'drawing' && styles.toolbarButtonActive]}
            onPress={() => toggleTool('drawing')}
          >
            <Edit3 size={20} color={activeTool === 'drawing' ? theme.colors.primary : theme.colors.text} />
            <Text style={[styles.toolbarButtonText, { color: theme.colors.text }]}>Draw</Text>
          </TouchableOpacity>
        )}

        {/* Measurement */}
        {enableMeasurement && (
          <TouchableOpacity
            style={[styles.toolbarButton, activeTool === 'measurement' && styles.toolbarButtonActive]}
            onPress={() => toggleTool('measurement')}
          >
            <Ruler size={20} color={activeTool === 'measurement' ? theme.colors.primary : theme.colors.text} />
            <Text style={[styles.toolbarButtonText, { color: theme.colors.text }]}>Measure</Text>
          </TouchableOpacity>
        )}

        {/* Analysis */}
        {enableAnalysis && (
          <TouchableOpacity
            style={[styles.toolbarButton, activeTool === 'analysis' && styles.toolbarButtonActive]}
            onPress={() => toggleTool('analysis')}
          >
            <BarChart3 size={20} color={activeTool === 'analysis' ? theme.colors.primary : theme.colors.text} />
            <Text style={[styles.toolbarButtonText, { color: theme.colors.text }]}>Analyze</Text>
          </TouchableOpacity>
        )}

        {/* Features */}
        <TouchableOpacity
          style={[styles.toolbarButton, activeTool === 'features' && styles.toolbarButtonActive]}
          onPress={() => toggleTool('features')}
        >
          <Filter size={20} color={activeTool === 'features' ? theme.colors.primary : theme.colors.text} />
          <Text style={[styles.toolbarButtonText, { color: theme.colors.text }]}>
            Features ({drawnFeatures.length})
          </Text>
        </TouchableOpacity>

        {/* Import/Export */}
        {enableLayerImport && (
          <TouchableOpacity
            style={[styles.toolbarButton, activeTool === 'import' && styles.toolbarButtonActive]}
            onPress={() => toggleTool('import')}
          >
            <Upload size={20} color={activeTool === 'import' ? theme.colors.primary : theme.colors.text} />
            <Text style={[styles.toolbarButtonText, { color: theme.colors.text }]}>Import</Text>
          </TouchableOpacity>
        )}

      </ScrollView>

      {/* Controls Toggle */}
      <TouchableOpacity
        style={[styles.controlsToggle, { backgroundColor: theme.colors.background }]}
        onPress={() => setShowControls(!showControls)}
      >
        {showControls ? <EyeOff size={18} color={theme.colors.text} /> : <Eye size={18} color={theme.colors.text} />}
      </TouchableOpacity>
    </View>
  );

  // Render map controls
  const renderMapControls = () => {
    if (!showControls) return null;

    return (
      <View style={[styles.mapControlsContainer, { backgroundColor: theme.colors.card }]}>
        {/* Zoom Controls */}
        <View style={styles.zoomControls}>
          <TouchableOpacity
            style={[styles.controlButton, { borderBottomWidth: 1, borderBottomColor: theme.colors.border }]}
            onPress={() => mapRef.current?.zoomIn()}
          >
            <Plus size={18} color={theme.colors.text} />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.controlButton}
            onPress={() => mapRef.current?.zoomOut()}
          >
            <Minus size={18} color={theme.colors.text} />
          </TouchableOpacity>
        </View>

        {/* Location Control */}
        <TouchableOpacity
          style={[styles.locationControl, { backgroundColor: theme.colors.primary }]}
          onPress={centerOnUserLocation}
        >
          <Target size={20} color="white" />
        </TouchableOpacity>

        {/* Feature Selection Control */}
        {selectedFeature && (
          <TouchableOpacity
            style={[styles.controlButton, { backgroundColor: theme.colors.warning }]}
            onPress={() => editFeature(selectedFeature)}
          >
            <Edit3 size={18} color="white" />
          </TouchableOpacity>
        )}

        {/* Menu Control */}
        <TouchableOpacity
          style={[styles.controlButton, { backgroundColor: theme.colors.background }]}
          onPress={() => setSidebarCollapsed(!sidebarCollapsed)}
        >
          <Menu size={18} color={theme.colors.text} />
        </TouchableOpacity>
      </View>
    );
  };

  // Render drawing status
  const renderDrawingStatus = () => {
    if (!isDrawing && !measurementMode) return null;

    const statusText = isDrawing 
      ? `Drawing: ${drawingTool} - Tap on the map to place ${drawingTool}`
      : measurementMode 
      ? 'Measurement Mode - Click points on the map to measure distances and areas'
      : '';

    const statusColor = isDrawing ? theme.colors.primary : theme.colors.warning;

    return (
      <View style={[styles.drawingStatus, { backgroundColor: statusColor + '15', borderColor: statusColor }]}>
        <View style={styles.drawingStatusContent}>
          <Text style={[styles.drawingStatusText, { color: statusColor }]}>
            {isDrawing ? `Drawing: ${drawingTool}` : 'Measuring'}
          </Text>
          <Text style={[styles.drawingStatusSubtext, { color: theme.colors.text }]}>
            {statusText}
          </Text>
        </View>
        <TouchableOpacity
          style={[styles.cancelDrawingButton, { backgroundColor: theme.colors.error }]}
          onPress={() => {
            if (isDrawing) {
              cancelDrawing();
            } else if (measurementMode) {
              stopMeasurement();
              setActiveTool('none');
            }
          }}
        >
          <X size={16} color="white" />
          <Text style={styles.cancelButtonText}>Cancel</Text>
        </TouchableOpacity>
      </View>
    );
  };

  // Render sidebar panels
  const renderSidebar = () => {
    if (sidebarCollapsed || activeTool === 'none') return null;

    return (
      <View style={[styles.sidebar, { backgroundColor: theme.colors.card, borderLeftColor: theme.colors.border }]}>
        {/* Sidebar Header */}
        <View style={[styles.sidebarHeader, { borderBottomColor: theme.colors.border }]}>
          <Text style={[styles.sidebarTitle, { color: theme.colors.text }]}>
            {activeTool === 'layers' && 'Map Layers'}
            {activeTool === 'drawing' && 'Drawing Tools'}  
            {activeTool === 'measurement' && 'Measurement Tools'}
            {activeTool === 'analysis' && 'Spatial Analysis'}
            {activeTool === 'features' && 'Feature Manager'}
            {activeTool === 'import' && 'Import/Export'}
          </Text>
          <TouchableOpacity onPress={() => setSidebarCollapsed(true)}>
            <X size={20} color={theme.colors.text} />
          </TouchableOpacity>
        </View>

        {/* Sidebar Content */}
        <ScrollView style={styles.sidebarContent} showsVerticalScrollIndicator={false}>
          {activeTool === 'layers' && renderLayersPanel()}
          {activeTool === 'drawing' && renderDrawingPanel()}
          {activeTool === 'measurement' && renderMeasurementPanel()}
          {activeTool === 'analysis' && renderAnalysisPanel()}
          {activeTool === 'features' && renderFeaturesPanel()}
          {activeTool === 'import' && renderImportExportPanel()}
        </ScrollView>
      </View>
    );
  };

  const renderLayersPanel = () => (
    <View style={styles.panelContent}>
      <Text style={[styles.panelSectionTitle, { color: theme.colors.text }]}>Base Layers</Text>
      {[
        { id: 'standard', label: 'Street Map', icon: Layers },
        { id: 'satellite', label: 'Satellite', icon: Layers },
        { id: 'terrain', label: 'Terrain', icon: Layers },
        { id: 'hybrid', label: 'Hybrid', icon: Layers },
      ].map((layer) => (
        <TouchableOpacity
          key={layer.id}
          style={[
            styles.panelOption,
            mapLayer === layer.id && styles.panelOptionActive,
            { backgroundColor: mapLayer === layer.id ? theme.colors.primary + '15' : 'transparent' }
          ]}
          onPress={() => setMapLayer(layer.id as MapLayer)}
        >
          <layer.icon size={20} color={mapLayer === layer.id ? theme.colors.primary : theme.colors.text} />
          <Text style={[styles.panelOptionText, { color: theme.colors.text }]}>{layer.label}</Text>
          {mapLayer === layer.id && <CheckCircle size={16} color={theme.colors.primary} />}
        </TouchableOpacity>
      ))}

      {/* Custom Layers Section */}
      {customLayers.length > 0 && (
        <>
          <Text style={[styles.panelSectionTitle, { color: theme.colors.text, marginTop: 20 }]}>
            Custom Layers ({customLayers.length})
          </Text>
          {customLayers.map((layer) => (
            <View key={layer.id} style={[styles.panelOption, { backgroundColor: 'transparent' }]}>
              <View style={styles.customLayerInfo}>
                <Text style={[styles.panelOptionText, { color: theme.colors.text }]}>{layer.name}</Text>
                <Text style={[styles.customLayerType, { color: theme.colors.muted }]}>
                  {layer.type.toUpperCase()}
                </Text>
              </View>
              <TouchableOpacity
                style={[styles.layerToggle, { backgroundColor: layer.visible ? theme.colors.primary : theme.colors.muted }]}
                onPress={() => {
                  const updatedLayers = customLayers.map(l => 
                    l.id === layer.id ? { ...l, visible: !l.visible } : l
                  );
                  setCustomLayers(updatedLayers);
                  mapRef.current?.toggleLayerVisibility(layer.id);
                }}
              >
                {layer.visible ? <Eye size={14} color="white" /> : <EyeOff size={14} color="white" />}
              </TouchableOpacity>
            </View>
          ))}
        </>
      )}
    </View>
  );

  const renderDrawingPanel = () => (
    <View style={styles.panelContent}>
      <Text style={[styles.panelSectionTitle, { color: theme.colors.text }]}>Drawing Tools</Text>
      <View style={styles.drawingToolsGrid}>
        {[
          { id: 'point', label: 'Point', icon: MapPin },
          { id: 'line', label: 'Line', icon: Route },
          { id: 'polygon', label: 'Polygon', icon: Pentagon },
          { id: 'rectangle', label: 'Rectangle', icon: Square },
          { id: 'circle', label: 'Circle', icon: Circle },
        ].map((tool) => (
          <TouchableOpacity
            key={tool.id}
            style={[
              styles.drawingToolButton,
              drawingTool === tool.id && styles.drawingToolButtonActive,
              { 
                backgroundColor: drawingTool === tool.id ? theme.colors.primary : theme.colors.background,
                borderColor: theme.colors.border,
              }
            ]}
            onPress={() => selectDrawingTool(tool.id as DrawingTool)}
          >
            <tool.icon size={24} color={drawingTool === tool.id ? 'white' : theme.colors.text} />
            <Text style={[
              styles.drawingToolButtonText, 
              { color: drawingTool === tool.id ? 'white' : theme.colors.text }
            ]}>
              {tool.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {drawingTool !== 'none' && (
        <View style={[styles.drawingInstructions, { backgroundColor: theme.colors.info + '15' }]}>
          <Info size={16} color={theme.colors.info} />
          <Text style={[styles.instructionsText, { color: theme.colors.info }]}>
            Tap on the map to place your {drawingTool}. Use the cancel button to stop drawing.
          </Text>
        </View>
      )}

      {/* Drawing Controls */}
      <View style={styles.drawingControls}>
        <TouchableOpacity
          style={[styles.drawingControlButton, { backgroundColor: theme.colors.warning }]}
          onPress={clearAllFeatures}
        >
          <Trash2 size={16} color="white" />
          <Text style={styles.drawingControlButtonText}>Clear All</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderMeasurementPanel = () => (
    <View style={styles.panelContent}>
      <Text style={[styles.panelSectionTitle, { color: theme.colors.text }]}>Measurement Tools</Text>
      <Text style={[styles.panelDescription, { color: theme.colors.muted }]}>
        Click on the map to start measuring distances and areas between points.
      </Text>
      
      {/* Measurement Controls */}
      <View style={styles.measurementControls}>
        <TouchableOpacity
          style={[
            styles.measurementButton,
            { backgroundColor: measurementMode ? theme.colors.error : theme.colors.primary }
          ]}
          onPress={() => {
            if (measurementMode) {
              stopMeasurement();
            } else {
              startMeasurement();
            }
          }}
        >
          {measurementMode ? <Pause size={16} color="white" /> : <Play size={16} color="white" />}
          <Text style={styles.measurementButtonText}>
            {measurementMode ? 'Stop Measuring' : 'Start Measuring'}
          </Text>
        </TouchableOpacity>

        {measurementResults.length > 0 && (
          <TouchableOpacity
            style={[styles.measurementButton, { backgroundColor: theme.colors.warning }]}
            onPress={() => setMeasurementResults([])}
          >
            <RotateCcw size={16} color="white" />
            <Text style={styles.measurementButtonText}>Clear Results</Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Measurement Results */}
      {measurementResults.length > 0 && (
        <View style={[styles.measurementResults, { backgroundColor: theme.colors.background }]}>
          <Text style={[styles.measurementResultsTitle, { color: theme.colors.text }]}>
            Measurements ({measurementResults.length})
          </Text>
          {measurementResults.slice(-5).map((result) => (
            <View key={result.id} style={[styles.measurementResultItem, { borderColor: theme.colors.border }]}>
              <Text style={[styles.measurementResultType, { color: theme.colors.primary }]}>
                {result.type.toUpperCase()} - {result.points} points
              </Text>
              <Text style={[styles.measurementResultValue, { color: theme.colors.text }]}>
                Distance: {result.formattedDistance}
              </Text>
              {result.formattedArea && (
                <Text style={[styles.measurementResultValue, { color: theme.colors.text }]}>
                  Area: {result.formattedArea}
                </Text>
              )}
            </View>
          ))}
        </View>
      )}
    </View>
  );

  const renderAnalysisPanel = () => (
    <View style={styles.panelContent}>
      <Text style={[styles.panelSectionTitle, { color: theme.colors.text }]}>Spatial Analysis</Text>
      <Text style={[styles.panelDescription, { color: theme.colors.muted }]}>
        Advanced spatial analysis tools powered by Turf.js
      </Text>
      
      {/* Analysis Results */}
      {analysisResults.length > 0 && (
        <View style={[styles.analysisResults, { backgroundColor: theme.colors.background }]}>
          <View style={styles.analysisResultsHeader}>
            <Text style={[styles.analysisResultsTitle, { color: theme.colors.text }]}>
              Results ({analysisResults.length})
            </Text>
            <TouchableOpacity onPress={clearAnalysisResults}>
              <X size={16} color={theme.colors.error} />
            </TouchableOpacity>
          </View>
          {analysisResults.slice(-3).map((result) => (
            <View key={result.id} style={[styles.analysisResultItem, { borderColor: theme.colors.border }]}>
              <Text style={[styles.analysisResultType, { color: theme.colors.primary }]}>
                {result.type.charAt(0).toUpperCase() + result.type.slice(1)}
              </Text>
              <Text style={[styles.analysisResultTime, { color: theme.colors.muted }]}>
                {new Date(result.timestamp).toLocaleTimeString()}
              </Text>
            </View>
          ))}
        </View>
      )}
      
      {/* Analysis Tools Grid */}
      <View style={styles.analysisToolsGrid}>
        <TouchableOpacity
          style={[styles.analysisToolButton, { backgroundColor: theme.colors.primary }]}
          onPress={() => runSpatialAnalysis('buffer', { distance: 100, units: 'meters' })}
          disabled={analysisInProgress}
        >
          <Zap size={20} color="white" />
          <Text style={styles.analysisToolButtonText}>Buffer</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.analysisToolButton, { backgroundColor: theme.colors.secondary }]}
          onPress={() => runSpatialAnalysis('intersect')}
          disabled={analysisInProgress}
        >
          <GitMerge size={20} color="white" />
          <Text style={styles.analysisToolButtonText}>Intersect</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.analysisToolButton, { backgroundColor: theme.colors.info }]}
          onPress={() => runSpatialAnalysis('union')}
          disabled={analysisInProgress}
        >
          <Plus size={20} color="white" />
          <Text style={styles.analysisToolButtonText}>Union</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.analysisToolButton, { backgroundColor: theme.colors.warning }]}
          onPress={() => runSpatialAnalysis('difference')}
          disabled={analysisInProgress}
        >
          <Scissors size={20} color="white" />
          <Text style={styles.analysisToolButtonText}>Difference</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.analysisToolButton, { backgroundColor: theme.colors.success }]}
          onPress={() => runSpatialAnalysis('centroid')}
          disabled={analysisInProgress}
        >
          <Target size={20} color="white" />
          <Text style={styles.analysisToolButtonText}>Centroid</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.analysisToolButton, { backgroundColor: theme.colors.error }]}
          onPress={() => runSpatialAnalysis('convexHull')}
          disabled={analysisInProgress}
        >
          <Pentagon size={20} color="white" />
          <Text style={styles.analysisToolButtonText}>Convex Hull</Text>
        </TouchableOpacity>
      </View>
      
      {analysisInProgress && (
        <Text style={[styles.analysisStatus, { color: theme.colors.primary }]}>
          Running analysis...
        </Text>
      )}
    </View>
  );

  const renderFeaturesPanel = () => (
    <View style={styles.panelContent}>
      <Text style={[styles.panelSectionTitle, { color: theme.colors.text }]}>
        Features ({drawnFeatures.length}/{maxFeatures})
      </Text>
      
      {drawnFeatures.length === 0 ? (
        <View style={styles.emptyState}>
          <MapPin size={32} color={theme.colors.muted} />
          <Text style={[styles.emptyStateText, { color: theme.colors.muted }]}>
            No features yet. Start by drawing on the map!
          </Text>
        </View>
      ) : (
        drawnFeatures.map((feature) => (
          <View
            key={feature.id}
            style={[
              styles.featureItem,
              selectedFeature?.id === feature.id && styles.featureItemSelected,
              { 
                backgroundColor: selectedFeature?.id === feature.id ? theme.colors.primary + '15' : theme.colors.background,
                borderColor: theme.colors.border,
              }
            ]}
          >
            <View style={[styles.featureIcon, { backgroundColor: feature.properties.color + '25' }]}>
              {feature.type === 'point' && <MapPin size={16} color={feature.properties.color} />}
              {feature.type === 'line' && <Route size={16} color={feature.properties.color} />}
              {feature.type === 'polygon' && <Pentagon size={16} color={feature.properties.color} />}
              {feature.type === 'rectangle' && <Square size={16} color={feature.properties.color} />}
              {feature.type === 'circle' && <Circle size={16} color={feature.properties.color} />}
            </View>
            
            <View style={styles.featureInfo}>
              <Text style={[styles.featureName, { color: theme.colors.text }]}>
                {feature.properties.name}
              </Text>
              <Text style={[styles.featureType, { color: theme.colors.muted }]}>
                {feature.type.toUpperCase()}
              </Text>
              {feature.properties.measurements && (
                <Text style={[styles.featureMeasurements, { color: theme.colors.info }]}>
                  {feature.properties.measurements.area && `Area: ${feature.properties.measurements.area.toFixed(2)} m²`}
                  {feature.properties.measurements.length && `Length: ${feature.properties.measurements.length.toFixed(2)} m`}
                  {feature.properties.measurements.radius && `Radius: ${feature.properties.measurements.radius.toFixed(2)} m`}
                </Text>
              )}
            </View>

            <View style={styles.featureActions}>
              <TouchableOpacity
                style={[styles.featureActionButton, { backgroundColor: theme.colors.info }]}
                onPress={() => mapRef.current?.centerOnFeature(feature.id)}
              >
                <Navigation size={14} color="white" />
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.featureActionButton, { backgroundColor: theme.colors.warning }]}
                onPress={() => editFeature(feature)}
              >
                <Edit3 size={14} color="white" />
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.featureActionButton, { backgroundColor: theme.colors.error }]}
                onPress={() => handleFeatureDeleted(feature.id)}
              >
                <Trash2 size={14} color="white" />
              </TouchableOpacity>
            </View>
          </View>
        ))
      )}
    </View>
  );

  const renderImportExportPanel = () => (
    <View style={styles.panelContent}>
      <Text style={[styles.panelSectionTitle, { color: theme.colors.text }]}>Import Layers</Text>
      <Text style={[styles.panelDescription, { color: theme.colors.muted }]}>
        Import GeoJSON, Shapefile, KML, or GPX files to add custom layers to your map.
      </Text>
      
      <TouchableOpacity
        style={[styles.importButton, { backgroundColor: theme.colors.primary }]}
        onPress={handleImportLayer}
      >
        <Upload size={16} color="white" />
        <Text style={styles.importButtonText}>Select File to Import</Text>
      </TouchableOpacity>

      <Text style={[styles.panelSectionTitle, { color: theme.colors.text, marginTop: 20 }]}>Export Data</Text>
      <Text style={[styles.panelDescription, { color: theme.colors.muted }]}>
        Export your map features and data in various formats.
      </Text>
      
      <View style={styles.exportButtonsRow}>
        <TouchableOpacity
          style={[styles.exportButton, { backgroundColor: theme.colors.secondary }]}
          onPress={handleExportGeoJSON}
          disabled={exportInProgress}
        >
          <FileText size={16} color="white" />
          <Text style={styles.exportButtonText}>GeoJSON</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.exportButton, { backgroundColor: theme.colors.info }]}
          onPress={handleExportShapefile}
          disabled={exportInProgress}
        >
          <Download size={16} color="white" />
          <Text style={styles.exportButtonText}>Shapefile</Text>
        </TouchableOpacity>
      </View>
      
      {exportInProgress && (
        <Text style={[styles.exportStatus, { color: theme.colors.primary }]}>
          Exporting...
        </Text>
      )}
    </View>
  );

  // Feature Edit Modal
  const renderFeatureEditModal = () => (
    <Modal
      visible={featureEditModalVisible}
      transparent={true}
      animationType="slide"
      onRequestClose={() => setFeatureEditModalVisible(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.modalContent, { backgroundColor: theme.colors.card }]}>
          <Text style={[styles.modalTitle, { color: theme.colors.text }]}>Edit Feature</Text>
          
          {editingFeature && (
            <>
              <TextInput
                style={[styles.textInput, { borderColor: theme.colors.border, color: theme.colors.text }]}
                value={editingFeature.properties.name}
                onChangeText={(text) => setEditingFeature({
                  ...editingFeature,
                  properties: { ...editingFeature.properties, name: text }
                })}
                placeholder="Feature Name"
                placeholderTextColor={theme.colors.muted}
              />
              
              <TextInput
                style={[styles.textInputMultiline, { borderColor: theme.colors.border, color: theme.colors.text }]}
                value={editingFeature.properties.description || ''}
                onChangeText={(text) => setEditingFeature({
                  ...editingFeature,
                  properties: { ...editingFeature.properties, description: text }
                })}
                placeholder="Description (optional)"
                placeholderTextColor={theme.colors.muted}
                multiline
                numberOfLines={3}
              />
              
              <View style={styles.modalButtons}>
                <TouchableOpacity
                  style={[styles.modalButton, { backgroundColor: theme.colors.muted }]}
                  onPress={() => setFeatureEditModalVisible(false)}
                >
                  <Text style={styles.modalButtonText}>Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.modalButton, { backgroundColor: theme.colors.primary }]}
                  onPress={() => saveFeatureEdit(editingFeature)}
                >
                  <Text style={styles.modalButtonText}>Save</Text>
                </TouchableOpacity>
              </View>
            </>
          )}
        </View>
      </View>
    </Modal>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Main Toolbar - Fixed at top, outside map */}
      {renderMainToolbar()}

      {/* Drawing/Measurement Status Bar */}
      {renderDrawingStatus()}

      {/* Main Content Area */}
      <View style={styles.contentArea}>
        {/* Map Container - Clean area without overlapping elements */}
        <View style={styles.mapArea}>
          <EnhancedMapWithFixes
            ref={mapRef}
            initialRegion={region}
            geoFeatures={[...geoFeatures, ...drawnFeatures]}
            onFeatureCreated={handleFeatureCreated}
            onFeatureDeleted={handleFeatureDeleted}
            onFeatureSelected={handleFeatureSelected}
            onLocationSelect={onLocationSelect}
            enableDrawing={enableDrawing}
            enableMeasurement={enableMeasurement}
            enableAnalysis={enableAnalysis}
            enableLayerImport={enableLayerImport}
            maxFeatures={maxFeatures}
          />
        </View>

        {/* Map Controls - Positioned outside map area */}
        {renderMapControls()}

        {/* Sidebar - Slides from right, outside map */}
        {renderSidebar()}
      </View>

      {/* Feature Edit Modal */}
      {renderFeatureEditModal()}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  mainToolbar: {
    height: TOOLBAR_HEIGHT,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  toolbarContent: {
    flexGrow: 1,
    flexDirection: 'row',
    alignItems: 'center',
    paddingRight: 16,
  },
  toolbarButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginRight: 8,
    borderRadius: 8,
  },
  toolbarButtonActive: {
    backgroundColor: 'rgba(0, 122, 255, 0.1)',
  },
  toolbarButtonText: {
    marginLeft: 6,
    fontSize: 14,
    fontFamily: 'Inter-Medium',
  },
  controlsToggle: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  
  // Drawing Status
  drawingStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  drawingStatusContent: {
    flex: 1,
  },
  drawingStatusText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
  },
  drawingStatusSubtext: {
    fontSize: 12,
    marginTop: 2,
    fontFamily: 'Inter-Regular',
  },
  cancelDrawingButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  cancelButtonText: {
    color: 'white',
    marginLeft: 4,
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },

  // Content Area
  contentArea: {
    flex: 1,
    flexDirection: 'row',
  },
  mapArea: {
    flex: 1,
    position: 'relative',
  },

  // Map Controls
  mapControlsContainer: {
    position: 'absolute',
    right: 16,
    top: 16,
    borderRadius: 8,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  zoomControls: {
    marginBottom: 8,
  },
  controlButton: {
    width: 44,
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
  },
  locationControl: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },

  // Sidebar
  sidebar: {
    width: SIDEBAR_WIDTH,
    borderLeftWidth: 1,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: -2, height: 0 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  sidebarHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
  },
  sidebarTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
  },
  sidebarContent: {
    flex: 1,
  },

  // Panel Content
  panelContent: {
    padding: 16,
  },
  panelSectionTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 12,
  },
  panelDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    marginBottom: 16,
    lineHeight: 20,
  },
  panelOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    marginBottom: 8,
    borderRadius: 8,
  },
  panelOptionActive: {
    borderWidth: 1,
  },
  panelOptionText: {
    flex: 1,
    marginLeft: 12,
    fontSize: 14,
    fontFamily: 'Inter-Regular',
  },

  // Custom Layer Styles
  customLayerInfo: {
    flex: 1,
    marginLeft: 12,
  },
  customLayerType: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    marginTop: 2,
  },
  layerToggle: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Drawing Tools
  drawingToolsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -4,
  },
  drawingToolButton: {
    width: (SIDEBAR_WIDTH - 48) / 2,
    height: 80,
    margin: 4,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
    borderWidth: 1,
  },
  drawingToolButtonActive: {
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  drawingToolButtonText: {
    marginTop: 8,
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },
  drawingInstructions: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: 12,
    borderRadius: 8,
    marginTop: 16,
  },
  instructionsText: {
    flex: 1,
    marginLeft: 8,
    fontSize: 12,
    lineHeight: 16,
    fontFamily: 'Inter-Regular',
  },
  drawingControls: {
    marginTop: 16,
  },
  drawingControlButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  drawingControlButtonText: {
    color: 'white',
    marginLeft: 8,
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
  },

  // Measurement Tools
  measurementControls: {
    gap: 12,
    marginBottom: 16,
  },
  measurementButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  measurementButtonText: {
    color: 'white',
    marginLeft: 8,
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
  },
  measurementResults: {
    padding: 12,
    borderRadius: 8,
    marginTop: 16,
  },
  measurementResultsTitle: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 8,
  },
  measurementResultItem: {
    padding: 8,
    marginBottom: 8,
    borderRadius: 4,
    borderWidth: 1,
  },
  measurementResultType: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 4,
  },
  measurementResultValue: {
    fontSize: 11,
    fontFamily: 'Inter-Regular',
  },

  // Analysis Panel
  analysisResults: {
    marginBottom: 16,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
  },
  analysisResultsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  analysisResultsTitle: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
  },
  analysisResultItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 8,
    marginBottom: 4,
    borderRadius: 4,
    borderWidth: 1,
  },
  analysisResultType: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },
  analysisResultTime: {
    fontSize: 10,
    fontFamily: 'Inter-Regular',
  },
  analysisToolsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -4,
    marginBottom: 12,
  },
  analysisToolButton: {
    width: (SIDEBAR_WIDTH - 48) / 3,
    height: 60,
    margin: 4,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
  },
  analysisToolButtonText: {
    color: 'white',
    fontSize: 10,
    fontFamily: 'Inter-Medium',
    marginTop: 4,
  },
  analysisStatus: {
    textAlign: 'center',
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    fontStyle: 'italic',
  },

  // Features Panel
  emptyState: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  emptyStateText: {
    marginTop: 12,
    fontSize: 14,
    textAlign: 'center',
    fontFamily: 'Inter-Regular',
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    marginBottom: 8,
    borderRadius: 8,
    borderWidth: 1,
  },
  featureItemSelected: {
    borderWidth: 2,
  },
  featureIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  featureInfo: {
    flex: 1,
    marginLeft: 12,
  },
  featureName: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
  },
  featureType: {
    fontSize: 11,
    marginTop: 2,
    fontFamily: 'Inter-Regular',
  },
  featureMeasurements: {
    fontSize: 10,
    fontFamily: 'Inter-Regular',
    marginTop: 2,
  },
  featureActions: {
    flexDirection: 'row',
    gap: 6,
  },
  featureActionButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Import/Export Panel
  importButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  importButtonText: {
    color: 'white',
    marginLeft: 8,
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
  },
  exportButtonsRow: {
    flexDirection: 'row',
    gap: 8,
    marginBottom: 16,
  },
  exportButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    paddingHorizontal: 12,
    borderRadius: 6,
  },
  exportButtonText: {
    color: 'white',
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    marginLeft: 6,
  },
  exportStatus: {
    textAlign: 'center',
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    fontStyle: 'italic',
  },

  // Modal Styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: SCREEN_WIDTH * 0.9,
    maxWidth: 400,
    padding: 20,
    borderRadius: 12,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.2,
        shadowRadius: 8,
      },
      android: {
        elevation: 8,
      },
    }),
  },
  modalTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 16,
    textAlign: 'center',
  },
  textInput: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    marginBottom: 12,
  },
  textInputMultiline: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    marginBottom: 16,
    minHeight: 80,
    textAlignVertical: 'top',
  },
  modalButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  modalButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  modalButtonText: {
    color: 'white',
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
  },
});
