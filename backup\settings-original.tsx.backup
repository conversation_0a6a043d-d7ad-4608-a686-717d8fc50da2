import { useState } from 'react';
import { View, Text, StyleSheet, Switch, TouchableOpacity, ScrollView, Alert, Platform } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '@/hooks/useTheme';
import { useAuth } from '@/hooks/useAuth';
import { useSettings } from '@/hooks/useSettings';
import { router } from 'expo-router';
import {
  Settings as SettingsIcon,
  Moon,
  Sun,
  Globe,
  Database,
  Shield,
  LogOut,
  ChevronRight,
  Save,
  MapPin,
  Wifi,
  WifiOff
} from 'lucide-react-native';
import * as Haptics from 'expo-haptics';
import SettingsSection from '@/components/settings/SettingsSection';
import SettingsItem from '@/components/settings/SettingsItem';

export default function SettingsScreen() {
  const { theme, toggleTheme, isDark } = useTheme();
  const { user, logout } = useAuth();
  const { 
    settings, 
    updateSettings, 
    syncData, 
    clearLocalData, 
    exportData,
    loading 
  } = useSettings();
  
  const handleLogout = async () => {
    if (Platform.OS !== 'web') {
      try {
        await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      } catch (e) {
        // Haptics might not be available
      }
    }
    
    // Confirm logout
    if (Platform.OS === 'web') {
      if (confirm('Are you sure you want to log out?')) {
        await logout();
        console.log('User logged out');
      }
    } else {
      Alert.alert(
        'Confirm Logout',
        'Are you sure you want to log out?',
        [
          { text: 'Cancel', style: 'cancel' },
          { 
            text: 'Log Out', 
            style: 'destructive', 
            onPress: async () => {
              await logout();
              console.log('User logged out');
            } 
          },
        ]
      );
    }
  };
  
  const handleClearData = () => {
    if (Platform.OS !== 'web') {
      try {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
      } catch (e) {
        // Haptics might not be available
      }
    }
    
    if (Platform.OS === 'web') {
      if (confirm('This will delete all locally stored data. Are you sure?')) {
        clearLocalData();
      }
    } else {
      Alert.alert(
        'Clear Local Data',
        'This will delete all locally stored data. Synced data will remain on the server. Are you sure?',
        [
          { text: 'Cancel', style: 'cancel' },
          { 
            text: 'Clear Data', 
            style: 'destructive', 
            onPress: () => clearLocalData() 
          },
        ]
      );
    }
  };
  
  const handleExportData = () => {
    if (Platform.OS !== 'web') {
      try {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      } catch (e) {
        // Haptics might not be available
      }
    }
    exportData();
  };
  
  const handleManualSync = () => {
    if (Platform.OS !== 'web') {
      try {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      } catch (e) {
        // Haptics might not be available
      }
    }
    syncData();
  };
  
  const toggleSyncOnCellular = () => {
    updateSettings({
      ...settings,
      syncOnCellularData: !settings.syncOnCellularData
    });
  };
  
  const toggleAutomaticBackup = () => {
    updateSettings({
      ...settings,
      automaticBackup: !settings.automaticBackup
    });
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]} edges={['bottom']}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: theme.colors.text }]}>Settings</Text>
        <Text style={[styles.subtitle, { color: theme.colors.muted }]}>
          ✨ Complete Settings Restored - Phase 8
        </Text>
      </View>

      <ScrollView contentContainerStyle={styles.scrollContent} showsVerticalScrollIndicator={false}>
        {/* User Profile Section */}
        {user && (
          <View style={[styles.profileSection, { backgroundColor: theme.colors.card }]}>
            <View style={styles.profileInfo}>
              <View style={[styles.profileAvatar, { backgroundColor: theme.colors.primary }]}>
                <Text style={styles.profileInitial}>
                  {user.name ? user.name.charAt(0).toUpperCase() : 'U'}
                </Text>
              </View>
              <View style={styles.profileDetails}>
                <Text style={[styles.profileName, { color: theme.colors.text }]}>{user.name || 'User'}</Text>
                <Text style={[styles.profileEmail, { color: theme.colors.muted }]}>{user.email || '<EMAIL>'}</Text>
                <View style={[styles.roleBadge, { backgroundColor: theme.colors.primaryLight }]}>
                  <Text style={[styles.roleText, { color: theme.colors.primary }]}>
                    {user.role ? user.role.charAt(0).toUpperCase() + user.role.slice(1) : 'User'}
                  </Text>
                </View>
              </View>
            </View>
            
            <TouchableOpacity style={styles.editProfileButton} onPress={() => console.log('Edit profile')}>
              <Text style={[styles.editProfileText, { color: theme.colors.primary }]}>Edit Profile</Text>
            </TouchableOpacity>
          </View>
        )}
        
        {/* App Settings */}
        <SettingsSection title="App Settings" icon={<SettingsIcon size={16} color={theme.colors.primary} />}>
          <SettingsItem
            title="Theme"
            description="Switch between light and dark mode"
            icon={isDark ? <Moon size={20} color={theme.colors.primary} /> : <Sun size={20} color={theme.colors.warning} />}
            action={
              <Switch
                value={isDark}
                onValueChange={toggleTheme}
                trackColor={{ false: '#767577', true: theme.colors.primaryLight }}
                thumbColor={isDark ? theme.colors.primary : '#f4f3f4'}
              />
            }
          />
          
          <SettingsItem
            title="Language"
            description="Change application language"
            icon={<Globe size={20} color={theme.colors.text} />}
            action={
              <View style={styles.settingValue}>
                <Text style={[styles.settingValueText, { color: theme.colors.text }]}>English</Text>
                <ChevronRight size={16} color={theme.colors.muted} />
              </View>
            }
            onPress={() => console.log('Language settings')}
          />
        </SettingsSection>
        
        {/* Data Management */}
        <SettingsSection title="Data Management" icon={<Database size={16} color={theme.colors.primary} />}>
          <SettingsItem
            title="Sync Data"
            description="Manually sync data with server"
            icon={<Save size={20} color={theme.colors.success} />}
            onPress={handleManualSync}
            loading={loading}
          />
          
          <SettingsItem
            title="Sync on Cellular Data"
            description="Allow syncing when on cellular network"
            icon={settings.syncOnCellularData ? <Wifi size={20} color={theme.colors.success} /> : <WifiOff size={20} color={theme.colors.muted} />}
            action={
              <Switch
                value={settings.syncOnCellularData}
                onValueChange={toggleSyncOnCellular}
                trackColor={{ false: '#767577', true: theme.colors.primaryLight }}
                thumbColor={settings.syncOnCellularData ? theme.colors.primary : '#f4f3f4'}
              />
            }
          />
          
          <SettingsItem
            title="Automatic Backup"
            description="Regularly backup data to prevent loss"
            icon={<Database size={20} color={theme.colors.warning} />}
            action={
              <Switch
                value={settings.automaticBackup}
                onValueChange={toggleAutomaticBackup}
                trackColor={{ false: '#767577', true: theme.colors.primaryLight }}
                thumbColor={settings.automaticBackup ? theme.colors.primary : '#f4f3f4'}
              />
            }
          />
          
          <SettingsItem
            title="Location Accuracy"
            description="Set minimum required GPS accuracy"
            icon={<MapPin size={20} color={theme.colors.secondary} />}
            action={
              <View style={styles.settingValue}>
                <Text style={[styles.settingValueText, { color: theme.colors.text }]}>
                  {settings.locationAccuracyThreshold}m
                </Text>
                <ChevronRight size={16} color={theme.colors.muted} />
              </View>
            }
            onPress={() => console.log('Location accuracy settings')}
          />
          
          <SettingsItem
            title="Export Data"
            description="Export all collected data"
            icon={<Save size={20} color={theme.colors.text} />}
            onPress={handleExportData}
          />
          
          <SettingsItem
            title="Clear Local Data"
            description="Remove all data from this device"
            icon={<Database size={20} color={theme.colors.error} />}
            onPress={handleClearData}
            textStyle={{ color: theme.colors.error }}
          />
        </SettingsSection>
        
        {/* Security */}
        <SettingsSection title="Security" icon={<Shield size={16} color={theme.colors.primary} />}>
          <SettingsItem
            title="Change Password"
            description="Update your account password"
            icon={<Shield size={20} color={theme.colors.text} />}
            onPress={() => console.log('Change password')}
          />
        </SettingsSection>
        
        {/* Logout Button */}
        {user && (
          <TouchableOpacity
            style={[styles.logoutButton, { backgroundColor: theme.colors.errorBackground }]}
            onPress={handleLogout}
          >
            <LogOut size={20} color={theme.colors.error} />
            <Text style={[styles.logoutText, { color: theme.colors.error }]}>Log Out</Text>
          </TouchableOpacity>
        )}
        
        {/* App Version */}
        <Text style={[styles.versionText, { color: theme.colors.muted }]}>
          FieldSync Pro v1.0.0
        </Text>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 16,
    paddingTop: 8,
    paddingBottom: 12,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  subtitle: {
    fontSize: 14,
    marginTop: 4,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 32,
  },
  profileSection: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
  },
  profileInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  profileAvatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  profileInitial: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
  },
  profileDetails: {
    flex: 1,
  },
  profileName: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
  },
  profileEmail: {
    fontSize: 14,
    marginBottom: 8,
  },
  roleBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  roleText: {
    fontSize: 12,
    fontWeight: '500',
  },
  editProfileButton: {
    alignSelf: 'flex-end',
  },
  editProfileText: {
    fontSize: 14,
    fontWeight: '600',
  },
  settingValue: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingValueText: {
    fontSize: 14,
    fontWeight: '500',
    marginRight: 4,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 8,
    marginTop: 24,
    marginBottom: 16,
  },
  logoutText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  versionText: {
    textAlign: 'center',
    fontSize: 12,
  },
});
