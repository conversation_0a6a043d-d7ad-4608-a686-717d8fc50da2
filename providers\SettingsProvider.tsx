import React, { createContext, useContext, useState, useEffect, useCallback, ReactNode } from 'react';
import { Platform, useColorScheme } from 'react-native';
import { StorageUtils, STORAGE_KEYS } from '@/lib/storage';

// Comprehensive settings interface
export interface AppSettings {
  // Map & Display
  coordinateSystem: string;
  baseMap: string;
  units: 'metric' | 'imperial';
  showScale: boolean;
  showCoordinates: boolean;
  
  // Data & Sync
  autoSync: boolean;
  syncInterval: number;
  offlineStorage: boolean;
  cloudBackup: boolean;
  dataRetention: number;
  
  // Analysis & Tools
  defaultBuffer: number;
  analysisUnits: 'meters' | 'feet';
  showAnalysisHistory: boolean;
  autoSaveResults: boolean;
  
  // UI & Accessibility
  theme: 'light' | 'dark' | 'auto';
  language: string;
  fontSize: 'small' | 'medium' | 'large';
  highContrast: boolean;
  reducedMotion: boolean;
  
  // Notifications
  pushNotifications: boolean;
  emailNotifications: boolean;
  soundEnabled: boolean;
  vibrationEnabled: boolean;
  
  // Security
  biometricAuth: boolean;
  sessionTimeout: number;
  dataEncryption: boolean;
  auditLogging: boolean;
}

// Unit conversion utilities
export const UnitUtils = {
  // Distance conversions
  convertDistance: (value: number, from: 'metric' | 'imperial', to: 'metric' | 'imperial'): number => {
    if (from === to) return value;
    
    if (from === 'metric' && to === 'imperial') {
      return value * 3.28084; // meters to feet
    } else {
      return value / 3.28084; // feet to meters
    }
  },
  
  // Area conversions
  convertArea: (value: number, from: 'metric' | 'imperial', to: 'metric' | 'imperial'): number => {
    if (from === to) return value;
    
    if (from === 'metric' && to === 'imperial') {
      return value * 10.7639; // square meters to square feet
    } else {
      return value / 10.7639; // square feet to square meters
    }
  },
  
  // Get unit labels
  getDistanceUnit: (units: 'metric' | 'imperial', short: boolean = false): string => {
    if (units === 'metric') {
      return short ? 'm' : 'meters';
    } else {
      return short ? 'ft' : 'feet';
    }
  },
  
  getAreaUnit: (units: 'metric' | 'imperial', short: boolean = false): string => {
    if (units === 'metric') {
      return short ? 'm²' : 'square meters';
    } else {
      return short ? 'ft²' : 'square feet';
    }
  },
  
  getLargeDistanceUnit: (units: 'metric' | 'imperial', short: boolean = false): string => {
    if (units === 'metric') {
      return short ? 'km' : 'kilometers';
    } else {
      return short ? 'mi' : 'miles';
    }
  },
  
  getLargeAreaUnit: (units: 'metric' | 'imperial', short: boolean = false): string => {
    if (units === 'metric') {
      return short ? 'ha' : 'hectares';
    } else {
      return short ? 'ac' : 'acres';
    }
  }
};

// Default settings
const DEFAULT_SETTINGS: AppSettings = {
  // Map & Display
  coordinateSystem: 'EPSG:4326',
  baseMap: 'satellite',
  units: 'metric',
  showScale: true,
  showCoordinates: true,
  
  // Data & Sync
  autoSync: true,
  syncInterval: 30,
  offlineStorage: true,
  cloudBackup: true,
  dataRetention: 90,
  
  // Analysis & Tools
  defaultBuffer: 100,
  analysisUnits: 'meters',
  showAnalysisHistory: true,
  autoSaveResults: true,
  
  // UI & Accessibility
  theme: 'auto',
  language: 'en',
  fontSize: 'medium',
  highContrast: false,
  reducedMotion: false,
  
  // Notifications
  pushNotifications: true,
  emailNotifications: false,
  soundEnabled: true,
  vibrationEnabled: true,
  
  // Security
  biometricAuth: false,
  sessionTimeout: 30,
  dataEncryption: true,
  auditLogging: true,
};

// Settings context type
interface SettingsContextType {
  settings: AppSettings;
  updateSetting: <K extends keyof AppSettings>(key: K, value: AppSettings[K]) => Promise<void>;
  updateSettings: (newSettings: Partial<AppSettings>) => Promise<void>;
  resetSettings: () => Promise<void>;
  resetSection: (section: string) => Promise<void>;
  isLoading: boolean;
  error: string | null;
  // Theme-specific helpers
  isDark: boolean;
  effectiveTheme: 'light' | 'dark';
  // Unit helpers
  unitUtils: typeof UnitUtils;
}

// Create context
const SettingsContext = createContext<SettingsContextType | null>(null);

// Settings provider component
export function SettingsProvider({ children }: { children: ReactNode }) {
  const [settings, setSettings] = useState<AppSettings>(DEFAULT_SETTINGS);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const systemColorScheme = useColorScheme();

  // Calculate effective theme
  const effectiveTheme = settings.theme === 'auto' 
    ? (systemColorScheme || 'light') 
    : settings.theme;
  const isDark = effectiveTheme === 'dark';

  // Load settings from storage
  const loadSettings = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const savedSettings = await StorageUtils.getJSON<AppSettings>(STORAGE_KEYS.USER_PREFERENCES);
      
      if (savedSettings) {
        // Merge with defaults to ensure all properties exist
        const mergedSettings = { ...DEFAULT_SETTINGS, ...savedSettings };
        setSettings(mergedSettings);
      }
    } catch (err) {
      console.error('Error loading settings:', err);
      setError('Failed to load settings');
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Save settings to storage
  const saveSettings = useCallback(async (newSettings: AppSettings) => {
    try {
      await StorageUtils.setJSON(STORAGE_KEYS.USER_PREFERENCES, newSettings);
    } catch (err) {
      console.error('Error saving settings:', err);
      setError('Failed to save settings');
      throw err;
    }
  }, []);

  // Update a single setting
  const updateSetting = useCallback(async <K extends keyof AppSettings>(
    key: K, 
    value: AppSettings[K]
  ) => {
    try {
      setError(null);
      const newSettings = { ...settings, [key]: value };
      
      // Update analysis units when measurement units change
      if (key === 'units') {
        newSettings.analysisUnits = value === 'metric' ? 'meters' : 'feet';
      }
      
      setSettings(newSettings);
      await saveSettings(newSettings);
    } catch (err) {
      console.error('Error updating setting:', err);
      setError(`Failed to update ${String(key)}`);
      throw err;
    }
  }, [settings, saveSettings]);

  // Update multiple settings
  const updateSettings = useCallback(async (newSettings: Partial<AppSettings>) => {
    try {
      setError(null);
      const updatedSettings = { ...settings, ...newSettings };
      
      // Update analysis units when measurement units change
      if (newSettings.units) {
        updatedSettings.analysisUnits = newSettings.units === 'metric' ? 'meters' : 'feet';
      }
      
      setSettings(updatedSettings);
      await saveSettings(updatedSettings);
    } catch (err) {
      console.error('Error updating settings:', err);
      setError('Failed to update settings');
      throw err;
    }
  }, [settings, saveSettings]);

  // Reset all settings
  const resetSettings = useCallback(async () => {
    try {
      setError(null);
      setSettings(DEFAULT_SETTINGS);
      await saveSettings(DEFAULT_SETTINGS);
    } catch (err) {
      console.error('Error resetting settings:', err);
      setError('Failed to reset settings');
      throw err;
    }
  }, [saveSettings]);

  // Reset specific section
  const resetSection = useCallback(async (section: string) => {
    try {
      setError(null);
      let updatedSettings = { ...settings };
      
      switch (section) {
        case 'map':
          updatedSettings = {
            ...updatedSettings,
            coordinateSystem: DEFAULT_SETTINGS.coordinateSystem,
            baseMap: DEFAULT_SETTINGS.baseMap,
            units: DEFAULT_SETTINGS.units,
            showScale: DEFAULT_SETTINGS.showScale,
            showCoordinates: DEFAULT_SETTINGS.showCoordinates,
          };
          break;
        case 'data':
          updatedSettings = {
            ...updatedSettings,
            autoSync: DEFAULT_SETTINGS.autoSync,
            syncInterval: DEFAULT_SETTINGS.syncInterval,
            offlineStorage: DEFAULT_SETTINGS.offlineStorage,
            cloudBackup: DEFAULT_SETTINGS.cloudBackup,
            dataRetention: DEFAULT_SETTINGS.dataRetention,
          };
          break;
        case 'ui':
          updatedSettings = {
            ...updatedSettings,
            theme: DEFAULT_SETTINGS.theme,
            language: DEFAULT_SETTINGS.language,
            fontSize: DEFAULT_SETTINGS.fontSize,
            highContrast: DEFAULT_SETTINGS.highContrast,
            reducedMotion: DEFAULT_SETTINGS.reducedMotion,
          };
          break;
        case 'notifications':
          updatedSettings = {
            ...updatedSettings,
            pushNotifications: DEFAULT_SETTINGS.pushNotifications,
            emailNotifications: DEFAULT_SETTINGS.emailNotifications,
            soundEnabled: DEFAULT_SETTINGS.soundEnabled,
            vibrationEnabled: DEFAULT_SETTINGS.vibrationEnabled,
          };
          break;
        case 'security':
          updatedSettings = {
            ...updatedSettings,
            biometricAuth: DEFAULT_SETTINGS.biometricAuth,
            sessionTimeout: DEFAULT_SETTINGS.sessionTimeout,
            dataEncryption: DEFAULT_SETTINGS.dataEncryption,
            auditLogging: DEFAULT_SETTINGS.auditLogging,
          };
          break;
      }
      
      setSettings(updatedSettings);
      await saveSettings(updatedSettings);
    } catch (err) {
      console.error('Error resetting section:', err);
      setError(`Failed to reset ${section} settings`);
      throw err;
    }
  }, [settings, saveSettings]);

  // Load settings on mount
  useEffect(() => {
    loadSettings();
  }, [loadSettings]);

  const contextValue: SettingsContextType = {
    settings,
    updateSetting,
    updateSettings,
    resetSettings,
    resetSection,
    isLoading,
    error,
    isDark,
    effectiveTheme,
    unitUtils: UnitUtils,
  };

  return (
    <SettingsContext.Provider value={contextValue}>
      {children}
    </SettingsContext.Provider>
  );
}

// Hook to use settings
export function useSettings() {
  const context = useContext(SettingsContext);
  if (!context) {
    throw new Error('useSettings must be used within a SettingsProvider');
  }
  return context;
}

// Export types
export type { SettingsContextType };
