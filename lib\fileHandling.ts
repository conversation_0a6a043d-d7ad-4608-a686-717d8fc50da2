// File handling utilities for map data import/export
export interface FileInfo {
  name: string;
  size: number;
  type: string;
  lastModified: number;
}

export interface ImportResult {
  success: boolean;
  data?: any;
  error?: string;
  format?: 'geojson' | 'kml' | 'gpx' | 'shapefile' | 'csv';
  featureCount?: number;
}

export interface ExportOptions {
  format: 'geojson' | 'shapefile' | 'kml' | 'gpx' | 'csv';
  filename?: string;
  compress?: boolean;
  includeMetadata?: boolean;
}

/**
 * Detect file format from filename or MIME type
 */
export const detectFileFormat = (file: File | string): string => {
  const filename = typeof file === 'string' ? file : file.name;
  const extension = filename.toLowerCase().split('.').pop();
  
  switch (extension) {
    case 'geojson':
    case 'json':
      return 'geojson';
    case 'kml':
      return 'kml';
    case 'gpx':
      return 'gpx';
    case 'shp':
    case 'zip':
      return 'shapefile';
    case 'csv':
      return 'csv';
    default:
      return 'unknown';
  }
};

/**
 * Validate GeoJSON structure
 */
export const validateGeoJSON = (data: any): boolean => {
  if (!data || typeof data !== 'object') return false;
  
  // Check if it's a valid GeoJSON object
  if (data.type === 'FeatureCollection') {
    return Array.isArray(data.features) && 
           data.features.every((feature: any) => 
             feature.type === 'Feature' && 
             feature.geometry && 
             feature.properties
           );
  } else if (data.type === 'Feature') {
    return data.geometry && data.properties;
  } else if (['Point', 'LineString', 'Polygon', 'MultiPoint', 'MultiLineString', 'MultiPolygon'].includes(data.type)) {
    return data.coordinates;
  }
  
  return false;
};

/**
 * Import GeoJSON file
 */
export const importGeoJSON = async (file: File): Promise<ImportResult> => {
  try {
    const text = await file.text();
    const data = JSON.parse(text);
    
    if (!validateGeoJSON(data)) {
      return {
        success: false,
        error: 'Invalid GeoJSON format',
      };
    }
    
    const featureCount = data.type === 'FeatureCollection' 
      ? data.features.length 
      : 1;
    
    return {
      success: true,
      data,
      format: 'geojson',
      featureCount,
    };
  } catch (error) {
    return {
      success: false,
      error: `Failed to parse GeoJSON: ${error.message}`,
    };
  }
};

/**
 * Import KML file (requires togeojson library)
 */
export const importKML = async (file: File): Promise<ImportResult> => {
  try {
    const text = await file.text();
    
    // Parse XML
    const parser = new DOMParser();
    const kmlDoc = parser.parseFromString(text, 'text/xml');
    
    // Check for parsing errors
    if (kmlDoc.getElementsByTagName('parsererror').length > 0) {
      return {
        success: false,
        error: 'Invalid KML format - XML parsing failed',
      };
    }
    
    // Convert to GeoJSON using togeojson library if available
    if (typeof window !== 'undefined' && (window as any).togeojson) {
      const geoJSON = (window as any).togeojson.kml(kmlDoc);
      
      return {
        success: true,
        data: geoJSON,
        format: 'kml',
        featureCount: geoJSON.features ? geoJSON.features.length : 0,
      };
    } else {
      return {
        success: false,
        error: 'KML conversion library not loaded',
      };
    }
  } catch (error) {
    return {
      success: false,
      error: `Failed to parse KML: ${error.message}`,
    };
  }
};

/**
 * Import GPX file (requires togeojson library)
 */
export const importGPX = async (file: File): Promise<ImportResult> => {
  try {
    const text = await file.text();
    
    // Parse XML
    const parser = new DOMParser();
    const gpxDoc = parser.parseFromString(text, 'text/xml');
    
    // Check for parsing errors
    if (gpxDoc.getElementsByTagName('parsererror').length > 0) {
      return {
        success: false,
        error: 'Invalid GPX format - XML parsing failed',
      };
    }
    
    // Convert to GeoJSON using togeojson library if available
    if (typeof window !== 'undefined' && (window as any).togeojson) {
      const geoJSON = (window as any).togeojson.gpx(gpxDoc);
      
      return {
        success: true,
        data: geoJSON,
        format: 'gpx',
        featureCount: geoJSON.features ? geoJSON.features.length : 0,
      };
    } else {
      return {
        success: false,
        error: 'GPX conversion library not loaded',
      };
    }
  } catch (error) {
    return {
      success: false,
      error: `Failed to parse GPX: ${error.message}`,
    };
  }
};

/**
 * Import Shapefile (requires shpjs library)
 */
export const importShapefile = async (file: File): Promise<ImportResult> => {
  try {
    const arrayBuffer = await file.arrayBuffer();
    
    // Convert using shpjs library if available
    if (typeof window !== 'undefined' && (window as any).shp) {
      const geoJSON = await (window as any).shp(arrayBuffer);
      
      return {
        success: true,
        data: geoJSON,
        format: 'shapefile',
        featureCount: geoJSON.features ? geoJSON.features.length : 0,
      };
    } else {
      return {
        success: false,
        error: 'Shapefile conversion library not loaded',
      };
    }
  } catch (error) {
    return {
      success: false,
      error: `Failed to parse Shapefile: ${error.message}`,
    };
  }
};

/**
 * Import CSV file with coordinates
 */
export const importCSV = async (file: File): Promise<ImportResult> => {
  try {
    const text = await file.text();
    const lines = text.split('\n').filter(line => line.trim());
    
    if (lines.length < 2) {
      return {
        success: false,
        error: 'CSV file must have at least a header and one data row',
      };
    }
    
    const headers = lines[0].split(',').map(h => h.trim().toLowerCase());
    
    // Find coordinate columns
    const latIndex = headers.findIndex(h => ['lat', 'latitude', 'y'].includes(h));
    const lngIndex = headers.findIndex(h => ['lng', 'lon', 'longitude', 'x'].includes(h));
    
    if (latIndex === -1 || lngIndex === -1) {
      return {
        success: false,
        error: 'CSV must contain latitude and longitude columns (lat/latitude/y, lng/lon/longitude/x)',
      };
    }
    
    const features = [];
    
    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(',').map(v => v.trim());
      
      if (values.length < Math.max(latIndex, lngIndex) + 1) continue;
      
      const lat = parseFloat(values[latIndex]);
      const lng = parseFloat(values[lngIndex]);
      
      if (isNaN(lat) || isNaN(lng)) continue;
      
      // Create properties from other columns
      const properties: any = {};
      headers.forEach((header, index) => {
        if (index !== latIndex && index !== lngIndex && values[index]) {
          properties[header] = values[index];
        }
      });
      
      features.push({
        type: 'Feature',
        geometry: {
          type: 'Point',
          coordinates: [lng, lat],
        },
        properties,
      });
    }
    
    const geoJSON = {
      type: 'FeatureCollection',
      features,
    };
    
    return {
      success: true,
      data: geoJSON,
      format: 'csv',
      featureCount: features.length,
    };
  } catch (error) {
    return {
      success: false,
      error: `Failed to parse CSV: ${error.message}`,
    };
  }
};

/**
 * Main import function that detects format and imports accordingly
 */
export const importFile = async (file: File): Promise<ImportResult> => {
  const format = detectFileFormat(file);
  
  switch (format) {
    case 'geojson':
      return importGeoJSON(file);
    case 'kml':
      return importKML(file);
    case 'gpx':
      return importGPX(file);
    case 'shapefile':
      return importShapefile(file);
    case 'csv':
      return importCSV(file);
    default:
      return {
        success: false,
        error: `Unsupported file format: ${format}`,
      };
  }
};

/**
 * Export GeoJSON with proper formatting
 */
export const exportGeoJSON = (data: any, options: ExportOptions = { format: 'geojson' }): Blob => {
  const geoJSON = {
    type: 'FeatureCollection',
    features: Array.isArray(data) ? data : data.features || [data],
    ...(options.includeMetadata && {
      metadata: {
        exportedAt: new Date().toISOString(),
        application: 'FieldSyncPro',
        version: '2.0',
      }
    })
  };
  
  const jsonString = JSON.stringify(geoJSON, null, 2);
  return new Blob([jsonString], { type: 'application/json' });
};

/**
 * Export to KML format
 */
export const exportKML = (data: any, options: ExportOptions = { format: 'kml' }): Blob => {
  const features = Array.isArray(data) ? data : data.features || [data];
  
  let kmlContent = `<?xml version="1.0" encoding="UTF-8"?>
<kml xmlns="http://www.opengis.net/kml/2.2">
  <Document>
    <name>${options.filename || 'FieldSyncPro Export'}</name>
    <description>Exported from FieldSyncPro</description>
`;

  features.forEach((feature: any, index: number) => {
    const name = feature.properties?.name || `Feature ${index + 1}`;
    const description = feature.properties?.description || '';
    const geometry = feature.geometry;
    
    kmlContent += `
    <Placemark>
      <name>${escapeXML(name)}</name>
      <description>${escapeXML(description)}</description>`;
    
    if (geometry.type === 'Point') {
      const [lng, lat] = geometry.coordinates;
      kmlContent += `
      <Point>
        <coordinates>${lng},${lat},0</coordinates>
      </Point>`;
    } else if (geometry.type === 'LineString') {
      const coords = geometry.coordinates.map(([lng, lat]: [number, number]) => `${lng},${lat},0`).join(' ');
      kmlContent += `
      <LineString>
        <coordinates>${coords}</coordinates>
      </LineString>`;
    } else if (geometry.type === 'Polygon') {
      const coords = geometry.coordinates[0].map(([lng, lat]: [number, number]) => `${lng},${lat},0`).join(' ');
      kmlContent += `
      <Polygon>
        <outerBoundaryIs>
          <LinearRing>
            <coordinates>${coords}</coordinates>
          </LinearRing>
        </outerBoundaryIs>
      </Polygon>`;
    }
    
    kmlContent += `
    </Placemark>`;
  });
  
  kmlContent += `
  </Document>
</kml>`;

  return new Blob([kmlContent], { type: 'application/vnd.google-earth.kml+xml' });
};

/**
 * Export to CSV format
 */
export const exportCSV = (data: any, options: ExportOptions = { format: 'csv' }): Blob => {
  const features = Array.isArray(data) ? data : data.features || [data];
  
  // Collect all property keys
  const allKeys = new Set<string>();
  features.forEach((feature: any) => {
    if (feature.properties) {
      Object.keys(feature.properties).forEach(key => allKeys.add(key));
    }
  });
  
  const propertyKeys = Array.from(allKeys).sort();
  const headers = ['latitude', 'longitude', 'geometry_type', ...propertyKeys];
  
  let csvContent = headers.join(',') + '\n';
  
  features.forEach((feature: any) => {
    const geometry = feature.geometry;
    let lat = '', lng = '';
    
    if (geometry.type === 'Point') {
      [lng, lat] = geometry.coordinates;
    } else if (geometry.type === 'LineString' || geometry.type === 'Polygon') {
      // Use first coordinate for non-point geometries
      const coords = geometry.type === 'Polygon' ? geometry.coordinates[0] : geometry.coordinates;
      if (coords.length > 0) {
        [lng, lat] = coords[0];
      }
    }
    
    const row = [
      lat,
      lng,
      geometry.type,
      ...propertyKeys.map(key => {
        const value = feature.properties?.[key] || '';
        return typeof value === 'string' && value.includes(',') ? `"${value}"` : value;
      })
    ];
    
    csvContent += row.join(',') + '\n';
  });
  
  return new Blob([csvContent], { type: 'text/csv' });
};

/**
 * Main export function
 */
export const exportData = (data: any, options: ExportOptions): Blob => {
  switch (options.format) {
    case 'geojson':
      return exportGeoJSON(data, options);
    case 'kml':
      return exportKML(data, options);
    case 'csv':
      return exportCSV(data, options);
    case 'shapefile':
      // Shapefile export is complex and requires specialized libraries
      // For now, fall back to GeoJSON
      return exportGeoJSON(data, options);
    default:
      throw new Error(`Unsupported export format: ${options.format}`);
  }
};

/**
 * Download exported data as file
 */
export const downloadFile = (blob: Blob, filename: string): void => {
  if (typeof window === 'undefined') return;
  
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

/**
 * Generate filename with timestamp
 */
export const generateFilename = (baseName: string, format: string): string => {
  const timestamp = new Date().toISOString().split('T')[0];
  const extension = format === 'geojson' ? 'geojson' : 
                   format === 'shapefile' ? 'zip' : format;
  return `${baseName}_${timestamp}.${extension}`;
};

/**
 * Escape XML special characters
 */
const escapeXML = (str: string): string => {
  return str
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;');
};

/**
 * Validate file size before processing
 */
export const validateFileSize = (file: File, maxSizeMB: number = 50): boolean => {
  const maxSizeBytes = maxSizeMB * 1024 * 1024;
  return file.size <= maxSizeBytes;
};

/**
 * Get file information
 */
export const getFileInfo = (file: File): FileInfo => {
  return {
    name: file.name,
    size: file.size,
    type: file.type || detectFileFormat(file),
    lastModified: file.lastModified,
  };
};

/**
 * Format file size in human-readable format
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};
