#!/bin/bash

echo "🔧 FieldSync Pro - Quick Fix & Restart"
echo "======================================"

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Stop all processes
echo "1. 🛑 Stopping all Metro/Expo processes..."
if command_exists pkill; then
    pkill -f "expo" 2>/dev/null || true
    pkill -f "metro" 2>/dev/null || true
    pkill -f "node.*expo" 2>/dev/null || true
fi

# Clear Metro cache
echo "2. 🧹 Clearing Metro cache..."
if command_exists npx; then
    npx expo start --clear --reset-cache &
    PID=$!
    sleep 3
    kill $PID 2>/dev/null || true
fi

# Wait a moment
echo "3. ⏱️ Waiting for cleanup..."
sleep 2

# Start fresh
echo "4. 🚀 Starting fresh development server..."
if command_exists npm; then
    npm run dev
elif command_exists npx; then
    npx expo start --clear
else
    echo "❌ Neither npm nor npx found. Please install Node.js"
    exit 1
fi

echo ""
echo "✅ If issues persist, try:"
echo "   1. rm -rf node_modules && npm install"
echo "   2. npm run reset-cache"
echo "   3. expo doctor"
