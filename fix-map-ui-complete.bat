@echo off
echo ===============================================
echo Fixing FieldSyncPro Map UI Issues
echo ===============================================
echo.

REM Navigate to project directory
cd /d "D:\devprojects\FieldSyncPro"
if %errorlevel% neq 0 (
    echo ERROR: Could not navigate to project directory
    exit /b 1
)

echo Current directory: %CD%
echo.

REM Clear all caches
echo Clearing all caches...
echo.

REM Clear Metro cache
echo 1. Clearing Metro cache...
if exist "%TEMP%\metro-cache" (
    rmdir /s /q "%TEMP%\metro-cache"
)
if exist "%LOCALAPPDATA%\Temp\metro-cache" (
    rmdir /s /q "%LOCALAPPDATA%\Temp\metro-cache"
)

REM Clear React Native cache
echo 2. Clearing React Native cache...
npx react-native start --reset-cache > nul 2>&1
timeout /t 2 > nul
taskkill /F /IM node.exe > nul 2>&1

REM Clear Expo cache
echo 3. Clearing Expo cache...
if exist ".expo" (
    rmdir /s /q ".expo"
)
if exist "%USERPROFILE%\.expo" (
    echo Clearing global Expo cache...
    rmdir /s /q "%USERPROFILE%\.expo\cache"
)

REM Clear watchman cache if exists
echo 4. Clearing Watchman cache...
where watchman > nul 2>&1
if %errorlevel% equ 0 (
    watchman watch-del-all > nul 2>&1
)

REM Clear node_modules and reinstall
echo 5. Reinstalling dependencies...
if exist "node_modules" (
    echo Removing node_modules...
    rmdir /s /q "node_modules"
)
if exist "package-lock.json" (
    del /f /q "package-lock.json"
)

echo Installing fresh dependencies...
npm install

if %errorlevel% neq 0 (
    echo ERROR: Failed to install dependencies
    exit /b 1
)

echo.
echo ===============================================
echo Starting FieldSyncPro with cleared caches
echo ===============================================
echo.

REM Start Expo with clear cache
echo Starting Expo development server...
npx expo start --clear

echo.
echo ===============================================
echo Instructions:
echo ===============================================
echo 1. Press 'a' for Android or 'w' for web
echo 2. Navigate to the map screen
echo 3. Test the new UI layout:
echo    - Menu button (bottom left) opens toolbar
echo    - Toolbar slides from bottom
echo    - Drawing tools work properly
echo    - No overlapping UI elements
echo    - Zoom controls on right side
echo    - Status badges at top
echo.
echo If you still see errors:
echo 1. Press Ctrl+C to stop
echo 2. Run: npx expo doctor
echo 3. Run: npm audit fix
echo ===============================================
