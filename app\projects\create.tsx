import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { useTheme } from '@/hooks/useTheme';
import * as Location from 'expo-location';
import {
  ArrowLeft,
  Save,
  MapPin,
  Calendar,
  Users,
  FileText,
  Globe,
  Building,
  User,
  CheckCircle,
  Clock,
  Archive,
  Eye,
  Settings as SettingsIcon,
} from 'lucide-react-native';

export interface ProjectData {
  id: string;
  name: string;
  description: string;
  category: string;
  status: 'draft' | 'active' | 'completed' | 'archived';
  location?: {
    latitude: number;
    longitude: number;
    address?: string;
    region?: string;
  };
  startDate: string;
  endDate?: string;
  teamSize: number;
  budget?: number;
  tags: string[];
  privacy: 'public' | 'private' | 'team';
  createdAt: number;
  updatedAt: number;
  createdBy: string;
}

const PROJECT_CATEGORIES = [
  { id: 'research', label: 'Research', icon: FileText, description: 'Scientific research and studies' },
  { id: 'construction', label: 'Construction', icon: Building, description: 'Construction and infrastructure' },
  { id: 'environmental', label: 'Environmental', icon: Globe, description: 'Environmental monitoring' },
  { id: 'survey', label: 'Survey', icon: User, description: 'Data collection and surveys' },
  { id: 'maintenance', label: 'Maintenance', icon: SettingsIcon, description: 'Maintenance and inspection' },
  { id: 'other', label: 'Other', icon: FileText, description: 'Other project types' },
];

const PROJECT_STATUSES = [
  { id: 'draft', label: 'Draft', icon: Clock, color: '#FF9800', description: 'Project in planning phase' },
  { id: 'active', label: 'Active', icon: CheckCircle, color: '#4CAF50', description: 'Currently active project' },
  { id: 'completed', label: 'Completed', icon: CheckCircle, color: '#2196F3', description: 'Project completed' },
  { id: 'archived', label: 'Archived', icon: Archive, color: '#9E9E9E', description: 'Archived project' },
];

interface CategorySelectorProps {
  selectedCategory: string;
  onSelect: (category: string) => void;
}

function CategorySelector({ selectedCategory, onSelect }: CategorySelectorProps) {
  const { theme } = useTheme();

  return (
    <View style={styles.categoryGrid}>
      {PROJECT_CATEGORIES.map((category) => {
        const IconComponent = category.icon;
        const isSelected = selectedCategory === category.id;
        
        return (
          <TouchableOpacity
            key={category.id}
            style={[
              styles.categoryCard,
              {
                backgroundColor: isSelected ? theme.colors.primary + '20' : theme.colors.card,
                borderColor: isSelected ? theme.colors.primary : '#E0E0E0',
              }
            ]}
            onPress={() => onSelect(category.id)}
          >
            <View style={[
              styles.categoryIcon,
              { backgroundColor: isSelected ? theme.colors.primary : theme.colors.primary + '20' }
            ]}>
              <IconComponent size={20} color={isSelected ? 'white' : theme.colors.primary} />
            </View>
            <Text style={[
              styles.categoryLabel,
              { color: isSelected ? theme.colors.primary : theme.colors.text }
            ]}>
              {category.label}
            </Text>
            <Text style={[styles.categoryDescription, { color: theme.colors.muted }]}>
              {category.description}
            </Text>
          </TouchableOpacity>
        );
      })}
    </View>
  );
}

interface StatusSelectorProps {
  selectedStatus: string;
  onSelect: (status: string) => void;
}

function StatusSelector({ selectedStatus, onSelect }: StatusSelectorProps) {
  const { theme } = useTheme();

  return (
    <View style={styles.statusGrid}>
      {PROJECT_STATUSES.map((status) => {
        const IconComponent = status.icon;
        const isSelected = selectedStatus === status.id;
        
        return (
          <TouchableOpacity
            key={status.id}
            style={[
              styles.statusCard,
              {
                backgroundColor: isSelected ? status.color + '20' : theme.colors.card,
                borderColor: isSelected ? status.color : '#E0E0E0',
              }
            ]}
            onPress={() => onSelect(status.id)}
          >
            <IconComponent size={20} color={isSelected ? status.color : theme.colors.text} />
            <Text style={[
              styles.statusLabel,
              { color: isSelected ? status.color : theme.colors.text }
            ]}>
              {status.label}
            </Text>
          </TouchableOpacity>
        );
      })}
    </View>
  );
}

interface TagInputProps {
  tags: string[];
  onTagsChange: (tags: string[]) => void;
}

function TagInput({ tags, onTagsChange }: TagInputProps) {
  const { theme } = useTheme();
  const [inputValue, setInputValue] = useState('');

  const addTag = () => {
    const tag = inputValue.trim();
    if (tag && !tags.includes(tag)) {
      onTagsChange([...tags, tag]);
      setInputValue('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    onTagsChange(tags.filter(tag => tag !== tagToRemove));
  };

  return (
    <View>
      <View style={styles.tagInputContainer}>
        <TextInput
          style={[styles.tagInput, { backgroundColor: theme.colors.background, color: theme.colors.text }]}
          value={inputValue}
          onChangeText={setInputValue}
          placeholder="Add tags (e.g., monitoring, survey)"
          placeholderTextColor={theme.colors.muted}
          onSubmitEditing={addTag}
          returnKeyType="done"
        />
        <TouchableOpacity
          style={[styles.addTagButton, { backgroundColor: theme.colors.primary }]}
          onPress={addTag}
        >
          <Text style={styles.addTagButtonText}>Add</Text>
        </TouchableOpacity>
      </View>
      
      {tags.length > 0 && (
        <View style={styles.tagsContainer}>
          {tags.map((tag, index) => (
            <TouchableOpacity
              key={index}
              style={[styles.tag, { backgroundColor: theme.colors.primary + '20' }]}
              onPress={() => removeTag(tag)}
            >
              <Text style={[styles.tagText, { color: theme.colors.primary }]}>{tag}</Text>
              <Text style={[styles.tagRemove, { color: theme.colors.primary }]}>×</Text>
            </TouchableOpacity>
          ))}
        </View>
      )}
    </View>
  );
}

export default function ProjectCreationScreen() {
  const { theme } = useTheme();
  const [projectData, setProjectData] = useState<Partial<ProjectData>>({
    name: '',
    description: '',
    category: '',
    status: 'draft',
    startDate: new Date().toISOString().split('T')[0],
    teamSize: 1,
    tags: [],
    privacy: 'team',
  });

  const updateProject = (updates: Partial<ProjectData>) => {
    setProjectData(prev => ({ ...prev, ...updates }));
  };

  const handleLocationCapture = async () => {
    try {
      // Request location permissions
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission needed', 'Location permission is required to set project location');
        return;
      }

      // Get current location
      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
      });

      // Reverse geocode to get address
      try {
        const addresses = await Location.reverseGeocodeAsync({
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
        });
        
        const address = addresses[0];
        const addressString = address ? 
          `${address.street || ''} ${address.city || ''} ${address.region || ''}`.trim() : 
          'Location captured';

        updateProject({
          location: {
            latitude: location.coords.latitude,
            longitude: location.coords.longitude,
            address: addressString,
            region: address?.region || '',
          }
        });
      } catch (geocodeError) {
        // If reverse geocoding fails, still save the coordinates
        updateProject({
          location: {
            latitude: location.coords.latitude,
            longitude: location.coords.longitude,
          }
        });
      }
    } catch (error) {
      console.error('Error getting location:', error);
      Alert.alert('Error', 'Failed to get location. Please try again.');
    }
  };

  const handleDatePicker = (field: 'startDate' | 'endDate') => {
    // For web platform, we'll use a simple input
    if (Platform.OS === 'web') {
      const input = document.createElement('input');
      input.type = 'date';
      input.value = projectData[field] || '';
      input.onchange = (e) => {
        const target = e.target as HTMLInputElement;
        updateProject({ [field]: target.value });
      };
      input.click();
    } else {
      // For native platforms, you would use a proper date picker
      Alert.alert('Date Picker', 'Native date picker would open here', [
        {
          text: 'Set Today',
          onPress: () => updateProject({ [field]: new Date().toISOString().split('T')[0] })
        },
        { text: 'Cancel', style: 'cancel' }
      ]);
    }
  };

  const validateProject = () => {
    if (!projectData.name?.trim()) {
      Alert.alert('Error', 'Please enter a project name');
      return false;
    }

    if (!projectData.category) {
      Alert.alert('Error', 'Please select a project category');
      return false;
    }

    if (!projectData.startDate) {
      Alert.alert('Error', 'Please set a start date');
      return false;
    }

    if (projectData.endDate && projectData.startDate && projectData.endDate < projectData.startDate) {
      Alert.alert('Error', 'End date cannot be before start date');
      return false;
    }

    return true;
  };

  const saveProject = async () => {
    if (!validateProject()) return;

    const newProject: ProjectData = {
      id: `project_${Date.now()}`,
      name: projectData.name!,
      description: projectData.description || '',
      category: projectData.category!,
      status: projectData.status as any,
      location: projectData.location,
      startDate: projectData.startDate!,
      endDate: projectData.endDate,
      teamSize: projectData.teamSize || 1,
      budget: projectData.budget,
      tags: projectData.tags || [],
      privacy: projectData.privacy as any,
      createdAt: Date.now(),
      updatedAt: Date.now(),
      createdBy: 'current_user', // In real app, this would be the actual user ID
    };

    try {
      console.log('Saving project:', newProject);
      
      if (Platform.OS === 'web') {
        const existingProjects = JSON.parse(localStorage.getItem('fieldsync_projects') || '[]');
        existingProjects.push(newProject);
        localStorage.setItem('fieldsync_projects', JSON.stringify(existingProjects));
      }

      Alert.alert('Success', 'Project created successfully!', [
        { text: 'OK', onPress: () => router.back() }
      ]);
    } catch (error) {
      console.error('Error saving project:', error);
      Alert.alert('Error', 'Failed to save project. Please try again.');
    }
  };

  const previewProject = () => {
    if (!validateProject()) return;
    
    Alert.alert(
      'Project Preview',
      `Name: ${projectData.name}\nCategory: ${projectData.category}\nStatus: ${projectData.status}\nTeam Size: ${projectData.teamSize}`,
      [{ text: 'OK' }]
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <TouchableOpacity
            style={[styles.headerButton, { backgroundColor: theme.colors.card }]}
            onPress={() => router.back()}
          >
            <ArrowLeft size={20} color={theme.colors.text} />
          </TouchableOpacity>
          <View>
            <Text style={[styles.headerTitle, { color: theme.colors.text }]}>Create Project</Text>
            <Text style={[styles.headerSubtitle, { color: theme.colors.muted }]}>
              🚀 Set up a new field project
            </Text>
          </View>
        </View>
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={[styles.headerButton, { backgroundColor: theme.colors.card }]}
            onPress={previewProject}
          >
            <Eye size={20} color={theme.colors.text} />
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.saveButton, { backgroundColor: theme.colors.primary }]}
            onPress={saveProject}
          >
            <Save size={20} color="white" />
            <Text style={styles.saveButtonText}>Create</Text>
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Basic Information */}
        <View style={[styles.section, { backgroundColor: theme.colors.card }]}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Basic Information</Text>
          
          <View style={styles.inputGroup}>
            <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Project Name *</Text>
            <TextInput
              style={[styles.input, { backgroundColor: theme.colors.background, color: theme.colors.text }]}
              value={projectData.name}
              onChangeText={(name) => updateProject({ name })}
              placeholder="Enter project name"
              placeholderTextColor={theme.colors.muted}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Description</Text>
            <TextInput
              style={[styles.textArea, { backgroundColor: theme.colors.background, color: theme.colors.text }]}
              value={projectData.description}
              onChangeText={(description) => updateProject({ description })}
              placeholder="Describe the project goals and objectives"
              placeholderTextColor={theme.colors.muted}
              multiline
              numberOfLines={4}
            />
          </View>
        </View>

        {/* Project Category */}
        <View style={[styles.section, { backgroundColor: theme.colors.card }]}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Project Category *</Text>
          <CategorySelector
            selectedCategory={projectData.category || ''}
            onSelect={(category) => updateProject({ category })}
          />
        </View>

        {/* Project Status */}
        <View style={[styles.section, { backgroundColor: theme.colors.card }]}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Project Status</Text>
          <StatusSelector
            selectedStatus={projectData.status || 'draft'}
            onSelect={(status) => updateProject({ status: status as any })}
          />
        </View>

        {/* Location */}
        <View style={[styles.section, { backgroundColor: theme.colors.card }]}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Location</Text>
          
          <TouchableOpacity
            style={[styles.locationButton, { backgroundColor: theme.colors.background }]}
            onPress={handleLocationCapture}
          >
            <MapPin size={20} color={theme.colors.primary} />
            <View style={styles.locationInfo}>
              <Text style={[styles.locationText, { color: projectData.location ? theme.colors.text : theme.colors.muted }]}>
                {projectData.location ? 
                  (projectData.location.address || `${projectData.location.latitude.toFixed(6)}, ${projectData.location.longitude.toFixed(6)}`) :
                  'Capture project location'
                }
              </Text>
              {projectData.location && (
                <Text style={[styles.locationCoords, { color: theme.colors.muted }]}>
                  {projectData.location.latitude.toFixed(6)}, {projectData.location.longitude.toFixed(6)}
                </Text>
              )}
            </View>
          </TouchableOpacity>
        </View>

        {/* Timeline */}
        <View style={[styles.section, { backgroundColor: theme.colors.card }]}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Timeline</Text>
          
          <View style={styles.dateRow}>
            <View style={styles.dateGroup}>
              <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Start Date *</Text>
              <TouchableOpacity
                style={[styles.dateButton, { backgroundColor: theme.colors.background }]}
                onPress={() => handleDatePicker('startDate')}
              >
                <Calendar size={16} color={theme.colors.primary} />
                <Text style={[styles.dateText, { color: theme.colors.text }]}>
                  {projectData.startDate || 'Select date'}
                </Text>
              </TouchableOpacity>
            </View>
            
            <View style={styles.dateGroup}>
              <Text style={[styles.inputLabel, { color: theme.colors.text }]}>End Date</Text>
              <TouchableOpacity
                style={[styles.dateButton, { backgroundColor: theme.colors.background }]}
                onPress={() => handleDatePicker('endDate')}
              >
                <Calendar size={16} color={theme.colors.primary} />
                <Text style={[styles.dateText, { color: projectData.endDate ? theme.colors.text : theme.colors.muted }]}>
                  {projectData.endDate || 'Optional'}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* Team & Budget */}
        <View style={[styles.section, { backgroundColor: theme.colors.card }]}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Team & Budget</Text>
          
          <View style={styles.teamBudgetRow}>
            <View style={styles.teamSizeGroup}>
              <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Team Size</Text>
              <View style={styles.numberInputContainer}>
                <TouchableOpacity
                  style={[styles.numberButton, { backgroundColor: theme.colors.background }]}
                  onPress={() => updateProject({ teamSize: Math.max(1, (projectData.teamSize || 1) - 1) })}
                >
                  <Text style={[styles.numberButtonText, { color: theme.colors.text }]}>−</Text>
                </TouchableOpacity>
                <Text style={[styles.numberValue, { color: theme.colors.text }]}>
                  {projectData.teamSize || 1}
                </Text>
                <TouchableOpacity
                  style={[styles.numberButton, { backgroundColor: theme.colors.background }]}
                  onPress={() => updateProject({ teamSize: (projectData.teamSize || 1) + 1 })}
                >
                  <Text style={[styles.numberButtonText, { color: theme.colors.text }]}>+</Text>
                </TouchableOpacity>
              </View>
            </View>
            
            <View style={styles.budgetGroup}>
              <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Budget (Optional)</Text>
              <TextInput
                style={[styles.input, { backgroundColor: theme.colors.background, color: theme.colors.text }]}
                value={projectData.budget?.toString()}
                onChangeText={(budget) => updateProject({ budget: budget ? parseFloat(budget) : undefined })}
                placeholder="$0"
                placeholderTextColor={theme.colors.muted}
                keyboardType="numeric"
              />
            </View>
          </View>
        </View>

        {/* Tags */}
        <View style={[styles.section, { backgroundColor: theme.colors.card }]}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Tags</Text>
          <TagInput
            tags={projectData.tags || []}
            onTagsChange={(tags) => updateProject({ tags })}
          />
        </View>

        {/* Privacy */}
        <View style={[styles.section, { backgroundColor: theme.colors.card }]}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Privacy</Text>
          
          <View style={styles.privacyOptions}>
            {[
              { id: 'public', label: 'Public', description: 'Visible to everyone' },
              { id: 'team', label: 'Team Only', description: 'Visible to team members' },
              { id: 'private', label: 'Private', description: 'Only visible to you' },
            ].map((option) => (
              <TouchableOpacity
                key={option.id}
                style={[
                  styles.privacyOption,
                  {
                    backgroundColor: projectData.privacy === option.id ? theme.colors.primary + '20' : theme.colors.background,
                    borderColor: projectData.privacy === option.id ? theme.colors.primary : '#E0E0E0',
                  }
                ]}
                onPress={() => updateProject({ privacy: option.id as any })}
              >
                <Text style={[
                  styles.privacyLabel,
                  { color: projectData.privacy === option.id ? theme.colors.primary : theme.colors.text }
                ]}>
                  {option.label}
                </Text>
                <Text style={[styles.privacyDescription, { color: theme.colors.muted }]}>
                  {option.description}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <View style={styles.bottomSpace} />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    flex: 1,
  },
  headerButton: {
    padding: 12,
    borderRadius: 12,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  headerSubtitle: {
    fontSize: 14,
    marginTop: 2,
  },
  headerActions: {
    flexDirection: 'row',
    gap: 12,
  },
  saveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    gap: 8,
  },
  saveButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  content: {
    flex: 1,
  },
  section: {
    margin: 20,
    padding: 20,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  input: {
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  textArea: {
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    height: 100,
    textAlignVertical: 'top',
  },
  
  // Category Selector
  categoryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  categoryCard: {
    flex: 1,
    minWidth: '45%',
    padding: 16,
    borderRadius: 12,
    borderWidth: 2,
    alignItems: 'center',
  },
  categoryIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  categoryLabel: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
    textAlign: 'center',
  },
  categoryDescription: {
    fontSize: 12,
    textAlign: 'center',
    lineHeight: 16,
  },
  
  // Status Selector
  statusGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  statusCard: {
    flex: 1,
    minWidth: '22%',
    padding: 12,
    borderRadius: 8,
    borderWidth: 2,
    alignItems: 'center',
    gap: 6,
  },
  statusLabel: {
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'center',
  },
  
  // Location
  locationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    gap: 12,
  },
  locationInfo: {
    flex: 1,
  },
  locationText: {
    fontSize: 16,
    fontWeight: '500',
  },
  locationCoords: {
    fontSize: 12,
    marginTop: 2,
    fontFamily: 'monospace',
  },
  
  // Timeline
  dateRow: {
    flexDirection: 'row',
    gap: 16,
  },
  dateGroup: {
    flex: 1,
  },
  dateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    gap: 8,
  },
  dateText: {
    fontSize: 16,
    flex: 1,
  },
  
  // Team & Budget
  teamBudgetRow: {
    flexDirection: 'row',
    gap: 16,
  },
  teamSizeGroup: {
    flex: 1,
  },
  numberInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 12,
    overflow: 'hidden',
  },
  numberButton: {
    padding: 16,
    justifyContent: 'center',
    alignItems: 'center',
    minWidth: 48,
  },
  numberButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  numberValue: {
    flex: 1,
    textAlign: 'center',
    fontSize: 16,
    fontWeight: '600',
  },
  budgetGroup: {
    flex: 1,
  },
  
  // Tags
  tagInputContainer: {
    flexDirection: 'row',
    gap: 8,
    marginBottom: 12,
  },
  tagInput: {
    flex: 1,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  addTagButton: {
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderRadius: 12,
    justifyContent: 'center',
  },
  addTagButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  tag: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    gap: 6,
  },
  tagText: {
    fontSize: 14,
    fontWeight: '500',
  },
  tagRemove: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  
  // Privacy
  privacyOptions: {
    gap: 12,
  },
  privacyOption: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 2,
  },
  privacyLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  privacyDescription: {
    fontSize: 14,
    lineHeight: 18,
  },
  
  bottomSpace: {
    height: 100,
  },
});
