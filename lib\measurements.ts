// Enhanced measurement utilities with proper calculations
export interface MeasurementResult {
  distance?: number; // in meters
  area?: number; // in square meters
  perimeter?: number; // in meters
  radius?: number; // in meters
  formattedDistance?: string;
  formattedArea?: string;
  formattedPerimeter?: string;
}

export interface Coordinate {
  latitude: number;
  longitude: number;
}

// Constants
const EARTH_RADIUS = 6371000; // Earth's radius in meters
const DEG_TO_RAD = Math.PI / 180;

/**
 * Calculate distance between two points using Haversine formula
 */
export const calculateDistance = (from: Coordinate, to: Coordinate): number => {
  const φ1 = from.latitude * DEG_TO_RAD;
  const φ2 = to.latitude * DEG_TO_RAD;
  const Δφ = (to.latitude - from.latitude) * DEG_TO_RAD;
  const Δλ = (to.longitude - from.longitude) * DEG_TO_RAD;

  const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
          Math.cos(φ1) * Math.cos(φ2) *
          Math.sin(Δλ/2) * Math.sin(Δλ/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

  return EARTH_RADIUS * c;
};

/**
 * Calculate area of a polygon using Shoelace formula with proper coordinate projection
 */
export const calculatePolygonArea = (points: Coordinate[]): number => {
  if (points.length < 3) return 0;
  
  // Use more accurate area calculation with proper projection
  let area = 0;
  const n = points.length;
  
  // Convert to radians and calculate centroid
  const avgLat = points.reduce((sum, p) => sum + p.latitude, 0) / n;
  const avgLng = points.reduce((sum, p) => sum + p.longitude, 0) / n;
  
  // Project coordinates to approximate local Cartesian system
  const projectedPoints = points.map(point => ({
    x: (point.longitude - avgLng) * Math.cos(avgLat * DEG_TO_RAD) * EARTH_RADIUS * DEG_TO_RAD,
    y: (point.latitude - avgLat) * EARTH_RADIUS * DEG_TO_RAD
  }));
  
  // Apply Shoelace formula
  for (let i = 0; i < n; i++) {
    const j = (i + 1) % n;
    area += projectedPoints[i].x * projectedPoints[j].y;
    area -= projectedPoints[j].x * projectedPoints[i].y;
  }
  
  return Math.abs(area) / 2;
};

/**
 * Calculate perimeter of a polygon
 */
export const calculatePerimeter = (points: Coordinate[]): number => {
  if (points.length < 2) return 0;
  
  let perimeter = 0;
  for (let i = 0; i < points.length - 1; i++) {
    perimeter += calculateDistance(points[i], points[i + 1]);
  }
  
  // Close the polygon by adding distance from last to first point
  if (points.length > 2) {
    perimeter += calculateDistance(points[points.length - 1], points[0]);
  }
  
  return perimeter;
};

/**
 * Calculate total length of a line
 */
export const calculateLineLength = (points: Coordinate[]): number => {
  if (points.length < 2) return 0;
  
  let totalLength = 0;
  for (let i = 0; i < points.length - 1; i++) {
    totalLength += calculateDistance(points[i], points[i + 1]);
  }
  
  return totalLength;
};

/**
 * Create buffer around a point
 */
export const createPointBuffer = (center: Coordinate, radius: number, steps: number = 32): Coordinate[] => {
  const points: Coordinate[] = [];
  
  for (let i = 0; i < steps; i++) {
    const angle = (i * 2 * Math.PI) / steps;
    
    // Calculate offset in degrees
    const latOffset = (radius / EARTH_RADIUS) * (180 / Math.PI) * Math.cos(angle);
    const lngOffset = (radius / EARTH_RADIUS) * (180 / Math.PI) * Math.sin(angle) / Math.cos(center.latitude * DEG_TO_RAD);
    
    points.push({
      latitude: center.latitude + latOffset,
      longitude: center.longitude + lngOffset,
    });
  }
  
  return points;
};

/**
 * Check if a point is inside a polygon using ray casting algorithm
 */
export const pointInPolygon = (point: Coordinate, polygon: Coordinate[]): boolean => {
  let inside = false;
  const x = point.longitude;
  const y = point.latitude;
  
  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
    const xi = polygon[i].longitude;
    const yi = polygon[i].latitude;
    const xj = polygon[j].longitude;
    const yj = polygon[j].latitude;
    
    if (((yi > y) !== (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi)) {
      inside = !inside;
    }
  }
  
  return inside;
};

/**
 * Calculate centroid of a polygon
 */
export const calculateCentroid = (points: Coordinate[]): Coordinate => {
  if (points.length === 0) return { latitude: 0, longitude: 0 };
  
  // Use spherical coordinates for better accuracy
  let x = 0, y = 0, z = 0;
  
  points.forEach(point => {
    const lat = point.latitude * DEG_TO_RAD;
    const lng = point.longitude * DEG_TO_RAD;
    
    x += Math.cos(lat) * Math.cos(lng);
    y += Math.cos(lat) * Math.sin(lng);
    z += Math.sin(lat);
  });
  
  x /= points.length;
  y /= points.length;
  z /= points.length;
  
  const lng = Math.atan2(y, x) / DEG_TO_RAD;
  const hyp = Math.sqrt(x * x + y * y);
  const lat = Math.atan2(z, hyp) / DEG_TO_RAD;
  
  return { latitude: lat, longitude: lng };
};

/**
 * Get bounding box of a set of points
 */
export const getBoundingBox = (points: Coordinate[]): {
  minLat: number;
  maxLat: number;
  minLng: number;
  maxLng: number;
  center: Coordinate;
  width: number;
  height: number;
} => {
  if (points.length === 0) {
    return { 
      minLat: 0, maxLat: 0, minLng: 0, maxLng: 0, 
      center: { latitude: 0, longitude: 0 },
      width: 0, height: 0
    };
  }
  
  let minLat = points[0].latitude;
  let maxLat = points[0].latitude;
  let minLng = points[0].longitude;
  let maxLng = points[0].longitude;
  
  points.forEach(point => {
    minLat = Math.min(minLat, point.latitude);
    maxLat = Math.max(maxLat, point.latitude);
    minLng = Math.min(minLng, point.longitude);
    maxLng = Math.max(maxLng, point.longitude);
  });
  
  const center = {
    latitude: (minLat + maxLat) / 2,
    longitude: (minLng + maxLng) / 2,
  };
  
  // Calculate width and height in meters
  const width = calculateDistance(
    { latitude: center.latitude, longitude: minLng },
    { latitude: center.latitude, longitude: maxLng }
  );
  const height = calculateDistance(
    { latitude: minLat, longitude: center.longitude },
    { latitude: maxLat, longitude: center.longitude }
  );
  
  return { minLat, maxLat, minLng, maxLng, center, width, height };
};

/**
 * Simplify a polygon using Douglas-Peucker algorithm
 */
export const simplifyPolygon = (points: Coordinate[], tolerance: number): Coordinate[] => {
  if (points.length <= 2) return points;
  
  // Find the point with maximum distance from line segment
  let maxDistance = 0;
  let maxIndex = 0;
  const end = points.length - 1;
  
  for (let i = 1; i < end; i++) {
    const distance = perpendicularDistance(points[i], points[0], points[end]);
    if (distance > maxDistance) {
      maxDistance = distance;
      maxIndex = i;
    }
  }
  
  // If max distance is greater than tolerance, recursively simplify
  if (maxDistance > tolerance) {
    const results1 = simplifyPolygon(points.slice(0, maxIndex + 1), tolerance);
    const results2 = simplifyPolygon(points.slice(maxIndex), tolerance);
    
    return [...results1.slice(0, -1), ...results2];
  } else {
    return [points[0], points[end]];
  }
};

/**
 * Calculate perpendicular distance from point to line segment
 */
const perpendicularDistance = (point: Coordinate, lineStart: Coordinate, lineEnd: Coordinate): number => {
  const A = point.longitude - lineStart.longitude;
  const B = point.latitude - lineStart.latitude;
  const C = lineEnd.longitude - lineStart.longitude;
  const D = lineEnd.latitude - lineStart.latitude;
  
  const dot = A * C + B * D;
  const lenSq = C * C + D * D;
  
  if (lenSq === 0) {
    return calculateDistance(point, lineStart);
  }
  
  const param = dot / lenSq;
  
  let closestPoint: Coordinate;
  if (param < 0) {
    closestPoint = lineStart;
  } else if (param > 1) {
    closestPoint = lineEnd;
  } else {
    closestPoint = {
      longitude: lineStart.longitude + param * C,
      latitude: lineStart.latitude + param * D,
    };
  }
  
  return calculateDistance(point, closestPoint);
};

/**
 * Format distance in human-readable format
 */
export const formatDistance = (meters: number): string => {
  if (meters < 1) {
    return `${Math.round(meters * 100)} cm`;
  } else if (meters < 1000) {
    return `${Math.round(meters * 10) / 10} m`;
  } else if (meters < 10000) {
    return `${Math.round(meters / 100) / 10} km`;
  } else {
    return `${Math.round(meters / 1000)} km`;
  }
};

/**
 * Format area in human-readable format
 */
export const formatArea = (squareMeters: number): string => {
  if (squareMeters < 1) {
    return `${Math.round(squareMeters * 10000)} cm²`;
  } else if (squareMeters < 10000) {
    return `${Math.round(squareMeters)} m²`;
  } else {
    const hectares = squareMeters / 10000;
    if (hectares < 100) {
      return `${Math.round(hectares * 100) / 100} ha`;
    } else if (hectares < 10000) {
      return `${Math.round(hectares)} ha`;
    } else {
      return `${Math.round(hectares / 100) / 10} km²`;
    }
  }
};

/**
 * Format coordinates in human-readable format
 */
export const formatCoordinate = (coord: Coordinate, precision: number = 6): string => {
  const lat = coord.latitude >= 0 ? `${coord.latitude.toFixed(precision)}°N` : `${Math.abs(coord.latitude).toFixed(precision)}°S`;
  const lng = coord.longitude >= 0 ? `${coord.longitude.toFixed(precision)}°E` : `${Math.abs(coord.longitude).toFixed(precision)}°W`;
  return `${lat}, ${lng}`;
};

/**
 * Calculate bearing between two points
 */
export const calculateBearing = (from: Coordinate, to: Coordinate): number => {
  const φ1 = from.latitude * DEG_TO_RAD;
  const φ2 = to.latitude * DEG_TO_RAD;
  const Δλ = (to.longitude - from.longitude) * DEG_TO_RAD;
  
  const y = Math.sin(Δλ) * Math.cos(φ2);
  const x = Math.cos(φ1) * Math.sin(φ2) - Math.sin(φ1) * Math.cos(φ2) * Math.cos(Δλ);
  
  let bearing = Math.atan2(y, x) / DEG_TO_RAD;
  return (bearing + 360) % 360; // Normalize to 0-360 degrees
};

/**
 * Calculate destination point given distance and bearing
 */
export const calculateDestination = (start: Coordinate, distance: number, bearing: number): Coordinate => {
  const δ = distance / EARTH_RADIUS; // Angular distance
  const θ = bearing * DEG_TO_RAD; // Bearing in radians
  const φ1 = start.latitude * DEG_TO_RAD;
  const λ1 = start.longitude * DEG_TO_RAD;
  
  const φ2 = Math.asin(Math.sin(φ1) * Math.cos(δ) + 
                      Math.cos(φ1) * Math.sin(δ) * Math.cos(θ));
  const λ2 = λ1 + Math.atan2(Math.sin(θ) * Math.sin(δ) * Math.cos(φ1),
                             Math.cos(δ) - Math.sin(φ1) * Math.sin(φ2));
  
  return {
    latitude: φ2 / DEG_TO_RAD,
    longitude: λ2 / DEG_TO_RAD,
  };
};

/**
 * Calculate measurements for different geometry types
 */
export const calculateGeometryMeasurements = (
  geometryType: string, 
  coordinates: any, 
  properties?: any
): MeasurementResult => {
  const result: MeasurementResult = {};
  
  try {
    switch (geometryType.toLowerCase()) {
      case 'point':
        // Points don't have measurable dimensions
        break;
        
      case 'line':
      case 'linestring':
        if (Array.isArray(coordinates) && coordinates.length >= 2) {
          const points = coordinates.map(([lng, lat]: [number, number]) => ({ latitude: lat, longitude: lng }));
          result.distance = calculateLineLength(points);
          result.formattedDistance = formatDistance(result.distance);
        }
        break;
        
      case 'polygon':
        if (Array.isArray(coordinates) && coordinates.length > 0) {
          const outerRing = coordinates[0] || coordinates;
          const points = outerRing.map(([lng, lat]: [number, number]) => ({ latitude: lat, longitude: lng }));
          
          if (points.length >= 3) {
            result.area = calculatePolygonArea(points);
            result.perimeter = calculatePerimeter(points);
            result.formattedArea = formatArea(result.area);
            result.formattedPerimeter = formatDistance(result.perimeter);
          }
        }
        break;
        
      case 'circle':
        if (Array.isArray(coordinates) && coordinates.length >= 2) {
          const radius = properties?.radius || 100;
          result.radius = radius;
          result.area = Math.PI * radius * radius;
          result.perimeter = 2 * Math.PI * radius;
          result.formattedArea = formatArea(result.area);
          result.formattedPerimeter = formatDistance(result.perimeter);
        }
        break;
        
      case 'rectangle':
        if (Array.isArray(coordinates) && coordinates.length >= 2) {
          // Treat rectangle as polygon
          const points = coordinates.map(([lng, lat]: [number, number]) => ({ latitude: lat, longitude: lng }));
          result.area = calculatePolygonArea(points);
          result.perimeter = calculatePerimeter(points);
          result.formattedArea = formatArea(result.area);
          result.formattedPerimeter = formatDistance(result.perimeter);
        }
        break;
    }
  } catch (error) {
    console.error('Error calculating measurements:', error);
  }
  
  return result;
};
