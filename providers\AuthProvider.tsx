import { createContext, useState, useEffect, ReactNode } from 'react';
import { Platform } from 'react-native';
import { router } from 'expo-router';
import { User } from '@/types';

// Platform-specific storage
let SecureStore: any = null;
if (Platform.OS !== 'web') {
  SecureStore = require('expo-secure-store');
}

type AuthContextType = {
  user: User | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<User>;
  logout: () => Promise<void>;
  register: (name: string, email: string, password: string) => Promise<void>;
};

export const AuthContext = createContext<AuthContextType | null>(null);

// Sample users for demo purposes
const DEMO_USERS: User[] = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'admin',
    lastSyncTimestamp: Date.now()
  },
  {
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'collector',
    teamId: 'team1',
    lastSyncTimestamp: Date.now()
  }
];

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  
  // Check for existing session on mount
  useEffect(() => {
    const checkSession = async () => {
      try {
        let sessionData = null;
        
        if (Platform.OS === 'web') {
          // Use localStorage on web
          sessionData = localStorage.getItem('user_session');
        } else {
          // Use SecureStore on native platforms
          if (SecureStore) {
            sessionData = await SecureStore.getItemAsync('user_session');
          }
        }
        
        if (sessionData) {
          const userData = JSON.parse(sessionData);
          setUser(userData);
        }
      } catch (error) {
        console.error('Error checking session:', error);
      } finally {
        setLoading(false);
      }
    };
    
    checkSession();
  }, []);
  
  const login = async (email: string, password: string) => {
    setLoading(true);
    
    try {
      // In a real app, this would be an API call
      const foundUser = DEMO_USERS.find(u => u.email === email);
      
      if (!foundUser) {
        throw new Error('User not found');
      }
      
      // In a real app, this would verify the password
      
      // Store session based on platform
      const userSessionData = JSON.stringify(foundUser);
      if (Platform.OS === 'web') {
        localStorage.setItem('user_session', userSessionData);
      } else if (SecureStore) {
        await SecureStore.setItemAsync('user_session', userSessionData);
      }
      
      setUser(foundUser);
      router.replace('/(tabs)');
      return foundUser;
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };
  
  const logout = async () => {
    try {
      setLoading(true);
      
      // Clear user state first
      setUser(null);
      
      // Remove session based on platform
      if (Platform.OS === 'web') {
        localStorage.removeItem('user_session');
        localStorage.removeItem('fieldsync_settings'); // Also clear any app settings
      } else if (SecureStore) {
        await SecureStore.deleteItemAsync('user_session');
      }
      
      // Small delay to ensure state updates propagate
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // Navigate to login
      router.replace('/(auth)/login');
      
      console.log('Logout successful');
    } catch (error) {
      console.error('Logout error:', error);
      // Re-throw error so calling component can handle it
      throw new Error('Failed to log out. Please try again.');
    } finally {
      setLoading(false);
    }
  };
  
  const register = async (name: string, email: string, password: string) => {
    setLoading(true);
    
    try {
      // In a real app, this would be an API call to create a new user
      const newUser: User = {
        id: `user_${Date.now()}`,
        name,
        email,
        role: 'collector',
        lastSyncTimestamp: Date.now()
      };
      
      // Store session based on platform
      const userSessionData = JSON.stringify(newUser);
      if (Platform.OS === 'web') {
        localStorage.setItem('user_session', userSessionData);
      } else if (SecureStore) {
        await SecureStore.setItemAsync('user_session', userSessionData);
      }
      
      setUser(newUser);
      router.replace('/(tabs)');
    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <AuthContext.Provider value={{ user, loading, login, logout, register }}>
      {children}
    </AuthContext.Provider>
  );
}