{"expo": {"name": "FieldSync Pro", "slug": "fieldsync-pro", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "fieldsync", "userInterfaceStyle": "automatic", "newArchEnabled": true, "updates": {"enabled": false, "checkAutomatically": "ON_ERROR_RECOVERY", "fallbackToCacheTimeout": 0}, "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.ossamahda.fieldsyncpro"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router"], "experiments": {"typedRoutes": true}}}