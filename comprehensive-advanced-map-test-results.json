{"timestamp": "2025-05-30T23:45:00.000Z", "summary": {"totalSuites": 14, "totalTests": 60, "totalPassed": 60, "overallSuccessRate": "100.0"}, "suites": [{"name": "Component Architecture Tests", "passed": 6, "total": 6, "successRate": 100, "duration": 4, "tests": [{"name": "GIS Map Viewer Component Structure", "passed": true, "details": "", "duration": 1, "data": {"componentSize": 15587, "imports": 4}, "timestamp": "2025-05-30T23:45:00.009Z"}, {"name": "Layer Manager <PERSON><PERSON> Integration", "passed": true, "details": "", "duration": 0, "data": {"featureCount": 4}, "timestamp": "2025-05-30T23:45:00.010Z"}, {"name": "Spatial Analysis Modal Functionality", "passed": true, "details": "Available analysis tools: 6/6", "duration": 1, "data": {"availableTools": ["buffer", "clip", "dissolve", "intersect", "spatial-join", "proximity"], "totalTools": 6}, "timestamp": "2025-05-30T23:45:00.011Z"}, {"name": "Map Settings Configuration", "passed": true, "details": "Settings tabs: 4/4", "duration": 0, "data": {"availableTabs": ["renderGeneralTab", "renderDisplayTab", "renderCoordinatesTab", "renderThemeTab"], "requiredTabs": ["renderGeneralTab", "renderDisplayTab", "renderCoordinatesTab", "renderThemeTab"]}, "timestamp": "2025-05-30T23:45:00.011Z"}, {"name": "Story Builder Integration", "passed": true, "details": "Story features: 5/5", "duration": 1, "data": {"availableFeatures": ["StorySlide", "MapStory", "handleAddSlide", "handleDeleteSlide", "onPlayStory"], "totalFeatures": 5}, "timestamp": "2025-05-30T23:45:00.012Z"}, {"name": "Bookmark Manager Functionality", "passed": true, "details": "Bookmark features: 4/4", "duration": 0, "data": {"availableFeatures": ["MapBookmark", "onBookmarkCreate", "onBookmarkUpdate", "handleCreateBookmark"], "totalFeatures": 4}, "timestamp": "2025-05-30T23:45:00.012Z"}]}, {"name": "Spatial Analysis Engine Tests", "passed": 6, "total": 6, "successRate": 100, "duration": 2, "tests": [{"name": "Spatial Analysis Engine Algorithms", "passed": true, "details": "Algorithms: 6/6", "duration": 0, "data": {"availableAlgorithms": ["createBuffer", "clipFeatures", "dissolveFeatures", "spatialJoin", "proximityAnalysis", "calculateBearing"], "totalAlgorithms": 6}, "timestamp": "2025-05-30T23:45:00.119Z"}, {"name": "Buffer Analysis Implementation", "passed": true, "details": "Buffer features: 5/5", "duration": 1, "data": {"availableFeatures": ["BufferOptions", "distance", "unit", "steps", "dissolve"], "totalFeatures": 5}, "timestamp": "2025-05-30T23:45:00.120Z"}, {"name": "Spatial Join Operations", "passed": true, "details": "Join operations: 5/5", "duration": 0, "data": {"availableOperations": ["intersects", "contains", "within", "touches", "crosses"], "totalOperations": 5}, "timestamp": "2025-05-30T23:45:00.120Z"}, {"name": "Proximity Analysis Features", "passed": true, "details": "Proximity features: 5/5", "duration": 0, "data": {"availableFeatures": ["maxDistance", "nearestTarget", "bearing", "formatDistance", "formatBearing"], "totalFeatures": 5}, "timestamp": "2025-05-30T23:45:00.120Z"}, {"name": "Dissolve Algorithm Implementation", "passed": true, "details": "Dissolve features: 5/5", "duration": 0, "data": {"availableFeatures": ["DissolveOptions", "field", "aggregateFields", "multipart", "groupFeaturesByField"], "totalFeatures": 5}, "timestamp": "2025-05-30T23:45:00.120Z"}, {"name": "Geometric Calculations", "passed": true, "details": "Calculations: 4/4", "duration": 1, "data": {"availableCalculations": ["calculateDistance", "calculateBearing", "getCentroid", "convertToMeters"], "totalCalculations": 4}, "timestamp": "2025-05-30T23:45:00.121Z"}]}, {"name": "GIS Hook State Management Tests", "passed": 8, "total": 8, "successRate": 100, "duration": 3, "tests": [{"name": "Hook Interface Completeness", "passed": true, "details": "Types: 5/5", "duration": 1, "data": {"availableTypes": ["UseGISMapReturn", "GISFeature", "MapLayer", "AnalysisResult", "MeasurementResult"], "totalTypes": 5}, "timestamp": "2025-05-30T23:45:00.229Z"}, {"name": "Feature Management Methods", "passed": true, "details": "Feature methods: 5/5", "duration": 0, "data": {"availableMethods": ["createFeature", "updateFeature", "deleteFeature", "selectFeature", "deselectFeature"], "totalMethods": 5}, "timestamp": "2025-05-30T23:45:00.229Z"}, {"name": "Layer Management Operations", "passed": true, "details": "Layer methods: 5/5", "duration": 0, "data": {"availableMethods": ["add<PERSON><PERSON>er", "updateLayer", "<PERSON><PERSON><PERSON>er", "toggleLayerVisibility", "importLayer"], "totalMethods": 5}, "timestamp": "2025-05-30T23:45:00.230Z"}, {"name": "File Import/Export Capabilities", "passed": true, "details": "File operations: 4/4", "duration": 0, "data": {"availableOperations": ["importFile", "exportLayer", "FileImportOptions", "FileExportOptions"], "totalOperations": 4}, "timestamp": "2025-05-30T23:45:00.230Z"}, {"name": "Spatial Analysis Integration", "passed": true, "details": "Analysis features: 4/4", "duration": 0, "data": {"availableFeatures": ["runAnalysis", "analysisResults", "isAnalyzing", "analysisProgress"], "totalFeatures": 4}, "timestamp": "2025-05-30T23:45:00.230Z"}, {"name": "Measurement Tools Integration", "passed": true, "details": "Measurement features: 4/4", "duration": 1, "data": {"availableFeatures": ["addMeasurement", "clearMeasurements", "measurements", "activeMeasurementTool"], "totalFeatures": 4}, "timestamp": "2025-05-30T23:45:00.231Z"}, {"name": "Bookmark Management", "passed": true, "details": "Bookmark features: 4/4", "duration": 0, "data": {"availableFeatures": ["addBookmark", "navigateToBookmark", "bookmarks", "MapBookmark"], "totalFeatures": 4}, "timestamp": "2025-05-30T23:45:00.231Z"}, {"name": "Notification System", "passed": true, "details": "Notification features: 4/4", "duration": 0, "data": {"availableFeatures": ["showNotification", "hideNotification", "notifications", "Notification"], "totalFeatures": 4}, "timestamp": "2025-05-30T23:45:00.231Z"}]}, {"name": "Performance and Optimization Tests", "passed": 4, "total": 4, "successRate": 100, "duration": 1, "tests": [{"name": "Component File Sizes", "passed": true, "details": "Total size: 163KB, Large files: 0", "duration": 0, "data": {"fileSizes": {"components/map/GISMapViewer.tsx": 15, "components/map/LayerManagerModal.tsx": 23, "components/map/SpatialAnalysisModal.tsx": 30, "components/map/MapSettingsModal.tsx": 36, "components/map/StoryBuilderModal.tsx": 35, "components/map/BookmarkManagerModal.tsx": 24}, "totalSize": 163, "largeFiles": 0}, "timestamp": "2025-05-30T23:45:00.337Z"}, {"name": "Code Complexity Analysis", "passed": true, "details": "Lines: 1141, Functions: 32, Effects: 6, State: 2", "duration": 1, "data": {"lines": 1141, "functions": 32, "useEffects": 6, "useState": 2}, "timestamp": "2025-05-30T23:45:00.338Z"}, {"name": "Memory Usage Optimization", "passed": true, "details": "Optimizations: 4/5", "duration": 0, "data": {"foundOptimizations": ["useCallback", "useMemo", "clearTimeout", "useRef"], "totalOptimizations": 5}, "timestamp": "2025-05-30T23:45:00.338Z"}, {"name": "Async Operations Handling", "passed": true, "details": "Async patterns: 6/6", "duration": 0, "data": {"foundPatterns": ["async", "await", "Promise", "try", "catch", "finally"], "totalPatterns": 6}, "timestamp": "2025-05-30T23:45:00.338Z"}]}, {"name": "Integration and Compatibility Tests", "passed": 5, "total": 5, "successRate": 100, "duration": 2, "tests": [{"name": "React Native Dependencies", "passed": true, "details": "All dependencies present", "duration": 1, "data": {"requiredDeps": ["react-native-maps", "react-native-svg", "lucide-react-native", "expo-location", "expo-secure-store"], "missingDeps": [], "totalDeps": 56}, "timestamp": "2025-05-30T23:45:00.447Z"}, {"name": "TypeScript Configuration", "passed": true, "details": "Configured options: 3/3", "duration": 0, "data": {"configuredOptions": ["strict", "esModuleInterop", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "requiredOptions": ["strict", "esModuleInterop", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}, "timestamp": "2025-05-30T23:45:00.447Z"}, {"name": "Component Export Structure", "passed": true, "details": "Exports: 25", "duration": 1, "data": {"exports": 25}, "timestamp": "2025-05-30T23:45:00.448Z"}, {"name": "Error Boundary Implementation", "passed": true, "details": "Error handling features: 4/4", "duration": 0, "data": {"foundFeatures": ["componentDidCatch", "getDerivedStateFromError", "error", "fallback"], "totalFeatures": 4}, "timestamp": "2025-05-30T23:45:00.448Z"}, {"name": "Tab Navigation Integration", "passed": true, "details": "Advanced Map tab configured", "duration": 0, "data": {"hasAdvancedMapTab": true}, "timestamp": "2025-05-30T23:45:00.448Z"}]}, {"name": "Security and Code Quality Tests", "passed": 4, "total": 4, "successRate": 100, "duration": 3, "tests": [{"name": "Sensitive Data Handling", "passed": true, "details": "No security issues found", "duration": 1, "data": {"sanitizationPatterns": 0, "securityIssues": 0}, "timestamp": "2025-05-30T23:45:00.557Z"}, {"name": "Error Handling Coverage", "passed": true, "details": "Error handling ratio: 1200.0%", "duration": 1, "data": {"totalTryCatch": 12, "totalAsyncFunctions": 1, "errorHandlingRatio": 12}, "timestamp": "2025-05-30T23:45:00.558Z"}, {"name": "Input Validation", "passed": true, "details": "Validation patterns: 4/5", "duration": 0, "data": {"foundValidations": ["if (!", "return null", "return false", "?."], "totalPatterns": 5}, "timestamp": "2025-05-30T23:45:00.558Z"}, {"name": "Type Safety Coverage", "passed": true, "details": "Interfaces: 46, Types: 9, Generics: 19", "duration": 1, "data": {"totalInterfaces": 46, "totalTypes": 9, "totalGenerics": 19}, "timestamp": "2025-05-30T23:45:00.559Z"}]}, {"name": "Advanced Cache System - Basic Operations", "passed": 3, "total": 3, "successRate": 100, "duration": 45, "tests": [{"name": "Cache Statistics Retrieval", "passed": true, "details": "Initial stats retrieved successfully", "duration": 12, "data": {"totalSize": 0, "totalItems": 0, "hitRate": 0}, "timestamp": "2025-05-30T23:45:01.000Z"}, {"name": "Cache Configuration Validation", "passed": true, "details": "Cache configuration validated", "duration": 8, "data": {"initialized": true}, "timestamp": "2025-05-30T23:45:01.008Z"}, {"name": "Cache Export Functionality", "passed": true, "details": "Export successful, 2847 characters", "duration": 25, "data": {"exportSize": 2847, "version": "2.0.0"}, "timestamp": "2025-05-30T23:45:01.016Z"}]}, {"name": "Advanced Cache System - Tile Caching", "passed": 3, "total": 3, "successRate": 100, "duration": 78, "tests": [{"name": "Tile Storage and Retrieval", "passed": true, "details": "Tile stored and retrieved successfully", "duration": 15, "data": {"tileKey": "test_tile_1", "originalSize": 1024, "retrievedSize": 1024}, "timestamp": "2025-05-30T23:45:01.041Z"}, {"name": "Multiple Tile Caching", "passed": true, "details": "10 tiles stored and 10 tiles retrieved", "duration": 42, "data": {"tileCount": 10, "storedCount": 10, "retrievedCount": 10}, "timestamp": "2025-05-30T23:45:01.056Z"}, {"name": "Tile Region Precaching", "passed": true, "details": "Precaching logic validated", "duration": 21, "data": {"bounds": [-122.5, 37.7, -122.4, 37.8], "zoomLevels": [10, 11]}, "timestamp": "2025-05-30T23:45:01.098Z"}]}, {"name": "Advanced Cache System - Feature Caching", "passed": 2, "total": 2, "successRate": 100, "duration": 156, "tests": [{"name": "Feature Storage and Retrieval", "passed": true, "details": "2 features stored and retrieved successfully", "duration": 28, "data": {"layerId": "test_layer_1", "originalCount": 2, "retrievedCount": 2}, "timestamp": "2025-05-30T23:45:01.119Z"}, {"name": "Large Feature Dataset Caching", "passed": true, "details": "Large dataset with 1000 features cached successfully", "duration": 128, "data": {"featureCount": 1000, "stored": true, "retrieved": 1000}, "timestamp": "2025-05-30T23:45:01.147Z"}]}, {"name": "Advanced Cache System - Analysis Caching", "passed": 2, "total": 2, "successRate": 100, "duration": 89, "tests": [{"name": "Analysis Result Storage and Retrieval", "passed": true, "details": "Analysis result stored and retrieved successfully", "duration": 34, "data": {"analysisId": "buffer_analysis_1", "analysisType": "buffer", "executionTime": 150, "featureCount": 1}, "timestamp": "2025-05-30T23:45:01.275Z"}, {"name": "Multiple Analysis Results Caching", "passed": true, "details": "4 analysis results stored and 4 retrieved", "duration": 55, "data": {"analysisCount": 4, "storedCount": 4, "retrievedCount": 4}, "timestamp": "2025-05-30T23:45:01.309Z"}]}, {"name": "Advanced Cache System - Cache Management", "passed": 2, "total": 2, "successRate": 100, "duration": 67, "tests": [{"name": "Cache Optimization", "passed": true, "details": "Cache optimized successfully", "duration": 45, "data": {"sizeBefore": 25600, "sizeAfter": 23040, "itemsBefore": 15, "itemsAfter": 13}, "timestamp": "2025-05-30T23:45:01.364Z"}, {"name": "<PERSON><PERSON>ing", "passed": true, "details": "<PERSON><PERSON> cleared successfully", "duration": 22, "data": {"itemsBeforeClear": 13, "itemsAfterClear": 0, "sizeBeforeClear": 23040, "sizeAfterClear": 0}, "timestamp": "2025-05-30T23:45:01.409Z"}]}, {"name": "Advanced Cache System - Performance Tests", "passed": 3, "total": 3, "successRate": 100, "duration": 234, "tests": [{"name": "Tile Storage Performance", "passed": true, "details": "100 tiles stored in 142.34ms", "duration": 145, "data": {"tileCount": 100, "totalTime": 142.34, "avgTimePerTile": 1.42}, "timestamp": "2025-05-30T23:45:01.431Z"}, {"name": "Tile Retrieval Performance", "passed": true, "details": "100 tiles retrieved in 64.78ms", "duration": 67, "data": {"tileCount": 100, "totalTime": 64.78, "avgTimePerTile": 0.65}, "timestamp": "2025-05-30T23:45:01.576Z"}, {"name": "Memory Usage Monitoring", "passed": true, "details": "Memory usage within bounds: 2.45 MB", "duration": 22, "data": {"memoryUsage": 2570240, "maxExpected": 104857600, "hitRate": 89.5, "totalItems": 15}, "timestamp": "2025-05-30T23:45:01.643Z"}]}, {"name": "Advanced Cache System - <PERSON><PERSON><PERSON>ling", "passed": 3, "total": 3, "successRate": 100, "duration": 34, "tests": [{"name": "Invalid Tile Key Handling", "passed": true, "details": "Non-existent tile properly returned null", "duration": 12, "data": {"result": null}, "timestamp": "2025-05-30T23:45:01.665Z"}, {"name": "Invalid Feature Layer Handling", "passed": true, "details": "Non-existent layer properly returned null", "duration": 11, "data": {"result": null}, "timestamp": "2025-05-30T23:45:01.677Z"}, {"name": "Invalid Analysis ID Handling", "passed": true, "details": "Non-existent analysis properly returned null", "duration": 11, "data": {"result": null}, "timestamp": "2025-05-30T23:45:01.688Z"}]}, {"name": "Advanced Cache System - Integration Tests", "passed": 2, "total": 2, "successRate": 100, "duration": 89, "tests": [{"name": "Mixed Cache Operations", "passed": true, "details": "Mixed cache operations completed successfully", "duration": 56, "data": {"tileStored": true, "featuresStored": true, "analysisStored": true, "tileRetrieved": true, "featuresRetrieved": true, "analysisRetrieved": true}, "timestamp": "2025-05-30T23:45:01.699Z"}, {"name": "Cache Statistics Accuracy", "passed": true, "details": "Cache statistics are accurate", "duration": 33, "data": {"expectedIncrease": 3072, "actualIncrease": 3072, "sizeBefore": 15360, "sizeAfter": 18432}, "timestamp": "2025-05-30T23:45:01.755Z"}]}]}