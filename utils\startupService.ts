/**
 * Startup Service
 * 
 * Handles app initialization and prevents runtime errors
 * related to Expo Updates and other startup issues.
 */

import { Platform } from 'react-native';

interface StartupConfig {
  skipUpdateCheck: boolean;
  enableErrorRecovery: boolean;
  maxRetries: number;
  retryDelay: number;
}

class StartupService {
  private config: StartupConfig;
  private isInitialized = false;
  private initializationPromise: Promise<void> | null = null;

  constructor() {
    this.config = {
      skipUpdateCheck: this.shouldSkipUpdateCheck(),
      enableErrorRecovery: true,
      maxRetries: 3,
      retryDelay: 1000,
    };
  }

  private shouldSkipUpdateCheck(): boolean {
    // Skip update checks in development
    if (process.env.EXPO_PUBLIC_DEV_MODE === 'true') {
      return true;
    }

    // Skip if explicitly disabled
    if (process.env.EXPO_PUBLIC_DISABLE_UPDATES === 'true') {
      return true;
    }

    // Skip on web platform
    if (Platform.OS === 'web') {
      return true;
    }

    return false;
  }

  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    if (this.initializationPromise) {
      return this.initializationPromise;
    }

    this.initializationPromise = this.performInitialization();
    return this.initializationPromise;
  }

  private async performInitialization(): Promise<void> {
    let retries = 0;

    while (retries < this.config.maxRetries) {
      try {
        await this.initializeWithErrorHandling();
        this.isInitialized = true;
        console.log('App startup completed successfully');
        return;
      } catch (error) {
        retries++;
        console.warn(`Startup attempt ${retries} failed:`, error);

        if (retries < this.config.maxRetries) {
          console.log(`Retrying in ${this.config.retryDelay}ms...`);
          await this.delay(this.config.retryDelay);
        } else {
          console.error('All startup attempts failed, continuing with limited functionality');
          // Don't throw - allow app to start with limited functionality
          this.isInitialized = true;
          return;
        }
      }
    }
  }

  private async initializeWithErrorHandling(): Promise<void> {
    try {
      // Initialize core services
      await this.initializeCoreServices();

      // Initialize update service only if enabled
      if (!this.config.skipUpdateCheck) {
        await this.initializeUpdateService();
      } else {
        console.log('Skipping update service initialization (disabled)');
      }

      // Initialize other services
      await this.initializeOtherServices();

    } catch (error) {
      if (this.config.enableErrorRecovery) {
        console.warn('Startup error, attempting recovery:', error);
        await this.attemptErrorRecovery(error);
      } else {
        throw error;
      }
    }
  }

  private async initializeCoreServices(): Promise<void> {
    // Initialize essential services that must work
    console.log('Initializing core services...');
    
    // Add any core service initialization here
    // For example: database, authentication, etc.
  }

  private async initializeUpdateService(): Promise<void> {
    try {
      console.log('Initializing update service...');

      // Check if expo-updates is available before importing
      try {
        // Try to import expo-updates first
        await import('expo-updates');

        // If successful, import and use our update service
        const { updateService } = await import('./updateService');

        // Check if updates are actually enabled
        if (updateService.isUpdatesEnabled()) {
          console.log('Update service enabled, checking for updates...');
          // Perform initial update check with timeout
          await Promise.race([
            updateService.checkForUpdates(),
            this.timeout(5000, 'Update check timeout')
          ]);
        } else {
          console.log('Update service disabled');
        }
      } catch (importError) {
        console.log('expo-updates not available, skipping update service');
      }
    } catch (error) {
      console.warn('Update service initialization failed:', error);
      // Don't throw - updates are not critical for app functionality
    }
  }

  private async initializeOtherServices(): Promise<void> {
    console.log('Initializing additional services...');
    
    // Initialize non-critical services
    // These can fail without preventing app startup
    try {
      // Add other service initializations here
      // For example: analytics, crash reporting, etc.
    } catch (error) {
      console.warn('Non-critical service initialization failed:', error);
      // Don't throw - these services are optional
    }
  }

  private async attemptErrorRecovery(error: any): Promise<void> {
    console.log('Attempting error recovery...');

    // Clear any cached data that might be causing issues
    try {
      if (Platform.OS !== 'web') {
        // Clear update cache if it exists
        const Updates = await import('expo-updates').catch(() => null);
        if (Updates?.default?.clearUpdateCacheExperimentalAsync) {
          await Updates.default.clearUpdateCacheExperimentalAsync();
          console.log('Cleared update cache');
        }
      }
    } catch (clearError) {
      console.warn('Failed to clear cache during recovery:', clearError);
    }

    // Reset configuration to safe defaults
    this.config.skipUpdateCheck = true;
    console.log('Reset to safe configuration');
  }

  private async timeout(ms: number, message: string): Promise<never> {
    return new Promise((_, reject) => {
      setTimeout(() => reject(new Error(message)), ms);
    });
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  public isReady(): boolean {
    return this.isInitialized;
  }

  public getConfig(): StartupConfig {
    return { ...this.config };
  }
}

// Export singleton instance
export const startupService = new StartupService();

// Export hook for React components
export function useStartupService() {
  return {
    initialize: startupService.initialize.bind(startupService),
    isReady: startupService.isReady.bind(startupService),
    getConfig: startupService.getConfig.bind(startupService),
  };
}
