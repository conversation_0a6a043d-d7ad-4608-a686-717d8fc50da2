import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Alert,
  Platform,
  Share,
} from 'react-native';
import { useTheme } from '@/hooks/useTheme';
import {
  X,
  Bookmark,
  Plus,
  Trash2,
  Edit3,
  MapPin,
  Share2,
  Download,
  Upload,
  Star,
  Clock,
  User,
  Globe,
  Navigation,
  Target,
  Save,
  Copy,
  Eye,
  Calendar,
  Tag,
  Search,
  Filter,
  MoreVertical,
} from 'lucide-react-native';

interface MapBookmark {
  id: string;
  name: string;
  description: string;
  extent: {
    latitude: number;
    longitude: number;
    latitudeDelta: number;
    longitudeDelta: number;
  };
  createdAt: string;
  updatedAt: string;
  author: string;
  tags: string[];
  thumbnail?: string;
  isPublic: boolean;
  isFavorite: boolean;
  visitCount: number;
  lastVisited?: string;
  category: 'personal' | 'project' | 'shared' | 'public';
  metadata?: {
    zoom: number;
    bearing?: number;
    pitch?: number;
    visibleLayers: string[];
    notes?: string;
  };
}

interface BookmarkManagerModalProps {
  visible: boolean;
  onClose: () => void;
  bookmarks: MapBookmark[];
  onBookmarkCreate: (bookmark: Omit<MapBookmark, 'id' | 'createdAt' | 'updatedAt' | 'visitCount'>) => void;
  onBookmarkUpdate: (id: string, updates: Partial<MapBookmark>) => void;
  onBookmarkDelete: (id: string) => void;
  onBookmarkNavigate: (bookmark: MapBookmark) => void;
  currentMapExtent: any;
  visibleLayers: string[];
  onImportBookmarks: () => void;
  onExportBookmarks: (bookmarks: MapBookmark[]) => void;
}

const BOOKMARK_CATEGORIES = [
  { key: 'all', label: 'All Bookmarks', icon: Bookmark },
  { key: 'personal', label: 'Personal', icon: User },
  { key: 'project', label: 'Project', icon: MapPin },
  { key: 'shared', label: 'Shared', icon: Share2 },
  { key: 'public', label: 'Public', icon: Globe },
  { key: 'favorites', label: 'Favorites', icon: Star },
];

export const BookmarkManagerModal: React.FC<BookmarkManagerModalProps> = ({
  visible,
  onClose,
  bookmarks,
  onBookmarkCreate,
  onBookmarkUpdate,
  onBookmarkDelete,
  onBookmarkNavigate,
  currentMapExtent,
  visibleLayers,
  onImportBookmarks,
  onExportBookmarks,
}) => {
  const { theme } = useTheme();
  const [activeCategory, setActiveCategory] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [isCreating, setIsCreating] = useState(false);
  const [editingBookmark, setEditingBookmark] = useState<MapBookmark | null>(null);
  const [sortBy, setSortBy] = useState<'name' | 'created' | 'visited' | 'category'>('created');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  const [newBookmark, setNewBookmark] = useState({
    name: '',
    description: '',
    tags: '',
    category: 'personal' as MapBookmark['category'],
    isPublic: false,
  });

  const filteredBookmarks = bookmarks
    .filter(bookmark => {
      const matchesSearch = bookmark.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           bookmark.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           bookmark.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
      
      const matchesCategory = activeCategory === 'all' || 
                             (activeCategory === 'favorites' ? bookmark.isFavorite : bookmark.category === activeCategory);
      
      return matchesSearch && matchesCategory;
    })
    .sort((a, b) => {
      let comparison = 0;
      switch (sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'created':
          comparison = new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
          break;
        case 'visited':
          comparison = (a.visitCount || 0) - (b.visitCount || 0);
          break;
        case 'category':
          comparison = a.category.localeCompare(b.category);
          break;
      }
      return sortOrder === 'asc' ? comparison : -comparison;
    });

  const handleCreateBookmark = useCallback(() => {
    if (!newBookmark.name.trim()) {
      Alert.alert('Error', 'Please enter a bookmark name.');
      return;
    }

    const bookmark: Omit<MapBookmark, 'id' | 'createdAt' | 'updatedAt' | 'visitCount'> = {
      name: newBookmark.name.trim(),
      description: newBookmark.description.trim(),
      extent: currentMapExtent,
      author: 'Current User', // This would come from auth context
      tags: newBookmark.tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0),
      isPublic: newBookmark.isPublic,
      isFavorite: false,
      category: newBookmark.category,
      lastVisited: new Date().toISOString(),
      metadata: {
        zoom: currentMapExtent?.latitudeDelta ? Math.round(1 / currentMapExtent.latitudeDelta * 360) : 10,
        visibleLayers: visibleLayers,
      },
    };

    onBookmarkCreate(bookmark);
    setIsCreating(false);
    setNewBookmark({
      name: '',
      description: '',
      tags: '',
      category: 'personal',
      isPublic: false,
    });
    Alert.alert('Success', 'Bookmark created successfully!');
  }, [newBookmark, currentMapExtent, visibleLayers, onBookmarkCreate]);

  const handleDeleteBookmark = useCallback((bookmark: MapBookmark) => {
    Alert.alert(
      'Delete Bookmark',
      `Are you sure you want to delete "${bookmark.name}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => onBookmarkDelete(bookmark.id),
        },
      ]
    );
  }, [onBookmarkDelete]);

  const handleShareBookmark = useCallback(async (bookmark: MapBookmark) => {
    try {
      const shareUrl = `https://fieldsynpro.app/map?bookmark=${bookmark.id}`;
      await Share.share({
        message: `Check out this location: ${bookmark.name}\n${bookmark.description}\n\n${shareUrl}`,
        url: shareUrl,
        title: bookmark.name,
      });
    } catch (error) {
      console.error('Error sharing bookmark:', error);
    }
  }, []);

  const handleToggleFavorite = useCallback((bookmark: MapBookmark) => {
    onBookmarkUpdate(bookmark.id, { isFavorite: !bookmark.isFavorite });
  }, [onBookmarkUpdate]);

  const handleExportSelected = useCallback(() => {
    const selectedBookmarks = filteredBookmarks.filter(b => b.category === activeCategory || activeCategory === 'all');
    onExportBookmarks(selectedBookmarks);
  }, [filteredBookmarks, activeCategory, onExportBookmarks]);

  const renderBookmarkItem = (bookmark: MapBookmark) => (
    <View key={bookmark.id} style={[styles.bookmarkItem, { backgroundColor: theme.colors.background, borderColor: theme.colors.border }]}>
      <TouchableOpacity
        style={styles.bookmarkContent}
        onPress={() => {
          onBookmarkNavigate(bookmark);
          onBookmarkUpdate(bookmark.id, { 
            visitCount: (bookmark.visitCount || 0) + 1,
            lastVisited: new Date().toISOString(),
          });
        }}
      >
        <View style={styles.bookmarkHeader}>
          <View style={styles.bookmarkInfo}>
            <Text style={[styles.bookmarkName, { color: theme.colors.text }]}>
              {bookmark.name}
            </Text>
            <Text style={[styles.bookmarkDescription, { color: theme.colors.muted }]} numberOfLines={2}>
              {bookmark.description}
            </Text>
          </View>
          
          <View style={styles.bookmarkMeta}>
            <View style={styles.categoryBadge}>
              <Text style={[styles.categoryText, { color: theme.colors.primary }]}>
                {bookmark.category.toUpperCase()}
              </Text>
            </View>
            {bookmark.isFavorite && (
              <Star size={14} color={theme.colors.warning} fill={theme.colors.warning} />
            )}
          </View>
        </View>

        <View style={styles.bookmarkDetails}>
          <View style={styles.bookmarkStats}>
            <View style={styles.statItem}>
              <Clock size={12} color={theme.colors.muted} />
              <Text style={[styles.statText, { color: theme.colors.muted }]}>
                {new Date(bookmark.createdAt).toLocaleDateString()}
              </Text>
            </View>
            <View style={styles.statItem}>
              <Eye size={12} color={theme.colors.muted} />
              <Text style={[styles.statText, { color: theme.colors.muted }]}>
                {bookmark.visitCount || 0} visits
              </Text>
            </View>
            {bookmark.metadata?.zoom && (
              <View style={styles.statItem}>
                <Target size={12} color={theme.colors.muted} />
                <Text style={[styles.statText, { color: theme.colors.muted }]}>
                  Zoom {bookmark.metadata.zoom}
                </Text>
              </View>
            )}
          </View>

          {bookmark.tags.length > 0 && (
            <View style={styles.tagsContainer}>
              {bookmark.tags.slice(0, 3).map(tag => (
                <View key={tag} style={[styles.tag, { backgroundColor: theme.colors.primary + '20' }]}>
                  <Text style={[styles.tagText, { color: theme.colors.primary }]}>
                    {tag}
                  </Text>
                </View>
              ))}
              {bookmark.tags.length > 3 && (
                <Text style={[styles.moreTagsText, { color: theme.colors.muted }]}>
                  +{bookmark.tags.length - 3} more
                </Text>
              )}
            </View>
          )}
        </View>
      </TouchableOpacity>

      <View style={styles.bookmarkActions}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => handleToggleFavorite(bookmark)}
        >
          <Star 
            size={16} 
            color={bookmark.isFavorite ? theme.colors.warning : theme.colors.muted}
            fill={bookmark.isFavorite ? theme.colors.warning : 'none'}
          />
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => setEditingBookmark(bookmark)}
        >
          <Edit3 size={16} color={theme.colors.muted} />
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => handleShareBookmark(bookmark)}
        >
          <Share2 size={16} color={theme.colors.muted} />
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => handleDeleteBookmark(bookmark)}
        >
          <Trash2 size={16} color={theme.colors.destructive} />
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderCreateForm = () => (
    <View style={styles.createForm}>
      <View style={styles.formHeader}>
        <Text style={[styles.formTitle, { color: theme.colors.text }]}>New Bookmark</Text>
        <TouchableOpacity onPress={() => setIsCreating(false)}>
          <X size={20} color={theme.colors.text} />
        </TouchableOpacity>
      </View>

      <View style={styles.formContent}>
        <View style={styles.inputGroup}>
          <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Name *</Text>
          <TextInput
            style={[styles.textInput, { backgroundColor: theme.colors.card, color: theme.colors.text }]}
            value={newBookmark.name}
            onChangeText={(text) => setNewBookmark(prev => ({ ...prev, name: text }))}
            placeholder="Enter bookmark name..."
            placeholderTextColor={theme.colors.muted}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Description</Text>
          <TextInput
            style={[styles.textAreaInput, { backgroundColor: theme.colors.card, color: theme.colors.text }]}
            value={newBookmark.description}
            onChangeText={(text) => setNewBookmark(prev => ({ ...prev, description: text }))}
            placeholder="Enter description..."
            placeholderTextColor={theme.colors.muted}
            multiline
            numberOfLines={3}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Tags</Text>
          <TextInput
            style={[styles.textInput, { backgroundColor: theme.colors.card, color: theme.colors.text }]}
            value={newBookmark.tags}
            onChangeText={(text) => setNewBookmark(prev => ({ ...prev, tags: text }))}
            placeholder="Enter tags separated by commas..."
            placeholderTextColor={theme.colors.muted}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Category</Text>
          <View style={styles.categorySelector}>
            {BOOKMARK_CATEGORIES.slice(1, -1).map(category => (
              <TouchableOpacity
                key={category.key}
                style={[
                  styles.categoryOption,
                  {
                    backgroundColor: newBookmark.category === category.key ? theme.colors.primary : theme.colors.card,
                    borderColor: theme.colors.border,
                  }
                ]}
                onPress={() => setNewBookmark(prev => ({ ...prev, category: category.key as any }))}
              >
                <category.icon 
                  size={14} 
                  color={newBookmark.category === category.key ? 'white' : theme.colors.text} 
                />
                <Text style={[
                  styles.categoryOptionText,
                  { color: newBookmark.category === category.key ? 'white' : theme.colors.text }
                ]}>
                  {category.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <View style={styles.formActions}>
          <TouchableOpacity
            style={[styles.cancelButton, { backgroundColor: theme.colors.muted }]}
            onPress={() => setIsCreating(false)}
          >
            <Text style={[styles.cancelButtonText, { color: 'white' }]}>Cancel</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.createButton, { backgroundColor: theme.colors.primary }]}
            onPress={handleCreateBookmark}
          >
            <Save size={16} color="white" />
            <Text style={[styles.createButtonText, { color: 'white' }]}>Save Bookmark</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={[styles.modalContainer, { backgroundColor: theme.colors.background }]}>
        {/* Header */}
        <View style={[styles.modalHeader, { backgroundColor: theme.colors.card, borderBottomColor: theme.colors.border }]}>
          <Text style={[styles.modalTitle, { color: theme.colors.text }]}>Bookmarks</Text>
          <View style={styles.headerActions}>
            <TouchableOpacity
              style={[styles.headerButton, { backgroundColor: theme.colors.muted }]}
              onPress={onImportBookmarks}
            >
              <Upload size={18} color="white" />
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.headerButton, { backgroundColor: theme.colors.info }]}
              onPress={handleExportSelected}
            >
              <Download size={18} color="white" />
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.headerButton, { backgroundColor: theme.colors.primary }]}
              onPress={() => setIsCreating(true)}
            >
              <Plus size={18} color="white" />
            </TouchableOpacity>
            <TouchableOpacity onPress={onClose}>
              <X size={24} color={theme.colors.text} />
            </TouchableOpacity>
          </View>
        </View>

        {isCreating ? (
          renderCreateForm()
        ) : (
          <>
            {/* Controls */}
            <View style={[styles.controlsContainer, { backgroundColor: theme.colors.card, borderBottomColor: theme.colors.border }]}>
              {/* Search */}
              <View style={[styles.searchContainer, { backgroundColor: theme.colors.background }]}>
                <Search size={16} color={theme.colors.muted} />
                <TextInput
                  style={[styles.searchInput, { color: theme.colors.text }]}
                  value={searchQuery}
                  onChangeText={setSearchQuery}
                  placeholder="Search bookmarks..."
                  placeholderTextColor={theme.colors.muted}
                />
              </View>

              {/* Categories */}
              <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.categoriesScroll}>
                {BOOKMARK_CATEGORIES.map(category => (
                  <TouchableOpacity
                    key={category.key}
                    style={[
                      styles.categoryTab,
                      {
                        backgroundColor: activeCategory === category.key ? theme.colors.primary : 'transparent',
                      }
                    ]}
                    onPress={() => setActiveCategory(category.key)}
                  >
                    <category.icon
                      size={16}
                      color={activeCategory === category.key ? 'white' : theme.colors.text}
                    />
                    <Text style={[
                      styles.categoryTabText,
                      { color: activeCategory === category.key ? 'white' : theme.colors.text }
                    ]}>
                      {category.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>

            {/* Bookmarks List */}
            <ScrollView style={styles.bookmarksList}>
              {filteredBookmarks.length === 0 ? (
                <View style={styles.emptyState}>
                  <Bookmark size={48} color={theme.colors.muted} />
                  <Text style={[styles.emptyStateText, { color: theme.colors.muted }]}>
                    {searchQuery ? 'No bookmarks match your search' : 'No bookmarks yet'}
                  </Text>
                  {!searchQuery && (
                    <TouchableOpacity
                      style={[styles.emptyStateButton, { backgroundColor: theme.colors.primary }]}
                      onPress={() => setIsCreating(true)}
                    >
                      <Plus size={16} color="white" />
                      <Text style={[styles.emptyStateButtonText, { color: 'white' }]}>
                        Create Your First Bookmark
                      </Text>
                    </TouchableOpacity>
                  )}
                </View>
              ) : (
                filteredBookmarks.map(renderBookmarkItem)
              )}
            </ScrollView>
          </>
        )}
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  headerButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
  },
  controlsContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    marginBottom: 12,
    gap: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 14,
    fontFamily: 'Inter-Regular',
  },
  categoriesScroll: {
    flexDirection: 'row',
  },
  categoryTab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginRight: 8,
    borderRadius: 20,
    gap: 6,
  },
  categoryTabText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },
  bookmarksList: {
    flex: 1,
    paddingHorizontal: 16,
  },
  bookmarkItem: {
    borderRadius: 12,
    borderWidth: 1,
    marginVertical: 6,
    overflow: 'hidden',
  },
  bookmarkContent: {
    padding: 16,
  },
  bookmarkHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  bookmarkInfo: {
    flex: 1,
    marginRight: 12,
  },
  bookmarkName: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 4,
  },
  bookmarkDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    lineHeight: 20,
  },
  bookmarkMeta: {
    alignItems: 'flex-end',
    gap: 6,
  },
  categoryBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    backgroundColor: '#F3F4F6',
  },
  categoryText: {
    fontSize: 10,
    fontFamily: 'Inter-Bold',
  },
  bookmarkDetails: {
    gap: 8,
  },
  bookmarkStats: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  statText: {
    fontSize: 11,
    fontFamily: 'Inter-Regular',
  },
  tagsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    gap: 6,
  },
  tag: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
  },
  tagText: {
    fontSize: 10,
    fontFamily: 'Inter-Medium',
  },
  moreTagsText: {
    fontSize: 10,
    fontFamily: 'Inter-Regular',
  },
  bookmarkActions: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#F9FAFB',
    gap: 12,
  },
  actionButton: {
    padding: 6,
  },
  createForm: {
    flex: 1,
  },
  formHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  formTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
  },
  formContent: {
    flex: 1,
    padding: 20,
  },
  inputGroup: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 6,
  },
  textInput: {
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderRadius: 8,
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  textAreaInput: {
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderRadius: 8,
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    textAlignVertical: 'top',
  },
  categorySelector: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  categoryOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    borderWidth: 1,
    gap: 6,
  },
  categoryOptionText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },
  formActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 12,
    marginTop: 20,
  },
  cancelButton: {
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 6,
  },
  cancelButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
  },
  createButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 6,
    gap: 6,
  },
  createButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
  },
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 48,
    gap: 16,
  },
  emptyStateText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
  },
  emptyStateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  emptyStateButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
  },
});
