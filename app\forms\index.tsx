import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  RefreshControl,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { useTheme } from '@/hooks/useTheme';
import {
  Plus,
  FileText,
  Calendar,
  Users,
  Edit3,
  Copy,
  Trash2,
  Eye,
  Download,
  Share,
  MoreVertical,
} from 'lucide-react-native';
import { FormSchema } from './builder';

interface FormCardProps {
  form: FormSchema;
  onEdit: () => void;
  onPreview: () => void;
  onDuplicate: () => void;
  onDelete: () => void;
  onShare: () => void;
}

function FormCard({ form, onEdit, onPreview, onDuplicate, onDelete, onShare }: FormCardProps) {
  const { theme } = useTheme();
  const [showActions, setShowActions] = useState(false);

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString();
  };

  const getFieldTypeSummary = (fields: any[]) => {
    const types = fields.reduce((acc, field) => {
      acc[field.type] = (acc[field.type] || 0) + 1;
      return acc;
    }, {});
    
    const summary = Object.entries(types)
      .slice(0, 3)
      .map(([type, count]) => `${count} ${type}`)
      .join(', ');
    
    return summary || 'No fields';
  };

  return (
    <View style={[styles.formCard, { backgroundColor: theme.colors.card }]}>
      <View style={styles.cardHeader}>
        <View style={styles.cardHeaderLeft}>
          <View style={[styles.formIcon, { backgroundColor: theme.colors.primary + '15' }]}>
            <FileText size={24} color={theme.colors.primary} />
          </View>
          <View style={styles.formInfo}>
            <Text style={[styles.formName, { color: theme.colors.text }]}>{form.name}</Text>
            <Text style={[styles.formDescription, { color: theme.colors.muted }]} numberOfLines={1}>
              {form.description || 'No description'}
            </Text>
          </View>
        </View>
        <TouchableOpacity onPress={() => setShowActions(!showActions)}>
          <MoreVertical size={20} color={theme.colors.muted} />
        </TouchableOpacity>
      </View>

      <View style={styles.cardContent}>
        <View style={styles.statsRow}>
          <View style={styles.statItem}>
            <Text style={[styles.statValue, { color: theme.colors.text }]}>{form.fields.length}</Text>
            <Text style={[styles.statLabel, { color: theme.colors.muted }]}>Fields</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={[styles.statValue, { color: theme.colors.text }]}>v{form.version}</Text>
            <Text style={[styles.statLabel, { color: theme.colors.muted }]}>Version</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={[styles.statValue, { color: theme.colors.text }]}>{formatDate(form.createdAt)}</Text>
            <Text style={[styles.statLabel, { color: theme.colors.muted }]}>Created</Text>
          </View>
        </View>

        <Text style={[styles.fieldSummary, { color: theme.colors.muted }]}>
          {getFieldTypeSummary(form.fields)}
        </Text>
      </View>

      {showActions && (
        <View style={[styles.actionsPanel, { backgroundColor: theme.colors.background }]}>
          <TouchableOpacity style={styles.actionButton} onPress={onPreview}>
            <Eye size={16} color={theme.colors.text} />
            <Text style={[styles.actionText, { color: theme.colors.text }]}>Preview</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.actionButton} onPress={onEdit}>
            <Edit3 size={16} color={theme.colors.text} />
            <Text style={[styles.actionText, { color: theme.colors.text }]}>Edit</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.actionButton} onPress={onDuplicate}>
            <Copy size={16} color={theme.colors.text} />
            <Text style={[styles.actionText, { color: theme.colors.text }]}>Duplicate</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.actionButton} onPress={onShare}>
            <Share size={16} color={theme.colors.text} />
            <Text style={[styles.actionText, { color: theme.colors.text }]}>Share</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.actionButton} onPress={onDelete}>
            <Trash2 size={16} color={theme.colors.error} />
            <Text style={[styles.actionText, { color: theme.colors.error }]}>Delete</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
}

interface EmptyStateProps {
  onCreateForm: () => void;
}

function EmptyState({ onCreateForm }: EmptyStateProps) {
  const { theme } = useTheme();

  return (
    <View style={styles.emptyState}>
      <View style={[styles.emptyIcon, { backgroundColor: theme.colors.primary + '15' }]}>
        <FileText size={64} color={theme.colors.primary} />
      </View>
      <Text style={[styles.emptyTitle, { color: theme.colors.text }]}>
        No forms created yet
      </Text>
      <Text style={[styles.emptyDescription, { color: theme.colors.muted }]}>
        Create your first custom data collection form to get started with field surveys and data gathering.
      </Text>
      <TouchableOpacity
        style={[styles.createFirstButton, { backgroundColor: theme.colors.primary }]}
        onPress={onCreateForm}
      >
        <Plus size={20} color="white" />
        <Text style={styles.createFirstButtonText}>Create Your First Form</Text>
      </TouchableOpacity>
    </View>
  );
}

export default function FormsManagementScreen() {
  const { theme } = useTheme();
  const [forms, setForms] = useState<FormSchema[]>([]);
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(true);

  const loadForms = async () => {
    try {
      if (Platform.OS === 'web') {
        const savedForms = localStorage.getItem('fieldsync_forms');
        if (savedForms) {
          setForms(JSON.parse(savedForms));
        }
      } else {
        // For native platforms, you would load from your database
        console.log('Loading forms from native storage');
      }
    } catch (error) {
      console.error('Error loading forms:', error);
    } finally {
      setLoading(false);
    }
  };

  const saveForms = async (updatedForms: FormSchema[]) => {
    try {
      if (Platform.OS === 'web') {
        localStorage.setItem('fieldsync_forms', JSON.stringify(updatedForms));
      }
      setForms(updatedForms);
    } catch (error) {
      console.error('Error saving forms:', error);
    }
  };

  useEffect(() => {
    loadForms();
  }, []);

  const onRefresh = async () => {
    setRefreshing(true);
    await loadForms();
    setRefreshing(false);
  };

  const createNewForm = () => {
    router.push('/forms/builder');
  };

  const editForm = (form: FormSchema) => {
    // In a real app, you would pass the form data to the builder
    Alert.alert('Edit Form', `Editing "${form.name}" would open the form builder with existing data`);
  };

  const previewForm = (form: FormSchema) => {
    router.push({
      pathname: '/forms/preview',
      params: { 
        formData: JSON.stringify({
          name: form.name,
          description: form.description,
          fields: form.fields
        })
      }
    });
  };

  const duplicateForm = async (form: FormSchema) => {
    const duplicatedForm: FormSchema = {
      ...form,
      id: `form_${Date.now()}`,
      name: `${form.name} (Copy)`,
      version: 1,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    };

    const updatedForms = [...forms, duplicatedForm];
    await saveForms(updatedForms);
    
    Alert.alert('Success', 'Form duplicated successfully');
  };

  const deleteForm = (form: FormSchema) => {
    Alert.alert(
      'Delete Form',
      `Are you sure you want to delete "${form.name}"? This action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            const updatedForms = forms.filter(f => f.id !== form.id);
            await saveForms(updatedForms);
            Alert.alert('Success', 'Form deleted successfully');
          }
        }
      ]
    );
  };

  const shareForm = (form: FormSchema) => {
    const formData = JSON.stringify(form, null, 2);
    
    if (Platform.OS === 'web') {
      // Create a downloadable file
      const blob = new Blob([formData], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${form.name.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_form.json`;
      a.click();
      URL.revokeObjectURL(url);
    } else {
      Alert.alert('Share Form', 'Form export functionality would be implemented here');
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: theme.colors.text }]}>Loading forms...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={styles.header}>
        <View>
          <Text style={[styles.headerTitle, { color: theme.colors.text }]}>Forms</Text>
          <Text style={[styles.headerSubtitle, { color: theme.colors.muted }]}>
            📝 Manage your custom data collection forms
          </Text>
        </View>
        <TouchableOpacity
          style={[styles.createButton, { backgroundColor: theme.colors.primary }]}
          onPress={createNewForm}
        >
          <Plus size={20} color="white" />
          <Text style={styles.createButtonText}>New Form</Text>
        </TouchableOpacity>
      </View>

      {/* Stats Bar */}
      {forms.length > 0 && (
        <View style={[styles.statsBar, { backgroundColor: theme.colors.card }]}>
          <View style={styles.statContainer}>
            <Text style={[styles.statValue, { color: theme.colors.text }]}>{forms.length}</Text>
            <Text style={[styles.statLabel, { color: theme.colors.muted }]}>Total Forms</Text>
          </View>
          <View style={styles.statContainer}>
            <Text style={[styles.statValue, { color: theme.colors.text }]}>
              {forms.reduce((sum, form) => sum + form.fields.length, 0)}
            </Text>
            <Text style={[styles.statLabel, { color: theme.colors.muted }]}>Total Fields</Text>
          </View>
          <View style={styles.statContainer}>
            <Text style={[styles.statValue, { color: theme.colors.text }]}>
              {forms.filter(form => form.createdAt > Date.now() - 7 * 24 * 60 * 60 * 1000).length}
            </Text>
            <Text style={[styles.statLabel, { color: theme.colors.muted }]}>This Week</Text>
          </View>
        </View>
      )}

      {/* Forms List */}
      {forms.length === 0 ? (
        <EmptyState onCreateForm={createNewForm} />
      ) : (
        <FlatList
          data={forms}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => (
            <FormCard
              form={item}
              onEdit={() => editForm(item)}
              onPreview={() => previewForm(item)}
              onDuplicate={() => duplicateForm(item)}
              onDelete={() => deleteForm(item)}
              onShare={() => shareForm(item)}
            />
          )}
          contentContainerStyle={styles.listContainer}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[theme.colors.primary]}
              tintColor={theme.colors.primary}
            />
          }
          showsVerticalScrollIndicator={false}
        />
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  headerSubtitle: {
    fontSize: 14,
    marginTop: 4,
  },
  createButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    gap: 8,
  },
  createButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  statsBar: {
    flexDirection: 'row',
    marginHorizontal: 20,
    marginBottom: 20,
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  statContainer: {
    flex: 1,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    textAlign: 'center',
  },
  listContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  formCard: {
    borderRadius: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
    overflow: 'hidden',
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
  },
  cardHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  formIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  formInfo: {
    flex: 1,
  },
  formName: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  formDescription: {
    fontSize: 14,
    lineHeight: 18,
  },
  cardContent: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  statsRow: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  fieldSummary: {
    fontSize: 12,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  actionsPanel: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginRight: 16,
    marginBottom: 8,
    gap: 6,
  },
  actionText: {
    fontSize: 14,
    fontWeight: '500',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyIcon: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 12,
    textAlign: 'center',
  },
  emptyDescription: {
    fontSize: 16,
    lineHeight: 24,
    textAlign: 'center',
    marginBottom: 32,
  },
  createFirstButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderRadius: 16,
    gap: 12,
  },
  createFirstButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
  },
});
