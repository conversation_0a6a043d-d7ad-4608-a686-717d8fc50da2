import { useEffect, useState } from 'react';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { Platform } from 'react-native';
import { useFonts, Inter_400Regular, Inter_500Medium, Inter_600SemiBold, Inter_700Bold } from '@expo-google-fonts/inter';
import * as SplashScreen from 'expo-splash-screen';
import { ThemeProvider } from '@/providers/ThemeProvider';
import { SettingsProvider } from '@/providers/SettingsProvider';
import { ConnectionProvider } from '@/providers/ConnectionProvider';
import { DatabaseProvider } from '@/providers/DatabaseProvider';
import { AuthProvider } from '@/providers/AuthProvider';
import { ErrorBoundary } from '@/components/ErrorBoundary';
import { AuthRedirect } from '@/components/AuthRedirect';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { startupService } from '@/utils/startupService';
import { globalErrorHandler } from '@/utils/errorHandler';
import { initializeScreens, getSafeScreenOptions } from '@/utils/screenUtils';

// Import professional map styles for web
if (Platform.OS === 'web') {
  require('@/components/map/professionalMapStyles.css');
}

// Apply Expo Updates patch for mobile platforms
if (Platform.OS !== 'web') {
  require('@/patches/expo-updates-patch.js');
}

// Apply react-native-screens patch for proper navigation
if (Platform.OS !== 'web') {
  require('@/patches/react-native-screens-patch.js');
}

// Initialize react-native-screens for proper navigation
initializeScreens();

// Keep the splash screen visible until fonts are loaded
SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  const [fontsLoaded, fontError] = useFonts({
    'Inter-Regular': Inter_400Regular,
    'Inter-Medium': Inter_500Medium,
    'Inter-SemiBold': Inter_600SemiBold,
    'Inter-Bold': Inter_700Bold,
  });

  const [startupComplete, setStartupComplete] = useState(false);

  useEffect(() => {
    // Initialize startup service to handle update-related errors
    const initializeApp = async () => {
      try {
        await startupService.initialize();
        setStartupComplete(true);
      } catch (error) {
        console.warn('Startup service failed, continuing anyway:', error);
        setStartupComplete(true); // Continue even if startup fails
      }
    };

    initializeApp();
  }, []);

  useEffect(() => {
    if ((fontsLoaded || fontError) && startupComplete) {
      SplashScreen.hideAsync();
    }
  }, [fontsLoaded, fontError, startupComplete]);

  // Return null to keep splash screen visible while fonts load and app initializes
  if ((!fontsLoaded && !fontError) || !startupComplete) {
    return null;
  }

  return (
    <ErrorBoundary>
      <GestureHandlerRootView style={{ flex: 1 }}>
        <SettingsProvider>
          <ThemeProvider>
            <ConnectionProvider>
              <DatabaseProvider>
                <AuthProvider>
                  <AuthRedirect />
                  <Stack screenOptions={getSafeScreenOptions()}>
                    <Stack.Screen name="(auth)" options={getSafeScreenOptions()} />
                    <Stack.Screen name="(tabs)" options={getSafeScreenOptions()} />
                    <Stack.Screen name="diagnostics" options={getSafeScreenOptions()} />
                    <Stack.Screen name="+not-found" options={{ title: 'Oops!', headerShown: false }} />
                  </Stack>
                  <StatusBar style="auto" />
                </AuthProvider>
              </DatabaseProvider>
            </ConnectionProvider>
          </ThemeProvider>
        </SettingsProvider>
      </GestureHandlerRootView>
    </ErrorBoundary>
  );
}
