import { useEffect } from 'react';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { Platform } from 'react-native';
import { useFonts, Inter_400Regular, Inter_500Medium, Inter_600SemiBold, Inter_700Bold } from '@expo-google-fonts/inter';
import * as SplashScreen from 'expo-splash-screen';
import { ThemeProvider } from '@/providers/ThemeProvider';
import { SettingsProvider } from '@/providers/SettingsProvider';
import { ConnectionProvider } from '@/providers/ConnectionProvider';
import { DatabaseProvider } from '@/providers/DatabaseProvider';
import { AuthProvider } from '@/providers/AuthProvider';
import { ErrorBoundary } from '@/components/ErrorBoundary';
import { AuthRedirect } from '@/components/AuthRedirect';
import { GestureHandlerRootView } from 'react-native-gesture-handler';

// Import professional map styles for web
if (Platform.OS === 'web') {
  require('@/components/map/professionalMapStyles.css');
}

// Keep the splash screen visible until fonts are loaded
SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  const [fontsLoaded, fontError] = useFonts({
    'Inter-Regular': Inter_400Regular,
    'Inter-Medium': Inter_500Medium,
    'Inter-SemiBold': Inter_600SemiBold,
    'Inter-Bold': Inter_700Bold,
  });

  useEffect(() => {
    if (fontsLoaded || fontError) {
      SplashScreen.hideAsync();
    }
  }, [fontsLoaded, fontError]);

  // Return null to keep splash screen visible while fonts load
  if (!fontsLoaded && !fontError) {
    return null;
  }

  return (
    <ErrorBoundary>
      <GestureHandlerRootView style={{ flex: 1 }}>
        <SettingsProvider>
          <ThemeProvider>
            <ConnectionProvider>
              <DatabaseProvider>
                <AuthProvider>
                  <AuthRedirect />
                  <Stack screenOptions={{ headerShown: false }}>
                    <Stack.Screen name="(auth)" options={{ headerShown: false }} />
                    <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
                    <Stack.Screen name="diagnostics" options={{ headerShown: false }} />
                    <Stack.Screen name="+not-found" options={{ title: 'Oops!' }} />
                  </Stack>
                  <StatusBar style="auto" />
                </AuthProvider>
              </DatabaseProvider>
            </ConnectionProvider>
          </ThemeProvider>
        </SettingsProvider>
      </GestureHandlerRootView>
    </ErrorBoundary>
  );
}
