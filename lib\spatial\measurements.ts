/**
 * Enhanced Measurement Utilities for FieldSyncPro
 * 
 * Provides accurate measurement capabilities for distance, area, bearing,
 * elevation, and other spatial calculations with support for multiple
 * coordinate systems and units.
 */

export interface Coordinate {
  latitude: number;
  longitude: number;
  elevation?: number;
}

export interface MeasurementOptions {
  units?: 'metric' | 'imperial' | 'nautical';
  precision?: number;
  includeElevation?: boolean;
  coordinateSystem?: string;
  geoidModel?: 'WGS84' | 'NAD83' | 'GRS80';
}

export interface DistanceMeasurement {
  distance: number;
  unit: string;
  bearing: number;
  coordinates: Coordinate[];
  segments?: Array<{
    distance: number;
    bearing: number;
    from: Coordinate;
    to: Coordinate;
  }>;
  totalDistance: number;
  metadata: {
    measurementType: 'distance';
    calculationMethod: 'haversine' | 'vincenty' | 'planar';
    accuracy: number;
    timestamp: string;
  };
}

export interface AreaMeasurement {
  area: number;
  unit: string;
  perimeter: number;
  perimeterUnit: string;
  coordinates: Coordinate[];
  centroid: Coordinate;
  metadata: {
    measurementType: 'area';
    calculationMethod: 'shoelace' | 'spherical' | 'planar';
    isValid: boolean;
    selfIntersecting: boolean;
    timestamp: string;
  };
}

export interface BearingMeasurement {
  bearing: number;
  backBearing: number;
  distance: number;
  distanceUnit: string;
  coordinates: [Coordinate, Coordinate];
  metadata: {
    measurementType: 'bearing';
    calculationMethod: 'spherical';
    magneticDeclination?: number;
    timestamp: string;
  };
}

export interface ElevationProfile {
  coordinates: Coordinate[];
  elevations: number[];
  distances: number[];
  totalDistance: number;
  elevationGain: number;
  elevationLoss: number;
  minElevation: number;
  maxElevation: number;
  grade: number[];
  metadata: {
    measurementType: 'elevation';
    elevationSource: 'gps' | 'dem' | 'barometric' | 'manual';
    accuracy: number;
    timestamp: string;
  };
}

export interface MeasurementResult {
  id: string;
  type: 'distance' | 'area' | 'bearing' | 'elevation' | 'perimeter';
  value: number;
  unit: string;
  coordinates: Coordinate[];
  timestamp: string;
  properties?: Record<string, any>;
  metadata?: any;
}

// Earth parameters for different geodetic systems
const EARTH_PARAMETERS = {
  WGS84: {
    a: 6378137.0, // Semi-major axis (meters)
    f: 1 / 298.257223563, // Flattening
    b: 6356752.314245, // Semi-minor axis (meters)
  },
  GRS80: {
    a: 6378137.0,
    f: 1 / 298.257222101,
    b: 6356752.314140,
  },
  NAD83: {
    a: 6378137.0,
    f: 1 / 298.257222101,
    b: 6356752.314140,
  },
};

// Unit conversion factors
const DISTANCE_UNITS = {
  metric: {
    meters: 1,
    kilometers: 1000,
    centimeters: 0.01,
    millimeters: 0.001,
  },
  imperial: {
    feet: 0.3048,
    yards: 0.9144,
    miles: 1609.344,
    inches: 0.0254,
  },
  nautical: {
    'nautical miles': 1852,
    'nautical yards': 1.853,
    fathoms: 1.8288,
  },
};

const AREA_UNITS = {
  metric: {
    'square meters': 1,
    'square kilometers': 1000000,
    hectares: 10000,
    'square centimeters': 0.0001,
  },
  imperial: {
    'square feet': 0.092903,
    'square yards': 0.836127,
    'square miles': 2589988.110336,
    acres: 4046.8564224,
  },
};

const DEG_TO_RAD = Math.PI / 180;
const RAD_TO_DEG = 180 / Math.PI;

/**
 * Enhanced Measurement Calculator
 */
export class EnhancedMeasurements {
  private options: MeasurementOptions;
  private earthParams: typeof EARTH_PARAMETERS.WGS84;

  constructor(options: MeasurementOptions = {}) {
    this.options = {
      units: 'metric',
      precision: 6,
      includeElevation: false,
      coordinateSystem: 'WGS84',
      geoidModel: 'WGS84',
      ...options,
    };

    this.earthParams = EARTH_PARAMETERS[this.options.geoidModel!];
  }

  /**
   * Calculate distance between multiple points
   */
  calculateDistance(coordinates: Coordinate[], options: Partial<MeasurementOptions> = {}): DistanceMeasurement {
    const opts = { ...this.options, ...options };
    
    if (coordinates.length < 2) {
      throw new Error('At least 2 coordinates required for distance measurement');
    }

    const segments: DistanceMeasurement['segments'] = [];
    let totalDistance = 0;
    let totalBearing = 0;

    for (let i = 0; i < coordinates.length - 1; i++) {
      const from = coordinates[i];
      const to = coordinates[i + 1];
      
      const segmentDistance = this.haversineDistance(from, to);
      const segmentBearing = this.calculateBearing(from, to);
      
      segments!.push({
        distance: segmentDistance,
        bearing: segmentBearing,
        from,
        to,
      });
      
      totalDistance += segmentDistance;
      totalBearing += segmentBearing;
    }

    // Average bearing for multi-segment lines
    const averageBearing = segments!.length > 1 ? totalBearing / segments!.length : segments![0].bearing;
    
    // Convert to requested units
    const unit = this.getDistanceUnit(opts.units!);
    const convertedDistance = this.convertDistance(totalDistance, 'meters', unit);

    return {
      distance: this.roundToPrecision(convertedDistance, opts.precision!),
      unit,
      bearing: this.roundToPrecision(averageBearing, 1),
      coordinates,
      segments,
      totalDistance: this.roundToPrecision(convertedDistance, opts.precision!),
      metadata: {
        measurementType: 'distance',
        calculationMethod: 'haversine',
        accuracy: 0.5, // Typical GPS accuracy in meters
        timestamp: new Date().toISOString(),
      },
    };
  }

  /**
   * Calculate area of a polygon
   */
  calculateArea(coordinates: Coordinate[], options: Partial<MeasurementOptions> = {}): AreaMeasurement {
    const opts = { ...this.options, ...options };
    
    if (coordinates.length < 3) {
      throw new Error('At least 3 coordinates required for area measurement');
    }

    // Ensure polygon is closed
    const coords = [...coordinates];
    if (!this.coordinatesEqual(coords[0], coords[coords.length - 1])) {
      coords.push(coords[0]);
    }

    // Calculate area using spherical excess formula for accuracy
    const area = this.sphericalArea(coords);
    const perimeter = this.calculatePerimeter(coords);
    const centroid = this.calculateCentroid(coords);
    
    // Convert to requested units
    const areaUnit = this.getAreaUnit(opts.units!);
    const perimeterUnit = this.getDistanceUnit(opts.units!);
    
    const convertedArea = this.convertArea(area, 'square meters', areaUnit);
    const convertedPerimeter = this.convertDistance(perimeter, 'meters', perimeterUnit);

    // Validate polygon
    const isValid = this.isValidPolygon(coords);
    const selfIntersecting = this.isSelfIntersecting(coords);

    return {
      area: this.roundToPrecision(convertedArea, opts.precision!),
      unit: areaUnit,
      perimeter: this.roundToPrecision(convertedPerimeter, opts.precision!),
      perimeterUnit,
      coordinates,
      centroid,
      metadata: {
        measurementType: 'area',
        calculationMethod: 'spherical',
        isValid,
        selfIntersecting,
        timestamp: new Date().toISOString(),
      },
    };
  }

  /**
   * Calculate bearing between two points
   */
  calculateBearingMeasurement(
    from: Coordinate, 
    to: Coordinate, 
    options: Partial<MeasurementOptions> = {}
  ): BearingMeasurement {
    const opts = { ...this.options, ...options };
    
    const bearing = this.calculateBearing(from, to);
    const backBearing = (bearing + 180) % 360;
    const distance = this.haversineDistance(from, to);
    
    const distanceUnit = this.getDistanceUnit(opts.units!);
    const convertedDistance = this.convertDistance(distance, 'meters', distanceUnit);

    return {
      bearing: this.roundToPrecision(bearing, 1),
      backBearing: this.roundToPrecision(backBearing, 1),
      distance: this.roundToPrecision(convertedDistance, opts.precision!),
      distanceUnit,
      coordinates: [from, to],
      metadata: {
        measurementType: 'bearing',
        calculationMethod: 'spherical',
        timestamp: new Date().toISOString(),
      },
    };
  }

  /**
   * Calculate elevation profile
   */
  calculateElevationProfile(
    coordinates: Coordinate[], 
    options: Partial<MeasurementOptions> = {}
  ): ElevationProfile {
    const opts = { ...this.options, ...options };
    
    if (coordinates.length < 2) {
      throw new Error('At least 2 coordinates required for elevation profile');
    }

    const elevations = coordinates.map(coord => coord.elevation || 0);
    const distances: number[] = [0];
    const grade: number[] = [];
    
    let totalDistance = 0;
    let elevationGain = 0;
    let elevationLoss = 0;

    for (let i = 1; i < coordinates.length; i++) {
      const segmentDistance = this.haversineDistance(coordinates[i - 1], coordinates[i]);
      totalDistance += segmentDistance;
      distances.push(totalDistance);
      
      // Calculate grade (rise over run)
      const elevationChange = elevations[i] - elevations[i - 1];
      const gradePercent = segmentDistance > 0 ? (elevationChange / segmentDistance) * 100 : 0;
      grade.push(gradePercent);
      
      // Track elevation gain/loss
      if (elevationChange > 0) {
        elevationGain += elevationChange;
      } else {
        elevationLoss += Math.abs(elevationChange);
      }
    }

    const minElevation = Math.min(...elevations);
    const maxElevation = Math.max(...elevations);

    return {
      coordinates,
      elevations,
      distances,
      totalDistance: this.roundToPrecision(totalDistance, opts.precision!),
      elevationGain: this.roundToPrecision(elevationGain, 1),
      elevationLoss: this.roundToPrecision(elevationLoss, 1),
      minElevation: this.roundToPrecision(minElevation, 1),
      maxElevation: this.roundToPrecision(maxElevation, 1),
      grade,
      metadata: {
        measurementType: 'elevation',
        elevationSource: 'gps',
        accuracy: 5, // Typical GPS elevation accuracy in meters
        timestamp: new Date().toISOString(),
      },
    };
  }

  /**
   * Validate coordinate bounds
   */
  validateCoordinate(coord: Coordinate): boolean {
    return coord.latitude >= -90 && coord.latitude <= 90 &&
           coord.longitude >= -180 && coord.longitude <= 180;
  }

  /**
   * Convert between coordinate formats
   */
  convertCoordinateFormat(
    coord: Coordinate, 
    fromFormat: 'decimal' | 'dms' | 'utm', 
    toFormat: 'decimal' | 'dms' | 'utm'
  ): string | Coordinate {
    if (fromFormat === toFormat) return coord;
    
    if (fromFormat === 'decimal' && toFormat === 'dms') {
      return {
        latitude: this.decimalToDMS(coord.latitude, 'lat'),
        longitude: this.decimalToDMS(coord.longitude, 'lng'),
      } as any;
    }
    
    // Additional format conversions would be implemented here
    return coord;
  }

  // Private helper methods

  private haversineDistance(from: Coordinate, to: Coordinate): number {
    const R = this.earthParams.a; // Use ellipsoid semi-major axis
    const φ1 = from.latitude * DEG_TO_RAD;
    const φ2 = to.latitude * DEG_TO_RAD;
    const Δφ = (to.latitude - from.latitude) * DEG_TO_RAD;
    const Δλ = (to.longitude - from.longitude) * DEG_TO_RAD;

    const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
            Math.cos(φ1) * Math.cos(φ2) *
            Math.sin(Δλ/2) * Math.sin(Δλ/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

    return R * c;
  }

  private calculateBearing(from: Coordinate, to: Coordinate): number {
    const φ1 = from.latitude * DEG_TO_RAD;
    const φ2 = to.latitude * DEG_TO_RAD;
    const Δλ = (to.longitude - from.longitude) * DEG_TO_RAD;

    const y = Math.sin(Δλ) * Math.cos(φ2);
    const x = Math.cos(φ1) * Math.sin(φ2) - 
              Math.sin(φ1) * Math.cos(φ2) * Math.cos(Δλ);

    let bearing = Math.atan2(y, x) * RAD_TO_DEG;
    return (bearing + 360) % 360;
  }

  private sphericalArea(coordinates: Coordinate[]): number {
    if (coordinates.length < 3) return 0;

    const R = this.earthParams.a;
    let area = 0;

    for (let i = 0; i < coordinates.length - 1; i++) {
      const p1 = coordinates[i];
      const p2 = coordinates[i + 1];
      
      area += (p2.longitude - p1.longitude) * DEG_TO_RAD * 
              (2 + Math.sin(p1.latitude * DEG_TO_RAD) + Math.sin(p2.latitude * DEG_TO_RAD));
    }

    area = Math.abs(area * R * R / 2);
    return area;
  }

  private calculatePerimeter(coordinates: Coordinate[]): number {
    let perimeter = 0;
    
    for (let i = 0; i < coordinates.length - 1; i++) {
      perimeter += this.haversineDistance(coordinates[i], coordinates[i + 1]);
    }
    
    return perimeter;
  }

  private calculateCentroid(coordinates: Coordinate[]): Coordinate {
    const n = coordinates.length;
    let lat = 0, lng = 0, elevation = 0;
    
    for (const coord of coordinates) {
      lat += coord.latitude;
      lng += coord.longitude;
      elevation += coord.elevation || 0;
    }
    
    return {
      latitude: lat / n,
      longitude: lng / n,
      elevation: elevation / n,
    };
  }

  private isValidPolygon(coordinates: Coordinate[]): boolean {
    if (coordinates.length < 4) return false; // Need at least 3 points + closure
    
    // Check if polygon is closed
    if (!this.coordinatesEqual(coordinates[0], coordinates[coordinates.length - 1])) {
      return false;
    }
    
    // Check for valid coordinates
    return coordinates.every(coord => this.validateCoordinate(coord));
  }

  private isSelfIntersecting(coordinates: Coordinate[]): boolean {
    // Simplified self-intersection check
    // In production, use a proper computational geometry library
    if (coordinates.length < 4) return false;
    
    for (let i = 0; i < coordinates.length - 3; i++) {
      for (let j = i + 2; j < coordinates.length - 1; j++) {
        if (i === 0 && j === coordinates.length - 2) continue; // Skip first/last edge
        
        if (this.lineSegmentsIntersect(
          coordinates[i], coordinates[i + 1],
          coordinates[j], coordinates[j + 1]
        )) {
          return true;
        }
      }
    }
    
    return false;
  }

  private lineSegmentsIntersect(
    p1: Coordinate, p2: Coordinate,
    p3: Coordinate, p4: Coordinate
  ): boolean {
    // Simplified line intersection check using cross products
    const d1 = this.crossProduct(p3, p4, p1);
    const d2 = this.crossProduct(p3, p4, p2);
    const d3 = this.crossProduct(p1, p2, p3);
    const d4 = this.crossProduct(p1, p2, p4);
    
    if (((d1 > 0 && d2 < 0) || (d1 < 0 && d2 > 0)) &&
        ((d3 > 0 && d4 < 0) || (d3 < 0 && d4 > 0))) {
      return true;
    }
    
    return false;
  }

  private crossProduct(p1: Coordinate, p2: Coordinate, p3: Coordinate): number {
    return (p2.latitude - p1.latitude) * (p3.longitude - p1.longitude) -
           (p2.longitude - p1.longitude) * (p3.latitude - p1.latitude);
  }

  private coordinatesEqual(coord1: Coordinate, coord2: Coordinate, tolerance = 1e-10): boolean {
    return Math.abs(coord1.latitude - coord2.latitude) < tolerance &&
           Math.abs(coord1.longitude - coord2.longitude) < tolerance;
  }

  private convertDistance(value: number, fromUnit: string, toUnit: string): number {
    // Find conversion factor for fromUnit
    let fromFactor = 1;
    let toFactor = 1;
    
    for (const unitSystem of Object.values(DISTANCE_UNITS)) {
      if (unitSystem[fromUnit as keyof typeof unitSystem]) {
        fromFactor = unitSystem[fromUnit as keyof typeof unitSystem];
        break;
      }
    }
    
    for (const unitSystem of Object.values(DISTANCE_UNITS)) {
      if (unitSystem[toUnit as keyof typeof unitSystem]) {
        toFactor = unitSystem[toUnit as keyof typeof unitSystem];
        break;
      }
    }
    
    return (value * fromFactor) / toFactor;
  }

  private convertArea(value: number, fromUnit: string, toUnit: string): number {
    let fromFactor = 1;
    let toFactor = 1;
    
    for (const unitSystem of Object.values(AREA_UNITS)) {
      if (unitSystem[fromUnit as keyof typeof unitSystem]) {
        fromFactor = unitSystem[fromUnit as keyof typeof unitSystem];
        break;
      }
    }
    
    for (const unitSystem of Object.values(AREA_UNITS)) {
      if (unitSystem[toUnit as keyof typeof unitSystem]) {
        toFactor = unitSystem[toUnit as keyof typeof unitSystem];
        break;
      }
    }
    
    return (value * fromFactor) / toFactor;
  }

  private getDistanceUnit(unitSystem: string): string {
    switch (unitSystem) {
      case 'metric':
        return 'meters';
      case 'imperial':
        return 'feet';
      case 'nautical':
        return 'nautical miles';
      default:
        return 'meters';
    }
  }

  private getAreaUnit(unitSystem: string): string {
    switch (unitSystem) {
      case 'metric':
        return 'square meters';
      case 'imperial':
        return 'square feet';
      default:
        return 'square meters';
    }
  }

  private roundToPrecision(value: number, precision: number): number {
    const factor = Math.pow(10, precision);
    return Math.round(value * factor) / factor;
  }

  private decimalToDMS(decimal: number, type: 'lat' | 'lng'): string {
    const abs = Math.abs(decimal);
    const degrees = Math.floor(abs);
    const minutes = Math.floor((abs - degrees) * 60);
    const seconds = ((abs - degrees) * 60 - minutes) * 60;
    
    const direction = type === 'lat' 
      ? (decimal >= 0 ? 'N' : 'S')
      : (decimal >= 0 ? 'E' : 'W');
    
    return `${degrees}°${minutes}'${seconds.toFixed(2)}"${direction}`;
  }
}

/**
 * Measurement utilities and helper functions
 */
export const MeasurementUtils = {
  /**
   * Create a measurement calculator instance
   */
  create: (options?: MeasurementOptions) => new EnhancedMeasurements(options),

  /**
   * Quick distance calculation
   */
  quickDistance: (from: Coordinate, to: Coordinate, unit = 'meters'): number => {
    const calc = new EnhancedMeasurements({ units: unit.includes('mile') ? 'imperial' : 'metric' });
    const result = calc.calculateDistance([from, to]);
    return result.distance;
  },

  /**
   * Quick area calculation
   */
  quickArea: (coordinates: Coordinate[], unit = 'square meters'): number => {
    const calc = new EnhancedMeasurements({ units: unit.includes('square feet') ? 'imperial' : 'metric' });
    const result = calc.calculateArea(coordinates);
    return result.area;
  },

  /**
   * Quick bearing calculation
   */
  quickBearing: (from: Coordinate, to: Coordinate): number => {
    const calc = new EnhancedMeasurements();
    const result = calc.calculateBearingMeasurement(from, to);
    return result.bearing;
  },

  /**
   * Format measurement value for display
   */
  formatMeasurement: (value: number, unit: string, precision = 2): string => {
    let formattedValue: string;
    
    // Auto-scale large values
    if (unit === 'meters' && value >= 1000) {
      formattedValue = (value / 1000).toFixed(precision);
      unit = 'km';
    } else if (unit === 'square meters' && value >= 10000) {
      formattedValue = (value / 10000).toFixed(precision);
      unit = 'ha';
    } else if (unit === 'feet' && value >= 5280) {
      formattedValue = (value / 5280).toFixed(precision);
      unit = 'miles';
    } else {
      formattedValue = value.toFixed(precision);
    }
    
    return `${formattedValue} ${unit}`;
  },

  /**
   * Convert measurement to different units
   */
  convertUnits: (value: number, fromUnit: string, toUnit: string): number => {
    const calc = new EnhancedMeasurements();
    return calc['convertDistance'](value, fromUnit, toUnit);
  },

  /**
   * Validate coordinate array
   */
  validateCoordinates: (coordinates: Coordinate[]): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];
    
    if (!Array.isArray(coordinates)) {
      errors.push('Coordinates must be an array');
      return { isValid: false, errors };
    }
    
    if (coordinates.length === 0) {
      errors.push('Coordinates array cannot be empty');
      return { isValid: false, errors };
    }
    
    for (let i = 0; i < coordinates.length; i++) {
      const coord = coordinates[i];
      
      if (!coord || typeof coord !== 'object') {
        errors.push(`Invalid coordinate at index ${i}`);
        continue;
      }
      
      if (typeof coord.latitude !== 'number' || isNaN(coord.latitude)) {
        errors.push(`Invalid latitude at index ${i}`);
      }
      
      if (typeof coord.longitude !== 'number' || isNaN(coord.longitude)) {
        errors.push(`Invalid longitude at index ${i}`);
      }
      
      if (coord.latitude < -90 || coord.latitude > 90) {
        errors.push(`Latitude out of range at index ${i}: ${coord.latitude}`);
      }
      
      if (coord.longitude < -180 || coord.longitude > 180) {
        errors.push(`Longitude out of range at index ${i}: ${coord.longitude}`);
      }
    }
    
    return { isValid: errors.length === 0, errors };
  },

  /**
   * Get supported units for different measurement types
   */
  getSupportedUnits: () => ({
    distance: {
      metric: Object.keys(DISTANCE_UNITS.metric),
      imperial: Object.keys(DISTANCE_UNITS.imperial),
      nautical: Object.keys(DISTANCE_UNITS.nautical),
    },
    area: {
      metric: Object.keys(AREA_UNITS.metric),
      imperial: Object.keys(AREA_UNITS.imperial),
    },
  }),
};

/**
 * Factory function to create measurement calculator
 */
export function createMeasurementCalculator(options?: MeasurementOptions): EnhancedMeasurements {
  return new EnhancedMeasurements(options);
}
