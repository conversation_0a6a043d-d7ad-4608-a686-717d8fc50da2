import React, { useState, useEffect, useCallback } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Modal, ScrollView } from 'react-native';
import { useTheme } from '@/hooks/useTheme';
import { Globe, Copy, Info, Settings } from 'lucide-react-native';
import type { Coordinate, CoordinateFormat } from '@/types/gis';

interface CoordinateDisplayProps {
  coordinate: Coordinate;
  format?: CoordinateFormat;
  precision?: number;
  showCopyButton?: boolean;
  showFormatSelector?: boolean;
  onFormatChange?: (format: CoordinateFormat) => void;
  style?: any;
}

interface CoordinateFormat {
  type: 'decimal' | 'dms' | 'utm' | 'mgrs';
  name: string;
  description: string;
  example: string;
}

const COORDINATE_FORMATS: CoordinateFormat[] = [
  {
    type: 'decimal',
    name: 'Decimal Degrees',
    description: 'Standard decimal format (WGS84)',
    example: '37.7749, -122.4194',
  },
  {
    type: 'dms',
    name: 'Degrees Minutes Seconds',
    description: 'Traditional DMS format',
    example: '37°46\'29.6"N 122°25\'9.8"W',
  },
  {
    type: 'utm',
    name: 'UTM Coordinates',
    description: 'Universal Transverse Mercator',
    example: '10S 551460 4179670',
  },
  {
    type: 'mgrs',
    name: 'Military Grid Reference',
    description: 'MGRS/USNG format',
    example: '10SEG5146079670',
  },
];

export const CoordinateDisplay: React.FC<CoordinateDisplayProps> = ({
  coordinate,
  format = 'decimal',
  precision = 6,
  showCopyButton = true,
  showFormatSelector = false,
  onFormatChange,
  style,
}) => {
  const { theme } = useTheme();
  const [selectedFormat, setSelectedFormat] = useState<CoordinateFormat['type']>(format);
  const [showFormatModal, setShowFormatModal] = useState(false);

  const formatCoordinate = useCallback((coord: Coordinate, formatType: CoordinateFormat['type']): string => {
    switch (formatType) {
      case 'decimal':
        return `${coord.latitude.toFixed(precision)}, ${coord.longitude.toFixed(precision)}`;
      
      case 'dms':
        return formatToDMS(coord.latitude, coord.longitude);
      
      case 'utm':
        return formatToUTM(coord.latitude, coord.longitude);
      
      case 'mgrs':
        return formatToMGRS(coord.latitude, coord.longitude);
      
      default:
        return `${coord.latitude.toFixed(precision)}, ${coord.longitude.toFixed(precision)}`;
    }
  }, [precision]);

  const formatToDMS = (lat: number, lng: number): string => {
    const formatDMS = (decimal: number, isLatitude: boolean): string => {
      const abs = Math.abs(decimal);
      const degrees = Math.floor(abs);
      const minutes = Math.floor((abs - degrees) * 60);
      const seconds = ((abs - degrees) * 60 - minutes) * 60;
      
      const direction = isLatitude 
        ? (decimal >= 0 ? 'N' : 'S')
        : (decimal >= 0 ? 'E' : 'W');
      
      return `${degrees}°${minutes}'${seconds.toFixed(1)}"${direction}`;
    };

    return `${formatDMS(lat, true)} ${formatDMS(lng, false)}`;
  };

  const formatToUTM = (lat: number, lng: number): string => {
    // Simplified UTM conversion (real implementation would be more complex)
    const zone = Math.floor((lng + 180) / 6) + 1;
    const hemisphere = lat >= 0 ? 'N' : 'S';
    
    // Simplified projection (not accurate, for demo only)
    const easting = Math.round(500000 + lng * 111320 * Math.cos(lat * Math.PI / 180));
    const northing = Math.round(lat * 110540);
    
    return `${zone}${hemisphere} ${easting} ${Math.abs(northing)}`;
  };

  const formatToMGRS = (lat: number, lng: number): string => {
    // Simplified MGRS conversion (real implementation would be more complex)
    const zone = Math.floor((lng + 180) / 6) + 1;
    const letter = 'CDEFGHJKLMNPQRSTUVWXX'[Math.floor((lat + 80) / 8)];
    
    // Grid square letters (simplified)
    const gridSquare = 'SG';
    
    // Simplified coordinates
    const easting = Math.round((lng + 180) * 100000) % 100000;
    const northing = Math.round((lat + 90) * 100000) % 100000;
    
    return `${zone}${letter}${gridSquare}${easting.toString().padStart(5, '0')}${northing.toString().padStart(5, '0')}`;
  };

  const handleCopyCoordinate = async () => {
    const formattedCoord = formatCoordinate(coordinate, selectedFormat);
    
    try {
      // In React Native, you would use Clipboard API
      // await Clipboard.setString(formattedCoord);
      
      // For now, just log (in a real app, show toast notification)
      console.log('Copied to clipboard:', formattedCoord);
    } catch (error) {
      console.error('Failed to copy coordinate:', error);
    }
  };

  const handleFormatChange = (newFormat: CoordinateFormat['type']) => {
    setSelectedFormat(newFormat);
    onFormatChange?.(newFormat);
    setShowFormatModal(false);
  };

  const formattedCoordinate = formatCoordinate(coordinate, selectedFormat);
  const currentFormatInfo = COORDINATE_FORMATS.find(f => f.type === selectedFormat);

  return (
    <>
      <View style={[styles.container, { backgroundColor: theme.colors.background }, style]}>
        <View style={styles.coordinateContent}>
          <Globe size={14} color={theme.colors.primary} />
          <View style={styles.coordinateText}>
            <Text style={[styles.coordinate, { color: theme.colors.text }]}>
              {formattedCoordinate}
            </Text>
            {currentFormatInfo && (
              <Text style={[styles.formatLabel, { color: theme.colors.muted }]}>
                {currentFormatInfo.name}
              </Text>
            )}
          </View>
        </View>

        <View style={styles.actions}>
          {showCopyButton && (
            <TouchableOpacity 
              style={styles.actionButton}
              onPress={handleCopyCoordinate}
            >
              <Copy size={14} color={theme.colors.muted} />
            </TouchableOpacity>
          )}
          
          {showFormatSelector && (
            <TouchableOpacity 
              style={styles.actionButton}
              onPress={() => setShowFormatModal(true)}
            >
              <Settings size={14} color={theme.colors.muted} />
            </TouchableOpacity>
          )}
        </View>
      </View>

      <Modal
        visible={showFormatModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowFormatModal(false)}
      >
        <View style={[styles.modalContainer, { backgroundColor: theme.colors.background }]}>
          <View style={[styles.modalHeader, { borderBottomColor: theme.colors.border }]}>
            <Text style={[styles.modalTitle, { color: theme.colors.text }]}>
              Coordinate Format
            </Text>
            <TouchableOpacity onPress={() => setShowFormatModal(false)}>
              <Text style={[styles.modalCloseButton, { color: theme.colors.primary }]}>
                Done
              </Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.formatList}>
            {COORDINATE_FORMATS.map(format => (
              <TouchableOpacity
                key={format.type}
                style={[
                  styles.formatItem,
                  {
                    backgroundColor: selectedFormat === format.type 
                      ? theme.colors.primary + '10' 
                      : 'transparent',
                    borderBottomColor: theme.colors.border,
                  }
                ]}
                onPress={() => handleFormatChange(format.type)}
              >
                <View style={styles.formatItemContent}>
                  <Text style={[styles.formatName, { color: theme.colors.text }]}>
                    {format.name}
                  </Text>
                  <Text style={[styles.formatDescription, { color: theme.colors.muted }]}>
                    {format.description}
                  </Text>
                  <Text style={[styles.formatExample, { color: theme.colors.muted }]}>
                    Example: {format.example}
                  </Text>
                  
                  {selectedFormat === format.type && (
                    <View style={styles.currentFormatPreview}>
                      <Text style={[styles.previewLabel, { color: theme.colors.primary }]}>
                        Current:
                      </Text>
                      <Text style={[styles.previewValue, { color: theme.colors.text }]}>
                        {formatCoordinate(coordinate, format.type)}
                      </Text>
                    </View>
                  )}
                </View>
                
                {selectedFormat === format.type && (
                  <View style={[styles.selectedIndicator, { backgroundColor: theme.colors.primary }]} />
                )}
              </TouchableOpacity>
            ))}
          </ScrollView>

          <View style={[styles.modalInfo, { backgroundColor: theme.colors.card }]}>
            <Info size={16} color={theme.colors.info} />
            <Text style={[styles.infoText, { color: theme.colors.muted }]}>
              Coordinate formats help represent the same location in different reference systems. 
              Choose the format that best suits your needs.
            </Text>
          </View>
        </View>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  coordinateContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    gap: 8,
  },
  coordinateText: {
    flex: 1,
  },
  coordinate: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    marginBottom: 2,
  },
  formatLabel: {
    fontSize: 11,
    fontFamily: 'Inter-Regular',
  },
  actions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  actionButton: {
    padding: 4,
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
  },
  modalCloseButton: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
  },
  formatList: {
    flex: 1,
  },
  formatItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  formatItemContent: {
    flex: 1,
  },
  formatName: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 4,
  },
  formatDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    marginBottom: 6,
  },
  formatExample: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    fontStyle: 'italic',
  },
  currentFormatPreview: {
    marginTop: 8,
    padding: 8,
    backgroundColor: 'rgba(59, 130, 246, 0.05)',
    borderRadius: 4,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  previewLabel: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
  },
  previewValue: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    flex: 1,
  },
  selectedIndicator: {
    width: 4,
    height: 40,
    borderRadius: 2,
  },
  modalInfo: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: 16,
    gap: 12,
  },
  infoText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    lineHeight: 16,
    flex: 1,
  },
});
