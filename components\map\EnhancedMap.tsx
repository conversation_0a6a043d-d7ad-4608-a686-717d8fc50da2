import React, { useState, useRef, useEffect, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Modal,
  TextInput,
  Animated,
  Dimensions,
  Platform,
  Alert,
} from 'react-native';
import MapView, {
  <PERSON>er,
  <PERSON>yline,
  Polygon,
  Circle,
  Heatmap,
  PROVIDER_GOOGLE,
  Callout,
  MapType,
  Region,
} from 'react-native-maps';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '@/hooks/useTheme';
import * as Location from 'expo-location';
import {
  MapPin,
  Layers,
  Ruler,
  Search,
  Navigation,
  Download,
  Upload,
  Settings,
  Map as MapIcon,
  Satellite,
  Plus,
  Minus,
  Target,
  Route,
  CircleDot,
  Square,
  Pentagon,
  Activity,
  Thermometer,
  Shield,
  Share2,
  Save,
  Trash2,
  Edit3,
  Eye,
  EyeOff,
  Info,
  ChevronRight,
  ChevronLeft,
  Filter,
  BarChart3,
  Zap,
  Cloud,
  Sun,
  Wind,
  Droplets,
  AlertTriangle,
} from 'lucide-react-native';
import { 
  calculateDistance, 
  calculate<PERSON>rea, 
  createBuffer, 
  pointInPolygon,
  getPolygonCenter,
  simplifyPolygon,
  getBoundingBox,
} from './spatial/SpatialAnalysis';

interface EnhancedMapProps {
  initialRegion?: Region;
  onLocationSelect?: (location: { latitude: number; longitude: number }) => void;
  geoFeatures?: any[];
  showAnalysisTools?: boolean;
  enableDrawing?: boolean;
  enableMeasurement?: boolean;
  enableGeofencing?: boolean;
  enableRouting?: boolean;
  enableHeatmap?: boolean;
  enableClustering?: boolean;
  offlineMode?: boolean;
}

type DrawingMode = 'none' | 'point' | 'line' | 'polygon' | 'circle' | 'rectangle';
type MapLayer = 'standard' | 'satellite' | 'terrain' | 'hybrid';
type AnalysisTool = 'buffer' | 'intersect' | 'union' | 'difference' | 'centroid' | 'simplify';

interface DrawnFeature {
  id: string;
  type: 'point' | 'line' | 'polygon' | 'circle';
  coordinates: any;
  properties: {
    name: string;
    color: string;
    fillColor?: string;
    strokeWidth?: number;
    radius?: number;
    area?: number;
    length?: number;
    created: number;
  };
  visible: boolean;
}

interface Geofence {
  id: string;
  name: string;
  type: 'circle' | 'polygon';
  coordinates: any;
  radius?: number;
  active: boolean;
  actions: {
    onEnter?: string;
    onExit?: string;
    onDwell?: number;
  };
}

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

export default function EnhancedMap({
  initialRegion,
  onLocationSelect,
  geoFeatures = [],
  showAnalysisTools = true,
  enableDrawing = true,
  enableMeasurement = true,
  enableGeofencing = true,
  enableRouting = true,
  enableHeatmap = true,
  enableClustering = true,
  offlineMode = false,
}: EnhancedMapProps) {
  const { theme } = useTheme();
  const mapRef = useRef<MapView>(null);
  
  // State management
  const [region, setRegion] = useState<Region>(initialRegion || {
    latitude: 37.78825,
    longitude: -122.4324,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  });
  
  const [mapType, setMapType] = useState<MapLayer>('standard');
  const [drawingMode, setDrawingMode] = useState<DrawingMode>('none');
  const [drawnFeatures, setDrawnFeatures] = useState<DrawnFeature[]>([]);
  const [selectedFeature, setSelectedFeature] = useState<DrawnFeature | null>(null);
  const [currentDrawing, setCurrentDrawing] = useState<any[]>([]);
  const [measurementMode, setMeasurementMode] = useState(false);
  const [geofences, setGeofences] = useState<Geofence[]>([]);
  const [showHeatmap, setShowHeatmap] = useState(false);
  const [heatmapData, setHeatmapData] = useState<any[]>([]);
  const [userLocation, setUserLocation] = useState<Location.LocationObject | null>(null);
  const [tracking, setTracking] = useState(false);
  const [route, setRoute] = useState<any>(null);
  
  // UI state
  const [showLayersPanel, setShowLayersPanel] = useState(false);
  const [showDrawingTools, setShowDrawingTools] = useState(false);
  const [showAnalysisPanel, setShowAnalysisPanel] = useState(false);
  const [showFeaturesList, setShowFeaturesList] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [showSearch, setShowSearch] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  
  // Animation values
  const panelAnimation = useRef(new Animated.Value(0)).current;
  const toolbarAnimation = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    requestLocationPermission();
    if (offlineMode) {
      loadOfflineMaps();
    }
  }, []);

  const requestLocationPermission = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status === 'granted') {
        const location = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.High,
        });
        setUserLocation(location);
        
        if (tracking) {
          Location.watchPositionAsync(
            {
              accuracy: Location.Accuracy.High,
              distanceInterval: 10,
            },
            (location) => {
              setUserLocation(location);
            }
          );
        }
      }
    } catch (error) {
      console.error('Error getting location:', error);
    }
  };

  const loadOfflineMaps = async () => {
    // Implement offline map loading logic
    console.log('Loading offline maps...');
  };

  const getMapType = (): MapType => {
    switch (mapType) {
      case 'satellite':
        return 'satellite';
      case 'terrain':
        return 'terrain';
      case 'hybrid':
        return 'hybrid';
      default:
        return 'standard';
    }
  };

  const handleMapPress = (e: any) => {
    const coordinate = e.nativeEvent.coordinate;
    
    if (drawingMode !== 'none') {
      handleDrawing(coordinate);
    } else if (measurementMode) {
      handleMeasurement(coordinate);
    } else if (onLocationSelect) {
      onLocationSelect(coordinate);
    }
  };

  const handleDrawing = (coordinate: any) => {
    switch (drawingMode) {
      case 'point':
        addPointFeature(coordinate);
        break;
      case 'line':
        addToLineDrawing(coordinate);
        break;
      case 'polygon':
        addToPolygonDrawing(coordinate);
        break;
      case 'circle':
        // Circle drawing handled differently
        break;
    }
  };

  const addPointFeature = (coordinate: any) => {
    const newFeature: DrawnFeature = {
      id: `point_${Date.now()}`,
      type: 'point',
      coordinates: coordinate,
      properties: {
        name: `Point ${drawnFeatures.length + 1}`,
        color: theme.colors.primary,
        created: Date.now(),
      },
      visible: true,
    };
    
    setDrawnFeatures([...drawnFeatures, newFeature]);
    setDrawingMode('none');
  };

  const addToLineDrawing = (coordinate: any) => {
    const newPoints = [...currentDrawing, coordinate];
    setCurrentDrawing(newPoints);
    
    if (newPoints.length >= 2) {
      // Show option to finish drawing
      Alert.alert(
        'Finish Drawing',
        'Continue drawing or finish?',
        [
          { text: 'Continue', style: 'cancel' },
          { 
            text: 'Finish', 
            onPress: () => finishLineDrawing(newPoints),
          },
        ]
      );
    }
  };

  const finishLineDrawing = (points: any[]) => {
    const length = calculateLineLength(points);
    
    const newFeature: DrawnFeature = {
      id: `line_${Date.now()}`,
      type: 'line',
      coordinates: points,
      properties: {
        name: `Line ${drawnFeatures.length + 1}`,
        color: theme.colors.primary,
        strokeWidth: 3,
        length,
        created: Date.now(),
      },
      visible: true,
    };
    
    setDrawnFeatures([...drawnFeatures, newFeature]);
    setCurrentDrawing([]);
    setDrawingMode('none');
  };

  const addToPolygonDrawing = (coordinate: any) => {
    const newPoints = [...currentDrawing, coordinate];
    setCurrentDrawing(newPoints);
    
    if (newPoints.length >= 3) {
      Alert.alert(
        'Finish Drawing',
        'Continue drawing or finish?',
        [
          { text: 'Continue', style: 'cancel' },
          { 
            text: 'Finish', 
            onPress: () => finishPolygonDrawing(newPoints),
          },
        ]
      );
    }
  };

  const finishPolygonDrawing = (points: any[]) => {
    const area = calculateArea(points);
    
    const newFeature: DrawnFeature = {
      id: `polygon_${Date.now()}`,
      type: 'polygon',
      coordinates: points,
      properties: {
        name: `Polygon ${drawnFeatures.length + 1}`,
        color: theme.colors.primary,
        fillColor: theme.colors.primary + '30',
        strokeWidth: 2,
        area,
        created: Date.now(),
      },
      visible: true,
    };
    
    setDrawnFeatures([...drawnFeatures, newFeature]);
    setCurrentDrawing([]);
    setDrawingMode('none');
  };

  const calculateLineLength = (points: any[]) => {
    let length = 0;
    for (let i = 1; i < points.length; i++) {
      length += calculateDistance(points[i - 1], points[i]);
    }
    return length;
  };

  const handleMeasurement = (coordinate: any) => {
    // Implement measurement logic
    console.log('Measuring at:', coordinate);
  };

  const performSpatialAnalysis = (tool: AnalysisTool) => {
    if (!selectedFeature) {
      Alert.alert('No Feature Selected', 'Please select a feature first.');
      return;
    }
    
    switch (tool) {
      case 'buffer':
        createBufferAnalysis();
        break;
      case 'centroid':
        findCentroid();
        break;
      case 'simplify':
        simplifyFeature();
        break;
      // Add more analysis tools
    }
  };

  const createBufferAnalysis = () => {
    Alert.prompt(
      'Buffer Distance',
      'Enter buffer distance in meters:',
      (distance) => {
        if (distance && selectedFeature) {
          const buffer = createBuffer(
            selectedFeature.coordinates,
            parseFloat(distance)
          );
          
          const bufferFeature: DrawnFeature = {
            id: `buffer_${Date.now()}`,
            type: 'polygon',
            coordinates: buffer,
            properties: {
              name: `Buffer of ${selectedFeature.properties.name}`,
              color: theme.colors.secondary,
              fillColor: theme.colors.secondary + '30',
              strokeWidth: 2,
              created: Date.now(),
            },
            visible: true,
          };
          
          setDrawnFeatures([...drawnFeatures, bufferFeature]);
        }
      },
      'plain-text',
      '100'
    );
  };

  const findCentroid = () => {
    if (selectedFeature && selectedFeature.type === 'polygon') {
      const centroid = getPolygonCenter(selectedFeature.coordinates);
      
      const centroidFeature: DrawnFeature = {
        id: `centroid_${Date.now()}`,
        type: 'point',
        coordinates: centroid,
        properties: {
          name: `Centroid of ${selectedFeature.properties.name}`,
          color: theme.colors.error,
          created: Date.now(),
        },
        visible: true,
      };
      
      setDrawnFeatures([...drawnFeatures, centroidFeature]);
    }
  };

  const simplifyFeature = () => {
    if (selectedFeature && (selectedFeature.type === 'line' || selectedFeature.type === 'polygon')) {
      const simplified = simplifyPolygon(selectedFeature.coordinates, 0.0001);
      
      const simplifiedFeature: DrawnFeature = {
        ...selectedFeature,
        id: `simplified_${Date.now()}`,
        coordinates: simplified,
        properties: {
          ...selectedFeature.properties,
          name: `Simplified ${selectedFeature.properties.name}`,
          color: theme.colors.success,
        },
      };
      
      setDrawnFeatures([...drawnFeatures, simplifiedFeature]);
    }
  };

  const generateHeatmapData = () => {
    // Generate sample heatmap data
    const data = [];
    for (let i = 0; i < 100; i++) {
      data.push({
        latitude: region.latitude + (Math.random() - 0.5) * region.latitudeDelta,
        longitude: region.longitude + (Math.random() - 0.5) * region.longitudeDelta,
        weight: Math.random(),
      });
    }
    setHeatmapData(data);
    setShowHeatmap(true);
  };

  const renderMapLayers = () => (
    <Modal
      visible={showLayersPanel}
      transparent
      animationType="slide"
      onRequestClose={() => setShowLayersPanel(false)}
    >
      <TouchableOpacity 
        style={styles.modalOverlay}
        activeOpacity={1}
        onPress={() => setShowLayersPanel(false)}
      >
        <View style={[styles.layersPanel, { backgroundColor: theme.colors.card }]}>
          <Text style={[styles.panelTitle, { color: theme.colors.text }]}>
            Map Layers
          </Text>
          
          <TouchableOpacity
            style={[styles.layerOption, mapType === 'standard' && styles.layerOptionActive]}
            onPress={() => setMapType('standard')}
          >
            <MapIcon size={24} color={mapType === 'standard' ? theme.colors.primary : theme.colors.text} />
            <Text style={[styles.layerText, { color: theme.colors.text }]}>Standard</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.layerOption, mapType === 'satellite' && styles.layerOptionActive]}
            onPress={() => setMapType('satellite')}
          >
            <Satellite size={24} color={mapType === 'satellite' ? theme.colors.primary : theme.colors.text} />
            <Text style={[styles.layerText, { color: theme.colors.text }]}>Satellite</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.layerOption, mapType === 'terrain' && styles.layerOptionActive]}
            onPress={() => setMapType('terrain')}
          >
            <Terrain size={24} color={mapType === 'terrain' ? theme.colors.primary : theme.colors.text} />
            <Text style={[styles.layerText, { color: theme.colors.text }]}>Terrain</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.layerOption, mapType === 'hybrid' && styles.layerOptionActive]}
            onPress={() => setMapType('hybrid')}
          >
            <Layers size={24} color={mapType === 'hybrid' ? theme.colors.primary : theme.colors.text} />
            <Text style={[styles.layerText, { color: theme.colors.text }]}>Hybrid</Text>
          </TouchableOpacity>
          
          <View style={styles.panelDivider} />
          
          <Text style={[styles.panelSubtitle, { color: theme.colors.text }]}>
            Data Layers
          </Text>
          
          <TouchableOpacity
            style={[styles.layerOption, showHeatmap && styles.layerOptionActive]}
            onPress={() => setShowHeatmap(!showHeatmap)}
          >
            <Thermometer size={24} color={showHeatmap ? theme.colors.primary : theme.colors.text} />
            <Text style={[styles.layerText, { color: theme.colors.text }]}>Heatmap</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.layerOption}
            onPress={generateHeatmapData}
          >
            <Activity size={24} color={theme.colors.text} />
            <Text style={[styles.layerText, { color: theme.colors.text }]}>Generate Heatmap</Text>
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    </Modal>
  );

  const renderDrawingTools = () => (
    <View style={[styles.drawingToolbar, { backgroundColor: theme.colors.card }]}>
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        <TouchableOpacity
          style={[
            styles.drawingTool,
            drawingMode === 'point' && { backgroundColor: theme.colors.primary }
          ]}
          onPress={() => setDrawingMode(drawingMode === 'point' ? 'none' : 'point')}
        >
          <MapPin size={20} color={drawingMode === 'point' ? 'white' : theme.colors.text} />
          <Text style={[styles.toolText, { color: drawingMode === 'point' ? 'white' : theme.colors.text }]}>
            Point
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.drawingTool,
            drawingMode === 'line' && { backgroundColor: theme.colors.primary }
          ]}
          onPress={() => setDrawingMode(drawingMode === 'line' ? 'none' : 'line')}
        >
          <Route size={20} color={drawingMode === 'line' ? 'white' : theme.colors.text} />
          <Text style={[styles.toolText, { color: drawingMode === 'line' ? 'white' : theme.colors.text }]}>
            Line
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.drawingTool,
            drawingMode === 'polygon' && { backgroundColor: theme.colors.primary }
          ]}
          onPress={() => setDrawingMode(drawingMode === 'polygon' ? 'none' : 'polygon')}
        >
          <Pentagon size={20} color={drawingMode === 'polygon' ? 'white' : theme.colors.text} />
          <Text style={[styles.toolText, { color: drawingMode === 'polygon' ? 'white' : theme.colors.text }]}>
            Polygon
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.drawingTool,
            drawingMode === 'circle' && { backgroundColor: theme.colors.primary }
          ]}
          onPress={() => setDrawingMode(drawingMode === 'circle' ? 'none' : 'circle')}
        >
          <CircleDot size={20} color={drawingMode === 'circle' ? 'white' : theme.colors.text} />
          <Text style={[styles.toolText, { color: drawingMode === 'circle' ? 'white' : theme.colors.text }]}>
            Circle
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.drawingTool,
            measurementMode && { backgroundColor: theme.colors.primary }
          ]}
          onPress={() => setMeasurementMode(!measurementMode)}
        >
          <Ruler size={20} color={measurementMode ? 'white' : theme.colors.text} />
          <Text style={[styles.toolText, { color: measurementMode ? 'white' : theme.colors.text }]}>
            Measure
          </Text>
        </TouchableOpacity>
      </ScrollView>
    </View>
  );

  const renderAnalysisTools = () => (
    <Modal
      visible={showAnalysisPanel}
      transparent
      animationType="slide"
      onRequestClose={() => setShowAnalysisPanel(false)}
    >
      <TouchableOpacity 
        style={styles.modalOverlay}
        activeOpacity={1}
        onPress={() => setShowAnalysisPanel(false)}
      >
        <View style={[styles.analysisPanel, { backgroundColor: theme.colors.card }]}>
          <Text style={[styles.panelTitle, { color: theme.colors.text }]}>
            Spatial Analysis Tools
          </Text>
          
          <TouchableOpacity
            style={styles.analysisTool}
            onPress={() => performSpatialAnalysis('buffer')}
          >
            <Shield size={24} color={theme.colors.text} />
            <View style={styles.toolInfo}>
              <Text style={[styles.toolName, { color: theme.colors.text }]}>Buffer</Text>
              <Text style={[styles.toolDescription, { color: theme.colors.muted }]}>
                Create buffer zones around features
              </Text>
            </View>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.analysisTool}
            onPress={() => performSpatialAnalysis('centroid')}
          >
            <Target size={24} color={theme.colors.text} />
            <View style={styles.toolInfo}>
              <Text style={[styles.toolName, { color: theme.colors.text }]}>Centroid</Text>
              <Text style={[styles.toolDescription, { color: theme.colors.muted }]}>
                Find center point of polygons
              </Text>
            </View>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.analysisTool}
            onPress={() => performSpatialAnalysis('simplify')}
          >
            <Zap size={24} color={theme.colors.text} />
            <View style={styles.toolInfo}>
              <Text style={[styles.toolName, { color: theme.colors.text }]}>Simplify</Text>
              <Text style={[styles.toolDescription, { color: theme.colors.muted }]}>
                Reduce complexity of geometries
              </Text>
            </View>
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    </Modal>
  );

  const renderFeaturesList = () => (
    <Modal
      visible={showFeaturesList}
      transparent
      animationType="slide"
      onRequestClose={() => setShowFeaturesList(false)}
    >
      <TouchableOpacity 
        style={styles.modalOverlay}
        activeOpacity={1}
        onPress={() => setShowFeaturesList(false)}
      >
        <View style={[styles.featuresPanel, { backgroundColor: theme.colors.card }]}>
          <Text style={[styles.panelTitle, { color: theme.colors.text }]}>
            Features ({drawnFeatures.length})
          </Text>
          
          <ScrollView>
            {drawnFeatures.map((feature) => (
              <TouchableOpacity
                key={feature.id}
                style={[
                  styles.featureItem,
                  selectedFeature?.id === feature.id && { backgroundColor: theme.colors.primary + '20' }
                ]}
                onPress={() => setSelectedFeature(feature)}
              >
                <View style={styles.featureIcon}>
                  {feature.type === 'point' && <MapPin size={20} color={feature.properties.color} />}
                  {feature.type === 'line' && <Route size={20} color={feature.properties.color} />}
                  {feature.type === 'polygon' && <Pentagon size={20} color={feature.properties.color} />}
                </View>
                
                <View style={styles.featureInfo}>
                  <Text style={[styles.featureName, { color: theme.colors.text }]}>
                    {feature.properties.name}
                  </Text>
                  <Text style={[styles.featureDetails, { color: theme.colors.muted }]}>
                    {feature.type === 'line' && `Length: ${feature.properties.length?.toFixed(2)}m`}
                    {feature.type === 'polygon' && `Area: ${feature.properties.area?.toFixed(2)}m²`}
                  </Text>
                </View>
                
                <TouchableOpacity
                  onPress={() => {
                    const updatedFeatures = drawnFeatures.map(f =>
                      f.id === feature.id ? { ...f, visible: !f.visible } : f
                    );
                    setDrawnFeatures(updatedFeatures);
                  }}
                >
                  {feature.visible ? (
                    <Eye size={20} color={theme.colors.text} />
                  ) : (
                    <EyeOff size={20} color={theme.colors.muted} />
                  )}
                </TouchableOpacity>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      </TouchableOpacity>
    </Modal>
  );

  return (
    <View style={styles.container}>
      <MapView
        ref={mapRef}
        style={styles.map}
        provider={Platform.OS === 'android' ? PROVIDER_GOOGLE : undefined}
        initialRegion={region}
        onRegionChangeComplete={setRegion}
        mapType={getMapType()}
        showsUserLocation={true}
        showsMyLocationButton={false}
        showsCompass={false}
        onPress={handleMapPress}
      >
        {/* User Location Marker */}
        {userLocation && (
          <Marker
            coordinate={{
              latitude: userLocation.coords.latitude,
              longitude: userLocation.coords.longitude,
            }}
            title="Your Location"
          >
            <View style={styles.userLocationMarker}>
              <View style={styles.userLocationDot} />
            </View>
          </Marker>
        )}
        
        {/* Drawn Features */}
        {drawnFeatures.map((feature) => {
          if (!feature.visible) return null;
          
          switch (feature.type) {
            case 'point':
              return (
                <Marker
                  key={feature.id}
                  coordinate={feature.coordinates}
                  title={feature.properties.name}
                  onPress={() => setSelectedFeature(feature)}
                >
                  <View style={[styles.customMarker, { backgroundColor: feature.properties.color }]}>
                    <MapPin size={16} color="white" />
                  </View>
                </Marker>
              );
              
            case 'line':
              return (
                <Polyline
                  key={feature.id}
                  coordinates={feature.coordinates}
                  strokeColor={feature.properties.color}
                  strokeWidth={feature.properties.strokeWidth || 3}
                  onPress={() => setSelectedFeature(feature)}
                />
              );
              
            case 'polygon':
              return (
                <Polygon
                  key={feature.id}
                  coordinates={feature.coordinates}
                  strokeColor={feature.properties.color}
                  fillColor={feature.properties.fillColor}
                  strokeWidth={feature.properties.strokeWidth || 2}
                  onPress={() => setSelectedFeature(feature)}
                />
              );
              
            default:
              return null;
          }
        })}
        
        {/* Current Drawing */}
        {currentDrawing.length > 0 && (
          <>
            {currentDrawing.map((coord, index) => (
              <Marker
                key={`drawing_${index}`}
                coordinate={coord}
                anchor={{ x: 0.5, y: 0.5 }}
              >
                <View style={styles.drawingMarker} />
              </Marker>
            ))}
            
            {drawingMode === 'line' && currentDrawing.length > 1 && (
              <Polyline
                coordinates={currentDrawing}
                strokeColor={theme.colors.primary}
                strokeWidth={3}
                {...(Platform.OS === 'ios' ? { strokeDasharray: [5, 5] } : {})}
              />
            )}
            
            {drawingMode === 'polygon' && currentDrawing.length > 2 && (
              <Polygon
                coordinates={currentDrawing}
                strokeColor={theme.colors.primary}
                fillColor={theme.colors.primary + '30'}
                strokeWidth={2}
                {...(Platform.OS === 'ios' ? { strokeDasharray: [5, 5] } : {})}
              />
            )}
          </>
        )}
        
        {/* Heatmap */}
        {showHeatmap && heatmapData.length > 0 && (
          <Heatmap
            points={heatmapData}
            radius={40}
            opacity={0.6}
            gradient={{
              colors: ['blue', 'green', 'yellow', 'orange', 'red'],
              startPoints: [0.1, 0.3, 0.5, 0.7, 0.9],
              colorMapSize: 256,
            }}
          />
        )}
        
        {/* Geofences */}
        {geofences.map((geofence) => {
          if (!geofence.active) return null;
          
          if (geofence.type === 'circle') {
            return (
              <Circle
                key={geofence.id}
                center={geofence.coordinates}
                radius={geofence.radius || 100}
                strokeColor={theme.colors.warning}
                fillColor={theme.colors.warning + '20'}
                strokeWidth={2}
              />
            );
          }
          
          return (
            <Polygon
              key={geofence.id}
              coordinates={geofence.coordinates}
              strokeColor={theme.colors.warning}
              fillColor={theme.colors.warning + '20'}
              strokeWidth={2}
            />
          );
        })}
      </MapView>
      
      {/* Main Toolbar */}
      <View style={[styles.toolbar, { backgroundColor: theme.colors.card }]}>
        <TouchableOpacity
          style={styles.toolbarButton}
          onPress={() => setShowLayersPanel(true)}
        >
          <Layers size={24} color={theme.colors.text} />
        </TouchableOpacity>
        
        {enableDrawing && (
          <TouchableOpacity
            style={styles.toolbarButton}
            onPress={() => setShowDrawingTools(!showDrawingTools)}
          >
            <Edit3 size={24} color={theme.colors.text} />
          </TouchableOpacity>
        )}
        
        {showAnalysisTools && (
          <TouchableOpacity
            style={styles.toolbarButton}
            onPress={() => setShowAnalysisPanel(true)}
          >
            <BarChart3 size={24} color={theme.colors.text} />
          </TouchableOpacity>
        )}
        
        <TouchableOpacity
          style={styles.toolbarButton}
          onPress={() => setShowFeaturesList(true)}
        >
          <Filter size={24} color={theme.colors.text} />
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.toolbarButton}
          onPress={() => setShowSearch(!showSearch)}
        >
          <Search size={24} color={theme.colors.text} />
        </TouchableOpacity>
      </View>
      
      {/* Zoom Controls */}
      <View style={[styles.zoomControls, { backgroundColor: theme.colors.card }]}>
        <TouchableOpacity
          style={styles.zoomButton}
          onPress={() => {
            const newRegion = {
              ...region,
              latitudeDelta: region.latitudeDelta * 0.5,
              longitudeDelta: region.longitudeDelta * 0.5,
            };
            mapRef.current?.animateToRegion(newRegion, 300);
          }}
        >
          <Plus size={20} color={theme.colors.text} />
        </TouchableOpacity>
        
        <View style={styles.zoomDivider} />
        
        <TouchableOpacity
          style={styles.zoomButton}
          onPress={() => {
            const newRegion = {
              ...region,
              latitudeDelta: region.latitudeDelta * 2,
              longitudeDelta: region.longitudeDelta * 2,
            };
            mapRef.current?.animateToRegion(newRegion, 300);
          }}
        >
          <Minus size={20} color={theme.colors.text} />
        </TouchableOpacity>
      </View>
      
      {/* Location Button */}
      <TouchableOpacity
        style={[styles.locationButton, { backgroundColor: theme.colors.card }]}
        onPress={() => {
          if (userLocation) {
            const newRegion = {
              latitude: userLocation.coords.latitude,
              longitude: userLocation.coords.longitude,
              latitudeDelta: 0.01,
              longitudeDelta: 0.01,
            };
            mapRef.current?.animateToRegion(newRegion, 300);
          }
        }}
      >
        <Target size={24} color={theme.colors.primary} />
      </TouchableOpacity>
      
      {/* Drawing Tools */}
      {showDrawingTools && renderDrawingTools()}
      
      {/* Search Bar */}
      {showSearch && (
        <View style={[styles.searchBar, { backgroundColor: theme.colors.card }]}>
          <Search size={20} color={theme.colors.muted} />
          <TextInput
            style={[styles.searchInput, { color: theme.colors.text }]}
            placeholder="Search location..."
            placeholderTextColor={theme.colors.placeholder}
            value={searchQuery}
            onChangeText={setSearchQuery}
            onSubmitEditing={() => {
              // Implement search functionality
              console.log('Searching for:', searchQuery);
            }}
          />
        </View>
      )}
      
      {/* Modals */}
      {renderMapLayers()}
      {renderAnalysisTools()}
      {renderFeaturesList()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  map: {
    flex: 1,
  },
  toolbar: {
    position: 'absolute',
    top: 50,
    left: 16,
    right: 16,
    flexDirection: 'row',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
    gap: 8,
    zIndex: 1000,
  },
  toolbarButton: {
    width: 40,
    height: 40,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  zoomControls: {
    position: 'absolute',
    right: 16,
    bottom: 120,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
    zIndex: 998,
  },
  zoomButton: {
    width: 44,
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
  },
  zoomDivider: {
    height: 1,
    backgroundColor: '#E0E0E0',
  },
  locationButton: {
    position: 'absolute',
    right: 16,
    bottom: 200,
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
    zIndex: 997,
  },
  userLocationMarker: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(0, 122, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  userLocationDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#007AFF',
  },
  customMarker: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  drawingMarker: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#007AFF',
    borderWidth: 2,
    borderColor: 'white',
  },
  drawingToolbar: {
    position: 'absolute',
    bottom: 20,
    left: 16,
    right: 16,
    borderRadius: 12,
    paddingVertical: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
    zIndex: 996,
  },
  drawingTool: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    marginHorizontal: 4,
    gap: 8,
  },
  toolText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
  },
  searchBar: {
    position: 'absolute',
    top: 120,
    left: 16,
    right: 16,
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
    gap: 12,
    zIndex: 999,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  layersPanel: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 20,
    paddingHorizontal: 16,
    paddingBottom: 32,
  },
  panelTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 20,
    textAlign: 'center',
  },
  panelSubtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    marginVertical: 12,
  },
  layerOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 8,
    gap: 12,
  },
  layerOptionActive: {
    backgroundColor: 'rgba(0, 122, 255, 0.1)',
  },
  layerText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
  },
  panelDivider: {
    height: 1,
    backgroundColor: '#E0E0E0',
    marginVertical: 16,
  },
  analysisPanel: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 20,
    paddingHorizontal: 16,
    paddingBottom: 32,
    maxHeight: '80%',
  },
  analysisTool: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 12,
    gap: 16,
  },
  toolInfo: {
    flex: 1,
  },
  toolName: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 4,
  },
  toolDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
  },
  featuresPanel: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 20,
    paddingHorizontal: 16,
    paddingBottom: 32,
    maxHeight: '60%',
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 8,
    gap: 12,
  },
  featureIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 122, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  featureInfo: {
    flex: 1,
  },
  featureName: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    marginBottom: 2,
  },
  featureDetails: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
  },
});