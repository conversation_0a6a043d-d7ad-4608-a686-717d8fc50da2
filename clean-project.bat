@echo off
echo 🧹 Cleaning FieldSync Pro Project...

:: Stop any running Metro processes
echo Stopping Metro processes...
taskkill /f /im "node.exe" 2>nul
taskkill /f /im "expo.exe" 2>nul

:: Clear Metro cache
echo Clearing Metro cache...
npx expo start --clear

:: Alternative commands for manual cleanup
echo.
echo 📝 If issues persist, try these commands manually:
echo 1. npm run reset-cache
echo 2. expo start --clear
echo 3. expo start --dev-client --clear
echo 4. rmdir /s /q node_modules ^&^& npm install
echo.
echo ✅ Project cleaned! Run 'npm run dev' to start fresh.
pause
