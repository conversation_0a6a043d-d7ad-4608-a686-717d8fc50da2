import React, { useState, useEffect } from 'react';
import { View, Alert, StyleSheet } from 'react-native';
import { StorageUtils, STORAGE_KEYS, generateStorageKey } from '@/lib/storage';
import ProfessionalMapUI from './ProfessionalMapUI';
import { useTheme } from '@/hooks/useTheme';

interface Region {
  latitude: number;
  longitude: number;
  latitudeDelta: number;
  longitudeDelta: number;
}

interface StoredFeature {
  id: string;
  type: 'point' | 'line' | 'polygon' | 'circle' | 'rectangle';
  coordinates: any;
  properties: {
    name: string;
    description?: string;
    color: string;
    created: number;
    measurements?: {
      area?: number;
      perimeter?: number;
      length?: number;
      radius?: number;
    };
  };
}

interface EnhancedMapIntegrationProps {
  projectId?: string;
  initialRegion?: Region;
  onFeatureCreated?: (feature: StoredFeature) => void;
  onFeatureDeleted?: (featureId: string) => void;
  enableOfflineStorage?: boolean;
  maxFeatures?: number;
  enableDrawing?: boolean;
  enableMeasurement?: boolean;
  enableAnalysis?: boolean;
}

const FEATURES_STORAGE_KEY = STORAGE_KEYS.FEATURES;

export default function EnhancedMapIntegration({
  projectId = 'default',
  initialRegion,
  onFeatureCreated,
  onFeatureDeleted,
  enableOfflineStorage = true,
  maxFeatures = 100,
  enableDrawing = true,
  enableMeasurement = true,
  enableAnalysis = true,
}: EnhancedMapIntegrationProps) {
  const { theme } = useTheme();
  const [features, setFeatures] = useState<StoredFeature[]>([]);
  const [loading, setLoading] = useState(true);
  const [region, setRegion] = useState<Region>(
    initialRegion || {
      latitude: 37.78825,
      longitude: -122.4324,
      latitudeDelta: 0.0922,
      longitudeDelta: 0.0421,
    }
  );

  // Load features from storage on mount
  useEffect(() => {
    loadFeatures();
  }, [projectId]);

  const loadFeatures = async () => {
    if (!enableOfflineStorage) {
      setLoading(false);
      return;
    }

    try {
      const storageKey = generateStorageKey(FEATURES_STORAGE_KEY, projectId);
      const parsedFeatures = await StorageUtils.getJSON<StoredFeature[]>(storageKey);
      
      if (parsedFeatures && Array.isArray(parsedFeatures)) {
        setFeatures(parsedFeatures);
        console.log(`Loaded ${parsedFeatures.length} features for project ${projectId}`);
      }
    } catch (error) {
      console.error('Error loading features:', error);
      Alert.alert(
        'Storage Error',
        'Failed to load saved features. Starting with empty map.',
        [{ text: 'OK' }]
      );
    } finally {
      setLoading(false);
    }
  };

  const saveFeatures = async (updatedFeatures: StoredFeature[]) => {
    if (!enableOfflineStorage) return;

    try {
      const storageKey = generateStorageKey(FEATURES_STORAGE_KEY, projectId);
      await StorageUtils.setJSON(storageKey, updatedFeatures);
      console.log(`Saved ${updatedFeatures.length} features for project ${projectId}`);
    } catch (error) {
      console.error('Error saving features:', error);
      Alert.alert(
        'Storage Error',
        'Failed to save features. Changes may be lost.',
        [{ text: 'OK' }]
      );
    }
  };

  const handleFeatureCreated = (newFeature: any) => {
    // Check feature limit
    if (features.length >= maxFeatures) {
      Alert.alert(
        'Feature Limit Reached',
        `Maximum of ${maxFeatures} features allowed. Please delete some features before adding new ones.`,
        [{ text: 'OK' }]
      );
      return;
    }

    // Convert to stored feature format
    const storedFeature: StoredFeature = {
      id: newFeature.id || `feature_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: newFeature.type,
      coordinates: newFeature.coordinates,
      properties: {
        name: newFeature.properties?.name || `${newFeature.type.charAt(0).toUpperCase() + newFeature.type.slice(1)} ${features.length + 1}`,
        description: newFeature.properties?.description || '',
        color: newFeature.properties?.color || theme.colors.primary,
        created: newFeature.properties?.created || Date.now(),
        measurements: newFeature.properties?.measurements,
      },
    };

    const updatedFeatures = [...features, storedFeature];
    setFeatures(updatedFeatures);
    saveFeatures(updatedFeatures);

    // Notify parent component
    if (onFeatureCreated) {
      onFeatureCreated(storedFeature);
    }

    console.log('Feature created:', storedFeature.id);
  };

  const handleFeatureDeleted = (featureId: string) => {
    const updatedFeatures = features.filter(f => f.id !== featureId);
    setFeatures(updatedFeatures);
    saveFeatures(updatedFeatures);

    // Notify parent component
    if (onFeatureDeleted) {
      onFeatureDeleted(featureId);
    }

    console.log('Feature deleted:', featureId);
  };

  const handleLocationSelect = (location: { latitude: number; longitude: number }) => {
    // Update region to center on selected location
    setRegion({
      latitude: location.latitude,
      longitude: location.longitude,
      latitudeDelta: 0.01,
      longitudeDelta: 0.01,
    });

    console.log('Location selected:', location);
  };

  const exportFeatures = async () => {
    if (features.length === 0) {
      Alert.alert('No Features', 'No features to export.');
      return;
    }

    try {
      // Create GeoJSON format export
      const geoJSON = {
        type: 'FeatureCollection',
        features: features.map(feature => ({
          type: 'Feature',
          geometry: {
            type: feature.type === 'point' ? 'Point' : 
                  feature.type === 'line' ? 'LineString' : 
                  feature.type === 'polygon' ? 'Polygon' :
                  feature.type === 'circle' ? 'Point' : // Circles as points with radius property
                  feature.type === 'rectangle' ? 'Polygon' : 'Point',
            coordinates: feature.coordinates,
          },
          properties: {
            ...feature.properties,
            id: feature.id,
            featureType: feature.type, // Preserve original type
          },
        })),
        metadata: {
          projectId,
          exportedAt: new Date().toISOString(),
          totalFeatures: features.length,
          version: '2.0',
        }
      };

      // In a real app, you would use a file sharing library
      // For now, we'll show the export data in console and alert
      console.log('Exported GeoJSON:', JSON.stringify(geoJSON, null, 2));
      
      Alert.alert(
        'Export Complete',
        `Successfully prepared ${features.length} features for export.\n\nGeoJSON data has been logged to console for development.`,
        [
          { text: 'OK' },
          { 
            text: 'Copy Data', 
            onPress: () => {
              // In a real app, copy to clipboard
              console.log('GeoJSON Export Data:', JSON.stringify(geoJSON, null, 2));
            }
          }
        ]
      );
    } catch (error) {
      console.error('Export error:', error);
      Alert.alert('Export Error', 'Failed to export features.');
    }
  };

  const clearAllFeatures = () => {
    Alert.alert(
      'Clear All Features',
      `Are you sure you want to delete all ${features.length} features? This cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear All',
          style: 'destructive',
          onPress: () => {
            setFeatures([]);
            saveFeatures([]);
            console.log('All features cleared');
          },
        },
      ]
    );
  };

  const getFeatureStats = () => {
    const stats = {
      total: features.length,
      points: features.filter(f => f.type === 'point').length,
      lines: features.filter(f => f.type === 'line').length,
      polygons: features.filter(f => f.type === 'polygon').length,
      circles: features.filter(f => f.type === 'circle').length,
      rectangles: features.filter(f => f.type === 'rectangle').length,
    };
    return stats;
  };

  if (loading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: theme.colors.background }]}>
        {/* Loading indicator would go here */}
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ProfessionalMapUI
        initialRegion={region}
        geoFeatures={features}
        onFeatureCreated={handleFeatureCreated}
        onFeatureDeleted={handleFeatureDeleted}
        onLocationSelect={handleLocationSelect}
        enableDrawing={enableDrawing}
        enableMeasurement={enableMeasurement}
        enableAnalysis={enableAnalysis}
        maxFeatures={maxFeatures}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
