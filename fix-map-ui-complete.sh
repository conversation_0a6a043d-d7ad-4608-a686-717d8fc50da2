#!/bin/bash
# Fix Map UI Script for Unix/Mac systems

echo "======================================="
echo "Fixing FieldSyncPro Map UI Issues"
echo "======================================="
echo

# Navigate to project directory
cd "$(dirname "$0")"
echo "Current directory: $(pwd)"
echo

# Clear all caches
echo "Clearing all caches..."

# Clear Metro cache
echo "1. Clearing Metro cache..."
rm -rf $TMPDIR/metro-*
rm -rf $TMPDIR/haste-*
rm -rf $TMPDIR/react-*

# Clear Expo cache
echo "2. Clearing Expo cache..."
rm -rf .expo
rm -rf ~/.expo

# Clear watchman cache if exists
echo "3. Clearing Watchman cache..."
if command -v watchman &> /dev/null; then
    watchman watch-del-all
fi

# Clear node_modules and reinstall
echo "4. Reinstalling dependencies..."
rm -rf node_modules
rm -f package-lock.json

echo "Installing fresh dependencies..."
npm install

if [ $? -ne 0 ]; then
    echo "ERROR: Failed to install dependencies"
    exit 1
fi

echo
echo "======================================="
echo "Starting FieldSyncPro with cleared caches"
echo "======================================="
echo

# Start Expo with clear cache
echo "Starting Expo development server..."
npx expo start --clear

echo
echo "======================================="
echo "Instructions:"
echo "======================================="
echo "1. Press 'a' for Android or 'w' for web"
echo "2. Navigate to the map screen"
echo "3. Test the new UI layout:"
echo "   - Menu button (bottom left) opens toolbar"
echo "   - Toolbar slides from bottom"
echo "   - Drawing tools work properly"
echo "   - No overlapping UI elements"
echo "   - Zoom controls on right side"
echo "   - Status badges at top"
echo
echo "If you still see errors:"
echo "1. Press Ctrl+C to stop"
echo "2. Run: npx expo doctor"
echo "3. Run: npm audit fix"
echo "======================================="
