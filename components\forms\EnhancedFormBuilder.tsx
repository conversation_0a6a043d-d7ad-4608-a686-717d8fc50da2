import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Modal,
  TextInput,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '@/hooks/useTheme';
import { FormSchema, FormPage, FormSection, FormQuestion, QuestionType } from '@/types';
import {
  Plus,
  Edit3,
  Trash2,
  ChevronUp,
  ChevronDown,
  FileText,
  Layers,
  HelpCircle,
  Save,
  Eye,
  X,
  Copy,
  Move,
  Grid3X3,
} from 'lucide-react-native';

interface EnhancedFormBuilderProps {
  initialSchema?: FormSchema;
  onSave: (schema: FormSchema) => void;
  onPreview: (schema: FormSchema) => void;
  disabled?: boolean;
}

const questionTypeOptions: Array<{ value: QuestionType; label: string; description: string }> = [
  { value: 'text', label: 'Text Input', description: 'Single or multi-line text' },
  { value: 'number', label: 'Number Input', description: 'Numeric values only' },
  { value: 'select', label: 'Single Choice', description: 'Select one option' },
  { value: 'multiselect', label: 'Multiple Choice', description: 'Select multiple options' },
  { value: 'date', label: 'Date Picker', description: 'Select a date' },
  { value: 'time', label: 'Time Picker', description: 'Select a time' },
  { value: 'datetime', label: 'Date & Time', description: 'Select date and time' },
  { value: 'phone', label: 'Phone Number', description: 'Phone with validation' },
  { value: 'location', label: 'GPS Location', description: 'Capture GPS coordinates' },
  { value: 'photo', label: 'Photo Capture', description: 'Take or select multiple photos' },
  { value: 'audio', label: 'Audio Recording', description: 'Record audio notes' },
  { value: 'video', label: 'Video Recording', description: 'Record video content' },
  { value: 'qr_scan', label: 'QR Code Scanner', description: 'Scan QR codes' },
  { value: 'barcode', label: 'Barcode Scanner', description: 'Scan product barcodes' },
  { value: 'signature', label: 'Digital Signature', description: 'Capture signatures' },
];

export default function EnhancedFormBuilder({
  initialSchema,
  onSave,
  onPreview,
  disabled = false,
}: EnhancedFormBuilderProps) {
  const { theme } = useTheme();
  const [schema, setSchema] = useState<FormSchema>(
    initialSchema || { pages: [], logicRules: [] }
  );
  const [selectedPage, setSelectedPage] = useState<number>(0);
  const [selectedSection, setSelectedSection] = useState<number | null>(null);
  const [showPageEditor, setShowPageEditor] = useState(false);
  const [showSectionEditor, setShowSectionEditor] = useState(false);
  const [showQuestionEditor, setShowQuestionEditor] = useState(false);
  const [editingPage, setEditingPage] = useState<FormPage | null>(null);
  const [editingSection, setEditingSection] = useState<FormSection | null>(null);
  const [editingQuestion, setEditingQuestion] = useState<FormQuestion | null>(null);

  // Initialize with at least one page if empty
  useEffect(() => {
    if (schema.pages.length === 0) {
      const defaultPage: FormPage = {
        id: `page_${Date.now()}`,
        title: 'Page 1',
        description: '',
        order: 0,
        sections: [],
      };
      setSchema(prev => ({ ...prev, pages: [defaultPage] }));
    }
  }, []);

  const addPage = () => {
    const newPage: FormPage = {
      id: `page_${Date.now()}`,
      title: `Page ${schema.pages.length + 1}`,
      description: '',
      order: schema.pages.length,
      sections: [],
    };
    
    setSchema(prev => ({
      ...prev,
      pages: [...prev.pages, newPage],
    }));
    
    setSelectedPage(schema.pages.length);
  };

  const updatePage = (pageIndex: number, updatedPage: FormPage) => {
    setSchema(prev => ({
      ...prev,
      pages: prev.pages.map((page, index) => 
        index === pageIndex ? updatedPage : page
      ),
    }));
  };

  const deletePage = (pageIndex: number) => {
    if (schema.pages.length <= 1) {
      Alert.alert('Cannot Delete', 'At least one page is required');
      return;
    }

    Alert.alert(
      'Delete Page',
      'Are you sure you want to delete this page and all its content?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            setSchema(prev => ({
              ...prev,
              pages: prev.pages
                .filter((_, index) => index !== pageIndex)
                .map((page, index) => ({ ...page, order: index })),
            }));
            
            if (selectedPage >= schema.pages.length - 1) {
              setSelectedPage(Math.max(0, schema.pages.length - 2));
            }
          },
        },
      ]
    );
  };

  const addSection = () => {
    const currentPage = schema.pages[selectedPage];
    if (!currentPage) return;

    const newSection: FormSection = {
      id: `section_${Date.now()}`,
      title: `Section ${currentPage.sections.length + 1}`,
      description: '',
      order: currentPage.sections.length,
      questions: [],
    };

    const updatedPage = {
      ...currentPage,
      sections: [...currentPage.sections, newSection],
    };

    updatePage(selectedPage, updatedPage);
  };

  const updateSection = (sectionIndex: number, updatedSection: FormSection) => {
    const currentPage = schema.pages[selectedPage];
    if (!currentPage) return;

    const updatedPage = {
      ...currentPage,
      sections: currentPage.sections.map((section, index) =>
        index === sectionIndex ? updatedSection : section
      ),
    };

    updatePage(selectedPage, updatedPage);
  };

  const deleteSection = (sectionIndex: number) => {
    const currentPage = schema.pages[selectedPage];
    if (!currentPage) return;

    Alert.alert(
      'Delete Section',
      'Are you sure you want to delete this section and all its questions?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            const updatedPage = {
              ...currentPage,
              sections: currentPage.sections
                .filter((_, index) => index !== sectionIndex)
                .map((section, index) => ({ ...section, order: index })),
            };
            
            updatePage(selectedPage, updatedPage);
            
            if (selectedSection === sectionIndex) {
              setSelectedSection(null);
            }
          },
        },
      ]
    );
  };

  const addQuestion = (sectionIndex: number, questionType: QuestionType) => {
    const currentPage = schema.pages[selectedPage];
    if (!currentPage || !currentPage.sections[sectionIndex]) return;

    const section = currentPage.sections[sectionIndex];
    const newQuestion: FormQuestion = {
      id: `question_${Date.now()}`,
      type: questionType,
      label: `New ${questionType} question`,
      required: false,
      description: '',
      placeholder: '',
      defaultValue: null,
      validation: [],
      options: questionType === 'select' || questionType === 'multiselect' ? [
        { label: 'Option 1', value: 'option1' },
        { label: 'Option 2', value: 'option2' },
      ] : undefined,
      properties: {},
    };

    const updatedSection = {
      ...section,
      questions: [...section.questions, newQuestion],
    };

    updateSection(sectionIndex, updatedSection);
  };

  const updateQuestion = (sectionIndex: number, questionIndex: number, updatedQuestion: FormQuestion) => {
    const currentPage = schema.pages[selectedPage];
    if (!currentPage || !currentPage.sections[sectionIndex]) return;

    const section = currentPage.sections[sectionIndex];
    const updatedSection = {
      ...section,
      questions: section.questions.map((question, index) =>
        index === questionIndex ? updatedQuestion : question
      ),
    };

    updateSection(sectionIndex, updatedSection);
  };

  const deleteQuestion = (sectionIndex: number, questionIndex: number) => {
    const currentPage = schema.pages[selectedPage];
    if (!currentPage || !currentPage.sections[sectionIndex]) return;

    Alert.alert(
      'Delete Question',
      'Are you sure you want to delete this question?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            const section = currentPage.sections[sectionIndex];
            const updatedSection = {
              ...section,
              questions: section.questions.filter((_, index) => index !== questionIndex),
            };
            
            updateSection(sectionIndex, updatedSection);
          },
        },
      ]
    );
  };

  const renderPageTabs = () => (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      style={styles.pageTabsContainer}
      contentContainerStyle={styles.pageTabsContent}
    >
      {schema.pages.map((page, index) => (
        <TouchableOpacity
          key={page.id}
          style={[
            styles.pageTab,
            {
              backgroundColor: selectedPage === index ? theme.colors.primary : theme.colors.card,
              borderColor: selectedPage === index ? theme.colors.primary : theme.colors.border,
            }
          ]}
          onPress={() => setSelectedPage(index)}
        >
          <Text style={[
            styles.pageTabText,
            {
              color: selectedPage === index ? 'white' : theme.colors.text,
            }
          ]}>
            {page.title}
          </Text>
          
          {!disabled && (
            <TouchableOpacity
              style={styles.pageTabEdit}
              onPress={() => {
                setEditingPage(page);
                setShowPageEditor(true);
              }}
            >
              <Edit3 size={12} color={selectedPage === index ? 'white' : theme.colors.muted} />
            </TouchableOpacity>
          )}
        </TouchableOpacity>
      ))}
      
      {!disabled && (
        <TouchableOpacity
          style={[styles.addPageButton, { backgroundColor: theme.colors.muted }]}
          onPress={addPage}
        >
          <Plus size={16} color="white" />
        </TouchableOpacity>
      )}
    </ScrollView>
  );

  const renderQuestionTypeSelector = (sectionIndex: number) => (
    <View style={styles.questionTypeGrid}>
      {questionTypeOptions.map((option) => (
        <TouchableOpacity
          key={option.value}
          style={[styles.questionTypeCard, { backgroundColor: theme.colors.card }]}
          onPress={() => addQuestion(sectionIndex, option.value)}
        >
          <Text style={[styles.questionTypeLabel, { color: theme.colors.text }]}>
            {option.label}
          </Text>
          <Text style={[styles.questionTypeDescription, { color: theme.colors.muted }]}>
            {option.description}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderCurrentPage = () => {
    const currentPage = schema.pages[selectedPage];
    if (!currentPage) return null;

    return (
      <View style={styles.pageContent}>
        <View style={styles.pageHeader}>
          <View style={styles.pageInfo}>
            <Text style={[styles.pageTitle, { color: theme.colors.text }]}>
              {currentPage.title}
            </Text>
            {currentPage.description && (
              <Text style={[styles.pageDescription, { color: theme.colors.muted }]}>
                {currentPage.description}
              </Text>
            )}
          </View>
          
          {!disabled && (
            <View style={styles.pageActions}>
              <TouchableOpacity
                style={[styles.actionButton, { backgroundColor: theme.colors.secondary }]}
                onPress={() => {
                  setEditingPage(currentPage);
                  setShowPageEditor(true);
                }}
              >
                <Edit3 size={16} color="white" />
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.actionButton, { backgroundColor: theme.colors.primary }]}
                onPress={addSection}
              >
                <Plus size={16} color="white" />
              </TouchableOpacity>
            </View>
          )}
        </View>

        <ScrollView style={styles.sectionsContainer}>
          {currentPage.sections.map((section, sectionIndex) => (
            <View
              key={section.id}
              style={[styles.sectionCard, { backgroundColor: theme.colors.card }]}
            >
              <View style={styles.sectionHeader}>
                <View style={styles.sectionInfo}>
                  <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
                    {section.title}
                  </Text>
                  {section.description && (
                    <Text style={[styles.sectionDescription, { color: theme.colors.muted }]}>
                      {section.description}
                    </Text>
                  )}
                </View>
                
                {!disabled && (
                  <View style={styles.sectionActions}>
                    <TouchableOpacity
                      style={[styles.smallActionButton, { backgroundColor: theme.colors.muted }]}
                      onPress={() => {
                        setEditingSection(section);
                        setShowSectionEditor(true);
                      }}
                    >
                      <Edit3 size={12} color="white" />
                    </TouchableOpacity>
                    
                    <TouchableOpacity
                      style={[styles.smallActionButton, { backgroundColor: theme.colors.error }]}
                      onPress={() => deleteSection(sectionIndex)}
                    >
                      <Trash2 size={12} color="white" />
                    </TouchableOpacity>
                  </View>
                )}
              </View>

              {/* Questions List */}
              <View style={styles.questionsContainer}>
                {section.questions.map((question, questionIndex) => (
                  <View
                    key={question.id}
                    style={[styles.questionItem, { backgroundColor: theme.colors.background }]}
                  >
                    <View style={styles.questionInfo}>
                      <Text style={[styles.questionLabel, { color: theme.colors.text }]}>
                        {question.label}
                        {question.required && (
                          <Text style={{ color: theme.colors.error }}> *</Text>
                        )}
                      </Text>
                      <Text style={[styles.questionType, { color: theme.colors.muted }]}>
                        {questionTypeOptions.find(opt => opt.value === question.type)?.label || question.type}
                      </Text>
                    </View>
                    
                    {!disabled && (
                      <View style={styles.questionActions}>
                        <TouchableOpacity
                          style={[styles.smallActionButton, { backgroundColor: theme.colors.secondary }]}
                          onPress={() => {
                            setEditingQuestion(question);
                            setShowQuestionEditor(true);
                          }}
                        >
                          <Edit3 size={12} color="white" />
                        </TouchableOpacity>
                        
                        <TouchableOpacity
                          style={[styles.smallActionButton, { backgroundColor: theme.colors.error }]}
                          onPress={() => deleteQuestion(sectionIndex, questionIndex)}
                        >
                          <Trash2 size={12} color="white" />
                        </TouchableOpacity>
                      </View>
                    )}
                  </View>
                ))}
                
                {!disabled && (
                  <View style={styles.addQuestionSection}>
                    <Text style={[styles.addQuestionTitle, { color: theme.colors.text }]}>
                      Add Question
                    </Text>
                    {renderQuestionTypeSelector(sectionIndex)}
                  </View>
                )}
              </View>
            </View>
          ))}
          
          {currentPage.sections.length === 0 && (
            <View style={[styles.emptyState, { backgroundColor: theme.colors.card }]}>
              <Layers size={48} color={theme.colors.muted} />
              <Text style={[styles.emptyTitle, { color: theme.colors.text }]}>
                No sections yet
              </Text>
              <Text style={[styles.emptyDescription, { color: theme.colors.muted }]}>
                Add sections to organize your form questions
              </Text>
              
              {!disabled && (
                <TouchableOpacity
                  style={[styles.emptyButton, { backgroundColor: theme.colors.primary }]}
                  onPress={addSection}
                >
                  <Plus size={20} color="white" />
                  <Text style={styles.emptyButtonText}>Add Section</Text>
                </TouchableOpacity>
              )}
            </View>
          )}
        </ScrollView>
      </View>
    );
  };

  const renderActions = () => {
    if (disabled) return null;

    return (
      <View style={[styles.actionsBar, { backgroundColor: theme.colors.card }]}>
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: theme.colors.secondary }]}
          onPress={() => onPreview(schema)}
        >
          <Eye size={16} color="white" />
          <Text style={styles.actionButtonText}>Preview</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: theme.colors.primary }]}
          onPress={() => onSave(schema)}
        >
          <Save size={16} color="white" />
          <Text style={styles.actionButtonText}>Save Form</Text>
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {renderPageTabs()}
      {renderCurrentPage()}
      {renderActions()}
      
      {/* Modals for editing would go here */}
      {/* PageEditor, SectionEditor, QuestionEditor modals */}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  pageTabsContainer: {
    maxHeight: 60,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  pageTabsContent: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    gap: 8,
  },
  pageTab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    gap: 8,
  },
  pageTabText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
  },
  pageTabEdit: {
    padding: 2,
  },
  addPageButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  pageContent: {
    flex: 1,
  },
  pageHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  pageInfo: {
    flex: 1,
  },
  pageTitle: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    marginBottom: 4,
  },
  pageDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
  },
  pageActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    gap: 4,
  },
  actionButtonText: {
    color: 'white',
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
  },
  sectionsContainer: {
    flex: 1,
    padding: 16,
  },
  sectionCard: {
    borderRadius: 12,
    marginBottom: 16,
    overflow: 'hidden',
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  sectionInfo: {
    flex: 1,
  },
  sectionTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 4,
  },
  sectionDescription: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
  },
  sectionActions: {
    flexDirection: 'row',
    gap: 6,
  },
  smallActionButton: {
    padding: 6,
    borderRadius: 4,
  },
  questionsContainer: {
    padding: 16,
  },
  questionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  questionInfo: {
    flex: 1,
  },
  questionLabel: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    marginBottom: 2,
  },
  questionType: {
    fontSize: 11,
    fontFamily: 'Inter-Regular',
    textTransform: 'uppercase',
  },
  questionActions: {
    flexDirection: 'row',
    gap: 6,
  },
  addQuestionSection: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  addQuestionTitle: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 12,
  },
  questionTypeGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  questionTypeCard: {
    width: '48%',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  questionTypeLabel: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 4,
  },
  questionTypeDescription: {
    fontSize: 10,
    fontFamily: 'Inter-Regular',
    lineHeight: 14,
  },
  emptyState: {
    padding: 32,
    borderRadius: 12,
    alignItems: 'center',
    gap: 12,
  },
  emptyTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
  },
  emptyDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
  },
  emptyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
    marginTop: 8,
  },
  emptyButtonText: {
    color: 'white',
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
  },
  actionsBar: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    gap: 12,
  },
});
