// Core GIS Types for Advanced Map Components

export interface Coordinate {
  latitude: number;
  longitude: number;
}

export interface MapExtent {
  latitude: number;
  longitude: number;
  latitudeDelta: number;
  longitudeDelta: number;
}

export interface LayerStyle {
  strokeColor?: string;
  strokeWidth?: number;
  fillColor?: string;
  fillOpacity?: number;
  iconType?: string;
  iconSize?: number;
  labelField?: string;
  labelSize?: number;
  labelColor?: string;
  dashPattern?: number[];
}

export interface LayerMetadata {
  description: string;
  source: string;
  lastUpdated: string;
  properties: string[];
  geometryType: 'Point' | 'LineString' | 'Polygon' | 'MultiPoint' | 'MultiLineString' | 'MultiPolygon';
  featureCount: number;
  extent: {
    minX: number;
    minY: number;
    maxX: number;
    maxY: number;
  };
}

export interface MapLayer {
  id: string;
  name: string;
  type: 'vector' | 'raster' | 'tile' | 'wms' | 'wmts' | 'geojson' | 'kml' | 'shapefile';
  source: 'catalog' | 'upload' | 'external';
  visible: boolean;
  opacity: number;
  minZoom?: number;
  maxZoom?: number;
  style?: LayerStyle;
  metadata?: LayerMetadata;
  data?: any;
  url?: string;
}

export interface GISFeature {
  id: string;
  type: 'point' | 'line' | 'polygon' | 'circle';
  coordinates: Coordinate[];
  properties: Record<string, any>;
  style?: LayerStyle;
  createdAt: string;
  updatedAt: string;
  author?: string;
}

export interface MeasurementResult {
  id: string;
  type: 'distance' | 'area' | 'bearing' | 'elevation';
  value: number;
  unit: string;
  coordinates: Coordinate[];
  timestamp: string;
  properties?: Record<string, any>;
}

export interface AnalysisResult {
  id: string;
  type: 'buffer' | 'clip' | 'dissolve' | 'merge' | 'union' | 'intersect' | 'spatial-join' | 'proximity' | 'isochrone' | 'grid-create' | 'hexbin';
  name: string;
  status: 'running' | 'completed' | 'failed';
  progress: number;
  parameters: Record<string, any>;
  result?: any;
  error?: string;
  startTime: string;
  endTime?: string;
  outputLayers?: string[];
  executionTime?: number;
  resourcesUsed?: {
    memory: number;
    cpu: number;
  };
}

export interface MapBookmark {
  id: string;
  name: string;
  description: string;
  extent: MapExtent;
  createdAt: string;
  updatedAt: string;
  author: string;
  tags: string[];
  thumbnail?: string;
  isPublic: boolean;
  isFavorite: boolean;
  visitCount: number;
  lastVisited?: string;
  category: 'personal' | 'project' | 'shared' | 'public';
  metadata?: {
    zoom: number;
    bearing?: number;
    pitch?: number;
    visibleLayers: string[];
    notes?: string;
  };
}

export interface StorySlide {
  id: string;
  title: string;
  description: string;
  content: string;
  duration: number; // seconds
  extent?: MapExtent;
  visibleLayers: string[];
  baseMap: string;
  media?: {
    type: 'image' | 'video';
    url: string;
    caption?: string;
    position: 'top' | 'bottom' | 'left' | 'right' | 'overlay';
    size: 'small' | 'medium' | 'large';
  };
  annotations?: Array<{
    id: string;
    type: 'text' | 'arrow' | 'highlight';
    position: { x: number; y: number };
    content: string;
    style: any;
  }>;
  transition?: {
    type: 'fade' | 'slide' | 'zoom' | 'none';
    duration: number;
  };
  interactive: boolean;
  autoAdvance: boolean;
}

export interface MapStory {
  id: string;
  title: string;
  description: string;
  author: string;
  createdAt: string;
  updatedAt: string;
  tags: string[];
  slides: StorySlide[];
  settings: {
    showProgress: boolean;
    showControls: boolean;
    allowSkipping: boolean;
    loop: boolean;
    theme: 'light' | 'dark' | 'auto';
    layout: 'sidebar' | 'overlay' | 'bottom';
  };
  sharing: {
    isPublic: boolean;
    allowComments: boolean;
    allowEmbedding: boolean;
    password?: string;
  };
  thumbnail?: string;
  viewCount?: number;
  likeCount?: number;
}

export interface MapSettings {
  // Basic Properties
  title: string;
  description: string;
  author: string;
  tags: string[];
  
  // Map Extent & View
  defaultExtent: MapExtent;
  minZoom: number;
  maxZoom: number;
  restrictPanning: boolean;
  
  // Coordinate System
  coordinateSystem: string;
  displayCoordinates: boolean;
  coordinateFormat: 'decimal' | 'dms' | 'utm' | 'mgrs';
  coordinatePrecision: number;
  
  // Base Map
  baseMapType: string;
  customBaseMapUrl?: string;
  baseMapOpacity: number;
  
  // UI Elements
  showScale: boolean;
  showNorthArrow: boolean;
  showZoomControls: boolean;
  showLayerPanel: boolean;
  showToolbar: boolean;
  showMeasurementTools: boolean;
  
  // Visual Theme
  primaryColor: string;
  secondaryColor: string;
  backgroundColor: string;
  textColor: string;
  fontFamily: string;
  fontSize: number;
  
  // Branding
  logoUrl?: string;
  logoPosition: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  logoSize: number;
  watermark?: string;
  
  // Performance
  enableWebGL: boolean;
  enable3D: boolean;
  renderQuality: 'low' | 'medium' | 'high';
  maxFeatures: number;
  clustering: boolean;
  
  // Security & Access
  requireAuthentication: boolean;
  allowDownload: boolean;
  allowPrint: boolean;
  allowShare: boolean;
  expirationDate?: string;
}

export interface NotificationMessage {
  id: string;
  type: 'success' | 'info' | 'warning' | 'error';
  message: string;
  timestamp: string;
  duration?: number;
  action?: {
    label: string;
    onPress: () => void;
  };
}

export interface SpatialOperation {
  id: string;
  name: string;
  description: string;
  category: 'proximity' | 'overlay' | 'generalization' | 'analysis' | 'network' | 'generation' | 'aggregation';
  parameters: Array<{
    key: string;
    label: string;
    type: 'number' | 'select' | 'boolean' | 'layer' | 'distance' | 'text';
    value?: any;
    options?: string[];
    unit?: string;
    min?: number;
    max?: number;
    required?: boolean;
    description?: string;
  }>;
  icon: any; // React component
  estimatedTime?: number; // seconds
  requiredLayers?: number;
  outputType?: 'layer' | 'table' | 'report';
}

export interface ExportOptions {
  format: 'png' | 'jpg' | 'pdf' | 'svg' | 'geojson' | 'shapefile' | 'kml' | 'gpx' | 'csv';
  quality?: 'low' | 'medium' | 'high';
  includeData?: boolean;
  includeLegend?: boolean;
  includeScale?: boolean;
  includeNorthArrow?: boolean;
  customSize?: {
    width: number;
    height: number;
  };
  dpi?: number;
  compression?: boolean;
}

export interface ImportOptions {
  format: 'shapefile' | 'geojson' | 'kml' | 'gpx' | 'csv' | 'excel' | 'geotiff';
  coordinateSystem?: string;
  encoding?: string;
  delimiter?: string; // for CSV
  latitudeField?: string; // for CSV
  longitudeField?: string; // for CSV
  preserveAttributes?: boolean;
  simplifyGeometry?: boolean;
  simplificationTolerance?: number;
}

export interface CollaborationUser {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role: 'owner' | 'editor' | 'viewer' | 'commentor';
  isOnline: boolean;
  lastSeen: string;
  cursor?: {
    x: number;
    y: number;
    color: string;
  };
}

export interface MapComment {
  id: string;
  content: string;
  author: CollaborationUser;
  createdAt: string;
  updatedAt: string;
  position?: Coordinate;
  layerId?: string;
  featureId?: string;
  replies?: MapComment[];
  status: 'open' | 'resolved' | 'closed';
  tags?: string[];
}

export interface VersionInfo {
  id: string;
  name: string;
  description: string;
  author: string;
  createdAt: string;
  changes: Array<{
    type: 'layer_added' | 'layer_removed' | 'layer_modified' | 'feature_added' | 'feature_removed' | 'feature_modified' | 'settings_changed';
    target: string;
    summary: string;
  }>;
  snapshot: {
    layers: MapLayer[];
    settings: MapSettings;
    bookmarks: MapBookmark[];
  };
}

// Utility Types
export type GeometryType = 'Point' | 'LineString' | 'Polygon' | 'MultiPoint' | 'MultiLineString' | 'MultiPolygon';
export type LayerType = 'vector' | 'raster' | 'tile' | 'wms' | 'wmts' | 'geojson' | 'kml' | 'shapefile';
export type CoordinateFormat = 'decimal' | 'dms' | 'utm' | 'mgrs';
export type AnalysisType = 'buffer' | 'clip' | 'dissolve' | 'merge' | 'union' | 'intersect' | 'spatial-join' | 'proximity' | 'isochrone';
export type MeasurementType = 'distance' | 'area' | 'bearing' | 'elevation';
export type DrawingTool = 'point' | 'line' | 'polygon' | 'circle' | 'rectangle' | 'freehand';
export type BaseMapType = 'satellite' | 'terrain' | 'street' | 'dark' | 'light' | 'hybrid' | 'osm' | 'custom';

// Event Types
export interface MapEvent {
  type: 'pan' | 'zoom' | 'click' | 'double-click' | 'long-press' | 'feature-select' | 'feature-hover';
  coordinate?: Coordinate;
  extent?: MapExtent;
  feature?: GISFeature;
  layer?: MapLayer;
  timestamp: string;
  user?: string;
}

export interface LayerEvent {
  type: 'added' | 'removed' | 'visibility-changed' | 'style-changed' | 'data-loaded' | 'data-error';
  layer: MapLayer;
  timestamp: string;
  user?: string;
  details?: any;
}

export interface AnalysisEvent {
  type: 'started' | 'progress' | 'completed' | 'failed' | 'cancelled';
  analysis: AnalysisResult;
  timestamp: string;
  user?: string;
}

// Hook Types
export interface UseMapState {
  mapRegion: MapExtent;
  layers: MapLayer[];
  selectedFeatures: GISFeature[];
  activeDrawingTool: DrawingTool | null;
  activeMeasurementTool: MeasurementType | null;
  isLoading: boolean;
  error: string | null;
}

export interface UseMapActions {
  setMapRegion: (region: MapExtent) => void;
  addLayer: (layer: MapLayer) => void;
  removeLayer: (layerId: string) => void;
  updateLayer: (layerId: string, updates: Partial<MapLayer>) => void;
  toggleLayerVisibility: (layerId: string) => void;
  selectFeature: (feature: GISFeature) => void;
  deselectFeature: (featureId: string) => void;
  clearSelection: () => void;
  setActiveDrawingTool: (tool: DrawingTool | null) => void;
  setActiveMeasurementTool: (tool: MeasurementType | null) => void;
  createFeature: (feature: Omit<GISFeature, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateFeature: (featureId: string, updates: Partial<GISFeature>) => void;
  deleteFeature: (featureId: string) => void;
}

// API Types
export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  error?: string;
  timestamp: string;
}

export interface LayerCatalogItem {
  id: string;
  name: string;
  description: string;
  category: string;
  tags: string[];
  type: LayerType;
  geometryType: GeometryType;
  extent: MapExtent;
  featureCount: number;
  size: number; // bytes
  lastUpdated: string;
  provider: string;
  license: string;
  downloadUrl: string;
  previewUrl?: string;
  metadata: LayerMetadata;
  rating: number;
  downloadCount: number;
  isPublic: boolean;
  cost?: number; // if premium layer
}

export interface MapTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  tags: string[];
  thumbnail: string;
  author: string;
  createdAt: string;
  settings: MapSettings;
  layers: MapLayer[];
  bookmarks: MapBookmark[];
  story?: MapStory;
  isPublic: boolean;
  rating: number;
  useCount: number;
  cost?: number;
}
