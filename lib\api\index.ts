// API Services Index
// Central export for all API services with robust error handling

export { BaseApi, ApiUtils } from './base';
export { AuthApi, authApi } from './auth';
export { ProjectsApi, projectsApi } from './projects';
export { TeamsApi, teamsApi } from './teams';
export { FormsApi, formsApi } from './forms';
export { SubmissionsApi, submissionsApi } from './submissions';

// Re-export types for convenience
export type {
  ApiResponse,
  PaginatedResponse,
  UserProfile,
  Team,
  TeamWithMembers,
  Project,
  ProjectWithStats,
  Form,
  Submission,
  SubmissionWithDetails,
  MediaAttachment
} from '@/types/database';

// API Configuration
export const API_CONFIG = {
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
  DEFAULT_TIMEOUT: 30000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000
};

// Helper function to create API clients with configuration
export const createApiClient = () => {
  return {
    auth: authApi,
    projects: projectsApi,
    teams: teamsApi,
    forms: formsApi,
    submissions: submissionsApi
  };
};

// Default API client instance
export const api = createApiClient();

// Utility functions for common operations
export const ApiHelpers = {
  /**
   * Handle API errors consistently across the app
   */
  handleApiError: (error: string | null, defaultMessage: string = 'An error occurred') => {
    if (!error) return null;
    
    // Check for common error patterns and provide user-friendly messages
    if (error.includes('JWT expired') || error.includes('Invalid JWT')) {
      return 'Your session has expired. Please log in again.';
    }
    
    if (error.includes('duplicate key')) {
      return 'This item already exists.';
    }
    
    if (error.includes('foreign key')) {
      return 'Cannot perform this action due to related data.';
    }
    
    if (error.includes('not null')) {
      return 'Required fields are missing.';
    }
    
    if (error.includes('permission denied') || error.includes('Row Level Security')) {
      return 'You do not have permission to perform this action.';
    }
    
    return error || defaultMessage;
  },

  /**
   * Format API response for UI consumption
   */
  formatApiResponse: <T>(response: { data: T | null; error: string | null }) => {
    return {
      success: !response.error && response.data !== null,
      data: response.data,
      error: ApiHelpers.handleApiError(response.error)
    };
  },

  /**
   * Check if response indicates authentication error
   */
  isAuthError: (error: string | null): boolean => {
    if (!error) return false;
    return error.includes('JWT') || 
           error.includes('authentication') || 
           error.includes('Unauthorized');
  },

  /**
   * Retry API call with exponential backoff
   */
  retryApiCall: async <T>(
    apiCall: () => Promise<{ data: T | null; error: string | null }>,
    maxRetries: number = API_CONFIG.RETRY_ATTEMPTS,
    baseDelay: number = API_CONFIG.RETRY_DELAY
  ): Promise<{ data: T | null; error: string | null }> => {
    let lastError: string | null = null;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        const result = await apiCall();
        
        // If successful or non-retryable error, return result
        if (!result.error || ApiHelpers.isAuthError(result.error)) {
          return result;
        }
        
        lastError = result.error;
        
        // Wait before retrying (exponential backoff)
        if (attempt < maxRetries) {
          const delay = baseDelay * Math.pow(2, attempt);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      } catch (error) {
        lastError = error instanceof Error ? error.message : 'Network error';
        
        // Wait before retrying
        if (attempt < maxRetries) {
          const delay = baseDelay * Math.pow(2, attempt);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }
    
    return {
      data: null,
      error: lastError || 'Max retry attempts exceeded'
    };
  },

  /**
   * Batch API calls with concurrency control
   */
  batchApiCalls: async <T>(
    apiCalls: Array<() => Promise<{ data: T | null; error: string | null }>>,
    concurrency: number = 5
  ): Promise<Array<{ data: T | null; error: string | null }>> => {
    const results: Array<{ data: T | null; error: string | null }> = [];
    
    for (let i = 0; i < apiCalls.length; i += concurrency) {
      const batch = apiCalls.slice(i, i + concurrency);
      const batchResults = await Promise.all(
        batch.map(call => call().catch(error => ({
          data: null,
          error: error instanceof Error ? error.message : 'Unknown error'
        })))
      );
      results.push(...batchResults);
    }
    
    return results;
  },

  /**
   * Format pagination info for UI
   */
  formatPaginationInfo: (response: PaginatedResponse<any>) => {
    return {
      currentPage: response.page,
      totalPages: response.totalPages,
      totalItems: response.totalCount,
      itemsPerPage: response.pageSize,
      hasNextPage: response.page < response.totalPages,
      hasPreviousPage: response.page > 1,
      startItem: ((response.page - 1) * response.pageSize) + 1,
      endItem: Math.min(response.page * response.pageSize, response.totalCount)
    };
  }
};

export default api;
