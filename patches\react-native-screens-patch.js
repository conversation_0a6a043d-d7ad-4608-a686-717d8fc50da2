/**
 * React Native Screens Patch
 * Fixes the "Screen_1.isScreen is not a function" error
 * by ensuring proper initialization and providing fallbacks
 */

import { Platform } from 'react-native';

let screensModule = null;
let isInitialized = false;

/**
 * Safe initialization of react-native-screens
 */
export const initializeScreensSafely = () => {
  if (Platform.OS === 'web') {
    // Web doesn't need react-native-screens
    isInitialized = true;
    return true;
  }

  if (isInitialized) {
    return true;
  }

  try {
    screensModule = require('react-native-screens');
    
    if (screensModule && typeof screensModule.enableScreens === 'function') {
      screensModule.enableScreens();
      isInitialized = true;
      console.log('✅ react-native-screens initialized successfully');
      return true;
    } else {
      console.warn('⚠️ react-native-screens.enableScreens not available');
      return false;
    }
  } catch (error) {
    console.warn('⚠️ Failed to initialize react-native-screens:', error);
    return false;
  }
};

/**
 * Safe isScreen function with fallback
 */
export const isScreenSafe = (component) => {
  if (Platform.OS === 'web') {
    // On web, we don't have screens, so return false
    return false;
  }

  try {
    if (screensModule && typeof screensModule.isScreen === 'function') {
      return screensModule.isScreen(component);
    }
    
    // Fallback: check if component has screen-like properties
    if (component && typeof component === 'object') {
      return !!(component.type && component.type.displayName && 
               component.type.displayName.includes('Screen'));
    }
    
    return false;
  } catch (error) {
    console.warn('⚠️ isScreen check failed:', error);
    return false;
  }
};

/**
 * Patch the global react-native-screens module
 */
export const patchReactNativeScreens = () => {
  if (Platform.OS === 'web') {
    return; // No patching needed on web
  }

  try {
    // Initialize screens first
    initializeScreensSafely();
    
    // Patch the module if it exists
    if (screensModule) {
      // Ensure isScreen function exists and is safe
      if (!screensModule.isScreen || typeof screensModule.isScreen !== 'function') {
        screensModule.isScreen = isScreenSafe;
      }
      
      // Wrap the original isScreen function for safety
      const originalIsScreen = screensModule.isScreen;
      screensModule.isScreen = (component) => {
        try {
          return originalIsScreen(component);
        } catch (error) {
          console.warn('⚠️ isScreen error caught and handled:', error);
          return isScreenSafe(component);
        }
      };
    }
  } catch (error) {
    console.warn('⚠️ Failed to patch react-native-screens:', error);
  }
};

// Auto-initialize when this module is imported
if (Platform.OS !== 'web') {
  // Use setTimeout to ensure this runs after the module system is ready
  setTimeout(() => {
    patchReactNativeScreens();
  }, 0);
}

export default {
  initializeScreensSafely,
  isScreenSafe,
  patchReactNativeScreens,
};
