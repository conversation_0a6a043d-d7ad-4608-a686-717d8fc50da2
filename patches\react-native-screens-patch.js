/**
 * React Native Screens Patch
 * Fixes the "Screen_1.isScreen is not a function" error
 * by ensuring proper initialization and providing fallbacks
 */

import { Platform } from 'react-native';

let screensModule = null;
let isInitialized = false;
let patchApplied = false;

/**
 * Safe initialization of react-native-screens
 */
export const initializeScreensSafely = () => {
  if (Platform.OS === 'web') {
    // Web doesn't need react-native-screens
    isInitialized = true;
    return true;
  }

  if (isInitialized) {
    return true;
  }

  try {
    screensModule = require('react-native-screens');
    
    if (screensModule && typeof screensModule.enableScreens === 'function') {
      screensModule.enableScreens();
      isInitialized = true;
      console.log('✅ react-native-screens initialized successfully');
      return true;
    } else {
      console.warn('⚠️ react-native-screens.enableScreens not available');
      return false;
    }
  } catch (error) {
    console.warn('⚠️ Failed to initialize react-native-screens:', error);
    return false;
  }
};

/**
 * Safe isScreen function with fallback
 */
export const isScreenSafe = (component) => {
  if (Platform.OS === 'web') {
    // On web, we don't have screens, so return false
    return false;
  }

  try {
    if (screensModule && typeof screensModule.isScreen === 'function') {
      return screensModule.isScreen(component);
    }
    
    // Fallback: check if component has screen-like properties
    if (component && typeof component === 'object') {
      return !!(component.type && component.type.displayName && 
               component.type.displayName.includes('Screen'));
    }
    
    return false;
  } catch (error) {
    console.warn('⚠️ isScreen check failed:', error);
    return false;
  }
};

/**
 * Patch the global react-native-screens module
 */
export const patchReactNativeScreens = () => {
  if (Platform.OS === 'web' || patchApplied) {
    return; // No patching needed on web or already patched
  }

  try {
    // Initialize screens first
    initializeScreensSafely();

    // Patch the module if it exists
    if (screensModule) {
      // Ensure isScreen function exists and is safe
      if (!screensModule.isScreen || typeof screensModule.isScreen !== 'function') {
        screensModule.isScreen = isScreenSafe;
      }

      // Wrap the original isScreen function for safety
      const originalIsScreen = screensModule.isScreen;
      screensModule.isScreen = (component) => {
        try {
          return originalIsScreen(component);
        } catch (error) {
          console.warn('⚠️ isScreen error caught and handled:', error);
          return isScreenSafe(component);
        }
      };

      patchApplied = true;
      console.log('✅ react-native-screens patch applied successfully');
    }
  } catch (error) {
    console.warn('⚠️ Failed to patch react-native-screens:', error);
  }
};

// Immediate initialization for react-native-screens
if (Platform.OS !== 'web') {
  try {
    // Patch immediately when module is imported
    patchReactNativeScreens();

    // Also patch the global require cache
    const Module = require('module');
    const originalRequire = Module.prototype.require;

    Module.prototype.require = function(id) {
      const result = originalRequire.apply(this, arguments);

      // Intercept react-native-screens imports and ensure they're patched
      if (id === 'react-native-screens' && result && !result._patched) {
        console.log('🔧 Patching react-native-screens on require...');

        // Ensure isScreen function exists
        if (!result.isScreen || typeof result.isScreen !== 'function') {
          result.isScreen = isScreenSafe;
        }

        // Wrap existing isScreen for safety
        const originalIsScreen = result.isScreen;
        result.isScreen = (component) => {
          try {
            return originalIsScreen(component);
          } catch (error) {
            console.warn('⚠️ isScreen error handled:', error);
            return isScreenSafe(component);
          }
        };

        result._patched = true;
        console.log('✅ react-native-screens patched on require');
      }

      return result;
    };
  } catch (error) {
    console.warn('⚠️ Failed to set up require patch:', error);
  }
}

export default {
  initializeScreensSafely,
  isScreenSafe,
  patchReactNativeScreens,
};
