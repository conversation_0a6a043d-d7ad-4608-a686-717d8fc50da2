@echo off
echo 🔧 FieldSync Pro - Quick Fix ^& Restart
echo ======================================

:: Stop all processes
echo 1. 🛑 Stopping all Metro/Expo processes...
taskkill /f /im "node.exe" 2>nul
taskkill /f /im "expo.exe" 2>nul

:: Clear Metro cache
echo 2. 🧹 Clearing Metro cache...
timeout /t 2 /nobreak >nul

:: Start fresh
echo 3. 🚀 Starting fresh development server...
npm run dev

if errorlevel 1 (
    echo.
    echo ❌ Failed to start. Trying alternative...
    npx expo start --clear
)

echo.
echo ✅ If issues persist, try:
echo    1. rmdir /s /q node_modules ^&^& npm install
echo    2. npm run reset-cache
echo    3. expo doctor
pause
