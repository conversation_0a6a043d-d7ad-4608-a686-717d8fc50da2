#!/bin/bash

# FieldSync Pro - Quick Test Script
# This script helps verify that all major issues have been resolved

echo "🔍 FieldSync Pro - Issue Verification Script"
echo "=============================================="

# Check if backup files were removed
echo "📁 Checking for backup files..."
backup_files=$(find ./app -name "*backup*" -o -name "*temp*" 2>/dev/null)
if [ -z "$backup_files" ]; then
    echo "✅ No backup files found"
else
    echo "⚠️  Backup files still exist:"
    echo "$backup_files"
fi

# Check for proper imports in key files
echo ""
echo "📦 Checking imports in key files..."

# Check DatabaseProvider
if grep -q "Platform" ./providers/DatabaseProvider.tsx 2>/dev/null; then
    echo "✅ DatabaseProvider has Platform import"
else
    echo "❌ DatabaseProvider missing Platform import"
fi

# Check AuthProvider
if grep -q "Platform" ./providers/AuthProvider.tsx 2>/dev/null; then
    echo "✅ AuthProvider has Platform import"
else
    echo "❌ AuthProvider missing Platform import"
fi

# Check for font family issues
echo ""
echo "🔤 Checking for font family references..."
font_issues=$(grep -r "fontFamily.*Inter" ./components 2>/dev/null)
if [ -z "$font_issues" ]; then
    echo "✅ No problematic font family references found"
else
    echo "⚠️  Font family references still exist:"
    echo "$font_issues"
fi

# Check tab layout for icons
echo ""
echo "🎨 Checking tab layout configuration..."
if grep -q "tabBarIcon.*color.*size" ./app/\(tabs\)/_layout.tsx 2>/dev/null; then
    echo "✅ Tab layout has proper icon configuration"
else
    echo "❌ Tab layout missing proper icon configuration"
fi

# Check settings hook for database context
echo ""
echo "⚙️  Checking settings hook integration..."
if grep -q "DatabaseContext" ./hooks/useSettings.ts 2>/dev/null; then
    echo "✅ Settings hook has database integration"
else
    echo "❌ Settings hook missing database integration"
fi

# Check for proper error handling
echo ""
echo "🛡️  Checking error handling patterns..."
try_catch_count=$(grep -r "try {" ./providers ./hooks 2>/dev/null | wc -l)
if [ "$try_catch_count" -gt 5 ]; then
    echo "✅ Good error handling coverage ($try_catch_count try-catch blocks found)"
else
    echo "⚠️  Limited error handling coverage ($try_catch_count try-catch blocks found)"
fi

# Check package.json for required dependencies
echo ""
echo "📋 Checking required dependencies..."
required_deps=("expo-sqlite" "expo-secure-store" "lucide-react-native" "zustand")
missing_deps=()

for dep in "${required_deps[@]}"; do
    if grep -q "\"$dep\"" ./package.json 2>/dev/null; then
        echo "✅ $dep is installed"
    else
        echo "❌ $dep is missing"
        missing_deps+=("$dep")
    fi
done

# Summary
echo ""
echo "📊 SUMMARY"
echo "=========="
if [ ${#missing_deps[@]} -eq 0 ] && [ -z "$backup_files" ]; then
    echo "🎉 All major issues appear to be resolved!"
    echo "   Ready for testing with: npm run dev"
else
    echo "⚠️  Some issues may still need attention:"
    if [ ${#missing_deps[@]} -gt 0 ]; then
        echo "   - Missing dependencies: ${missing_deps[*]}"
    fi
    if [ ! -z "$backup_files" ]; then
        echo "   - Backup files still present"
    fi
fi

echo ""
echo "🚀 Next steps:"
echo "   1. Run: npm run dev"
echo "   2. Navigate to Settings tab"
echo "   3. Check console for errors"
echo "   4. Visit /diagnostics for detailed status"
echo "   5. Test theme toggle and settings persistence"
