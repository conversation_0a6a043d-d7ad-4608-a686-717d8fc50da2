const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');

const config = getDefaultConfig(__dirname);

// Configure resolver for web platform to avoid native-only modules
config.resolver = {
  ...config.resolver,
  alias: {
    // Redirect react-native-maps to web polyfill
    'react-native-maps': path.resolve(__dirname, 'polyfills/react-native-maps.web.js'),
    'react-native-maps/lib/MapMarkerNativeComponent': path.resolve(__dirname, 'polyfills/MapMarkerNativeComponent.web.js'),
    'react-native/Libraries/Utilities/codegenNativeCommands': path.resolve(__dirname, 'polyfills/codegenNativeCommands.web.js'),
    // WebSocket polyfills for React Native
    'ws': path.resolve(__dirname, 'polyfills/ws.native.js'),
    // Platform-specific Supabase configuration
    '@/lib/supabase': path.resolve(__dirname, 'lib/supabase.ts'),
  },
  // Exclude Node.js modules from mobile bundles
  blockList: [
    // Block entire ws package for mobile platforms
    /node_modules\/ws\//,
    // Block Node.js specific modules
    /node_modules\/.*\/lib\/.*stream.*\.js$/,
    /node_modules\/.*\/lib\/.*buffer.*\.js$/,
    /node_modules\/.*\/lib\/.*util\.js$/,
    // Block specific problematic files
    /node_modules\/@supabase\/realtime-js\/.*ws.*\.js$/,
    /node_modules\/@supabase\/realtime-js\/.*websocket.*\.js$/,
  ],
  resolveRequest: (context, moduleName, platform) => {
    // For mobile platforms, resolve ws and related modules to our polyfill
    if ((platform === 'ios' || platform === 'android')) {
      if (moduleName === 'ws' || moduleName.includes('ws/')) {
        return {
          filePath: path.resolve(__dirname, 'polyfills/ws.native.js'),
          type: 'sourceFile',
        };
      }

      // Block Node.js stream module
      if (moduleName === 'stream' || moduleName.includes('stream')) {
        return {
          filePath: path.resolve(__dirname, 'polyfills/stream.native.js'),
          type: 'sourceFile',
        };
      }

      // Use native Supabase config
      if (moduleName.includes('lib/supabase')) {
        return {
          filePath: path.resolve(__dirname, 'lib/supabase.native.ts'),
          type: 'sourceFile',
        };
      }
    }

    // For web platform, resolve native modules to polyfills
    if (platform === 'web') {
      if (moduleName === 'react-native-maps') {
        return {
          filePath: path.resolve(__dirname, 'polyfills/react-native-maps.web.js'),
          type: 'sourceFile',
        };
      }
      if (moduleName.includes('codegenNativeCommands')) {
        return {
          filePath: path.resolve(__dirname, 'polyfills/codegenNativeCommands.web.js'),
          type: 'sourceFile',
        };
      }
      if (moduleName.includes('MapMarkerNativeComponent')) {
        return {
          filePath: path.resolve(__dirname, 'polyfills/MapMarkerNativeComponent.web.js'),
          type: 'sourceFile',
        };
      }
    }

    // Use default resolver for other cases
    return context.resolveRequest(context, moduleName, platform);
  },
  // Platform-specific extensions
  platforms: ['web', 'ios', 'android', 'native'],
  sourceExts: [...config.resolver.sourceExts, 'tsx', 'ts', 'jsx', 'js'],
};

// Enhanced transformer configuration
config.transformer = {
  ...config.transformer,
  getTransformOptions: async () => ({
    transform: {
      experimentalImportSupport: false,
      inlineRequires: false,
    },
  }),
  minifierConfig: {
    keep_fnames: true,
    mangle: {
      keep_fnames: true,
    },
  },
};

// Add server configuration to handle file system errors
config.server = {
  ...config.server,
  enhanceMiddleware: (middleware) => {
    return (req, res, next) => {
      // Handle symbolication errors gracefully
      if (req.url && req.url.includes('symbolicate')) {
        try {
          return middleware(req, res, next);
        } catch (error) {
          if (error.code === 'EISDIR') {
            console.warn('⚠️ Symbolication error handled:', error.message);
            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ stack: [] }));
            return;
          }
          throw error;
        }
      }
      return middleware(req, res, next);
    };
  },
};

module.exports = config;
