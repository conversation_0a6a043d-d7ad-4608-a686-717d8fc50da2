const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');

const config = getDefaultConfig(__dirname);

// Configure resolver for web platform to avoid native-only modules
config.resolver = {
  ...config.resolver,
  alias: {
    // Redirect react-native-maps to web polyfill
    'react-native-maps': path.resolve(__dirname, 'polyfills/react-native-maps.web.js'),
    'react-native-maps/lib/MapMarkerNativeComponent': path.resolve(__dirname, 'polyfills/MapMarkerNativeComponent.web.js'),
    'react-native/Libraries/Utilities/codegenNativeCommands': path.resolve(__dirname, 'polyfills/codegenNativeCommands.web.js'),
  },
  resolveRequest: (context, moduleName, platform) => {
    // For web platform, resolve native modules to polyfills
    if (platform === 'web') {
      if (moduleName === 'react-native-maps') {
        return {
          filePath: path.resolve(__dirname, 'polyfills/react-native-maps.web.js'),
          type: 'sourceFile',
        };
      }
      if (moduleName.includes('codegenNativeCommands')) {
        return {
          filePath: path.resolve(__dirname, 'polyfills/codegenNativeCommands.web.js'),
          type: 'sourceFile',
        };
      }
      if (moduleName.includes('MapMarkerNativeComponent')) {
        return {
          filePath: path.resolve(__dirname, 'polyfills/MapMarkerNativeComponent.web.js'),
          type: 'sourceFile',
        };
      }
    }
    
    // Use default resolver for other cases
    return context.resolveRequest(context, moduleName, platform);
  },
  platforms: ['web', 'ios', 'android', 'native'],
  sourceExts: [...config.resolver.sourceExts, 'tsx', 'ts', 'jsx', 'js'],
};

// Enhanced transformer configuration
config.transformer = {
  ...config.transformer,
  getTransformOptions: async () => ({
    transform: {
      experimentalImportSupport: false,
      inlineRequires: false,
    },
  }),
};

module.exports = config;
