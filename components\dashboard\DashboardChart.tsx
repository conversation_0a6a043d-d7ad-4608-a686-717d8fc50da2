import { View, Text, StyleSheet, Dimensions } from 'react-native';
import { useTheme } from '@/hooks/useTheme';
import { useEffect, useState } from 'react';

const { width } = Dimensions.get('window');

type ChartData = Array<{
  name: string;
  value: number;
  color?: string;
}>;

type DashboardChartProps = {
  type: 'bar' | 'line' | 'pie';
  data: ChartData;
  height: number;
};

export default function DashboardChart({ type, data, height }: DashboardChartProps) {
  const { theme } = useTheme();
  const [maxValue, setMaxValue] = useState(0);
  
  useEffect(() => {
    if (data.length > 0) {
      setMaxValue(Math.max(...data.map(item => item.value)));
    }
  }, [data]);
  
  const renderBarChart = () => {
    return (
      <View style={[styles.chartContainer, { height }]}>
        <View style={styles.barContainer}>
          {data.map((item, index) => (
            <View key={index} style={styles.barGroup}>
              <View style={styles.barLabelContainer}>
                <Text 
                  style={[styles.barValue, { color: theme.colors.text }]} 
                  numberOfLines={1}
                >
                  {item.value}
                </Text>
              </View>
              
              <View style={[styles.bar, { backgroundColor: theme.colors.border, height: height - 60 }]}>
                <View 
                  style={[
                    styles.barFill, 
                    { 
                      backgroundColor: item.color || theme.colors.primary,
                      height: `${(item.value / maxValue) * 100}%`,
                    }
                  ]} 
                />
              </View>
              
              <Text 
                style={[styles.barLabel, { color: theme.colors.text }]} 
                numberOfLines={1}
                ellipsizeMode="tail"
              >
                {item.name}
              </Text>
            </View>
          ))}
        </View>
      </View>
    );
  };
  
  const renderPieChart = () => {
    const total = data.reduce((sum, item) => sum + item.value, 0);
    let cumulativePercentage = 0;
    
    return (
      <View style={[styles.chartContainer, { height }]}>
        <View style={styles.pieChartRow}>
          <View style={styles.pieContainer}>
            <View style={[styles.pieChart, { width: height * 0.8, height: height * 0.8 }]}>
              {data.map((item, index) => {
                const percentage = (item.value / total) * 100;
                const startAngle = cumulativePercentage * 3.6; // 3.6 degrees per percentage point
                cumulativePercentage += percentage;
                const endAngle = cumulativePercentage * 3.6;
                
                return (
                  <View
                    key={index}
                    style={[
                      styles.pieSlice,
                      {
                        backgroundColor: item.color || `hsl(${index * (360 / data.length)}, 70%, 60%)`,
                        transform: [
                          { rotate: `${startAngle}deg` },
                          { translateX: height * 0.4 },
                          { translateY: height * 0.4 },
                        ],
                        width: `${percentage}%`,
                        height: `${percentage}%`,
                      },
                    ]}
                  />
                );
              })}
            </View>
          </View>
          
          <View style={styles.legendContainer}>
            {data.map((item, index) => (
              <View key={index} style={styles.legendItem}>
                <View 
                  style={[
                    styles.legendColor, 
                    { backgroundColor: item.color || `hsl(${index * (360 / data.length)}, 70%, 60%)` }
                  ]} 
                />
                <Text style={[styles.legendLabel, { color: theme.colors.text }]}>
                  {item.name} ({item.value})
                </Text>
              </View>
            ))}
          </View>
        </View>
      </View>
    );
  };
  
  const renderLineChart = () => {
    return (
      <View style={[styles.chartContainer, { height }]}>
        <View style={styles.lineChartContainer}>
          <View style={[styles.lineChart, { height: height - 60 }]}>
            {data.map((item, index) => {
              const x = (index / (data.length - 1)) * 100;
              const y = 100 - ((item.value / maxValue) * 100);
              
              return (
                <View
                  key={index}
                  style={[
                    styles.linePoint,
                    {
                      left: `${x}%`,
                      top: `${y}%`,
                      backgroundColor: theme.colors.primary,
                    },
                  ]}
                >
                  <Text style={styles.linePointValue}>{item.value}</Text>
                </View>
              );
            })}
            
            {/* Connect the points with lines */}
            {data.map((item, index) => {
              if (index === data.length - 1) return null;
              
              const x1 = (index / (data.length - 1)) * 100;
              const y1 = 100 - ((item.value / maxValue) * 100);
              const x2 = ((index + 1) / (data.length - 1)) * 100;
              const y2 = 100 - ((data[index + 1].value / maxValue) * 100);
              
              // Calculate the line length and angle
              const length = Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
              const angle = Math.atan2(y2 - y1, x2 - x1) * (180 / Math.PI);
              
              return (
                <View
                  key={`line-${index}`}
                  style={[
                    styles.line,
                    {
                      left: `${x1}%`,
                      top: `${y1}%`,
                      width: `${length}%`,
                      transform: [{ rotate: `${angle}deg` }],
                      backgroundColor: theme.colors.primary,
                    },
                  ]}
                />
              );
            })}
          </View>
          
          <View style={styles.lineLabelsContainer}>
            {data.map((item, index) => (
              <Text 
                key={index}
                style={[styles.lineLabel, { color: theme.colors.text }]}
                numberOfLines={1}
                ellipsizeMode="tail"
              >
                {item.name}
              </Text>
            ))}
          </View>
        </View>
      </View>
    );
  };
  
  // Render the appropriate chart based on the type
  switch (type) {
    case 'bar':
      return renderBarChart();
    case 'pie':
      return renderPieChart();
    case 'line':
      return renderLineChart();
    default:
      return <Text>Unsupported chart type</Text>;
  }
}

const styles = StyleSheet.create({
  chartContainer: {
    width: '100%',
  },
  
  // Bar Chart Styles
  barContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'flex-end',
    height: '100%',
  },
  barGroup: {
    alignItems: 'center',
    flex: 1,
  },
  barLabelContainer: {
    marginBottom: 4,
  },
  barValue: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    textAlign: 'center',
  },
  bar: {
    width: '60%',
    borderRadius: 4,
    overflow: 'hidden',
    justifyContent: 'flex-end',
  },
  barFill: {
    width: '100%',
    borderRadius: 4,
  },
  barLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    marginTop: 8,
    textAlign: 'center',
    maxWidth: '90%',
  },
  
  // Pie Chart Styles
  pieChartRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  pieContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  pieChart: {
    position: 'relative',
    borderRadius: 100,
    overflow: 'hidden',
  },
  pieSlice: {
    position: 'absolute',
    borderRadius: 100,
  },
  legendContainer: {
    flex: 1,
    paddingLeft: 16,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  legendColor: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 8,
  },
  legendLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
  },
  
  // Line Chart Styles
  lineChartContainer: {
    width: '100%',
    height: '100%',
  },
  lineChart: {
    width: '100%',
    position: 'relative',
    borderLeftWidth: 1,
    borderBottomWidth: 1,
    borderColor: '#E0E0E0',
  },
  linePoint: {
    position: 'absolute',
    width: 8,
    height: 8,
    borderRadius: 4,
    marginLeft: -4,
    marginTop: -4,
  },
  linePointValue: {
    position: 'absolute',
    top: -20,
    left: -10,
    fontSize: 10,
    fontFamily: 'Inter-Medium',
    color: 'white',
    backgroundColor: 'rgba(0,0,0,0.7)',
    paddingHorizontal: 4,
    paddingVertical: 2,
    borderRadius: 4,
  },
  line: {
    position: 'absolute',
    height: 2,
    transformOrigin: 'left',
  },
  lineLabelsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  lineLabel: {
    fontSize: 10,
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
    width: 50,
  },
});