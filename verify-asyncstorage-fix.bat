@echo off
REM AsyncStorage Fix Verification Script (Windows)
REM Check if all fixes are properly applied

echo 🔍 Verifying AsyncStorage Fix
echo =============================

set checks_passed=0
set total_checks=0

:check_file
set /a total_checks+=1
if exist "%~1" (
    echo ✅ %~1 exists
    set /a checks_passed+=1
) else (
    echo ❌ %~1 missing
)
goto :eof

:check_content
set /a total_checks+=1
findstr /c:"%~1" "%~2" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ %~3
    set /a checks_passed+=1
) else (
    echo ❌ %~3
)
goto :eof

echo 📁 File Structure Check
echo =======================

REM Check if storage utility exists
call :check_file "lib\storage.ts"

REM Check if components exist
call :check_file "components\map\MapScreen.tsx"
call :check_file "components\map\EnhancedMapIntegration.tsx"
call :check_file "components\map\ProfessionalMapUI.tsx"

echo.
echo 🔧 Component Integration Check
echo ==============================

REM Check MapScreen uses EnhancedMapIntegration
call :check_content "EnhancedMapIntegration" "components\map\MapScreen.tsx" "MapScreen uses EnhancedMapIntegration"

REM Check EnhancedMapIntegration uses storage utility
call :check_content "StorageUtils" "components\map\EnhancedMapIntegration.tsx" "EnhancedMapIntegration uses StorageUtils"

REM Check MapIntegration uses storage utility
call :check_content "StorageUtils" "components\map\MapIntegration.tsx" "MapIntegration uses StorageUtils"

echo.
echo ❌ AsyncStorage Import Check
echo ============================

REM Check that AsyncStorage is NOT directly imported
findstr /s /c:"AsyncStorage from" components\map\*.tsx >nul 2>&1
if %errorlevel% equ 0 (
    echo ❌ Found direct AsyncStorage imports that need to be fixed
) else (
    echo ✅ No direct AsyncStorage imports found
    set /a checks_passed+=1
)
set /a total_checks+=1

echo.
echo 📦 Package Check
echo ================

REM Check if AsyncStorage package is installed
if exist "node_modules\@react-native-async-storage" (
    echo ✅ AsyncStorage package installed
    set /a checks_passed+=1
) else (
    echo ❌ AsyncStorage package not installed
)
set /a total_checks+=1

echo.
echo 📊 VERIFICATION SUMMARY
echo ======================

set /a success_rate=(checks_passed * 100) / total_checks

echo Checks Passed: %checks_passed%/%total_checks%
echo Success Rate: %success_rate%%%

if %success_rate% geq 90 (
    echo 🎉 EXCELLENT! All fixes are properly applied
    echo ✨ Your AsyncStorage error should be resolved
    echo.
    echo 🚀 Next Steps:
    echo    1. Run: npm run web
    echo    2. Navigate to: http://localhost:8081/map
    echo    3. Enjoy your professional map interface!
) else if %success_rate% geq 70 (
    echo ⚠️  GOOD! Most fixes applied, minor issues remain
    echo.
    echo 🔧 Recommendations:
    echo    1. Review failed checks above
    echo    2. Run the complete fix script
    echo    3. Clear Metro cache and restart
) else (
    echo ❌ NEEDS ATTENTION! Several fixes missing
    echo.
    echo 🚨 Required Actions:
    echo    1. Run: complete-fix-asyncstorage.bat
    echo    2. Ensure all files are properly created
    echo    3. Clear caches and restart development server
)

echo.
echo 🔗 Available Fix Scripts:
echo    • complete-fix-asyncstorage.bat (Windows)
echo    • complete-fix-asyncstorage.sh (Linux/Mac)

pause
