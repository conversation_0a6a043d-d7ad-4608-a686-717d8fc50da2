/* Professional Map UI Styles */
/* Optimized for the new ProfessionalMapUI and OptimizedLeafletMap components */

/* ================================ */
/* Global Map Container Styles */
/* ================================ */

.professional-map-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.professional-map-content {
  display: flex;
  flex: 1;
  position: relative;
  overflow: hidden;
}

.professional-map-area {
  flex: 1;
  position: relative;
  background-color: #f5f5f5;
}

/* ================================ */
/* Leaflet Map Customizations */
/* ================================ */

.leaflet-container {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  width: 100%;
  height: 100%;
}

/* Custom Marker Styles */
.custom-map-marker {
  background: transparent !important;
  border: none !important;
  border-radius: 0 !important;
}

.custom-marker-content {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

/* ================================ */
/* Drawing Control Enhancements */
/* ================================ */

.leaflet-draw-toolbar {
  margin-top: 10px !important;
  border-radius: 8px !important;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}

.leaflet-draw-toolbar a {
  background-color: #ffffff !important;
  border: 1px solid #e1e5e9 !important;
  color: #374151 !important;
  transition: all 0.2s ease !important;
  width: 36px !important;
  height: 36px !important;
  line-height: 34px !important;
}

.leaflet-draw-toolbar a:hover {
  background-color: #3b82f6 !important;
  color: white !important;
  border-color: #3b82f6 !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3) !important;
}

.leaflet-draw-toolbar a:first-child {
  border-radius: 8px 0 0 8px !important;
}

.leaflet-draw-toolbar a:last-child {
  border-radius: 0 8px 8px 0 !important;
}

/* Drawing Actions */
.leaflet-draw-actions {
  background-color: #ffffff !important;
  border-radius: 6px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  border: 1px solid #e1e5e9 !important;
  margin-top: 5px !important;
}

.leaflet-draw-actions a {
  background-color: transparent !important;
  border: none !important;
  color: #374151 !important;
  padding: 8px 12px !important;
  font-size: 13px !important;
  font-weight: 500 !important;
}

.leaflet-draw-actions a:hover {
  background-color: #f3f4f6 !important;
  color: #1f2937 !important;
}

/* Cancel/Save buttons */
.leaflet-draw-actions-bottom a:first-child {
  color: #ef4444 !important;
}

.leaflet-draw-actions-bottom a:first-child:hover {
  background-color: #fef2f2 !important;
  color: #dc2626 !important;
}

.leaflet-draw-actions-top a {
  color: #10b981 !important;
}

.leaflet-draw-actions-top a:hover {
  background-color: #f0fdf4 !important;
  color: #059669 !important;
}

/* ================================ */
/* Popup Enhancements */
/* ================================ */

.leaflet-popup-content-wrapper {
  background-color: #ffffff !important;
  color: #1f2937 !important;
  border-radius: 12px !important;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 4px 10px rgba(0, 0, 0, 0.05) !important;
  border: 1px solid #e5e7eb !important;
  padding: 0 !important;
  min-width: 200px !important;
}

.leaflet-popup-content {
  margin: 16px !important;
  line-height: 1.5 !important;
  font-size: 14px !important;
}

.leaflet-popup-content h3 {
  margin: 0 0 8px 0 !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  color: #111827 !important;
}

.leaflet-popup-content p {
  margin: 4px 0 !important;
  font-size: 13px !important;
}

.leaflet-popup-tip {
  background-color: #ffffff !important;
  border: 1px solid #e5e7eb !important;
  border-top: none !important;
  border-right: none !important;
}

.leaflet-popup-close-button {
  color: #6b7280 !important;
  font-size: 18px !important;
  font-weight: bold !important;
  right: 8px !important;
  top: 8px !important;
  width: 24px !important;
  height: 24px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 4px !important;
  transition: all 0.2s ease !important;
}

.leaflet-popup-close-button:hover {
  background-color: #f3f4f6 !important;
  color: #374151 !important;
}

/* ================================ */
/* Control Positioning */
/* ================================ */

.leaflet-control-container .leaflet-top.leaflet-left {
  top: 16px !important;
  left: 16px !important;
}

.leaflet-control-container .leaflet-top.leaflet-right {
  top: 16px !important;
  right: 16px !important;
}

.leaflet-control-container .leaflet-bottom.leaflet-right {
  bottom: 16px !important;
  right: 16px !important;
}

/* Zoom Control Styling */
.leaflet-control-zoom {
  border-radius: 8px !important;
  overflow: hidden !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
  border: 1px solid #e1e5e9 !important;
}

.leaflet-control-zoom a {
  background-color: #ffffff !important;
  color: #374151 !important;
  border: none !important;
  width: 36px !important;
  height: 36px !important;
  line-height: 34px !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  transition: all 0.2s ease !important;
}

.leaflet-control-zoom a:hover {
  background-color: #f3f4f6 !important;
  color: #1f2937 !important;
}

.leaflet-control-zoom-in {
  border-bottom: 1px solid #e1e5e9 !important;
}

/* Attribution Control */
.leaflet-control-attribution {
  background-color: rgba(255, 255, 255, 0.9) !important;
  border-radius: 4px !important;
  padding: 4px 8px !important;
  font-size: 11px !important;
  color: #6b7280 !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

/* ================================ */
/* Layer Control Enhancements */
/* ================================ */

.leaflet-control-layers {
  background-color: #ffffff !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  border: 1px solid #e1e5e9 !important;
  padding: 12px !important;
  min-width: 180px !important;
}

.leaflet-control-layers-toggle {
  background-color: #ffffff !important;
  border-radius: 8px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
  border: 1px solid #e1e5e9 !important;
  width: 36px !important;
  height: 36px !important;
  background-image: none !important;
  position: relative !important;
}

.leaflet-control-layers-toggle::before {
  content: '⋯';
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) rotate(90deg) !important;
  font-size: 18px !important;
  color: #374151 !important;
  font-weight: bold !important;
}

.leaflet-control-layers-toggle:hover {
  background-color: #f3f4f6 !important;
}

.leaflet-control-layers label {
  font-size: 13px !important;
  color: #374151 !important;
  font-weight: 500 !important;
  margin-bottom: 8px !important;
  display: flex !important;
  align-items: center !important;
}

.leaflet-control-layers input[type="radio"],
.leaflet-control-layers input[type="checkbox"] {
  margin-right: 8px !important;
  accent-color: #3b82f6 !important;
}

/* ================================ */
/* Dark Mode Support */
/* ================================ */

@media (prefers-color-scheme: dark) {
  .professional-map-area {
    background-color: #1f2937;
  }

  .leaflet-draw-toolbar a,
  .leaflet-draw-actions,
  .leaflet-popup-content-wrapper,
  .leaflet-control-zoom a,
  .leaflet-control-layers,
  .leaflet-control-layers-toggle {
    background-color: #374151 !important;
    border-color: #4b5563 !important;
    color: #f9fafb !important;
  }

  .leaflet-draw-toolbar a:hover,
  .leaflet-control-zoom a:hover {
    background-color: #4b5563 !important;
  }

  .leaflet-popup-tip {
    background-color: #374151 !important;
    border-color: #4b5563 !important;
  }

  .leaflet-control-attribution {
    background-color: rgba(55, 65, 81, 0.9) !important;
    color: #d1d5db !important;
  }
}

/* ================================ */
/* Responsive Design */
/* ================================ */

@media (max-width: 768px) {
  .leaflet-control-container .leaflet-top.leaflet-left,
  .leaflet-control-container .leaflet-top.leaflet-right {
    top: 10px !important;
    left: 10px !important;
    right: 10px !important;
  }

  .leaflet-control-container .leaflet-bottom.leaflet-right {
    bottom: 10px !important;
    right: 10px !important;
  }

  .leaflet-draw-toolbar a,
  .leaflet-control-zoom a {
    width: 40px !important;
    height: 40px !important;
    line-height: 38px !important;
  }

  .leaflet-popup-content-wrapper {
    min-width: 180px !important;
  }

  .leaflet-control-layers {
    min-width: 160px !important;
    padding: 10px !important;
  }
}

@media (max-width: 480px) {
  .leaflet-draw-toolbar a,
  .leaflet-control-zoom a {
    width: 44px !important;
    height: 44px !important;
    line-height: 42px !important;
  }

  .leaflet-popup-content {
    margin: 12px !important;
  }

  .leaflet-popup-content h3 {
    font-size: 15px !important;
  }

  .leaflet-popup-content p {
    font-size: 12px !important;
  }
}

/* ================================ */
/* Animation Enhancements */
/* ================================ */

.leaflet-marker-icon,
.leaflet-marker-shadow {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.leaflet-marker-icon:hover {
  transform: scale(1.1) !important;
}

.leaflet-popup {
  animation: popup-appear 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes popup-appear {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Drawing path animations */
.leaflet-interactive {
  transition: all 0.2s ease !important;
}

.leaflet-interactive:hover {
  filter: brightness(1.1) !important;
  cursor: pointer !important;
}

/* ================================ */
/* Custom Map Feature Styles */
/* ================================ */

/* Point features */
.custom-point-marker {
  border-radius: 50%;
  border: 3px solid white;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  transition: all 0.2s ease;
}

.custom-point-marker:hover {
  transform: scale(1.2);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

/* Line features */
.custom-line-feature {
  stroke-linecap: round;
  stroke-linejoin: round;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.custom-line-feature:hover {
  stroke-width: 5px !important;
  filter: drop-shadow(0 3px 6px rgba(0, 0, 0, 0.2));
}

/* Polygon features */
.custom-polygon-feature {
  stroke-linejoin: round;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.custom-polygon-feature:hover {
  stroke-width: 3px !important;
  filter: drop-shadow(0 3px 6px rgba(0, 0, 0, 0.2));
}

/* ================================ */
/* Performance Optimizations */
/* ================================ */

.leaflet-container {
  image-rendering: pixelated;
  image-rendering: -moz-crisp-edges;
  image-rendering: crisp-edges;
}

.leaflet-tile {
  filter: contrast(1.1) saturate(1.1);
  transition: opacity 0.2s ease;
}

.leaflet-tile-loaded {
  opacity: 1;
}

/* ================================ */
/* Print Styles */
/* ================================ */

@media print {
  .leaflet-control-container {
    display: none !important;
  }

  .leaflet-popup {
    display: none !important;
  }

  .professional-map-container {
    height: 100vh !important;
    width: 100vw !important;
  }
}

/* ================================ */
/* Accessibility Enhancements */
/* ================================ */

.leaflet-container:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.leaflet-draw-toolbar a:focus,
.leaflet-control-zoom a:focus {
  outline: 2px solid #3b82f6 !important;
  outline-offset: 2px !important;
  z-index: 1000 !important;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .leaflet-draw-toolbar a,
  .leaflet-control-zoom a {
    border-width: 2px !important;
  }

  .leaflet-popup-content-wrapper {
    border-width: 2px !important;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .leaflet-marker-icon,
  .leaflet-marker-shadow,
  .leaflet-interactive,
  .leaflet-tile,
  .custom-point-marker,
  .custom-line-feature,
  .custom-polygon-feature {
    transition: none !important;
    animation: none !important;
  }

  .leaflet-popup {
    animation: none !important;
  }
}

/* ================================ */
/* Custom Loading Styles */
/* ================================ */

.leaflet-tile-container {
  position: relative;
}

.leaflet-loading {
  opacity: 0.7;
}

.leaflet-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* ================================ */
/* Error State Styles */
/* ================================ */

.leaflet-tile-error {
  background-color: #fef2f2 !important;
  border: 1px solid #fecaca !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  color: #dc2626 !important;
  font-size: 12px !important;
  font-weight: 500 !important;
}

.leaflet-tile-error::before {
  content: '⚠';
  margin-right: 4px !important;
}

/* ================================ */
/* Custom Utility Classes */
/* ================================ */

.map-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.map-slide-up {
  animation: slideUp 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.map-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* ================================ */
/* Debug Styles (Development Only) */
/* ================================ */

.debug-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  border: 2px dashed #ef4444;
  background: rgba(239, 68, 68, 0.1);
  z-index: 10000;
}

.debug-info {
  position: absolute;
  top: 5px;
  left: 5px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  font-size: 10px;
  font-family: monospace;
  border-radius: 3px;
  pointer-events: none;
  z-index: 10001;
}

/* Hide debug styles in production */
body:not(.debug-mode) .debug-overlay,
body:not(.debug-mode) .debug-info {
  display: none !important;
}
