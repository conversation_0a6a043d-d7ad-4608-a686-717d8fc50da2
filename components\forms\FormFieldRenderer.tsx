import React from 'react';
import { View, Text, StyleSheet, TextInput, Switch } from 'react-native';
import { useTheme } from '@/hooks/useTheme';
import { FormQuestion, QuestionType } from '@/types';
import EnhancedDatePicker from '../ui/EnhancedDatePicker';
import PhoneInput from './fields/PhoneInput';
import QRBarcodeScanner from './fields/QRBarcodeScanner';
import VideoRecorder from './fields/VideoRecorder';
import AudioRecorder from './fields/AudioRecorder';
import MultiPhotosPicker from './fields/MultiPhotosPicker';
import SignatureCapture from '../ui/SignatureCapture';
import * as Location from 'expo-location';
import { TouchableOpacity } from 'react-native';
import { MapPin, CheckSquare, Square } from 'lucide-react-native';

interface FormFieldRendererProps {
  question: FormQuestion;
  value: any;
  onChange: (value: any) => void;
  disabled?: boolean;
  showValidation?: boolean;
}

export default function FormFieldRenderer({
  question,
  value,
  onChange,
  disabled = false,
  showValidation = true,
}: FormFieldRendererProps) {
  const { theme } = useTheme();

  // Early return if question is invalid
  if (!question || !question.id || !question.type) {
    console.warn('FormFieldRenderer: Invalid question object', question);
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <Text style={[styles.errorText, { color: theme.colors.error }]}>
          Invalid form field configuration
        </Text>
      </View>
    );
  }

  const validateField = (fieldValue: any) => {
    if (!showValidation) return null;

    // Required validation
    if (question.required && (!fieldValue || fieldValue === '')) {
      return `${question.label} is required`;
    }

    // Type-specific validation
    if (fieldValue && question.validation) {
      for (const rule of question.validation) {
        switch (rule.type) {
          case 'min':
            if (question.type === 'text' && fieldValue.length < rule.value) {
              return rule.message;
            }
            if (question.type === 'number' && parseFloat(fieldValue) < rule.value) {
              return rule.message;
            }
            break;
          case 'max':
            if (question.type === 'text' && fieldValue.length > rule.value) {
              return rule.message;
            }
            if (question.type === 'number' && parseFloat(fieldValue) > rule.value) {
              return rule.message;
            }
            break;
          case 'pattern':
            const regex = new RegExp(rule.value);
            if (!regex.test(fieldValue)) {
              return rule.message;
            }
            break;
        }
      }
    }

    return null;
  };

  const renderBasicInput = () => {
    switch (question.type) {
      case 'text':
        return (
          <TextInput
            style={[
              styles.textInput,
              {
                backgroundColor: theme.colors.background,
                borderColor: theme.colors.border,
                color: theme.colors.text,
              },
            ]}
            value={value || ''}
            onChangeText={onChange}
            placeholder={question.placeholder}
            placeholderTextColor={theme.colors.placeholder}
            editable={!disabled}
            multiline={question.properties?.multiline}
            numberOfLines={question.properties?.numberOfLines || 1}
          />
        );

      case 'number':
        return (
          <TextInput
            style={[
              styles.textInput,
              {
                backgroundColor: theme.colors.background,
                borderColor: theme.colors.border,
                color: theme.colors.text,
              },
            ]}
            value={value?.toString() || ''}
            onChangeText={(text) => onChange(text ? parseFloat(text) : null)}
            placeholder={question.placeholder}
            placeholderTextColor={theme.colors.placeholder}
            keyboardType="numeric"
            editable={!disabled}
          />
        );

      case 'date':
        return (
          <EnhancedDatePicker
            value={value}
            onChange={onChange}
            mode="date"
            placeholder={question.placeholder}
            required={question.required}
          />
        );

      case 'time':
        return (
          <EnhancedDatePicker
            value={value}
            onChange={onChange}
            mode="time"
            placeholder={question.placeholder}
            required={question.required}
          />
        );

      case 'datetime':
        return (
          <EnhancedDatePicker
            value={value}
            onChange={onChange}
            mode="datetime"
            placeholder={question.placeholder}
            required={question.required}
          />
        );

      case 'phone':
        return (
          <PhoneInput
            value={value}
            onChange={onChange}
            placeholder={question.placeholder}
            required={question.required}
            disabled={disabled}
            enableValidation={question.properties?.enableValidation !== false}
          />
        );

      default:
        return null;
    }
  };

  const renderChoiceField = () => {
    if (!['select', 'multiselect'].includes(question.type)) return null;

    const options = question.options || [];
    const isMultiple = question.type === 'multiselect';
    const selectedValues = isMultiple ? (value || []) : [value];

    const handleOptionChange = (optionValue: string, isSelected: boolean) => {
      if (isMultiple) {
        const currentValues = value || [];
        if (isSelected) {
          onChange([...currentValues, optionValue]);
        } else {
          onChange(currentValues.filter((v: string) => v !== optionValue));
        }
      } else {
        onChange(isSelected ? optionValue : null);
      }
    };

    return (
      <View style={styles.choiceContainer}>
        {options.map((option, index) => {
          const isSelected = selectedValues.includes(option.value);
          
          return (
            <TouchableOpacity
              key={index}
              style={[
                styles.choiceOption,
                {
                  backgroundColor: isSelected 
                    ? theme.colors.primary + '10' 
                    : theme.colors.background,
                  borderColor: isSelected 
                    ? theme.colors.primary 
                    : theme.colors.border,
                },
              ]}
              onPress={() => handleOptionChange(option.value, !isSelected)}
              disabled={disabled}
            >
              <View style={styles.choiceIndicator}>
                {isMultiple ? (
                  isSelected ? (
                    <CheckSquare size={20} color={theme.colors.primary} />
                  ) : (
                    <Square size={20} color={theme.colors.muted} />
                  )
                ) : (
                  <View
                    style={[
                      styles.radioButton,
                      {
                        borderColor: isSelected 
                          ? theme.colors.primary 
                          : theme.colors.muted,
                        backgroundColor: isSelected 
                          ? theme.colors.primary 
                          : 'transparent',
                      },
                    ]}
                  >
                    {isSelected && <View style={styles.radioButtonInner} />}
                  </View>
                )}
              </View>
              
              <Text style={[
                styles.choiceLabel,
                { color: isSelected ? theme.colors.primary : theme.colors.text },
              ]}>
                {option.label}
              </Text>
            </TouchableOpacity>
          );
        })}
      </View>
    );
  };

  const renderLocationField = () => {
    if (question.type !== 'location') return null;

    const handleLocationCapture = async () => {
      try {
        const { status } = await Location.requestForegroundPermissionsAsync();
        if (status !== 'granted') {
          alert('Location permission is required');
          return;
        }

        const location = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.High,
        });

        const locationData = {
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
          accuracy: location.coords.accuracy,
          altitude: location.coords.altitude,
          timestamp: Date.now(),
        };

        onChange(locationData);
      } catch (error) {
        console.error('Error getting location:', error);
        alert('Failed to get location');
      }
    };

    return (
      <View>
        {value ? (
          <View style={[
            styles.locationDisplay,
            {
              backgroundColor: theme.colors.background,
              borderColor: theme.colors.primary,
            },
          ]}>
            <MapPin size={20} color={theme.colors.primary} />
            <View style={styles.locationInfo}>
              <Text style={[styles.locationText, { color: theme.colors.text }]}>
                Location captured
              </Text>
              <Text style={[styles.locationCoords, { color: theme.colors.muted, fontFamily: 'monospace' }]}>
                {value.latitude?.toFixed(6)}, {value.longitude?.toFixed(6)}
              </Text>
              {value.accuracy && (
                <Text style={[styles.locationAccuracy, { color: theme.colors.muted }]}>
                  Accuracy: ±{Math.round(value.accuracy)}m
                </Text>
              )}
            </View>
          </View>
        ) : (
          <TouchableOpacity
            style={[
              styles.locationButton,
              {
                backgroundColor: theme.colors.background,
                borderColor: theme.colors.border,
              },
            ]}
            onPress={handleLocationCapture}
            disabled={disabled}
          >
            <MapPin size={20} color={theme.colors.primary} />
            <Text style={[styles.locationButtonText, { color: theme.colors.text }]}>
              Capture GPS Location
            </Text>
          </TouchableOpacity>
        )}
      </View>
    );
  };

  const renderMediaField = () => {
    switch (question.type) {
      case 'photo':
        return (
          <MultiPhotosPicker
            value={value}
            onChange={onChange}
            placeholder={question.placeholder}
            required={question.required}
            disabled={disabled}
            maxPhotos={question.properties?.maxPhotos || 5}
            allowCamera={question.properties?.allowCamera !== false}
            allowGallery={question.properties?.allowGallery !== false}
            showThumbnails={question.properties?.showThumbnails !== false}
            captureLocation={question.properties?.captureLocation !== false}
          />
        );

      case 'audio':
        return (
          <AudioRecorder
            value={value}
            onChange={onChange}
            placeholder={question.placeholder}
            required={question.required}
            disabled={disabled}
            maxDuration={question.properties?.maxDuration || 300}
            enablePlayback={question.properties?.enablePlayback !== false}
            allowRetake={question.properties?.allowRetake !== false}
            quality={question.properties?.quality || 'medium'}
          />
        );

      case 'video':
        return (
          <VideoRecorder
            value={value}
            onChange={onChange}
            placeholder={question.placeholder}
            required={question.required}
            disabled={disabled}
            maxDuration={question.properties?.maxDuration || 60}
            enablePlayback={question.properties?.enablePlayback !== false}
            allowRetake={question.properties?.allowRetake !== false}
          />
        );

      case 'signature':
        return (
          <SignatureCapture
            value={value}
            onChange={onChange}
            placeholder={question.placeholder}
            required={question.required}
            disabled={disabled}
          />
        );

      case 'qr_scan':
        return (
          <QRBarcodeScanner
            value={value}
            onChange={onChange}
            placeholder={question.placeholder}
            required={question.required}
            disabled={disabled}
            allowedTypes={['qr']}
            enableManualInput={question.properties?.enableManualInput !== false}
            enableValidation={question.properties?.enableValidation !== false}
          />
        );

      case 'barcode':
        return (
          <QRBarcodeScanner
            value={value}
            onChange={onChange}
            placeholder={question.placeholder}
            required={question.required}
            disabled={disabled}
            allowedTypes={['barcode']}
            enableManualInput={question.properties?.enableManualInput !== false}
            enableValidation={question.properties?.enableValidation !== false}
          />
        );

      default:
        return null;
    }
  };

  const renderFieldContent = () => {
    // Basic input types
    if (['text', 'number', 'date', 'time', 'datetime', 'phone'].includes(question.type)) {
      return renderBasicInput();
    }

    // Choice types
    if (['select', 'multiselect'].includes(question.type)) {
      return renderChoiceField();
    }

    // Location type
    if (question.type === 'location') {
      return renderLocationField();
    }

    // Media and advanced types
    if (['photo', 'audio', 'video', 'signature', 'qr_scan', 'barcode'].includes(question.type)) {
      return renderMediaField();
    }

    // Fallback for unknown types
    return (
      <View style={[styles.unsupportedField, { backgroundColor: theme.colors.background }]}>
        <Text style={[styles.unsupportedText, { color: theme.colors.muted }]}>
          Unsupported field type: {question.type}
        </Text>
      </View>
    );
  };

  const validationError = validateField(value);

  return (
    <View style={styles.container}>
      {/* Field Label */}
      <View style={styles.labelContainer}>
        <Text style={[styles.label, { color: theme.colors.text }]}>
          {question.label}
          {question.required && (
            <Text style={{ color: theme.colors.error }}> *</Text>
          )}
        </Text>
        
        {question.description && (
          <Text style={[styles.description, { color: theme.colors.muted }]}>
            {question.description}
          </Text>
        )}
      </View>

      {/* Field Input */}
      <View style={styles.inputContainer}>
        {renderFieldContent()}
      </View>

      {/* Validation Error */}
      {validationError && (
        <Text style={[styles.errorText, { color: theme.colors.error }]}>
          {validationError}
        </Text>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginVertical: 12,
  },
  labelContainer: {
    marginBottom: 8,
  },
  label: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 4,
  },
  description: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    lineHeight: 20,
  },
  inputContainer: {
    marginBottom: 4,
  },
  textInput: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 14,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
  },
  choiceContainer: {
    gap: 8,
  },
  choiceOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    gap: 12,
  },
  choiceIndicator: {
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioButtonInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: 'white',
  },
  choiceLabel: {
    flex: 1,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
  },
  locationDisplay: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 14,
    borderRadius: 8,
    borderWidth: 2,
    gap: 12,
  },
  locationInfo: {
    flex: 1,
  },
  locationText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 2,
  },
  locationCoords: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    marginBottom: 2,
  },
  locationAccuracy: {
    fontSize: 11,
    fontFamily: 'Inter-Regular',
  },
  locationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    borderRadius: 8,
    borderWidth: 1,
    gap: 8,
  },
  locationButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
  },
  unsupportedField: {
    paddingVertical: 20,
    paddingHorizontal: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  unsupportedText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    fontStyle: 'italic',
  },
  errorText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    marginTop: 4,
  },
});
