import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Platform,
  Modal,
  Vibration,
} from 'react-native';
import { useTheme } from '@/hooks/useTheme';
import { 
  QrCode, 
  Camera, 
  Edit3, 
  CheckCircle, 
  XCircle, 
  Flashlight,
  RotateCcw,
} from 'lucide-react-native';
import { Camera as ExpoCamera } from 'expo-camera';
import { BarCodeScanner } from 'expo-barcode-scanner';

interface QRBarcodeScannerProps {
  value?: string;
  onChange: (value: string, type?: string) => void;
  placeholder?: string;
  required?: boolean;
  error?: string;
  disabled?: boolean;
  allowedTypes?: ('qr' | 'barcode')[];
  enableManualInput?: boolean;
  enableValidation?: boolean;
}

export default function QRBarcodeScanner({
  value = '',
  onChange,
  placeholder = 'Scan or enter code',
  required = false,
  error,
  disabled = false,
  allowedTypes = ['qr', 'barcode'],
  enableManualInput = true,
  enableValidation = true,
}: QRBarcodeScannerProps) {
  const { theme } = useTheme();
  const [hasCameraPermission, setHasCameraPermission] = useState<boolean | null>(null);
  const [showScanner, setShowScanner] = useState(false);
  const [scannedData, setScannedData] = useState<{
    data: string;
    type: string;
    timestamp: number;
  } | null>(null);
  const [flashOn, setFlashOn] = useState(false);
  const [lastScanTime, setLastScanTime] = useState(0);

  const cameraRef = useRef<ExpoCamera>(null);

  useEffect(() => {
    (async () => {
      const { status } = await BarCodeScanner.requestPermissionsAsync();
      setHasCameraPermission(status === 'granted');
    })();
  }, []);

  const getSupportedBarcodeTypes = () => {
    const qrTypes = [BarCodeScanner.Constants.BarCodeType.qr];
    const barcodeTypes = [
      BarCodeScanner.Constants.BarCodeType.ean13,
      BarCodeScanner.Constants.BarCodeType.ean8,
      BarCodeScanner.Constants.BarCodeType.upc_a,
      BarCodeScanner.Constants.BarCodeType.upc_e,
      BarCodeScanner.Constants.BarCodeType.code128,
      BarCodeScanner.Constants.BarCodeType.code39,
      BarCodeScanner.Constants.BarCodeType.code93,
      BarCodeScanner.Constants.BarCodeType.codabar,
      BarCodeScanner.Constants.BarCodeType.datamatrix,
      BarCodeScanner.Constants.BarCodeType.pdf417,
    ];

    let supportedTypes: any[] = [];
    
    if (allowedTypes.includes('qr')) {
      supportedTypes = [...supportedTypes, ...qrTypes];
    }
    
    if (allowedTypes.includes('barcode')) {
      supportedTypes = [...supportedTypes, ...barcodeTypes];
    }

    return supportedTypes;
  };

  const handleScanPress = async () => {
    if (!hasCameraPermission) {
      const { status } = await BarCodeScanner.requestPermissionsAsync();
      setHasCameraPermission(status === 'granted');
      
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Camera permission is needed to scan codes');
        return;
      }
    }
    
    setShowScanner(true);
  };

  const handleBarCodeScanned = ({ type, data }: { type: string; data: string }) => {
    const now = Date.now();
    
    // Prevent duplicate scans within 2 seconds
    if (now - lastScanTime < 2000) {
      return;
    }
    
    setLastScanTime(now);
    
    // Vibrate on successful scan
    if (Platform.OS !== 'web') {
      Vibration.vibrate(100);
    }

    const scannedInfo = {
      data,
      type: getBarCodeTypeName(type),
      timestamp: now,
    };

    setScannedData(scannedInfo);
    onChange(data, scannedInfo.type);
    
    // Auto-close scanner after successful scan
    setTimeout(() => {
      setShowScanner(false);
    }, 1000);
  };

  const getBarCodeTypeName = (type: string) => {
    const typeMap: Record<string, string> = {
      [BarCodeScanner.Constants.BarCodeType.qr]: 'QR Code',
      [BarCodeScanner.Constants.BarCodeType.ean13]: 'EAN-13',
      [BarCodeScanner.Constants.BarCodeType.ean8]: 'EAN-8',
      [BarCodeScanner.Constants.BarCodeType.upc_a]: 'UPC-A',
      [BarCodeScanner.Constants.BarCodeType.upc_e]: 'UPC-E',
      [BarCodeScanner.Constants.BarCodeType.code128]: 'Code 128',
      [BarCodeScanner.Constants.BarCodeType.code39]: 'Code 39',
      [BarCodeScanner.Constants.BarCodeType.code93]: 'Code 93',
      [BarCodeScanner.Constants.BarCodeType.codabar]: 'Codabar',
      [BarCodeScanner.Constants.BarCodeType.datamatrix]: 'Data Matrix',
      [BarCodeScanner.Constants.BarCodeType.pdf417]: 'PDF417',
    };
    
    return typeMap[type] || 'Unknown';
  };

  const validateCode = (code: string) => {
    if (!enableValidation || !code.trim()) {
      return null;
    }

    // Basic validation patterns
    const patterns = {
      qr: /^[\s\S]+$/, // QR codes can contain any characters
      ean13: /^\d{13}$/,
      ean8: /^\d{8}$/,
      upc_a: /^\d{12}$/,
      upc_e: /^\d{8}$/,
      isbn: /^(97[89])?\d{9}[\dX]$/,
    };

    // Try to detect code type and validate
    for (const [type, pattern] of Object.entries(patterns)) {
      if (pattern.test(code)) {
        return { isValid: true, detectedType: type };
      }
    }

    return { isValid: code.length > 0, detectedType: 'unknown' };
  };

  const handleTextChange = (text: string) => {
    onChange(text);
    setScannedData(null);
  };

  const renderValidationInfo = () => {
    if (scannedData) {
      return (
        <View style={styles.validationIndicator}>
          <CheckCircle size={16} color={theme.colors.success} />
          <View style={styles.validationDetails}>
            <Text style={[styles.validationText, { color: theme.colors.success }]}>
              Scanned successfully
            </Text>
            <Text style={[styles.scannedType, { color: theme.colors.muted }]}>
              {scannedData.type} • {new Date(scannedData.timestamp).toLocaleTimeString()}
            </Text>
          </View>
        </View>
      );
    }

    if (value && enableValidation) {
      const validation = validateCode(value);
      if (validation) {
        return (
          <View style={styles.validationIndicator}>
            {validation.isValid ? (
              <CheckCircle size={16} color={theme.colors.success} />
            ) : (
              <XCircle size={16} color={theme.colors.warning} />
            )}
            <Text style={[
              styles.validationText,
              { color: validation.isValid ? theme.colors.success : theme.colors.warning }
            ]}>
              {validation.isValid ? 'Valid code' : 'Unrecognized format'}
              {validation.detectedType !== 'unknown' && ` (${validation.detectedType.toUpperCase()})`}
            </Text>
          </View>
        );
      }
    }

    return null;
  };

  const renderScannerModal = () => (
    <Modal
      visible={showScanner}
      animationType="slide"
      presentationStyle="fullScreen"
    >
      <View style={styles.scannerContainer}>
        <BarCodeScanner
          onBarCodeScanned={handleBarCodeScanned}
          barCodeTypes={getSupportedBarcodeTypes()}
          style={styles.scanner}
        >
          <View style={styles.scannerOverlay}>
            <View style={styles.scannerHeader}>
              <TouchableOpacity
                style={[styles.scannerButton, { backgroundColor: theme.colors.error }]}
                onPress={() => setShowScanner(false)}
              >
                <XCircle size={24} color="white" />
              </TouchableOpacity>
              
              <Text style={styles.scannerTitle}>
                Scan {allowedTypes.join(' or ').toUpperCase()}
              </Text>
              
              <TouchableOpacity
                style={[
                  styles.scannerButton, 
                  { backgroundColor: flashOn ? theme.colors.warning : theme.colors.muted }
                ]}
                onPress={() => setFlashOn(!flashOn)}
              >
                <Flashlight size={24} color="white" />
              </TouchableOpacity>
            </View>

            <View style={styles.scanArea}>
              <View style={styles.scanFrame}>
                <View style={styles.scanCorner} />
                <View style={[styles.scanCorner, styles.scanCornerTopRight]} />
                <View style={[styles.scanCorner, styles.scanCornerBottomLeft]} />
                <View style={[styles.scanCorner, styles.scanCornerBottomRight]} />
                
                {/* Animated scanning line */}
                <View style={styles.scanLine} />
              </View>
            </View>

            <View style={styles.scannerFooter}>
              <Text style={styles.scannerInstructions}>
                Position the {allowedTypes.join(' or ')} within the frame
              </Text>
              
              <View style={styles.scannerActions}>
                <TouchableOpacity
                  style={[styles.actionButton, { backgroundColor: theme.colors.secondary }]}
                  onPress={() => {
                    setShowScanner(false);
                    // Focus on manual input
                  }}
                >
                  <Edit3 size={20} color="white" />
                  <Text style={styles.actionButtonText}>Manual Entry</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </BarCodeScanner>
      </View>
    </Modal>
  );

  const getIconComponent = () => {
    if (allowedTypes.includes('qr') && allowedTypes.includes('barcode')) {
      return <QrCode size={20} color={theme.colors.muted} />;
    } else if (allowedTypes.includes('qr')) {
      return <QrCode size={20} color={theme.colors.muted} />;
    } else {
      return <QrCode size={20} color={theme.colors.muted} />; // Use QR icon for both for now
    }
  };

  return (
    <View style={styles.container}>
      <View style={[
        styles.inputContainer,
        {
          borderColor: error ? theme.colors.error : 
                      scannedData ? theme.colors.success :
                      theme.colors.border,
          backgroundColor: disabled ? theme.colors.muted + '20' : theme.colors.card,
        }
      ]}>
        <View style={styles.inputIcon}>
          {getIconComponent()}
        </View>

        {enableManualInput ? (
          <TextInput
            style={[
              styles.input,
              {
                color: disabled ? theme.colors.muted : theme.colors.text,
              }
            ]}
            value={value}
            onChangeText={handleTextChange}
            placeholder={placeholder}
            placeholderTextColor={theme.colors.placeholder}
            editable={!disabled}
            autoCapitalize="none"
            autoCorrect={false}
            multiline={false}
          />
        ) : (
          <View style={styles.readOnlyInput}>
            <Text style={[
              styles.readOnlyText,
              {
                color: value ? theme.colors.text : theme.colors.placeholder,
              }
            ]}>
              {value || placeholder}
            </Text>
          </View>
        )}

        <TouchableOpacity
          style={[styles.scanButton, { backgroundColor: theme.colors.primary }]}
          onPress={handleScanPress}
          disabled={disabled}
        >
          <Camera size={18} color="white" />
        </TouchableOpacity>

        {value && (
          <TouchableOpacity
            style={[styles.clearButton, { backgroundColor: theme.colors.muted }]}
            onPress={() => {
              onChange('');
              setScannedData(null);
            }}
            disabled={disabled}
          >
            <XCircle size={18} color="white" />
          </TouchableOpacity>
        )}
      </View>

      {renderValidationInfo()}

      {error && (
        <Text style={[styles.errorText, { color: theme.colors.error }]}>
          {error}
        </Text>
      )}

      {required && !value && (
        <Text style={[styles.requiredText, { color: theme.colors.muted }]}>
          * Required field
        </Text>
      )}

      {renderScannerModal()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 4,
    gap: 8,
  },
  inputIcon: {
    paddingVertical: 8,
  },
  input: {
    flex: 1,
    paddingVertical: 12,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
  },
  readOnlyInput: {
    flex: 1,
    paddingVertical: 12,
  },
  readOnlyText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
  },
  scanButton: {
    padding: 8,
    borderRadius: 6,
  },
  clearButton: {
    padding: 8,
    borderRadius: 6,
  },
  validationIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    gap: 8,
  },
  validationDetails: {
    flex: 1,
  },
  validationText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },
  scannedType: {
    fontSize: 11,
    fontFamily: 'Inter-Regular',
    marginTop: 2,
  },
  errorText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    marginTop: 4,
  },
  requiredText: {
    fontSize: 11,
    fontFamily: 'Inter-Regular',
    marginTop: 4,
  },
  scannerContainer: {
    flex: 1,
  },
  scanner: {
    flex: 1,
  },
  scannerOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.4)',
  },
  scannerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
    paddingBottom: 16,
  },
  scannerButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scannerTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
  },
  scanArea: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  scanFrame: {
    width: 250,
    height: 250,
    position: 'relative',
  },
  scanCorner: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: 30,
    height: 30,
    borderTopWidth: 4,
    borderLeftWidth: 4,
    borderColor: 'white',
  },
  scanCornerTopRight: {
    top: 0,
    right: 0,
    left: undefined,
    borderTopWidth: 4,
    borderRightWidth: 4,
    borderLeftWidth: 0,
  },
  scanCornerBottomLeft: {
    bottom: 0,
    top: undefined,
    borderBottomWidth: 4,
    borderLeftWidth: 4,
    borderTopWidth: 0,
  },
  scanCornerBottomRight: {
    bottom: 0,
    right: 0,
    top: undefined,
    left: undefined,
    borderBottomWidth: 4,
    borderRightWidth: 4,
    borderTopWidth: 0,
    borderLeftWidth: 0,
  },
  scanLine: {
    position: 'absolute',
    top: '50%',
    left: 0,
    right: 0,
    height: 2,
    backgroundColor: 'red',
    opacity: 0.8,
  },
  scannerFooter: {
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingBottom: Platform.OS === 'ios' ? 40 : 20,
    gap: 16,
  },
  scannerInstructions: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: 'white',
    textAlign: 'center',
  },
  scannerActions: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  actionButtonText: {
    color: 'white',
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
  },
});
