import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Alert,
  Dimensions,
} from 'react-native';
import { useTheme } from '@/providers/ThemeProvider';
import {
  Plus,
  Play,
  Pause,
  Edit3,
  Trash2,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Book<PERSON>pen,
  Clock,
  Eye,
  EyeOff,
  Save,
  FolderOpen,
  Share,
  Download,
  ChevronRight,
  ChevronLeft,
  MoreVertical,
} from 'lucide-react-native';

interface StorySlide {
  id: string;
  title: string;
  description: string;
  content: string;
  duration: number;
  visibleLayers: string[];
  mapExtent?: {
    latitude: number;
    longitude: number;
    latitudeDelta: number;
    longitudeDelta: number;
  };
  baseMap: string;
  interactive: boolean;
  autoAdvance: boolean;
}

interface MapStory {
  id: string;
  title: string;
  description: string;
  author: string;
  createdAt: string;
  updatedAt: string;
  tags: string[];
  slides: StorySlide[];
  settings: {
    showProgress: boolean;
    showControls: boolean;
    allowSkipping: boolean;
    loop: boolean;
    theme: 'light' | 'dark' | 'auto';
    layout: 'sidebar' | 'overlay' | 'bottom';
  };
  sharing: {
    isPublic: boolean;
    allowComments: boolean;
    allowEmbedding: boolean;
    password?: string;
  };
  thumbnail?: string;
  viewCount?: number;
  likeCount?: number;
}

interface StoryBuilderPanelProps {
  currentStory: MapStory | null;
  storyProjects: MapStory[];
  isStoryMode: boolean;
  isStoryPlaying: boolean;
  currentSlideIndex: number;
  availableLayers: Array<{ id: string; name: string; type: string }>;
  currentMapExtent: any;
  onStoryCreate: () => void;
  onStorySelect: (story: MapStory) => void;
  onStoryUpdate: (story: MapStory) => void;
  onStoryDelete: (storyId: string) => void;
  onSlideAdd: (story: MapStory) => void;
  onSlideUpdate: (story: MapStory, slideIndex: number, updates: Partial<StorySlide>) => void;
  onSlideDelete: (story: MapStory, slideIndex: number) => void;
  onSlideDuplicate: (story: MapStory, slideIndex: number) => void;
  onSlideReorder: (story: MapStory, fromIndex: number, toIndex: number) => void;
  onStoryPlay: (story: MapStory) => void;
  onStoryPause: () => void;
  onStoryStop: () => void;
  onSlideNavigate: (slideIndex: number) => void;
  onStoryExport: (story: MapStory) => void;
  onStoryShare: (story: MapStory) => void;
  onStoryBuilderOpen: (story: MapStory) => void;
  onShowNotification: (type: 'success' | 'error' | 'warning' | 'info', message: string) => void;
}

const { width: screenWidth } = Dimensions.get('window');

export const StoryBuilderPanel: React.FC<StoryBuilderPanelProps> = ({
  currentStory,
  storyProjects,
  isStoryMode,
  isStoryPlaying,
  currentSlideIndex,
  availableLayers,
  currentMapExtent,
  onStoryCreate,
  onStorySelect,
  onStoryUpdate,
  onStoryDelete,
  onSlideAdd,
  onSlideUpdate,
  onSlideDelete,
  onSlideDuplicate,
  onSlideReorder,
  onStoryPlay,
  onStoryPause,
  onStoryStop,
  onSlideNavigate,
  onStoryExport,
  onStoryShare,
  onStoryBuilderOpen,
  onShowNotification,
}) => {
  const { theme } = useTheme();
  const [selectedStoryId, setSelectedStoryId] = useState<string | null>(currentStory?.id || null);
  const [isCreatingStory, setIsCreatingStory] = useState(false);
  const [newStoryTitle, setNewStoryTitle] = useState('');

  const handleCreateStory = useCallback(() => {
    if (!newStoryTitle.trim()) {
      onShowNotification('error', 'Please enter a story title');
      return;
    }

    onStoryCreate();
    setNewStoryTitle('');
    setIsCreatingStory(false);
    onShowNotification('success', 'New story project created');
  }, [newStoryTitle, onStoryCreate, onShowNotification]);

  const handleStorySelect = useCallback((story: MapStory) => {
    setSelectedStoryId(story.id);
    onStorySelect(story);
  }, [onStorySelect]);

  const handleSlideAdd = useCallback(() => {
    if (!currentStory) return;
    onSlideAdd(currentStory);
    onShowNotification('success', 'New slide added');
  }, [currentStory, onSlideAdd, onShowNotification]);

  const handleSlideDelete = useCallback((slideIndex: number) => {
    if (!currentStory) return;
    
    Alert.alert(
      'Delete Slide',
      'Are you sure you want to delete this slide?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            onSlideDelete(currentStory, slideIndex);
            onShowNotification('info', 'Slide deleted');
          },
        },
      ]
    );
  }, [currentStory, onSlideDelete, onShowNotification]);

  const handleSlideDuplicate = useCallback((slideIndex: number) => {
    if (!currentStory) return;
    onSlideDuplicate(currentStory, slideIndex);
    onShowNotification('success', 'Slide duplicated');
  }, [currentStory, onSlideDuplicate, onShowNotification]);

  const renderStoryProjects = () => (
    <View style={styles.section}>
      <View style={styles.sectionHeader}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Story Projects ({storyProjects.length})
        </Text>
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: theme.colors.primary }]}
          onPress={() => setIsCreatingStory(true)}
        >
          <Plus size={16} color="white" />
        </TouchableOpacity>
      </View>

      {isCreatingStory && (
        <View style={[styles.createStoryForm, { backgroundColor: theme.colors.background }]}>
          <TextInput
            style={[styles.storyTitleInput, { backgroundColor: theme.colors.card, color: theme.colors.text }]}
            placeholder="Enter story title..."
            placeholderTextColor={theme.colors.muted}
            value={newStoryTitle}
            onChangeText={setNewStoryTitle}
            autoFocus
          />
          <View style={styles.createStoryActions}>
            <TouchableOpacity
              style={[styles.createStoryButton, { backgroundColor: theme.colors.muted }]}
              onPress={() => {
                setIsCreatingStory(false);
                setNewStoryTitle('');
              }}
            >
              <Text style={[styles.createStoryButtonText, { color: 'white' }]}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.createStoryButton, { backgroundColor: theme.colors.primary }]}
              onPress={handleCreateStory}
            >
              <Text style={[styles.createStoryButtonText, { color: 'white' }]}>Create</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      <ScrollView style={styles.storyList} showsVerticalScrollIndicator={false}>
        {storyProjects.map(story => (
          <TouchableOpacity
            key={story.id}
            style={[
              styles.storyItem,
              {
                backgroundColor: selectedStoryId === story.id ? theme.colors.primary : theme.colors.background,
                borderColor: theme.colors.border,
              }
            ]}
            onPress={() => handleStorySelect(story)}
          >
            <View style={styles.storyItemContent}>
              <Text style={[
                styles.storyTitle,
                { color: selectedStoryId === story.id ? 'white' : theme.colors.text }
              ]}>
                {story.title}
              </Text>
              <Text style={[
                styles.storyMeta,
                { color: selectedStoryId === story.id ? 'rgba(255,255,255,0.8)' : theme.colors.muted }
              ]}>
                {story.slides.length} slides • {story.author}
              </Text>
              <Text style={[
                styles.storyDate,
                { color: selectedStoryId === story.id ? 'rgba(255,255,255,0.6)' : theme.colors.muted }
              ]}>
                {new Date(story.updatedAt).toLocaleDateString()}
              </Text>
            </View>
            
            <View style={styles.storyActions}>
              <TouchableOpacity
                onPress={() => onStoryBuilderOpen(story)}
                style={styles.storyActionButton}
              >
                <Edit3 size={14} color={selectedStoryId === story.id ? 'white' : theme.colors.primary} />
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => onStoryShare(story)}
                style={styles.storyActionButton}
              >
                <Share size={14} color={selectedStoryId === story.id ? 'white' : theme.colors.muted} />
              </TouchableOpacity>
            </View>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <BookOpen size={20} color={theme.colors.primary} />
          <Text style={[styles.headerTitle, { color: theme.colors.text }]}>Story Builder</Text>
        </View>
        <View style={styles.headerActions}>
          {currentStory && (
            <>
              <TouchableOpacity
                style={[styles.headerButton, { backgroundColor: theme.colors.background }]}
                onPress={() => onStoryExport(currentStory)}
              >
                <Download size={16} color={theme.colors.text} />
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.headerButton, { backgroundColor: theme.colors.background }]}
                onPress={() => onStoryShare(currentStory)}
              >
                <Share size={16} color={theme.colors.text} />
              </TouchableOpacity>
            </>
          )}
        </View>
      </View>

      {renderStoryProjects()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  headerTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8,
  },
  headerButton: {
    padding: 8,
    borderRadius: 6,
  },
  section: {
    marginBottom: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
  },
  actionButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    alignItems: 'center',
    justifyContent: 'center',
  },
  createStoryForm: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 12,
    gap: 12,
  },
  storyTitleInput: {
    padding: 12,
    borderRadius: 6,
    fontSize: 14,
    fontFamily: 'Inter-Regular',
  },
  createStoryActions: {
    flexDirection: 'row',
    gap: 8,
  },
  createStoryButton: {
    flex: 1,
    padding: 10,
    borderRadius: 6,
    alignItems: 'center',
  },
  createStoryButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
  },
  storyList: {
    maxHeight: 300,
  },
  storyItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    borderWidth: 1,
  },
  storyItemContent: {
    flex: 1,
  },
  storyTitle: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 4,
  },
  storyMeta: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    marginBottom: 2,
  },
  storyDate: {
    fontSize: 11,
    fontFamily: 'Inter-Regular',
  },
  storyActions: {
    flexDirection: 'row',
    gap: 8,
  },
  storyActionButton: {
    padding: 4,
  },
});
