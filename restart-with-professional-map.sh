#!/bin/bash

# Professional Map UI - Restart Development Server
# This ensures all new components and changes are loaded

echo "🔄 Restarting FieldSyncPro with Professional Map UI..."
echo "=================================================="

# Kill any existing Metro processes
echo "🛑 Stopping existing development servers..."
pkill -f "expo start" 2>/dev/null || true
pkill -f "metro" 2>/dev/null || true
pkill -f "react-native start" 2>/dev/null || true

# Wait a moment for processes to clean up
sleep 2

# Clear Metro cache
echo "🧹 Clearing Metro cache..."
npx expo start --clear 2>/dev/null || npx react-native start --reset-cache 2>/dev/null || echo "Cache clear attempted"

# Wait for cache clear
sleep 1

echo ""
echo "🚀 Starting development server with new Professional Map UI..."
echo "============================================================"

# Start the development server
if command -v npx expo start >/dev/null 2>&1; then
    echo "Starting with Expo CLI..."
    npx expo start --web
else
    echo "Starting with npm..."
    npm run web
fi

echo ""
echo "✅ Professional Map UI should now be available at:"
echo "   🌐 Web: http://localhost:8081/map"
echo "   📱 Mobile: Use Expo Go app to scan QR code"
echo ""
echo "🎯 New Features Available:"
echo "   • No overlapping UI elements"
echo "   • Functional drawing tools (Point, Line, Polygon, Rectangle, Circle)"
echo "   • Professional layout with sidebar panels"
echo "   • Enhanced feature management"
echo "   • Responsive design"
echo ""
echo "📝 Testing Instructions:"
echo "   1. Navigate to the map tab"
echo "   2. Click 'Draw' in the main toolbar"
echo "   3. Select a drawing tool from the sidebar"
echo "   4. Draw on the map - no more overlapping elements!"
echo "   5. Check 'Features' panel to manage created features"
