import { supabase, TABLES } from '../supabase';
import { BaseApi } from './base';
import { 
  Project, 
  ProjectInsert, 
  ProjectUpdate,
  ProjectWithStats,
  Team,
  Form,
  ApiResponse,
  PaginatedResponse
} from '@/types/database';

export interface ProjectFilters {
  status?: 'draft' | 'active' | 'completed' | 'archived';
  createdBy?: string;
  organization?: string;
  search?: string;
  teamId?: string;
}

export interface ProjectAnalytics {
  submissionsByDate: Array<{ date: string; count: number }>;
  completionRates: Record<string, number>;
  teamPerformance: Array<{ teamName: string; submissions: number; completionRate: number }>;
  geographicDistribution: any;
}

/**
 * Projects API Service
 * Handles all project-related operations including CRUD, analytics, and team assignments
 */
export class ProjectsApi extends BaseApi {
  constructor() {
    super(TABLES.PROJECTS);
  }

  /**
   * Create a new project
   */
  async createProject(projectData: Omit<ProjectInsert, 'id' | 'created_at' | 'updated_at'>): Promise<ApiResponse<Project>> {
    const userId = await this.getCurrentUserId();
    if (!userId) {
      return {
        data: null,
        error: 'User not authenticated'
      };
    }

    const newProject: ProjectInsert = {
      ...projectData,
      created_by: userId,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    return this.handleResponse<Project>(
      supabase
        .from(TABLES.PROJECTS)
        .insert(newProject)
        .select(`
          *,
          user_profiles:created_by (
            id,
            full_name,
            email,
            role
          )
        `)
        .single(),
      'create project'
    );
  }

  /**
   * Get projects with pagination and filters
   */
  async getProjects(
    page: number = 1,
    pageSize: number = 20,
    filters: ProjectFilters = {}
  ): Promise<PaginatedResponse<ProjectWithStats>> {
    const from = (page - 1) * pageSize;
    const to = from + pageSize - 1;

    let query = supabase
      .from(TABLES.PROJECTS)
      .select(`
        *,
        user_profiles:created_by (
          id,
          full_name,
          email,
          role
        ),
        project_stats (
          total_forms,
          total_submissions,
          completed_submissions,
          team_count,
          completion_rate
        )
      `, { count: 'exact' })
      .range(from, to)
      .order('updated_at', { ascending: false });

    // Apply filters
    if (filters.status) {
      query = query.eq('status', filters.status);
    }
    if (filters.createdBy) {
      query = query.eq('created_by', filters.createdBy);
    }
    if (filters.organization) {
      query = query.eq('organization', filters.organization);
    }
    if (filters.search) {
      query = query.or(`name.ilike.%${filters.search}%,description.ilike.%${filters.search}%`);
    }
    if (filters.teamId) {
      query = query.in('id', 
        supabase
          .from(TABLES.PROJECT_TEAMS)
          .select('project_id')
          .eq('team_id', filters.teamId)
          .eq('is_active', true)
      );
    }

    return this.handlePaginatedResponse<ProjectWithStats>(
      query,
      'get projects',
      page,
      pageSize
    );
  }

  /**
   * Get project by ID with full details
   */
  async getProjectById(projectId: string): Promise<ApiResponse<ProjectWithStats>> {
    return this.handleResponse<ProjectWithStats>(
      supabase
        .from(TABLES.PROJECTS)
        .select(`
          *,
          user_profiles:created_by (
            id,
            full_name,
            email,
            role
          ),
          project_stats (
            total_forms,
            total_submissions,
            completed_submissions,
            team_count,
            completion_rate
          ),
          teams:project_teams!inner (
            team_id,
            assigned_at,
            teams (
              id,
              name,
              description,
              created_at
            )
          ),
          forms (
            id,
            name,
            description,
            status,
            version,
            created_at
          )
        `)
        .eq('id', projectId)
        .single(),
      'get project by id'
    );
  }

  /**
   * Update project
   */
  async updateProject(
    projectId: string,
    updates: Omit<ProjectUpdate, 'updated_at'>
  ): Promise<ApiResponse<Project>> {
    const projectUpdate: ProjectUpdate = {
      ...updates,
      updated_at: new Date().toISOString()
    };

    return this.handleResponse<Project>(
      supabase
        .from(TABLES.PROJECTS)
        .update(projectUpdate)
        .eq('id', projectId)
        .select(`
          *,
          user_profiles:created_by (
            id,
            full_name,
            email,
            role
          )
        `)
        .single(),
      'update project'
    );
  }

  /**
   * Delete project (soft delete by changing status to archived)
   */
  async deleteProject(projectId: string): Promise<ApiResponse<Project>> {
    return this.updateProject(projectId, { status: 'archived' });
  }

  /**
   * Assign team to project
   */
  async assignTeamToProject(
    projectId: string,
    teamId: string
  ): Promise<ApiResponse<any>> {
    const userId = await this.getCurrentUserId();
    if (!userId) {
      return {
        data: null,
        error: 'User not authenticated'
      };
    }

    return this.handleResponse(
      supabase
        .from(TABLES.PROJECT_TEAMS)
        .insert({
          project_id: projectId,
          team_id: teamId,
          assigned_by: userId,
          assigned_at: new Date().toISOString()
        })
        .select(`
          *,
          teams (
            id,
            name,
            description
          )
        `)
        .single(),
      'assign team to project'
    );
  }

  /**
   * Remove team from project
   */
  async removeTeamFromProject(
    projectId: string,
    teamId: string
  ): Promise<ApiResponse<null>> {
    return this.handleResponse<null>(
      supabase
        .from(TABLES.PROJECT_TEAMS)
        .update({ is_active: false })
        .eq('project_id', projectId)
        .eq('team_id', teamId),
      'remove team from project'
    );
  }

  /**
   * Get teams assigned to project
   */
  async getProjectTeams(projectId: string): Promise<ApiResponse<Team[]>> {
    return this.handleResponse<Team[]>(
      supabase
        .from(TABLES.PROJECT_TEAMS)
        .select(`
          teams (
            id,
            name,
            description,
            created_at,
            is_active
          )
        `)
        .eq('project_id', projectId)
        .eq('is_active', true),
      'get project teams'
    );
  }

  /**
   * Get project forms
   */
  async getProjectForms(
    projectId: string,
    status?: 'draft' | 'published' | 'archived'
  ): Promise<ApiResponse<Form[]>> {
    let query = supabase
      .from(TABLES.FORMS)
      .select(`
        *,
        user_profiles:created_by (
          id,
          full_name,
          email
        )
      `)
      .eq('project_id', projectId)
      .order('created_at', { ascending: false });

    if (status) {
      query = query.eq('status', status);
    }

    return this.handleResponse<Form[]>(
      query,
      'get project forms'
    );
  }

  /**
   * Get project analytics
   */
  async getProjectAnalytics(
    projectId: string,
    dateFrom?: string,
    dateTo?: string
  ): Promise<ApiResponse<ProjectAnalytics>> {
    try {
      const { data, error } = await supabase
        .rpc('get_project_analytics', {
          project_id: projectId,
          date_from: dateFrom,
          date_to: dateTo
        });

      if (error) {
        throw error;
      }

      return {
        data: data as ProjectAnalytics,
        error: null
      };
    } catch (error) {
      console.error('Get project analytics failed:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to get project analytics'
      };
    }
  }

  /**
   * Search projects with advanced text search
   */
  async searchProjects(
    searchTerm: string,
    page: number = 1,
    pageSize: number = 20,
    filters: Omit<ProjectFilters, 'search'> = {}
  ): Promise<PaginatedResponse<Project>> {
    const userId = await this.getCurrentUserId();
    if (!userId) {
      return {
        data: null,
        error: 'User not authenticated',
        page,
        pageSize,
        totalPages: 0,
        totalCount: 0
      };
    }

    try {
      const { data, error } = await supabase
        .rpc('search_projects', {
          search_term: searchTerm,
          user_id: userId,
          limit: pageSize
        });

      if (error) {
        throw error;
      }

      // Apply additional filters to the results
      let filteredData = data || [];
      if (filters.status) {
        filteredData = filteredData.filter(p => p.status === filters.status);
      }
      if (filters.organization) {
        filteredData = filteredData.filter(p => p.organization === filters.organization);
      }

      const totalCount = filteredData.length;
      const totalPages = Math.ceil(totalCount / pageSize);
      const startIndex = (page - 1) * pageSize;
      const paginatedData = filteredData.slice(startIndex, startIndex + pageSize);

      return {
        data: paginatedData,
        error: null,
        page,
        pageSize,
        totalPages,
        totalCount
      };
    } catch (error) {
      console.error('Search projects failed:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Search failed',
        page,
        pageSize,
        totalPages: 0,
        totalCount: 0
      };
    }
  }

  /**
   * Get user's projects (projects created by user or assigned to user's teams)
   */
  async getUserProjects(
    userId?: string,
    page: number = 1,
    pageSize: number = 20
  ): Promise<PaginatedResponse<ProjectWithStats>> {
    const currentUserId = userId || await this.getCurrentUserId();
    if (!currentUserId) {
      return {
        data: null,
        error: 'User not authenticated',
        page,
        pageSize,
        totalPages: 0,
        totalCount: 0
      };
    }

    const from = (page - 1) * pageSize;
    const to = from + pageSize - 1;

    const query = supabase
      .from(TABLES.PROJECTS)
      .select(`
        *,
        user_profiles:created_by (
          id,
          full_name,
          email,
          role
        ),
        project_stats (
          total_forms,
          total_submissions,
          completed_submissions,
          team_count,
          completion_rate
        )
      `, { count: 'exact' })
      .range(from, to)
      .order('updated_at', { ascending: false });

    // Get projects the user has access to
    // Either created by user or user is in an assigned team
    query.or(`created_by.eq.${currentUserId},id.in.(
      select project_id from project_teams 
      where team_id in (
        select team_id from team_members 
        where user_id = '${currentUserId}' and is_active = true
      ) and is_active = true
    )`);

    return this.handlePaginatedResponse<ProjectWithStats>(
      query,
      'get user projects',
      page,
      pageSize
    );
  }

  /**
   * Get project statistics
   */
  async getProjectStats(projectId?: string): Promise<ApiResponse<any>> {
    let query = supabase
      .from('project_stats')
      .select('*');

    if (projectId) {
      query = query.eq('project_id', projectId);
    }

    return this.handleResponse(
      query,
      'get project stats'
    );
  }

  /**
   * Duplicate project
   */
  async duplicateProject(
    projectId: string,
    newName: string,
    includeTeams: boolean = false
  ): Promise<ApiResponse<Project>> {
    try {
      // Get original project
      const originalResponse = await this.getProjectById(projectId);
      if (!originalResponse.data) {
        return {
          data: null,
          error: 'Original project not found'
        };
      }

      const original = originalResponse.data;
      const userId = await this.getCurrentUserId();
      if (!userId) {
        return {
          data: null,
          error: 'User not authenticated'
        };
      }

      // Create new project
      const newProject: ProjectInsert = {
        name: newName,
        description: `Copy of ${original.name}`,
        status: 'draft',
        created_by: userId,
        organization: original.organization,
        region: original.region,
        settings: original.settings,
        metadata: {
          ...original.metadata,
          duplicated_from: projectId,
          duplicated_at: new Date().toISOString()
        }
      };

      const createResponse = await this.createProject(newProject);
      if (!createResponse.data) {
        return createResponse;
      }

      // Assign teams if requested
      if (includeTeams && original.teams) {
        for (const teamAssignment of original.teams) {
          await this.assignTeamToProject(
            createResponse.data.id,
            teamAssignment.team_id
          );
        }
      }

      return createResponse;
    } catch (error) {
      console.error('Duplicate project failed:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to duplicate project'
      };
    }
  }

  /**
   * Subscribe to project changes
   */
  subscribeToProjectChanges(callback: (payload: any) => void) {
    return this.subscribeToChanges(callback);
  }

  /**
   * Subscribe to specific project changes
   */
  subscribeToProjectById(projectId: string, callback: (payload: any) => void) {
    return this.subscribeToChanges(callback, `id=eq.${projectId}`);
  }
}

// Export singleton instance
export const projectsApi = new ProjectsApi();
export default projectsApi;
