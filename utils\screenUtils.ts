/**
 * Screen utilities for react-native-screens compatibility
 * Handles initialization and error recovery for navigation screens
 */

import { Platform } from 'react-native';

let screensInitialized = false;

/**
 * Initialize react-native-screens with proper error handling
 */
export const initializeScreens = (): boolean => {
  if (Platform.OS === 'web') {
    // Web doesn't need react-native-screens
    return true;
  }

  if (screensInitialized) {
    return true;
  }

  try {
    const screens = require('react-native-screens');
    
    // Check if enableScreens function exists
    if (screens && typeof screens.enableScreens === 'function') {
      screens.enableScreens();
      screensInitialized = true;
      console.log('✅ react-native-screens initialized successfully');
      return true;
    } else {
      console.warn('⚠️ react-native-screens.enableScreens not available');
      return false;
    }
  } catch (error) {
    console.warn('⚠️ Failed to initialize react-native-screens:', error);
    return false;
  }
};

/**
 * Check if screens are properly initialized
 */
export const areScreensInitialized = (): boolean => {
  return screensInitialized || Platform.OS === 'web';
};

/**
 * Get screen configuration with fallbacks
 */
export const getScreenConfig = () => {
  const isInitialized = areScreensInitialized();
  
  return {
    headerShown: false,
    // Add additional screen options that work without react-native-screens
    gestureEnabled: isInitialized,
    animationEnabled: isInitialized,
  };
};

/**
 * Safe screen options that work with or without react-native-screens
 */
export const getSafeScreenOptions = () => {
  try {
    return {
      headerShown: false,
      gestureEnabled: true,
      animationEnabled: true,
    };
  } catch (error) {
    // Fallback options if there are any issues
    return {
      headerShown: false,
    };
  }
};
