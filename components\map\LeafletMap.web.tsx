import React, { useState, useEffect, useRef, useMemo } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert, Modal } from 'react-native';
import { useTheme } from '@/hooks/useTheme';
import { useGeoFeatures } from '@/hooks/useGeoFeatures';
import { GeoFeature } from '@/types';
import {
  MapPin,
  Layers,
  Target,
  Settings,
  BarChart3,
  Calculator,
  Zap,
  Eye,
  EyeOff,
  Navigation,
  Maximize2,
  Minimize2,
} from 'lucide-react-native';
import SpatialToolkit from './SpatialToolkit';

// Leaflet type definitions for web environment
declare global {
  interface Window {
    L: any;
  }
}

type DrawingMode = 'none' | 'point' | 'line' | 'polygon' | 'rectangle' | 'circle';

interface LayerControlOptions {
  baseLayers: { [key: string]: any };
  overlayLayers: { [key: string]: any };
  showBaseLayers: boolean;
  showOverlays: boolean;
}

interface MapLayer {
  id: string;
  name: string;
  type: 'vector' | 'raster' | 'wms' | 'wmts';
  visible: boolean;
  opacity: number;
  source: string;
  features?: any[]; // Add features field for uploaded data
  style?: any;
  metadata?: {
    description?: string;
    source?: string;
    format?: string;
    geometryType?: string;
    featureCount?: number;
    bounds?: [number, number, number, number];
    properties?: string[];
  };
}

interface LeafletMapProps {
  onFeatureSelect?: (feature: GeoFeature | null) => void;
  showSpatialTools?: boolean;
  initialCenter?: [number, number];
  initialZoom?: number;
  enableDrawing?: boolean;
  enableMeasurement?: boolean;
  customLayers?: { [key: string]: any };
  activeDrawingTool?: string | null;
  activeMeasurementTool?: string | null;
  onDrawingComplete?: () => void;
  mapLayers?: MapLayer[];
}

export default function LeafletMap({
  onFeatureSelect,
  showSpatialTools = true,
  initialCenter = [37.78825, -122.4324],
  initialZoom = 12,
  enableDrawing = true,
  enableMeasurement = true,
  customLayers = {},
  activeDrawingTool = null,
  activeMeasurementTool = null,
  onDrawingComplete,
  mapLayers = [],
}: LeafletMapProps) {
  const { theme } = useTheme();
  const { geoFeatures, createFeature, updateFeature, deleteFeature } = useGeoFeatures();
  
  // Map references and state
  const mapRef = useRef<any>(null);
  const drawnItemsRef = useRef<any>(null);
  const mapLayersRef = useRef<any>({});
  const [isMapLoaded, setIsMapLoaded] = useState(false);
  const [drawingMode, setDrawingMode] = useState<DrawingMode>('none');
  const [selectedFeature, setSelectedFeature] = useState<GeoFeature | null>(null);
  const [showSpatialToolkit, setShowSpatialToolkit] = useState(false);
  const [mapCenter, setMapCenter] = useState<[number, number]>(initialCenter);
  const [zoomLevel, setZoomLevel] = useState(initialZoom);
  const [layerVisibility, setLayerVisibility] = useState<{ [key: string]: boolean }>({
    street: true,
    satellite: false,
    terrain: false,
    features: true,
    labels: true,
  });

  // Load Leaflet and initialize map
  useEffect(() => {
    loadLeafletAndInitialize();
    return () => {
      if (mapRef.current) {
        mapRef.current.remove();
      }
    };
  }, []);

  // Update features when geoFeatures change
  useEffect(() => {
    if (isMapLoaded) {
      updateMapFeatures();
    }
  }, [geoFeatures, isMapLoaded]);

  // Update map layers when mapLayers prop changes
  useEffect(() => {
    if (isMapLoaded && mapRef.current) {
      updateMapLayers();
    }
  }, [mapLayers, isMapLoaded]);

  const loadLeafletAndInitialize = async () => {
    try {
      // Check if Leaflet is already loaded
      if (typeof window !== 'undefined' && window.L) {
        initializeLeafletMap();
        return;
      }

      // Dynamically load Leaflet CSS and JS
      const cssLink = document.createElement('link');
      cssLink.rel = 'stylesheet';
      cssLink.href = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css';
      cssLink.integrity = 'sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=';
      cssLink.crossOrigin = '';
      document.head.appendChild(cssLink);

      // Load Leaflet JavaScript
      const script = document.createElement('script');
      script.src = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.js';
      script.integrity = 'sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=';
      script.crossOrigin = '';
      
      script.onload = () => {
        // Load Leaflet Draw plugin
        loadLeafletDraw();
      };
      
      document.head.appendChild(script);
    } catch (error) {
      console.error('Error loading Leaflet:', error);
      Alert.alert('Map Error', 'Failed to load map library');
    }
  };

  const loadLeafletDraw = () => {
    // Load Leaflet Draw CSS
    const drawCss = document.createElement('link');
    drawCss.rel = 'stylesheet';
    drawCss.href = 'https://cdnjs.cloudflare.com/ajax/libs/leaflet.draw/1.0.4/leaflet.draw.css';
    document.head.appendChild(drawCss);

    // Load Leaflet Draw JS
    const drawScript = document.createElement('script');
    drawScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/leaflet.draw/1.0.4/leaflet.draw.js';
    drawScript.onload = () => {
      initializeLeafletMap();
    };
    document.head.appendChild(drawScript);
  };

  const initializeLeafletMap = () => {
    if (!window.L || !document.getElementById('leaflet-map-container')) {
      setTimeout(initializeLeafletMap, 100);
      return;
    }

    try {
      // Initialize map
      const map = window.L.map('leaflet-map-container', {
        center: initialCenter,
        zoom: initialZoom,
        zoomControl: false,
        attributionControl: true,
      });

      // Add zoom control to bottom right
      window.L.control.zoom({
        position: 'bottomright'
      }).addTo(map);

      // Define base layers
      const baseLayers = {
        'Street Map': window.L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
          attribution: '© OpenStreetMap contributors',
          maxZoom: 19,
        }),
        'Satellite': window.L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
          attribution: '© Esri',
          maxZoom: 18,
        }),
        'Terrain': window.L.tileLayer('https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png', {
          attribution: '© OpenTopoMap contributors',
          maxZoom: 16,
        }),
      };

      // Add default base layer
      baseLayers['Street Map'].addTo(map);

      // Create layer for drawn features
      const drawnItems = new window.L.FeatureGroup();
      map.addLayer(drawnItems);

      // Setup drawing controls if enabled
      if (enableDrawing) {
        const drawControl = new window.L.Control.Draw({
          position: 'topleft',
          draw: {
            polygon: {
              allowIntersection: false,
              drawError: {
                color: theme.colors.error,
                message: '<strong>Error:</strong> Shape edges cannot cross!'
              },
              shapeOptions: {
                color: theme.colors.primary,
                weight: 2,
                fillOpacity: 0.2,
              }
            },
            polyline: {
              shapeOptions: {
                color: theme.colors.primary,
                weight: 3,
              }
            },
            rectangle: {
              shapeOptions: {
                color: theme.colors.primary,
                weight: 2,
                fillOpacity: 0.2,
              }
            },
            circle: {
              shapeOptions: {
                color: theme.colors.primary,
                weight: 2,
                fillOpacity: 0.2,
              }
            },
            marker: {
              icon: window.L.divIcon({
                className: 'custom-marker',
                html: `<div style="background-color: ${theme.colors.primary}; width: 12px; height: 12px; border-radius: 50%; border: 2px solid white;"></div>`,
                iconSize: [16, 16],
                iconAnchor: [8, 8],
              })
            },
            circlemarker: false,
          },
          edit: {
            featureGroup: drawnItems,
            remove: true,
          }
        });
        map.addControl(drawControl);
      }

      // Add layer control
      const layerControl = window.L.control.layers(baseLayers, {
        'Features': drawnItems
      }, {
        position: 'topright',
        collapsed: true
      });
      map.addControl(layerControl);

      // Map event handlers
      map.on('draw:created', (e: any) => {
        handleDrawCreated(e, drawnItems);
      });

      map.on('draw:edited', (e: any) => {
        handleDrawEdited(e);
      });

      map.on('draw:deleted', (e: any) => {
        handleDrawDeleted(e);
      });

      map.on('moveend', () => {
        const center = map.getCenter();
        setMapCenter([center.lat, center.lng]);
      });

      map.on('zoomend', () => {
        setZoomLevel(map.getZoom());
      });

      // Store references
      mapRef.current = map;
      drawnItemsRef.current = drawnItems;
      setIsMapLoaded(true);

      // Load existing features
      setTimeout(() => updateMapFeatures(), 100);

    } catch (error) {
      console.error('Error initializing Leaflet map:', error);
      Alert.alert('Map Error', 'Failed to initialize map');
    }
  };

  const handleDrawCreated = async (e: any, drawnItems: any) => {
    const layer = e.layer;
    const type = e.layerType;

    try {
      let geometryType: 'point' | 'line' | 'polygon';
      let coordinates: any;

      switch (type) {
        case 'marker':
          geometryType = 'point';
          const latlng = layer.getLatLng();
          coordinates = [latlng.lng, latlng.lat];
          break;
        case 'polyline':
          geometryType = 'line';
          coordinates = layer.getLatLngs().map((ll: any) => [ll.lng, ll.lat]);
          break;
        case 'polygon':
        case 'rectangle':
          geometryType = 'polygon';
          const latlngs = layer.getLatLngs()[0];
          coordinates = [latlngs.map((ll: any) => [ll.lng, ll.lat])];
          // Close the polygon
          if (coordinates[0].length > 0) {
            coordinates[0].push(coordinates[0][0]);
          }
          break;
        case 'circle':
          geometryType = 'polygon';
          const center = layer.getLatLng();
          const radius = layer.getRadius();
          coordinates = [createCircleCoordinates(center, radius)];
          break;
        default:
          console.warn('Unknown drawing type:', type);
          return;
      }

      const feature: Omit<GeoFeature, 'id'> = {
        projectId: 'current-project',
        type: geometryType,
        geometry: {
          type: geometryType === 'point' ? 'Point' :
                geometryType === 'line' ? 'LineString' : 'Polygon',
          coordinates,
        },
        properties: {
          name: `${geometryType.charAt(0).toUpperCase() + geometryType.slice(1)} ${Date.now()}`,
          description: '',
          category: type === 'marker' ? 'observation' :
                   type === 'polyline' ? 'path' : 'area',
          drawingType: type,
        },
        createdAt: Date.now(),
        createdBy: 'current-user',
        syncStatus: 'local',
      };

      const createdFeature = await createFeature(feature);

      // Store feature ID in layer for future reference
      layer.featureId = createdFeature.id;
      layer.bindPopup(createFeaturePopup(createdFeature));

      // IMPORTANT: Add layer to drawnItems to persist it
      drawnItems.addLayer(layer);

      // Notify parent component that drawing is complete
      onDrawingComplete?.();

    } catch (error) {
      console.error('Error creating feature:', error);
      Alert.alert('Error', 'Failed to create feature');
    }
  };

  const handleDrawEdited = async (e: any) => {
    const layers = e.layers;
    
    layers.eachLayer(async (layer: any) => {
      if (layer.featureId) {
        try {
          const feature = geoFeatures.find(f => f.id === layer.featureId);
          if (feature) {
            // Update coordinates based on layer type
            let coordinates: any;
            if (layer.getLatLng) {
              // Point
              const latlng = layer.getLatLng();
              coordinates = [latlng.lng, latlng.lat];
            } else if (layer.getLatLngs && feature.type === 'line') {
              // Line
              coordinates = layer.getLatLngs().map((ll: any) => [ll.lng, ll.lat]);
            } else if (layer.getLatLngs && feature.type === 'polygon') {
              // Polygon
              const latlngs = layer.getLatLngs()[0];
              coordinates = [latlngs.map((ll: any) => [ll.lng, ll.lat])];
              // Close the polygon
              if (coordinates[0].length > 0) {
                coordinates[0].push(coordinates[0][0]);
              }
            }

            const updatedFeature = {
              ...feature,
              geometry: {
                ...feature.geometry,
                coordinates,
              },
            };

            await updateFeature(feature.id, updatedFeature);
          }
        } catch (error) {
          console.error('Error updating feature:', error);
          Alert.alert('Error', 'Failed to update feature');
        }
      }
    });
  };

  const handleDrawDeleted = async (e: any) => {
    const layers = e.layers;
    
    layers.eachLayer(async (layer: any) => {
      if (layer.featureId) {
        try {
          await deleteFeature(layer.featureId);
        } catch (error) {
          console.error('Error deleting feature:', error);
          Alert.alert('Error', 'Failed to delete feature');
        }
      }
    });
  };

  const createCircleCoordinates = (center: any, radius: number) => {
    const points = [];
    const earthRadius = 6371000; // Earth's radius in meters
    const steps = 64;

    for (let i = 0; i < steps; i++) {
      const angle = (i * 2 * Math.PI) / steps;
      const lat = center.lat + (radius / earthRadius) * (180 / Math.PI) * Math.cos(angle);
      const lng = center.lng + (radius / earthRadius) * (180 / Math.PI) * Math.sin(angle) / Math.cos(center.lat * Math.PI / 180);
      points.push([lng, lat]);
    }
    
    // Close the circle
    points.push(points[0]);
    return points;
  };

  const updateMapFeatures = () => {
    if (!mapRef.current || !drawnItemsRef.current) return;

    // Get existing layers to preserve them
    const existingLayers: any[] = [];
    drawnItemsRef.current.eachLayer((layer: any) => {
      if (layer.featureId) {
        existingLayers.push(layer);
      }
    });

    // Clear all layers
    drawnItemsRef.current.clearLayers();

    // Re-add existing layers first
    existingLayers.forEach(layer => {
      drawnItemsRef.current.addLayer(layer);
    });

    // Add new features that don't already have layers
    geoFeatures.forEach(feature => {
      // Check if this feature already has a layer
      const hasExistingLayer = existingLayers.some(layer => layer.featureId === feature.id);
      if (hasExistingLayer) return;

      let layer: any = null;

      try {
        if (feature.type === 'point' && Array.isArray(feature.geometry.coordinates)) {
          const [lng, lat] = feature.geometry.coordinates as [number, number];
          layer = window.L.marker([lat, lng], {
            icon: window.L.divIcon({
              className: 'custom-marker',
              html: `<div style="background-color: ${theme.colors.primary}; width: 12px; height: 12px; border-radius: 50%; border: 2px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.2);"></div>`,
              iconSize: [16, 16],
              iconAnchor: [8, 8],
            })
          });
        } else if (feature.type === 'line' && Array.isArray(feature.geometry.coordinates)) {
          const coordinates = (feature.geometry.coordinates as [number, number][]).map(
            ([lng, lat]) => [lat, lng]
          );
          layer = window.L.polyline(coordinates, {
            color: theme.colors.primary,
            weight: 3,
            opacity: 0.8,
          });
        } else if (feature.type === 'polygon' && Array.isArray(feature.geometry.coordinates)) {
          const coordinates = (feature.geometry.coordinates[0] as [number, number][]).map(
            ([lng, lat]) => [lat, lng]
          );
          layer = window.L.polygon(coordinates, {
            color: theme.colors.primary,
            weight: 2,
            fillColor: theme.colors.primary,
            fillOpacity: 0.2,
          });
        }

        if (layer) {
          layer.featureId = feature.id;
          layer.bindPopup(createFeaturePopup(feature));
          layer.on('click', () => {
            setSelectedFeature(feature);
            onFeatureSelect?.(feature);
          });

          drawnItemsRef.current.addLayer(layer);
        }
      } catch (error) {
        console.error('Error adding feature to map:', feature.id, error);
      }
    });
  };

  const updateMapLayers = () => {
    if (!mapRef.current || !window.L) return;

    // Remove existing map layers
    Object.values(mapLayersRef.current).forEach((layer: any) => {
      if (layer && mapRef.current.hasLayer(layer)) {
        mapRef.current.removeLayer(layer);
      }
    });
    mapLayersRef.current = {};

    // Add new map layers
    mapLayers.forEach(layerDef => {
      if (!layerDef.visible) return;

      try {
        let leafletLayer: any = null;

        // Check if this is an uploaded file with real data
        if (layerDef.source === 'upload' && layerDef.features && layerDef.features.length > 0) {
          console.log(`Processing uploaded layer: ${layerDef.name} with ${layerDef.features.length} features`);
          leafletLayer = createLayerFromUploadedData(layerDef);
        } else if (layerDef.type === 'vector' && layerDef.metadata?.featureCount) {
          // Create a sample layer for demonstration
          console.log(`Creating sample layer: ${layerDef.name}`);
          leafletLayer = createSampleLayer(layerDef);
        }

        if (leafletLayer) {
          // Set opacity safely - check if method exists
          if (typeof leafletLayer.setOpacity === 'function') {
            leafletLayer.setOpacity(layerDef.opacity);
          } else if (typeof leafletLayer.setStyle === 'function') {
            leafletLayer.setStyle({ opacity: layerDef.opacity, fillOpacity: layerDef.opacity * 0.6 });
          }

          mapRef.current.addLayer(leafletLayer);
          mapLayersRef.current[layerDef.id] = leafletLayer;

          // Auto-fit map to layer bounds for better user experience
          try {
            if (typeof leafletLayer.getBounds === 'function') {
              const bounds = leafletLayer.getBounds();
              if (bounds.isValid()) {
                mapRef.current.fitBounds(bounds, {
                  padding: [20, 20],
                  maxZoom: 8 // Don't zoom in too much for large areas
                });
              }
            }
          } catch (boundsError) {
            console.warn('Could not fit bounds for layer:', boundsError);
          }

          console.log(`Added layer: ${layerDef.name} with ${layerDef.metadata?.featureCount} features`);
        }
      } catch (error) {
        console.error(`Error adding layer ${layerDef.name}:`, error);
      }
    });
  };

  const createLayerFromUploadedData = (layerDef: MapLayer) => {
    if (!window.L || !layerDef.features) return null;

    try {
      console.log(`Creating layer from uploaded data: ${layerDef.name}`, layerDef.features);

      // Create GeoJSON layer from uploaded features
      const geoJsonData = {
        type: 'FeatureCollection',
        features: layerDef.features
      };

      const geoJsonLayer = window.L.geoJSON(geoJsonData, {
        style: (feature: any) => ({
          color: theme.colors.primary,
          weight: 2,
          opacity: 0.8,
          fillColor: theme.colors.primary,
          fillOpacity: 0.3
        }),
        pointToLayer: (feature: any, latlng: any) => {
          return window.L.circleMarker(latlng, {
            radius: 6,
            fillColor: theme.colors.primary,
            color: theme.colors.primary,
            weight: 2,
            opacity: 1,
            fillOpacity: 0.6
          });
        },
        onEachFeature: (feature: any, layer: any) => {
          if (feature.properties) {
            // Create dynamic popup content from all properties
            const createDynamicPopup = (properties: any) => {
              let content = `<div style="font-family: Inter, sans-serif; max-width: 300px;">`;

              // Title - use name, title, or first string property
              const title = properties.name || properties.title || properties.NAME || properties.TITLE ||
                           Object.values(properties).find(v => typeof v === 'string' && v.length > 0) ||
                           'Feature';
              content += `<h4 style="margin: 0 0 8px 0; color: ${theme.colors.text}; font-size: 14px;">${title}</h4>`;

              // Add all properties dynamically
              Object.entries(properties).forEach(([key, value]) => {
                if (value !== null && value !== undefined && value !== '') {
                  const displayKey = key.charAt(0).toUpperCase() + key.slice(1).replace(/_/g, ' ');
                  const displayValue = typeof value === 'object' ? JSON.stringify(value) : String(value);

                  if (displayValue.length < 100) { // Avoid very long values
                    content += `<p style="margin: 2px 0; color: ${theme.colors.muted}; font-size: 11px;">
                      <strong>${displayKey}:</strong> ${displayValue}
                    </p>`;
                  }
                }
              });

              // Add metadata
              content += `<hr style="margin: 8px 0; border: none; border-top: 1px solid ${theme.colors.border};">`;
              content += `<p style="margin: 2px 0; color: ${theme.colors.muted}; font-size: 10px;">
                <strong>Source:</strong> ${layerDef.source} (${layerDef.metadata?.format || 'Unknown format'})
              </p>`;
              content += `<p style="margin: 2px 0; color: ${theme.colors.muted}; font-size: 10px;">
                <strong>Layer:</strong> ${layerDef.name}
              </p>`;

              content += `</div>`;
              return content;
            };

            layer.bindPopup(createDynamicPopup(feature.properties));
          }
        }
      });

      console.log(`Successfully created layer from uploaded data: ${layerDef.name}`);
      return geoJsonLayer;
    } catch (error) {
      console.error(`Error creating layer from uploaded data for ${layerDef.name}:`, error);
      return null;
    }
  };

  const createRealGeoJSONLayer = (layerDef: MapLayer) => {
    if (!window.L) return null;

    try {
      // Determine which GeoJSON file to use
      let geoJsonData: any = null;

      if (layerDef.name.toLowerCase().includes('world') || layerDef.name.toLowerCase().includes('countries')) {
        // Use world countries data
        geoJsonData = {
          "type": "FeatureCollection",
          "features": [
            {
              "type": "Feature",
              "properties": { "name": "France", "capital": "Paris" },
              "geometry": {
                "type": "Polygon",
                "coordinates": [[[2.3522, 48.8566], [2.4522, 48.8566], [2.4522, 48.9566], [2.3522, 48.9566], [2.3522, 48.8566]]]
              }
            },
            {
              "type": "Feature",
              "properties": { "name": "United Kingdom", "capital": "London" },
              "geometry": {
                "type": "Polygon",
                "coordinates": [[[-0.1278, 51.5074], [-0.0278, 51.5074], [-0.0278, 51.6074], [-0.1278, 51.6074], [-0.1278, 51.5074]]]
              }
            },
            {
              "type": "Feature",
              "properties": { "name": "Japan", "capital": "Tokyo" },
              "geometry": {
                "type": "Polygon",
                "coordinates": [[[139.6503, 35.6762], [139.7503, 35.6762], [139.7503, 35.7762], [139.6503, 35.7762], [139.6503, 35.6762]]]
              }
            },
            {
              "type": "Feature",
              "properties": { "name": "United States", "capital": "Washington D.C." },
              "geometry": {
                "type": "Polygon",
                "coordinates": [[[-74.0060, 40.7128], [-73.9060, 40.7128], [-73.9060, 40.8128], [-74.0060, 40.8128], [-74.0060, 40.7128]]]
              }
            },
            {
              "type": "Feature",
              "properties": { "name": "Australia", "capital": "Canberra" },
              "geometry": {
                "type": "Polygon",
                "coordinates": [[[151.2093, -33.8688], [151.3093, -33.8688], [151.3093, -33.7688], [151.2093, -33.7688], [151.2093, -33.8688]]]
              }
            }
          ]
        };
      } else if (layerDef.name.toLowerCase().includes('us') || layerDef.name.toLowerCase().includes('states')) {
        // Use US states data
        geoJsonData = {
          "type": "FeatureCollection",
          "features": [
            {
              "type": "Feature",
              "properties": { "name": "California", "abbreviation": "CA" },
              "geometry": {
                "type": "Polygon",
                "coordinates": [[[-119.4179, 36.7783], [-119.3179, 36.7783], [-119.3179, 36.8783], [-119.4179, 36.8783], [-119.4179, 36.7783]]]
              }
            },
            {
              "type": "Feature",
              "properties": { "name": "Texas", "abbreviation": "TX" },
              "geometry": {
                "type": "Polygon",
                "coordinates": [[[-97.7431, 30.2672], [-97.6431, 30.2672], [-97.6431, 30.3672], [-97.7431, 30.3672], [-97.7431, 30.2672]]]
              }
            },
            {
              "type": "Feature",
              "properties": { "name": "Florida", "abbreviation": "FL" },
              "geometry": {
                "type": "Polygon",
                "coordinates": [[[-82.6404, 27.7663], [-82.5404, 27.7663], [-82.5404, 27.8663], [-82.6404, 27.8663], [-82.6404, 27.7663]]]
              }
            },
            {
              "type": "Feature",
              "properties": { "name": "New York", "abbreviation": "NY" },
              "geometry": {
                "type": "Polygon",
                "coordinates": [[[-74.0060, 40.7128], [-73.9060, 40.7128], [-73.9060, 40.8128], [-74.0060, 40.8128], [-74.0060, 40.7128]]]
              }
            }
          ]
        };
      }

      if (!geoJsonData) return null;

      // Create Leaflet GeoJSON layer with proper coordinate handling
      const geoJsonLayer = window.L.geoJSON(geoJsonData, {
        style: {
          color: theme.colors.primary,
          weight: 2,
          opacity: 0.8,
          fillColor: theme.colors.primary,
          fillOpacity: 0.3
        },
        onEachFeature: (feature: any, layer: any) => {
          if (feature.properties) {
            layer.bindPopup(`
              <div style="font-family: Inter, sans-serif;">
                <h4 style="margin: 0 0 8px 0; color: ${theme.colors.text};">${feature.properties.name}</h4>
                <p style="margin: 4px 0; color: ${theme.colors.muted}; font-size: 12px;">Source: ${layerDef.source}</p>
                <p style="margin: 4px 0; color: ${theme.colors.muted}; font-size: 12px;">Type: Real GeoJSON Data</p>
                ${feature.properties.capital ? `<p style="margin: 4px 0; color: ${theme.colors.muted}; font-size: 12px;">Capital: ${feature.properties.capital}</p>` : ''}
              </div>
            `);
          }
        }
      });

      return geoJsonLayer;
    } catch (error) {
      console.error(`Error creating real GeoJSON layer for ${layerDef.name}:`, error);
      return null;
    }
  };

  const createSampleLayer = (layerDef: MapLayer) => {
    if (!window.L) return null;

    try {
      // Create a feature group to hold the layer data
      const featureGroup = window.L.featureGroup();

      // Try to load real GeoJSON data first, fallback to generated data
      const shouldUseRealData = layerDef.name.toLowerCase().includes('world') ||
                               layerDef.name.toLowerCase().includes('countries') ||
                               layerDef.name.toLowerCase().includes('us') ||
                               layerDef.name.toLowerCase().includes('states');

      if (shouldUseRealData) {
        return createRealGeoJSONLayer(layerDef);
      }

      // Add sample features based on layer metadata
      const featureCount = Math.min(layerDef.metadata?.featureCount || 10, 50); // Reduced for better performance

      // Use realistic geographic bounds for different layer types
      // Note: These are actual geographic coordinates in WGS84
      const getRealisticBounds = () => {
        if (layerDef.name.toLowerCase().includes('world') || layerDef.name.toLowerCase().includes('countries')) {
          // World-wide distribution - real country locations
          return [
            { lat: 35.6762, lng: 139.6503 }, // Tokyo, Japan
            { lat: 40.7128, lng: -74.0060 }, // New York, USA
            { lat: 51.5074, lng: -0.1278 },  // London, UK
            { lat: 48.8566, lng: 2.3522 },   // Paris, France
            { lat: -33.8688, lng: 151.2093 }, // Sydney, Australia
            { lat: 55.7558, lng: 37.6176 },  // Moscow, Russia
            { lat: 39.9042, lng: 116.4074 }, // Beijing, China
            { lat: -23.5505, lng: -46.6333 }, // São Paulo, Brazil
            { lat: 30.0444, lng: 31.2357 },  // Cairo, Egypt
            { lat: 28.6139, lng: 77.2090 },  // New Delhi, India
          ];
        } else if (layerDef.name.toLowerCase().includes('us') || layerDef.name.toLowerCase().includes('states')) {
          // US states - real state centers
          return [
            { lat: 32.3617, lng: -86.2792 }, // Alabama
            { lat: 58.3019, lng: -134.4197 }, // Alaska
            { lat: 34.0489, lng: -111.0937 }, // Arizona
            { lat: 34.7465, lng: -92.2896 }, // Arkansas
            { lat: 36.7783, lng: -119.4179 }, // California
            { lat: 39.5501, lng: -105.7821 }, // Colorado
            { lat: 41.6032, lng: -73.0877 }, // Connecticut
            { lat: 38.9108, lng: -75.5277 }, // Delaware
            { lat: 27.7663, lng: -82.6404 }, // Florida
            { lat: 32.1656, lng: -82.9001 }, // Georgia
          ];
        } else if (layerDef.name.toLowerCase().includes('europe')) {
          // European cities
          return [
            { lat: 52.5200, lng: 13.4050 },  // Berlin, Germany
            { lat: 41.9028, lng: 12.4964 },  // Rome, Italy
            { lat: 40.4168, lng: -3.7038 },  // Madrid, Spain
            { lat: 52.3676, lng: 4.9041 },   // Amsterdam, Netherlands
            { lat: 59.3293, lng: 18.0686 },  // Stockholm, Sweden
            { lat: 50.0755, lng: 14.4378 },  // Prague, Czech Republic
            { lat: 47.4979, lng: 19.0402 },  // Budapest, Hungary
            { lat: 50.8503, lng: 4.3517 },   // Brussels, Belgium
          ];
        } else {
          // Default to some major world cities
          return [
            { lat: 40.7128, lng: -74.0060 }, // New York
            { lat: 51.5074, lng: -0.1278 },  // London
            { lat: 35.6762, lng: 139.6503 }, // Tokyo
            { lat: 48.8566, lng: 2.3522 },   // Paris
          ];
        }
      };

      const bounds = getRealisticBounds();

      for (let i = 0; i < featureCount; i++) {
        // Use real geographic coordinates from the bounds array
        const locationIndex = i % bounds.length;
        const baseLocation = bounds[locationIndex];

        // Add small random offset to create variation around real locations
        const offsetRange = 0.5; // 0.5 degrees offset for variation
        let lat = baseLocation.lat + (Math.random() - 0.5) * offsetRange;
        let lng = baseLocation.lng + (Math.random() - 0.5) * offsetRange;

        // Validate and normalize coordinates for Leaflet (WGS84)
        lat = Math.max(-85, Math.min(85, lat)); // Leaflet max bounds
        lng = ((lng + 180) % 360) - 180; // Normalize to -180 to 180

        // Ensure coordinates are valid numbers
        if (isNaN(lat) || isNaN(lng)) {
          console.warn(`Invalid coordinates generated for feature ${i}: lat=${lat}, lng=${lng}`);
          continue;
        }

        let feature: any = null;

        try {
          if (layerDef.metadata?.geometryType === 'Point') {
            feature = window.L.circleMarker([lat, lng], {
              radius: 5,
              fillColor: theme.colors.primary,
              color: theme.colors.primary,
              weight: 1,
              opacity: 1,
              fillOpacity: 0.6
            });
          } else if (layerDef.metadata?.geometryType === 'Polygon') {
            // Create more realistic polygon sizes based on layer type
            let size = 0.5; // Default size in degrees
            if (layerDef.name.toLowerCase().includes('countries')) {
              size = Math.random() * 5 + 2; // Countries: 2-7 degrees
            } else if (layerDef.name.toLowerCase().includes('states')) {
              size = Math.random() * 3 + 1; // States: 1-4 degrees
            } else if (layerDef.name.toLowerCase().includes('cities')) {
              size = Math.random() * 0.1 + 0.05; // Cities: 0.05-0.15 degrees
            }

            // Create a more natural polygon shape
            const points = [];
            const numPoints = 6; // Hexagon-like shape
            for (let p = 0; p < numPoints; p++) {
              const angle = (p / numPoints) * 2 * Math.PI;
              const radius = size * (0.8 + Math.random() * 0.4); // Vary radius for natural look
              const pointLat = lat + radius * Math.cos(angle);
              const pointLng = lng + radius * Math.sin(angle);
              points.push([pointLat, pointLng]);
            }

            feature = window.L.polygon(points, {
              fillColor: theme.colors.primary,
              color: theme.colors.primary,
              weight: 1,
              opacity: 0.8,
              fillOpacity: 0.3
            });
          } else if (layerDef.metadata?.geometryType === 'LineString') {
            // Create more realistic line features
            const linePoints = [];
            const numPoints = Math.floor(Math.random() * 5) + 3; // 3-7 points
            let currentLat = lat;
            let currentLng = lng;

            for (let p = 0; p < numPoints; p++) {
              linePoints.push([currentLat, currentLng]);
              // Move to next point with some randomness
              currentLat += (Math.random() - 0.5) * 0.1;
              currentLng += (Math.random() - 0.5) * 0.1;
            }

            feature = window.L.polyline(linePoints, {
              color: theme.colors.primary,
              weight: 2,
              opacity: 0.8
            });
          } else {
            // Default to point if geometry type is unknown
            feature = window.L.circleMarker([lat, lng], {
              radius: 4,
              fillColor: theme.colors.primary,
              color: theme.colors.primary,
              weight: 1,
              opacity: 1,
              fillOpacity: 0.6
            });
          }

          if (feature) {
            feature.bindPopup(`
              <div style="font-family: Inter, sans-serif;">
                <h4 style="margin: 0 0 8px 0; color: ${theme.colors.text};">${layerDef.name}</h4>
                <p style="margin: 4px 0; color: ${theme.colors.muted}; font-size: 12px;">Source: ${layerDef.source}</p>
                <p style="margin: 4px 0; color: ${theme.colors.muted}; font-size: 12px;">Type: ${layerDef.metadata?.geometryType || 'Unknown'}</p>
                <p style="margin: 4px 0; color: ${theme.colors.muted}; font-size: 12px;">Feature ${i + 1} of ${featureCount}</p>
              </div>
            `);
            featureGroup.addLayer(feature);
          }
        } catch (featureError) {
          console.warn(`Error creating feature ${i} for layer ${layerDef.name}:`, featureError);
        }
      }

      return featureGroup;
    } catch (error) {
      console.error(`Error creating sample layer for ${layerDef.name}:`, error);
      return null;
    }
  };

  const createFeaturePopup = (feature: GeoFeature) => {
    return `
      <div style="min-width: 200px;">
        <h3 style="margin: 0 0 8px 0; color: ${theme.colors.text};">
          ${feature.properties.name || 'Unnamed Feature'}
        </h3>
        <p style="margin: 0 0 4px 0; color: ${theme.colors.muted}; font-size: 12px;">
          Type: ${feature.type.toUpperCase()}
        </p>
        ${feature.properties.description ? `
          <p style="margin: 4px 0; color: ${theme.colors.text}; font-size: 13px;">
            ${feature.properties.description}
          </p>
        ` : ''}
        <p style="margin: 4px 0 0 0; color: ${theme.colors.muted}; font-size: 11px;">
          Created: ${new Date(feature.createdAt).toLocaleDateString()}
        </p>
      </div>
    `;
  };

  const centerOnFeature = (feature: GeoFeature) => {
    if (!mapRef.current) return;

    let bounds: any = null;

    if (feature.type === 'point' && Array.isArray(feature.geometry.coordinates)) {
      const [lng, lat] = feature.geometry.coordinates as [number, number];
      mapRef.current.setView([lat, lng], Math.max(15, zoomLevel));
    } else if (feature.type === 'line' && Array.isArray(feature.geometry.coordinates)) {
      const coordinates = (feature.geometry.coordinates as [number, number][]).map(
        ([lng, lat]) => [lat, lng]
      );
      bounds = window.L.latLngBounds(coordinates);
    } else if (feature.type === 'polygon' && Array.isArray(feature.geometry.coordinates)) {
      const coordinates = (feature.geometry.coordinates[0] as [number, number][]).map(
        ([lng, lat]) => [lat, lng]
      );
      bounds = window.L.latLngBounds(coordinates);
    }

    if (bounds) {
      mapRef.current.fitBounds(bounds, { padding: [20, 20] });
    }
  };

  const getCurrentLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          if (mapRef.current) {
            mapRef.current.setView([latitude, longitude], 16);
          }
        },
        (error) => {
          console.error('Error getting location:', error);
          Alert.alert('Location Error', 'Unable to get current location');
        }
      );
    } else {
      Alert.alert('Not Supported', 'Geolocation is not supported by this browser');
    }
  };

  const toggleLayer = (layerName: string) => {
    setLayerVisibility(prev => ({
      ...prev,
      [layerName]: !prev[layerName]
    }));
  };

  const renderMapControls = () => (
    <View style={[styles.mapControls, { backgroundColor: theme.colors.card + 'F0' }]}>
      <TouchableOpacity
        style={styles.controlButton}
        onPress={getCurrentLocation}
        accessibilityLabel="Center on current location"
      >
        <Target size={20} color={theme.colors.text} />
      </TouchableOpacity>

      <View style={[styles.controlSeparator, { backgroundColor: theme.colors.border }]} />

      <TouchableOpacity
        style={styles.controlButton}
        onPress={() => setShowSpatialToolkit(true)}
        accessibilityLabel="Open spatial analysis tools"
      >
        <Calculator size={20} color={theme.colors.text} />
      </TouchableOpacity>

      {showSpatialTools && (
        <TouchableOpacity
          style={styles.controlButton}
          onPress={() => setShowSpatialToolkit(true)}
          accessibilityLabel="Advanced spatial tools"
        >
          <BarChart3 size={20} color={theme.colors.text} />
        </TouchableOpacity>
      )}
    </View>
  );

  const renderSelectedFeatureInfo = () => {
    if (!selectedFeature) return null;

    return (
      <View style={[styles.featureInfo, { backgroundColor: theme.colors.card }]}>
        <Text style={[styles.featureTitle, { color: theme.colors.text }]}>
          {selectedFeature.properties.name || 'Unnamed Feature'}
        </Text>
        <Text style={[styles.featureType, { color: theme.colors.muted }]}>
          {selectedFeature.type.toUpperCase()}
        </Text>
        <View style={styles.featureActions}>
          <TouchableOpacity
            style={[styles.featureButton, { backgroundColor: theme.colors.primary }]}
            onPress={() => centerOnFeature(selectedFeature)}
          >
            <Navigation size={16} color="white" />
            <Text style={styles.featureButtonText}>Center</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.featureButton, { backgroundColor: theme.colors.secondary }]}
            onPress={() => setShowSpatialToolkit(true)}
          >
            <Calculator size={16} color="white" />
            <Text style={styles.featureButtonText}>Analyze</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <div
        id="leaflet-map-container"
        style={{
          width: '100%',
          height: '100%',
          backgroundColor: theme.colors.background,
        }}
      />
      
      {isMapLoaded && (
        <>
          {renderMapControls()}
          {renderSelectedFeatureInfo()}
          
          {showSpatialToolkit && (
            <SpatialToolkit
              visible={showSpatialToolkit}
              onClose={() => setShowSpatialToolkit(false)}
              features={geoFeatures}
              selectedFeature={selectedFeature}
              onAnalysisResult={(result) => {
                console.log('Analysis result:', result);
              }}
            />
          )}
        </>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },
  mapControls: {
    position: 'absolute',
    top: 16,
    right: 16,
    flexDirection: 'row',
    borderRadius: 8,
    padding: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    zIndex: 1000,
  },
  controlButton: {
    padding: 8,
    borderRadius: 6,
    marginHorizontal: 2,
  },
  controlSeparator: {
    width: 1,
    marginHorizontal: 4,
  },
  featureInfo: {
    position: 'absolute',
    bottom: 16,
    left: 16,
    right: 16,
    padding: 12,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    zIndex: 1000,
  },
  featureTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 4,
  },
  featureType: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    marginBottom: 8,
    textTransform: 'uppercase',
  },
  featureActions: {
    flexDirection: 'row',
    gap: 8,
  },
  featureButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    gap: 4,
  },
  featureButtonText: {
    color: 'white',
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
  },
});
