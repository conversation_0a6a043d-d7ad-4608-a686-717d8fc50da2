{"name": "fieldsync-pro", "main": "expo-router/entry", "version": "1.0.0", "private": true, "scripts": {"dev": "cross-env EXPO_NO_TELEMETRY=1 expo start", "start": "cross-env EXPO_NO_TELEMETRY=1 expo start", "reset-cache": "cross-env EXPO_NO_TELEMETRY=1 expo start --clear", "build:web": "expo export --platform web", "lint": "expo lint", "clean": "rm -rf node_modules && npm install", "doctor": "expo doctor", "fix-metro": "npx expo install --fix", "validate:advanced-map": "node tests/validate-advanced-map.js", "test:advanced-map": "npm run validate:advanced-map", "docs:advanced-map": "echo 'Opening Advanced Map documentation...' && start ADVANCED_MAP_README.md", "start:advanced-map": "npm run validate:advanced-map && npm run dev", "android": "expo run:android", "ios": "expo run:ios"}, "dependencies": {"@expo-google-fonts/inter": "^0.2.3", "@expo/vector-icons": "^14.1.0", "@lucide/lab": "^0.1.2", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/netinfo": "^11.4.1", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@supabase/supabase-js": "^2.49.8", "@turf/turf": "^7.2.0", "@types/uuid": "^10.0.0", "expo": "^53.0.0", "expo-av": "~14.0.7", "expo-barcode-scanner": "~13.0.1", "expo-blur": "~14.1.3", "expo-camera": "~16.1.5", "expo-constants": "~17.1.3", "expo-document-picker": "~13.1.5", "expo-file-system": "~16.0.6", "expo-font": "~13.2.2", "expo-haptics": "~14.1.3", "expo-image-picker": "~15.0.7", "expo-linear-gradient": "~14.1.3", "expo-linking": "~7.1.3", "expo-location": "~15.1.1", "expo-router": "~5.0.2", "expo-secure-store": "~12.8.1", "expo-splash-screen": "~0.30.6", "expo-sqlite": "~13.4.0", "expo-status-bar": "~2.2.2", "expo-symbols": "~0.4.3", "expo-system-ui": "~5.0.5", "expo-web-browser": "~14.1.5", "formik": "^2.4.5", "lucide-react-native": "^0.475.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.1", "react-native-chart-kit": "^6.12.0", "react-native-gesture-handler": "~2.24.0", "react-native-maps": "1.11.3", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.3.0", "react-native-screens": "~4.10.0", "react-native-svg": "15.11.2", "react-native-url-polyfill": "^2.0.0", "react-native-uuid": "^2.0.3", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "recharts": "^2.8.0", "uuid": "^11.1.0", "yup": "^1.3.3", "zustand": "^4.5.1", "@react-native-picker/picker": "2.11.0", "expo-updates": "~0.28.14"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "@types/react-native": "^0.72.8", "babel-plugin-module-resolver": "^5.0.2", "cross-env": "^7.0.3", "typescript": "~5.8.3"}}