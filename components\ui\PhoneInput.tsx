import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Modal,
  ScrollView,
  Alert,
} from 'react-native';
import { useTheme } from '@/hooks/useTheme';
import { Phone, ChevronDown, Check } from 'lucide-react-native';

interface PhoneInputProps {
  value?: string;
  onChange: (value: string) => void;
  placeholder?: string;
  required?: boolean;
  label?: string;
  countryCode?: string;
  format?: string;
  allowInternational?: boolean;
}

interface Country {
  name: string;
  code: string;
  dialCode: string;
  flag: string;
}

const COUNTRIES: Country[] = [
  { name: 'United States', code: 'US', dialCode: '+1', flag: '🇺🇸' },
  { name: 'Canada', code: 'CA', dialCode: '+1', flag: '🇨🇦' },
  { name: 'United Kingdom', code: 'GB', dialCode: '+44', flag: '🇬🇧' },
  { name: 'Australia', code: 'AU', dialCode: '+61', flag: '🇦🇺' },
  { name: 'Germany', code: 'DE', dialCode: '+49', flag: '🇩🇪' },
  { name: 'France', code: 'FR', dialCode: '+33', flag: '🇫🇷' },
  { name: 'Japan', code: 'JP', dialCode: '+81', flag: '🇯🇵' },
  { name: 'China', code: 'CN', dialCode: '+86', flag: '🇨🇳' },
  { name: 'India', code: 'IN', dialCode: '+91', flag: '🇮🇳' },
  { name: 'Brazil', code: 'BR', dialCode: '+55', flag: '🇧🇷' },
  { name: 'Mexico', code: 'MX', dialCode: '+52', flag: '🇲🇽' },
  { name: 'South Africa', code: 'ZA', dialCode: '+27', flag: '🇿🇦' },
];

export default function PhoneInput({
  value = '',
  onChange,
  placeholder = 'Enter phone number',
  required,
  label,
  countryCode = '+1',
  format = '(###) ###-####',
  allowInternational = false,
}: PhoneInputProps) {
  const { theme } = useTheme();
  const [selectedCountry, setSelectedCountry] = useState<Country>(
    COUNTRIES.find(c => c.dialCode === countryCode) || COUNTRIES[0]
  );
  const [showCountryPicker, setShowCountryPicker] = useState(false);
  const [phoneNumber, setPhoneNumber] = useState('');

  useEffect(() => {
    // Parse existing value if it exists
    if (value) {
      const country = COUNTRIES.find(c => value.startsWith(c.dialCode));
      if (country) {
        setSelectedCountry(country);
        setPhoneNumber(value.substring(country.dialCode.length));
      } else {
        setPhoneNumber(value);
      }
    }
  }, [value]);

  const formatPhoneNumber = (input: string, pattern: string) => {
    const numbers = input.replace(/\D/g, '');
    let formatted = '';
    let numberIndex = 0;

    for (let i = 0; i < pattern.length && numberIndex < numbers.length; i++) {
      if (pattern[i] === '#') {
        formatted += numbers[numberIndex];
        numberIndex++;
      } else {
        formatted += pattern[i];
      }
    }

    return formatted;
  };

  const handlePhoneNumberChange = (text: string) => {
    const formattedNumber = formatPhoneNumber(text, format);
    setPhoneNumber(formattedNumber);
    
    const fullNumber = selectedCountry.dialCode + text.replace(/\D/g, '');
    onChange(fullNumber);
  };

  const handleCountrySelect = (country: Country) => {
    setSelectedCountry(country);
    setShowCountryPicker(false);
    
    const cleanNumber = phoneNumber.replace(/\D/g, '');
    const fullNumber = country.dialCode + cleanNumber;
    onChange(fullNumber);
  };

  const validatePhoneNumber = () => {
    const cleanNumber = phoneNumber.replace(/\D/g, '');
    
    if (required && cleanNumber.length === 0) {
      Alert.alert('Validation Error', 'Phone number is required');
      return false;
    }

    if (cleanNumber.length > 0 && cleanNumber.length < 7) {
      Alert.alert('Validation Error', 'Please enter a valid phone number');
      return false;
    }

    return true;
  };

  const renderCountryPicker = () => (
    <Modal
      visible={showCountryPicker}
      transparent
      animationType="slide"
      onRequestClose={() => setShowCountryPicker(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.modalContent, { backgroundColor: theme.colors.card }]}>
          <View style={styles.modalHeader}>
            <Text style={[styles.modalTitle, { color: theme.colors.text }]}>
              Select Country
            </Text>
            <TouchableOpacity onPress={() => setShowCountryPicker(false)}>
              <Text style={[styles.modalClose, { color: theme.colors.primary }]}>Done</Text>
            </TouchableOpacity>
          </View>
          
          <ScrollView style={styles.countryList}>
            {COUNTRIES.map((country) => (
              <TouchableOpacity
                key={country.code}
                style={[
                  styles.countryItem,
                  {
                    backgroundColor: selectedCountry.code === country.code 
                      ? theme.colors.primary + '10' 
                      : 'transparent'
                  }
                ]}
                onPress={() => handleCountrySelect(country)}
              >
                <Text style={styles.countryFlag}>{country.flag}</Text>
                <View style={styles.countryInfo}>
                  <Text style={[styles.countryName, { color: theme.colors.text }]}>
                    {country.name}
                  </Text>
                  <Text style={[styles.countryCode, { color: theme.colors.muted }]}>
                    {country.dialCode}
                  </Text>
                </View>
                {selectedCountry.code === country.code && (
                  <Check size={20} color={theme.colors.primary} />
                )}
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      </View>
    </Modal>
  );

  return (
    <View style={styles.container}>
      {label && (
        <Text style={[styles.label, { color: theme.colors.text }]}>
          {label}
          {required && <Text style={{ color: theme.colors.error }}> *</Text>}
        </Text>
      )}
      
      <View style={[
        styles.inputContainer,
        {
          backgroundColor: theme.colors.background,
          borderColor: theme.colors.border,
        },
      ]}>
        <Phone size={20} color={theme.colors.muted} />
        
        {allowInternational && (
          <TouchableOpacity
            style={[styles.countrySelector, { borderColor: theme.colors.border }]}
            onPress={() => setShowCountryPicker(true)}
          >
            <Text style={styles.countryFlag}>{selectedCountry.flag}</Text>
            <Text style={[styles.dialCode, { color: theme.colors.text }]}>
              {selectedCountry.dialCode}
            </Text>
            <ChevronDown size={16} color={theme.colors.muted} />
          </TouchableOpacity>
        )}
        
        <TextInput
          style={[styles.phoneInput, { color: theme.colors.text }]}
          value={phoneNumber}
          onChangeText={handlePhoneNumberChange}
          placeholder={placeholder}
          placeholderTextColor={theme.colors.placeholder}
          keyboardType="phone-pad"
          onBlur={validatePhoneNumber}
        />
      </View>
      
      {renderCountryPicker()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
  },
  label: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    marginBottom: 6,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 14,
    borderRadius: 8,
    borderWidth: 1,
    gap: 10,
  },
  countrySelector: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingRight: 8,
    borderRightWidth: 1,
    gap: 4,
  },
  countryFlag: {
    fontSize: 20,
  },
  dialCode: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
  },
  phoneInput: {
    flex: 1,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    maxHeight: '70%',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  modalTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
  },
  modalClose: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
  },
  countryList: {
    maxHeight: 400,
  },
  countryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    gap: 12,
  },
  countryInfo: {
    flex: 1,
  },
  countryName: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    marginBottom: 2,
  },
  countryCode: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
  },
});
