import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Platform,
  Modal,
  ScrollView,
  Image,
  Dimensions,
} from 'react-native';
import { useTheme } from '@/hooks/useTheme';
import { 
  Camera as CameraIcon, 
  Image as ImageIcon, 
  Plus, 
  X, 
  Edit3, 
  Trash2,
  RotateCcw,
  Download,
  Grid3X3,
  Maximize2,
} from 'lucide-react-native';
import { CameraView, CameraType, FlashMode, Camera } from 'expo-camera';
import * as ImagePicker from 'expo-image-picker';
import * as FileSystem from 'expo-file-system';

interface PhotoData {
  id: string;
  uri: string;
  timestamp: number;
  location?: {
    latitude: number;
    longitude: number;
    accuracy?: number;
  };
  metadata?: {
    width: number;
    height: number;
    size: number;
  };
}

interface MultiPhotoPickerProps {
  value?: PhotoData[];
  onChange: (photos: PhotoData[]) => void;
  placeholder?: string;
  required?: boolean;
  error?: string;
  disabled?: boolean;
  maxPhotos?: number;
  allowCamera?: boolean;
  allowGallery?: boolean;
  showThumbnails?: boolean;
  captureLocation?: boolean;
}

export default function MultiPhotosPicker({
  value = [],
  onChange,
  placeholder = 'Add photos',
  required = false,
  error,
  disabled = false,
  maxPhotos = 10,
  allowCamera = true,
  allowGallery = true,
  showThumbnails = true,
  captureLocation = true,
}: MultiPhotoPickerProps) {
  const { theme } = useTheme();
  const [hasCameraPermission, setHasCameraPermission] = useState<boolean | null>(null);
  const [hasMediaLibraryPermission, setHasMediaLibraryPermission] = useState<boolean | null>(null);
  const [showCamera, setShowCamera] = useState(false);
  const [showFullImage, setShowFullImage] = useState<PhotoData | null>(null);
  const [selectedPhotos, setSelectedPhotos] = useState<string[]>([]);
  const [isSelectionMode, setIsSelectionMode] = useState(false);
  const [cameraType, setCameraType] = useState<CameraType>('back');
  const [flashMode, setFlashMode] = useState<FlashMode>('auto');

  const cameraRef = useRef<CameraView>(null);
  const { width } = Dimensions.get('window');
  const thumbnailSize = (width - 48) / 3; // 3 columns with margins

  useEffect(() => {
    requestPermissions();
  }, []);

  const requestPermissions = async () => {
    const cameraResult = await Camera.requestCameraPermissionsAsync();
    const mediaResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
    
    setHasCameraPermission(cameraResult.status === 'granted');
    setHasMediaLibraryPermission(mediaResult.status === 'granted');
  };

  const getCurrentLocation = async () => {
    if (!captureLocation) return null;

    try {
      const { Location } = await import('expo-location');
      const { status } = await Location.requestForegroundPermissionsAsync();
      
      if (status === 'granted') {
        const location = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.High,
        });
        
        return {
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
          accuracy: location.coords.accuracy,
        };
      }
    } catch (error) {
      console.log('Failed to get location:', error);
    }
    
    return null;
  };

  const generatePhotoId = () => {
    return `photo_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  };

  const getImageMetadata = async (uri: string) => {
    try {
      const fileInfo = await FileSystem.getInfoAsync(uri);
      
      // For now, we'll return basic metadata
      // In a real implementation, you might use expo-image-manipulator to get dimensions
      return {
        width: 0,
        height: 0,
        size: fileInfo.size || 0,
      };
    } catch (error) {
      return {
        width: 0,
        height: 0,
        size: 0,
      };
    }
  };

  const handleCameraCapture = async () => {
    if (!allowCamera) return;

    if (!hasCameraPermission) {
      const result = await Camera.requestCameraPermissionsAsync();
      setHasCameraPermission(result.status === 'granted');
      
      if (result.status !== 'granted') {
        Alert.alert('Permission Required', 'Camera permission is needed to take photos');
        return;
      }
    }

    if (value.length >= maxPhotos) {
      Alert.alert('Limit Reached', `Maximum ${maxPhotos} photos allowed`);
      return;
    }
    
    setShowCamera(true);
  };

  const takePicture = async () => {
    if (!cameraRef.current) return;

    try {
      const photo = await cameraRef.current.takePictureAsync({
        quality: 0.8,
        base64: false,
        exif: true,
      });

      const location = await getCurrentLocation();
      const metadata = await getImageMetadata(photo.uri);

      const newPhoto: PhotoData = {
        id: generatePhotoId(),
        uri: photo.uri,
        timestamp: Date.now(),
        location,
        metadata,
      };

      const updatedPhotos = [...value, newPhoto];
      onChange(updatedPhotos);
      setShowCamera(false);

      Alert.alert('Success', 'Photo captured successfully!');
    } catch (error) {
      Alert.alert('Error', 'Failed to capture photo');
    }
  };

  const handleGalleryPick = async () => {
    if (!allowGallery) return;

    if (!hasMediaLibraryPermission) {
      const result = await ImagePicker.requestMediaLibraryPermissionsAsync();
      setHasMediaLibraryPermission(result.status === 'granted');
      
      if (result.status !== 'granted') {
        Alert.alert('Permission Required', 'Gallery permission is needed to select photos');
        return;
      }
    }

    const remainingSlots = maxPhotos - value.length;
    if (remainingSlots <= 0) {
      Alert.alert('Limit Reached', `Maximum ${maxPhotos} photos allowed`);
      return;
    }

    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsMultipleSelection: remainingSlots > 1,
        selectionLimit: remainingSlots,
        quality: 0.8,
        exif: true,
      });

      if (!result.canceled && result.assets) {
        const location = await getCurrentLocation();
        
        const newPhotos: PhotoData[] = await Promise.all(
          result.assets.map(async (asset) => {
            const metadata = await getImageMetadata(asset.uri);
            return {
              id: generatePhotoId(),
              uri: asset.uri,
              timestamp: Date.now(),
              location,
              metadata,
            };
          })
        );

        const updatedPhotos = [...value, ...newPhotos];
        onChange(updatedPhotos);

        Alert.alert('Success', `${newPhotos.length} photo(s) added successfully!`);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to select photos from gallery');
    }
  };

  const handleDeletePhoto = (photoId: string) => {
    Alert.alert(
      'Delete Photo',
      'Are you sure you want to delete this photo?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            const updatedPhotos = value.filter(photo => photo.id !== photoId);
            onChange(updatedPhotos);
          },
        },
      ]
    );
  };

  const handleDeleteSelected = () => {
    if (selectedPhotos.length === 0) return;

    Alert.alert(
      'Delete Photos',
      `Are you sure you want to delete ${selectedPhotos.length} photo(s)?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            const updatedPhotos = value.filter(photo => !selectedPhotos.includes(photo.id));
            onChange(updatedPhotos);
            setSelectedPhotos([]);
            setIsSelectionMode(false);
          },
        },
      ]
    );
  };

  const toggleSelection = (photoId: string) => {
    if (selectedPhotos.includes(photoId)) {
      setSelectedPhotos(prev => prev.filter(id => id !== photoId));
    } else {
      setSelectedPhotos(prev => [...prev, photoId]);
    }
  };

  const toggleSelectionMode = () => {
    setIsSelectionMode(!isSelectionMode);
    setSelectedPhotos([]);
  };

  const renderCameraModal = () => (
    <Modal
      visible={showCamera}
      animationType="slide"
      presentationStyle="fullScreen"
    >
      <View style={styles.cameraContainer}>
        <CameraView
          ref={cameraRef}
          style={styles.camera}
          facing={cameraType}
          flash={flashMode}
        >
          <View style={styles.cameraOverlay}>
            <View style={styles.cameraHeader}>
              <TouchableOpacity
                style={[styles.cameraButton, { backgroundColor: theme.colors.error }]}
                onPress={() => setShowCamera(false)}
              >
                <X size={24} color="white" />
              </TouchableOpacity>
              
              <Text style={styles.cameraTitle}>
                Take Photo ({value.length + 1}/{maxPhotos})
              </Text>
              
              <TouchableOpacity
                style={[styles.cameraButton, { backgroundColor: theme.colors.muted }]}
                onPress={() => setCameraType(current => current === 'back' ? 'front' : 'back')}
              >
                <RotateCcw size={24} color="white" />
              </TouchableOpacity>
            </View>

            <View style={styles.cameraFooter}>
              <TouchableOpacity
                style={[styles.captureButton, { backgroundColor: theme.colors.primary }]}
                onPress={takePicture}
              >
                <CameraIcon size={32} color="white" />
              </TouchableOpacity>
            </View>
          </View>
        </CameraView>
      </View>
    </Modal>
  );

  const renderFullImageModal = () => (
    <Modal
      visible={!!showFullImage}
      animationType="fade"
      presentationStyle="fullScreen"
      onRequestClose={() => setShowFullImage(null)}
    >
      {showFullImage && (
        <View style={styles.fullImageContainer}>
          <View style={styles.fullImageHeader}>
            <TouchableOpacity
              style={[styles.headerButton, { backgroundColor: theme.colors.error }]}
              onPress={() => setShowFullImage(null)}
            >
              <X size={24} color="white" />
            </TouchableOpacity>
            
            <Text style={styles.fullImageTitle}>
              Photo Details
            </Text>
            
            <TouchableOpacity
              style={[styles.headerButton, { backgroundColor: theme.colors.error }]}
              onPress={() => {
                handleDeletePhoto(showFullImage.id);
                setShowFullImage(null);
              }}
            >
              <Trash2 size={24} color="white" />
            </TouchableOpacity>
          </View>

          <ScrollView
            style={styles.fullImageScroll}
            maximumZoomScale={3}
            minimumZoomScale={1}
            showsVerticalScrollIndicator={false}
            showsHorizontalScrollIndicator={false}
          >
            <Image
              source={{ uri: showFullImage.uri }}
              style={styles.fullImage}
              resizeMode="contain"
            />
          </ScrollView>

          <View style={styles.fullImageInfo}>
            <Text style={[styles.infoText, { color: theme.colors.text }]}>
              Captured: {new Date(showFullImage.timestamp).toLocaleString()}
            </Text>
            {showFullImage.location && (
              <Text style={[styles.infoText, { color: theme.colors.muted }]}>
                Location: {showFullImage.location.latitude.toFixed(6)}, {showFullImage.location.longitude.toFixed(6)}
              </Text>
            )}
            {showFullImage.metadata && showFullImage.metadata.size > 0 && (
              <Text style={[styles.infoText, { color: theme.colors.muted }]}>
                Size: {(showFullImage.metadata.size / 1024 / 1024).toFixed(2)} MB
              </Text>
            )}
          </View>
        </View>
      )}
    </Modal>
  );

  const renderAddButtons = () => {
    if (value.length >= maxPhotos) return null;

    return (
      <View style={styles.addButtonsContainer}>
        {allowCamera && (
          <TouchableOpacity
            style={[styles.addButton, { backgroundColor: theme.colors.primary }]}
            onPress={handleCameraCapture}
            disabled={disabled}
          >
            <CameraIcon size={20} color="white" />
            <Text style={styles.addButtonText}>Camera</Text>
          </TouchableOpacity>
        )}

        {allowGallery && (
          <TouchableOpacity
            style={[styles.addButton, { backgroundColor: theme.colors.secondary }]}
            onPress={handleGalleryPick}
            disabled={disabled}
          >
            <ImageIcon size={20} color="white" />
            <Text style={styles.addButtonText}>Gallery</Text>
          </TouchableOpacity>
        )}
      </View>
    );
  };

  const renderPhotoGrid = () => {
    if (value.length === 0) return null;

    return (
      <View style={styles.photoGrid}>
        <View style={styles.photoHeader}>
          <Text style={[styles.photoCount, { color: theme.colors.text }]}>
            {value.length} of {maxPhotos} photos
          </Text>
          
          <View style={styles.photoActions}>
            <TouchableOpacity
              style={[
                styles.actionButton,
                { backgroundColor: isSelectionMode ? theme.colors.error : theme.colors.muted }
              ]}
              onPress={toggleSelectionMode}
            >
              <Grid3X3 size={16} color="white" />
            </TouchableOpacity>
            
            {isSelectionMode && selectedPhotos.length > 0 && (
              <TouchableOpacity
                style={[styles.actionButton, { backgroundColor: theme.colors.error }]}
                onPress={handleDeleteSelected}
              >
                <Trash2 size={16} color="white" />
              </TouchableOpacity>
            )}
          </View>
        </View>

        <View style={styles.photoList}>
          {value.map((photo) => (
            <TouchableOpacity
              key={photo.id}
              style={[
                styles.photoThumbnail,
                {
                  width: thumbnailSize,
                  height: thumbnailSize,
                  borderColor: selectedPhotos.includes(photo.id) ? theme.colors.primary : 'transparent',
                }
              ]}
              onPress={() => {
                if (isSelectionMode) {
                  toggleSelection(photo.id);
                } else {
                  setShowFullImage(photo);
                }
              }}
              onLongPress={() => {
                if (!isSelectionMode) {
                  setIsSelectionMode(true);
                  setSelectedPhotos([photo.id]);
                }
              }}
            >
              <Image
                source={{ uri: photo.uri }}
                style={styles.thumbnailImage}
                resizeMode="cover"
              />
              
              {isSelectionMode && (
                <View style={[
                  styles.selectionOverlay,
                  { backgroundColor: selectedPhotos.includes(photo.id) ? theme.colors.primary + '80' : 'transparent' }
                ]}>
                  {selectedPhotos.includes(photo.id) && (
                    <View style={[styles.checkmark, { backgroundColor: theme.colors.primary }]}>
                      <Text style={styles.checkmarkText}>✓</Text>
                    </View>
                  )}
                </View>
              )}

              {!isSelectionMode && (
                <TouchableOpacity
                  style={[styles.deleteButton, { backgroundColor: theme.colors.error }]}
                  onPress={() => handleDeletePhoto(photo.id)}
                >
                  <X size={12} color="white" />
                </TouchableOpacity>
              )}

              <TouchableOpacity
                style={[styles.expandButton, { backgroundColor: theme.colors.muted + 'CC' }]}
                onPress={() => setShowFullImage(photo)}
              >
                <Maximize2 size={12} color="white" />
              </TouchableOpacity>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    );
  };

  const renderEmptyState = () => {
    if (value.length > 0) return null;

    return (
      <View style={[styles.emptyContainer, { backgroundColor: theme.colors.card }]}>
        <ImageIcon size={48} color={theme.colors.muted} />
        <Text style={[styles.emptyTitle, { color: theme.colors.text }]}>
          {placeholder}
        </Text>
        <Text style={[styles.emptySubtitle, { color: theme.colors.muted }]}>
          Add up to {maxPhotos} photos using camera or gallery
        </Text>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {renderEmptyState()}
      {showThumbnails && renderPhotoGrid()}
      {renderAddButtons()}

      {error && (
        <Text style={[styles.errorText, { color: theme.colors.error }]}>
          {error}
        </Text>
      )}

      {required && value.length === 0 && (
        <Text style={[styles.requiredText, { color: theme.colors.muted }]}>
          * Required field
        </Text>
      )}

      {renderCameraModal()}
      {renderFullImageModal()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  emptyContainer: {
    borderRadius: 12,
    paddingVertical: 32,
    paddingHorizontal: 16,
    alignItems: 'center',
    gap: 8,
    marginBottom: 12,
  },
  emptyTitle: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
  },
  addButtonsContainer: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 8,
  },
  addButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  addButtonText: {
    color: 'white',
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
  },
  photoGrid: {
    marginBottom: 12,
  },
  photoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  photoCount: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
  },
  photoActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    padding: 8,
    borderRadius: 6,
  },
  photoList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  photoThumbnail: {
    borderRadius: 8,
    overflow: 'hidden',
    borderWidth: 2,
    position: 'relative',
  },
  thumbnailImage: {
    width: '100%',
    height: '100%',
  },
  selectionOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkmark: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkmarkText: {
    color: 'white',
    fontSize: 14,
    fontFamily: 'Inter-Bold',
  },
  deleteButton: {
    position: 'absolute',
    top: 4,
    right: 4,
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  expandButton: {
    position: 'absolute',
    bottom: 4,
    right: 4,
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    marginTop: 4,
  },
  requiredText: {
    fontSize: 11,
    fontFamily: 'Inter-Regular',
    marginTop: 4,
  },
  cameraContainer: {
    flex: 1,
  },
  camera: {
    flex: 1,
  },
  cameraOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.2)',
    justifyContent: 'space-between',
  },
  cameraHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
    paddingBottom: 16,
  },
  cameraButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cameraTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
  },
  cameraFooter: {
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingBottom: Platform.OS === 'ios' ? 40 : 20,
  },
  captureButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  fullImageContainer: {
    flex: 1,
    backgroundColor: 'black',
  },
  fullImageHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
    paddingBottom: 16,
    backgroundColor: 'rgba(0,0,0,0.8)',
  },
  headerButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  fullImageTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
  },
  fullImageScroll: {
    flex: 1,
  },
  fullImage: {
    width: '100%',
    height: '100%',
    minHeight: 400,
  },
  fullImageInfo: {
    backgroundColor: 'rgba(0,0,0,0.8)',
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 4,
  },
  infoText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
  },
});
