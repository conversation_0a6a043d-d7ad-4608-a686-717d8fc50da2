/**
 * Enhanced GIS Map Hook for FieldSyncPro
 * 
 * Provides comprehensive state management for advanced GIS mapping features
 * following React best practices and software engineering excellence.
 */

import { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { Alert } from 'react-native';
import * as SecureStore from 'expo-secure-store';
import { 
  Feature, 
  FeatureCollection, 
  EnhancedSpatialAnalysis, 
  createSpatialAnalysis,
  Coordinate,
  AnalysisOptions 
} from '@/lib/spatial/spatialAnalysis';
import { 
  EnhancedFileHandler, 
  createFileHandler, 
  FileImportOptions, 
  FileExportOptions,
  ImportResult,
  ExportResult 
} from '@/lib/spatial/fileHandler';

export interface MapRegion {
  latitude: number;
  longitude: number;
  latitudeDelta: number;
  longitudeDelta: number;
}

export interface GISFeature {
  id: string;
  type: 'point' | 'line' | 'polygon' | 'circle' | 'rectangle' | 'multipoint' | 'multiline' | 'multipolygon';
  coordinates: Coordinate[];
  properties: Record<string, any>;
  style?: {
    strokeColor?: string;
    strokeWidth?: number;
    fillColor?: string;
    fillOpacity?: number;
    iconType?: string;
    iconSize?: number;
    labelField?: string;
    labelSize?: number;
    labelColor?: string;
  };
  geometry?: {
    type: string;
    coordinates: any;
  };
  createdAt: string;
  updatedAt: string;
  metadata?: {
    source?: string;
    accuracy?: number;
    elevation?: number;
    timestamp?: string;
    tags?: string[];
  };
}

export interface MapLayer {
  id: string;
  name: string;
  type: 'vector' | 'raster' | 'tile' | 'wms' | 'wmts' | 'geojson' | 'kml' | 'shapefile';
  source: 'catalog' | 'upload' | 'external' | 'analysis';
  visible: boolean;
  opacity: number;
  minZoom?: number;
  maxZoom?: number;
  style?: LayerStyle;
  features?: GISFeature[];
  metadata?: LayerMetadata;
  data?: any;
  url?: string;
  createdAt: string;
  updatedAt: string;
}

export interface LayerStyle {
  strokeColor?: string;
  strokeWidth?: number;
  fillColor?: string;
  fillOpacity?: number;
  iconType?: string;
  iconSize?: number;
  labelField?: string;
  labelSize?: number;
  labelColor?: string;
  clustering?: boolean;
  heatmap?: boolean;
}

export interface LayerMetadata {
  description: string;
  source: string;
  lastUpdated: string;
  properties: string[];
  geometryType: 'Point' | 'LineString' | 'Polygon' | 'MultiPoint' | 'MultiLineString' | 'MultiPolygon' | 'Mixed';
  featureCount: number;
  extent: {
    minX: number;
    minY: number;
    maxX: number;
    maxY: number;
  };
  coordinateSystem?: string;
  accuracy?: number;
  dataQuality?: 'high' | 'medium' | 'low';
}

export interface MeasurementResult {
  id: string;
  type: 'distance' | 'area' | 'bearing' | 'elevation' | 'perimeter';
  value: number;
  unit: string;
  coordinates: Coordinate[];
  timestamp: string;
  properties?: Record<string, any>;
}

export interface AnalysisResult {
  id: string;
  type: string;
  name: string;
  status: 'running' | 'completed' | 'failed' | 'cancelled';
  progress: number;
  parameters: Record<string, any>;
  result?: {
    features: GISFeature[];
    metadata: any;
  };
  error?: string;
  startTime: string;
  endTime?: string;
  outputLayers?: string[];
  processingTime?: number;
}

export interface MapBookmark {
  id: string;
  name: string;
  description?: string;
  extent: MapRegion;
  visibleLayers: string[];
  createdAt: string;
  updatedAt: string;
  category?: string;
  tags?: string[];
  isShared?: boolean;
  visitCount?: number;
  lastVisited?: string;
}

export interface StorySlide {
  id: string;
  title: string;
  description: string;
  content?: string;
  extent?: MapRegion;
  visibleLayers: string[];
  duration?: number;
  transition?: 'fade' | 'slide' | 'zoom';
  media?: {
    type: 'image' | 'video' | 'audio';
    url: string;
    position?: 'overlay' | 'sidebar' | 'bottom';
  };
  annotations?: Array<{
    id: string;
    type: 'text' | 'arrow' | 'highlight';
    position: Coordinate;
    content: string;
    style?: any;
  }>;
}

export interface MapStory {
  id: string;
  title: string;
  description: string;
  author: string;
  slides: StorySlide[];
  settings: {
    showProgress: boolean;
    showControls: boolean;
    allowSkipping: boolean;
    loop: boolean;
    theme: 'light' | 'dark' | 'auto';
    layout: 'sidebar' | 'overlay' | 'bottom';
  };
  sharing: {
    isPublic: boolean;
    allowComments: boolean;
    allowEmbedding: boolean;
    shareUrl?: string;
  };
  createdAt: string;
  updatedAt: string;
  tags?: string[];
}

export interface Notification {
  id: string;
  type: 'success' | 'info' | 'warning' | 'error';
  title?: string;
  message: string;
  timestamp: string;
  autoHide?: boolean;
  duration?: number;
  action?: {
    label: string;
    onPress: () => void;
  };
}

export interface GISMapOptions {
  initialRegion?: MapRegion;
  maxFeatures?: number;
  enableCaching?: boolean;
  enableOffline?: boolean;
  enableRealtime?: boolean;
  storage?: {
    prefix?: string;
    version?: number;
  };
  analysis?: {
    maxFileSize?: number;
    defaultUnits?: string;
  };
  performance?: {
    renderingOptimization?: boolean;
    memoryManagement?: boolean;
    backgroundProcessing?: boolean;
  };
}

export interface GISMapState {
  // Core map state
  mapRegion: MapRegion;
  layers: MapLayer[];
  selectedFeatures: string[];
  
  // Measurements and analysis
  measurements: MeasurementResult[];
  analysisResults: AnalysisResult[];
  isAnalyzing: boolean;
  analysisProgress: number;
  
  // Drawing and editing
  activeDrawingTool: string | null;
  activeMeasurementTool: string | null;
  isDrawing: boolean;
  isMeasuring: boolean;
  
  // Story mode
  currentStory: MapStory | null;
  currentSlideIndex: number;
  isStoryMode: boolean;
  isPlaying: boolean;
  
  // Bookmarks
  bookmarks: MapBookmark[];
  
  // UI state
  notifications: Notification[];
  isLoading: boolean;
  error: string | null;
  
  // Performance metrics
  performance: {
    renderTime: number;
    featureCount: number;
    memoryUsage: number;
    lastUpdate: string;
  };
}

/**
 * UseGISMapReturn interface - defines the complete return type of the useGISMap hook
 */
export interface UseGISMapReturn extends GISMapState {
  // Core map functions
  setMapRegion: (region: MapRegion) => void;
  
  // Layer management
  addLayer: (layer: Omit<MapLayer, 'id' | 'createdAt' | 'updatedAt'>) => string;
  updateLayer: (layerId: string, updates: Partial<MapLayer>) => void;
  removeLayer: (layerId: string) => void;
  toggleLayerVisibility: (layerId: string) => void;
  
  // Feature management
  createFeature: (feature: Omit<GISFeature, 'id' | 'createdAt' | 'updatedAt'>, layerId?: string) => string;
  updateFeature: (featureId: string, updates: Partial<GISFeature>) => void;
  deleteFeature: (featureId: string) => void;
  selectFeature: (featureId: string) => void;
  deselectFeature: (featureId: string) => void;
  clearSelectedFeatures: () => void;
  
  // File operations
  importFile: (file: File, options?: FileImportOptions) => Promise<string | null>;
  importLayer: (file: File, options?: FileImportOptions) => Promise<string | null>;
  exportLayer: (layerId: string, options: FileExportOptions) => Promise<boolean>;
  
  // Spatial analysis
  runAnalysis: (type: string, parameters: Record<string, any>) => Promise<string | null>;
  
  // Measurements
  addMeasurement: (measurement: Omit<MeasurementResult, 'id' | 'timestamp'>) => string;
  clearMeasurements: () => void;
  
  // Drawing and tools
  setActiveDrawingTool: (tool: string | null) => void;
  setActiveMeasurementTool: (tool: string | null) => void;
  
  // Bookmarks
  addBookmark: (bookmark: Omit<MapBookmark, 'id' | 'createdAt' | 'updatedAt'>) => string;
  navigateToBookmark: (bookmarkId: string) => void;
  
  // Notifications
  showNotification: (type: Notification['type'], message: string, options?: Partial<Notification>) => string;
  hideNotification: (notificationId: string) => void;
  
  // Utility functions
  getLayerById: (id: string) => MapLayer | undefined;
  getVisibleLayers: () => MapLayer[];
  getFeatureCount: () => number;
  getFeatureById: (id: string) => GISFeature | undefined;
  getSelectedFeatures: () => GISFeature[];
  
  // Performance
  performance: GISMapState['performance'];
  
  // Options
  options: GISMapOptions;
}

const DEFAULT_REGION: MapRegion = {
  latitude: 37.78825,
  longitude: -122.4324,
  latitudeDelta: 0.0922,
  longitudeDelta: 0.0421,
};

const DEFAULT_OPTIONS: GISMapOptions = {
  maxFeatures: 25000,
  enableCaching: true,
  enableOffline: false,
  enableRealtime: false,
  storage: {
    prefix: 'gis_map',
    version: 1,
  },
  analysis: {
    maxFileSize: 50 * 1024 * 1024,
    defaultUnits: 'meters',
  },
  performance: {
    renderingOptimization: true,
    memoryManagement: true,
    backgroundProcessing: true,
  },
};

/**
 * Enhanced GIS Map Hook
 * 
 * Provides comprehensive state management for advanced GIS mapping functionality
 * with performance optimization, error handling, and data persistence.
 */
export function useGISMap(options: GISMapOptions = {}): UseGISMapReturn {
  const opts = useMemo(() => ({ ...DEFAULT_OPTIONS, ...options }), [options]);
  
  // Core state
  const [state, setState] = useState<GISMapState>({
    mapRegion: opts.initialRegion || DEFAULT_REGION,
    layers: [],
    selectedFeatures: [],
    measurements: [],
    analysisResults: [],
    isAnalyzing: false,
    analysisProgress: 0,
    activeDrawingTool: null,
    activeMeasurementTool: null,
    isDrawing: false,
    isMeasuring: false,
    currentStory: null,
    currentSlideIndex: 0,
    isStoryMode: false,
    isPlaying: false,
    bookmarks: [],
    notifications: [],
    isLoading: false,
    error: null,
    performance: {
      renderTime: 0,
      featureCount: 0,
      memoryUsage: 0,
      lastUpdate: new Date().toISOString(),
    },
  });

  // Service instances
  const spatialAnalysis = useRef<EnhancedSpatialAnalysis>();
  const fileHandler = useRef<EnhancedFileHandler>();
  const notificationTimeouts = useRef<Map<string, NodeJS.Timeout>>(new Map());

  // Initialize services
  useEffect(() => {
    spatialAnalysis.current = createSpatialAnalysis(opts.maxFeatures);
    fileHandler.current = createFileHandler(opts.analysis?.maxFileSize);
  }, [opts.maxFeatures, opts.analysis?.maxFileSize]);

  // Storage utility functions
  const getStorageKey = useCallback((key: string) => `${opts.storage?.prefix}_${key}`, [opts.storage?.prefix]);
  
  const storeData = useCallback(async (key: string, data: any) => {
    try {
      await SecureStore.setItemAsync(getStorageKey(key), JSON.stringify(data));
    } catch (error) {
      console.error(`Failed to store ${key}:`, error);
    }
  }, [getStorageKey]);

  const retrieveData = useCallback(async (key: string) => {
    try {
      const data = await SecureStore.getItemAsync(getStorageKey(key));
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error(`Failed to retrieve ${key}:`, error);
      return null;
    }
  }, [getStorageKey]);

  // Load persisted data on mount
  useEffect(() => {
    if (opts.enableCaching) {
      loadPersistedData();
    }
  }, [opts.enableCaching]);

  // Auto-save state changes
  useEffect(() => {
    if (opts.enableCaching && state.layers.length > 0) {
      persistData();
    }
  }, [state.layers, state.bookmarks, opts.enableCaching]);

  // Performance monitoring
  useEffect(() => {
    updatePerformanceMetrics();
  }, [state.layers]);

  /**
   * Load persisted data from storage
   */
  const loadPersistedData = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true }));

      const [layersData, bookmarksData, measurementsData] = await Promise.all([
        retrieveData('layers'),
        retrieveData('bookmarks'),
        retrieveData('measurements'),
      ]);

      setState(prev => ({
        ...prev,
        layers: layersData || [],
        bookmarks: bookmarksData || [],
        measurements: measurementsData || [],
        isLoading: false,
      }));

      showNotification('info', 'Map data loaded successfully');
    } catch (error) {
      console.error('Failed to load persisted data:', error);
      setState(prev => ({ 
        ...prev, 
        isLoading: false,
        error: 'Failed to load saved data',
      }));
    }
  }, [retrieveData]);

  /**
   * Persist data to storage
   */
  const persistData = useCallback(async () => {
    try {
      await Promise.all([
        storeData('layers', state.layers),
        storeData('bookmarks', state.bookmarks),
        storeData('measurements', state.measurements),
      ]);
    } catch (error) {
      console.error('Failed to persist data:', error);
    }
  }, [state.layers, state.bookmarks, state.measurements, storeData]);

  /**
   * Update performance metrics
   */
  const updatePerformanceMetrics = useCallback(() => {
    const featureCount = state.layers.reduce((count, layer) => 
      count + (layer.features?.length || 0), 0
    );

    setState(prev => ({
      ...prev,
      performance: {
        ...prev.performance,
        featureCount,
        lastUpdate: new Date().toISOString(),
      },
    }));
  }, [state.layers]);

  /**
   * Show notification with auto-hide
   */
  const showNotification = useCallback((
    type: Notification['type'], 
    message: string, 
    options: Partial<Notification> = {}
  ) => {
    const notification: Notification = {
      id: `notification_${Date.now()}`,
      type,
      message,
      timestamp: new Date().toISOString(),
      autoHide: options.autoHide !== false,
      duration: options.duration || 5000,
      ...options,
    };

    setState(prev => ({
      ...prev,
      notifications: [notification, ...prev.notifications.slice(0, 4)],
    }));

    // Auto-hide notification
    if (notification.autoHide) {
      const timeout = setTimeout(() => {
        hideNotification(notification.id);
      }, notification.duration);
      
      notificationTimeouts.current.set(notification.id, timeout);
    }

    return notification.id;
  }, []);

  /**
   * Hide notification
   */
  const hideNotification = useCallback((notificationId: string) => {
    setState(prev => ({
      ...prev,
      notifications: prev.notifications.filter(n => n.id !== notificationId),
    }));

    const timeout = notificationTimeouts.current.get(notificationId);
    if (timeout) {
      clearTimeout(timeout);
      notificationTimeouts.current.delete(notificationId);
    }
  }, []);

  /**
   * Set map region
   */
  const setMapRegion = useCallback((region: MapRegion) => {
    setState(prev => ({ ...prev, mapRegion: region }));
  }, []);

  /**
   * Add layer to map
   */
  const addLayer = useCallback((layer: Omit<MapLayer, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newLayer: MapLayer = {
      ...layer,
      id: `layer_${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    setState(prev => ({
      ...prev,
      layers: [...prev.layers, newLayer],
    }));

    showNotification('success', `Added layer: ${layer.name}`);
    return newLayer.id;
  }, [showNotification]);

  /**
   * Update layer
   */
  const updateLayer = useCallback((layerId: string, updates: Partial<MapLayer>) => {
    setState(prev => ({
      ...prev,
      layers: prev.layers.map(layer =>
        layer.id === layerId
          ? { ...layer, ...updates, updatedAt: new Date().toISOString() }
          : layer
      ),
    }));
  }, []);

  /**
   * Remove layer
   */
  const removeLayer = useCallback((layerId: string) => {
    const layer = state.layers.find(l => l.id === layerId);
    
    setState(prev => ({
      ...prev,
      layers: prev.layers.filter(l => l.id !== layerId),
      selectedFeatures: prev.selectedFeatures.filter(fId => 
        !layer?.features?.some(f => f.id === fId)
      ),
    }));

    if (layer) {
      showNotification('info', `Removed layer: ${layer.name}`);
    }
  }, [state.layers, showNotification]);

  /**
   * Toggle layer visibility
   */
  const toggleLayerVisibility = useCallback((layerId: string) => {
    const layer = state.layers.find(l => l.id === layerId);
    if (layer) {
      updateLayer(layerId, { visible: !layer.visible });
      showNotification('info', `Layer ${layer.visible ? 'hidden' : 'shown'}: ${layer.name}`);
    }
  }, [state.layers, updateLayer, showNotification]);

  /**
   * Create a new feature
   */
  const createFeature = useCallback((
    feature: Omit<GISFeature, 'id' | 'createdAt' | 'updatedAt'>,
    layerId?: string
  ): string => {
    const newFeature: GISFeature = {
      ...feature,
      id: `feature_${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    if (layerId) {
      // Add to existing layer
      setState(prev => ({
        ...prev,
        layers: prev.layers.map(layer =>
          layer.id === layerId
            ? {
                ...layer,
                features: [...(layer.features || []), newFeature],
                updatedAt: new Date().toISOString(),
              }
            : layer
        ),
      }));
    } else {
      // Create new layer for the feature
      const newLayer: MapLayer = {
        id: `layer_${Date.now()}`,
        name: `New Layer (${feature.type})`,
        type: 'geojson',
        source: 'external',
        visible: true,
        opacity: 1,
        features: [newFeature],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      setState(prev => ({
        ...prev,
        layers: [...prev.layers, newLayer],
      }));
    }

    showNotification('success', `Created ${feature.type} feature`);
    return newFeature.id;
  }, [showNotification]);

  /**
   * Update feature
   */
  const updateFeature = useCallback((featureId: string, updates: Partial<GISFeature>) => {
    setState(prev => ({
      ...prev,
      layers: prev.layers.map(layer => ({
        ...layer,
        features: layer.features?.map(feature =>
          feature.id === featureId
            ? { ...feature, ...updates, updatedAt: new Date().toISOString() }
            : feature
        ),
      })),
    }));
  }, []);

  /**
   * Delete feature
   */
  const deleteFeature = useCallback((featureId: string) => {
    setState(prev => ({
      ...prev,
      layers: prev.layers.map(layer => ({
        ...layer,
        features: layer.features?.filter(feature => feature.id !== featureId),
      })),
      selectedFeatures: prev.selectedFeatures.filter(id => id !== featureId),
    }));

    showNotification('info', 'Feature deleted');
  }, [showNotification]);

  /**
   * Select feature
   */
  const selectFeature = useCallback((featureId: string) => {
    setState(prev => ({
      ...prev,
      selectedFeatures: [...prev.selectedFeatures, featureId],
    }));
  }, []);

  /**
   * Deselect feature
   */
  const deselectFeature = useCallback((featureId: string) => {
    setState(prev => ({
      ...prev,
      selectedFeatures: prev.selectedFeatures.filter(id => id !== featureId),
    }));
  }, []);

  /**
   * Clear selected features
   */
  const clearSelectedFeatures = useCallback(() => {
    setState(prev => ({ ...prev, selectedFeatures: [] }));
  }, []);

  /**
   * Import file as layer
   */
  const importFile = useCallback(async (
    file: File, 
    options: FileImportOptions = {}
  ): Promise<string | null> => {
    if (!fileHandler.current) return null;

    try {
      setState(prev => ({ ...prev, isLoading: true }));
      showNotification('info', 'Importing file...');

      const result: ImportResult = await fileHandler.current.importFile(file, options);

      if (result.success && result.features.length > 0) {
        const layerId = addLayer({
          name: file.name.replace(/\.[^/.]+$/, ''),
          type: result.metadata.format as any,
          source: 'upload',
          visible: true,
          opacity: 1,
          features: result.features,
          metadata: {
            description: `Imported from ${file.name}`,
            source: 'File upload',
            lastUpdated: new Date().toISOString(),
            properties: result.metadata.properties,
            geometryType: result.metadata.geometryTypes[0] as any || 'Mixed',
            featureCount: result.features.length,
            extent: result.metadata.bounds || { minX: 0, minY: 0, maxX: 0, maxY: 0 },
          },
        });

        showNotification('success', `Imported ${result.features.length} features from ${file.name}`);
        return layerId;
      } else {
        showNotification('error', result.metadata.errors[0] || 'Import failed');
        return null;
      }
    } catch (error) {
      showNotification('error', `Import failed: ${error}`);
      return null;
    } finally {
      setState(prev => ({ ...prev, isLoading: false }));
    }
  }, [addLayer, showNotification]);

  /**
   * Import layer (alias for importFile for compatibility)
   */
  const importLayer = useCallback(async (
    file: File, 
    options: FileImportOptions = {}
  ): Promise<string | null> => {
    return importFile(file, options);
  }, [importFile]);

  /**
   * Export layer as file
   */
  const exportLayer = useCallback(async (
    layerId: string, 
    options: FileExportOptions
  ): Promise<boolean> => {
    if (!fileHandler.current) return false;

    try {
      const layer = state.layers.find(l => l.id === layerId);
      if (!layer || !layer.features) {
        showNotification('error', 'Layer not found or has no features');
        return false;
      }

      setState(prev => ({ ...prev, isLoading: true }));
      showNotification('info', 'Exporting layer...');

      const result: ExportResult = await fileHandler.current.exportFeatures(layer.features, options);

      if (result.success && result.data) {
        // Download file
        const blob = new Blob([result.data], { type: result.mimeType });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = result.fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        showNotification('success', `Exported layer: ${layer.name}`);
        return true;
      } else {
        showNotification('error', result.error || 'Export failed');
        return false;
      }
    } catch (error) {
      showNotification('error', `Export failed: ${error}`);
      return false;
    } finally {
      setState(prev => ({ ...prev, isLoading: false }));
    }
  }, [state.layers, showNotification]);

  /**
   * Run spatial analysis
   */
  const runAnalysis = useCallback(async (
    type: string,
    parameters: Record<string, any>
  ): Promise<string | null> => {
    if (!spatialAnalysis.current) return null;

    try {
      const analysisId = `analysis_${Date.now()}`;
      
      // Create analysis result entry
      const analysis: AnalysisResult = {
        id: analysisId,
        type,
        name: `${type} analysis`,
        status: 'running',
        progress: 0,
        parameters,
        startTime: new Date().toISOString(),
      };

      setState(prev => ({
        ...prev,
        analysisResults: [analysis, ...prev.analysisResults],
        isAnalyzing: true,
        analysisProgress: 0,
      }));

      showNotification('info', `Starting ${type} analysis...`);

      // Simulate analysis processing
      const progressInterval = setInterval(() => {
        setState(prev => ({
          ...prev,
          analysisProgress: Math.min(prev.analysisProgress + 10, 90),
        }));
      }, 200);

      // Simplified analysis result for demo
      setTimeout(() => {
        clearInterval(progressInterval);
        
        const completedAnalysis: AnalysisResult = {
          ...analysis,
          status: 'completed',
          progress: 100,
          result: {
            features: [],
            metadata: { message: `${type} analysis completed` },
          },
          endTime: new Date().toISOString(),
          processingTime: 2000,
        };

        setState(prev => ({
          ...prev,
          analysisResults: prev.analysisResults.map(a =>
            a.id === analysisId ? completedAnalysis : a
          ),
          isAnalyzing: false,
          analysisProgress: 0,
        }));

        showNotification('success', `${type} analysis completed successfully`);
      }, 2000);

      return analysisId;

    } catch (error) {
      setState(prev => ({
        ...prev,
        analysisResults: prev.analysisResults.map(a =>
          a.id === analysisId 
            ? { ...a, status: 'failed' as const, error: error instanceof Error ? error.message : 'Unknown error' }
            : a
        ),
        isAnalyzing: false,
        analysisProgress: 0,
      }));

      showNotification('error', `Analysis failed: ${error}`);
      return null;
    }
  }, [showNotification]);

  /**
   * Add measurement
   */
  const addMeasurement = useCallback((measurement: Omit<MeasurementResult, 'id' | 'timestamp'>) => {
    const newMeasurement: MeasurementResult = {
      ...measurement,
      id: `measurement_${Date.now()}`,
      timestamp: new Date().toISOString(),
    };

    setState(prev => ({
      ...prev,
      measurements: [newMeasurement, ...prev.measurements.slice(0, 99)], // Keep last 100
    }));

    showNotification('info', `${measurement.type}: ${measurement.value} ${measurement.unit}`);
    return newMeasurement.id;
  }, [showNotification]);

  /**
   * Clear measurements
   */
  const clearMeasurements = useCallback(() => {
    setState(prev => ({ ...prev, measurements: [] }));
    showNotification('info', 'Measurements cleared');
  }, [showNotification]);

  /**
   * Set drawing tool
   */
  const setActiveDrawingTool = useCallback((tool: string | null) => {
    setState(prev => ({
      ...prev,
      activeDrawingTool: tool,
      activeMeasurementTool: tool ? null : prev.activeMeasurementTool,
    }));
  }, []);

  /**
   * Set measurement tool
   */
  const setActiveMeasurementTool = useCallback((tool: string | null) => {
    setState(prev => ({
      ...prev,
      activeMeasurementTool: tool,
      activeDrawingTool: tool ? null : prev.activeDrawingTool,
    }));
  }, []);

  /**
   * Add bookmark
   */
  const addBookmark = useCallback((bookmark: Omit<MapBookmark, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newBookmark: MapBookmark = {
      ...bookmark,
      id: `bookmark_${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      visitCount: 0,
    };

    setState(prev => ({
      ...prev,
      bookmarks: [newBookmark, ...prev.bookmarks],
    }));

    showNotification('success', `Bookmark created: ${bookmark.name}`);
    return newBookmark.id;
  }, [showNotification]);

  /**
   * Navigate to bookmark
   */
  const navigateToBookmark = useCallback((bookmarkId: string) => {
    const bookmark = state.bookmarks.find(b => b.id === bookmarkId);
    if (bookmark) {
      setMapRegion(bookmark.extent);
      
      // Update visit count
      setState(prev => ({
        ...prev,
        bookmarks: prev.bookmarks.map(b =>
          b.id === bookmarkId
            ? { 
                ...b, 
                visitCount: (b.visitCount || 0) + 1,
                lastVisited: new Date().toISOString(),
              }
            : b
        ),
      }));

      showNotification('info', `Navigated to: ${bookmark.name}`);
    }
  }, [state.bookmarks, setMapRegion, showNotification]);

  /**
   * Get feature by ID
   */
  const getFeatureById = useCallback((id: string): GISFeature | undefined => {
    for (const layer of state.layers) {
      const feature = layer.features?.find(f => f.id === id);
      if (feature) return feature;
    }
    return undefined;
  }, [state.layers]);

  /**
   * Get selected features
   */
  const getSelectedFeatures = useCallback((): GISFeature[] => {
    const features: GISFeature[] = [];
    for (const featureId of state.selectedFeatures) {
      const feature = getFeatureById(featureId);
      if (feature) features.push(feature);
    }
    return features;
  }, [state.selectedFeatures, getFeatureById]);

  /**
   * Cleanup function
   */
  useEffect(() => {
    return () => {
      // Clear all notification timeouts
      notificationTimeouts.current.forEach(timeout => clearTimeout(timeout));
      notificationTimeouts.current.clear();
    };
  }, []);

  // Return hook interface
  return {
    // State
    ...state,
    
    // Core map functions
    setMapRegion,
    
    // Layer management
    addLayer,
    updateLayer,
    removeLayer,
    toggleLayerVisibility,
    
    // Feature management
    createFeature,
    updateFeature,
    deleteFeature,
    selectFeature,
    deselectFeature,
    clearSelectedFeatures,
    
    // File operations
    importFile,
    importLayer,
    exportLayer,
    
    // Spatial analysis
    runAnalysis,
    
    // Measurements
    addMeasurement,
    clearMeasurements,
    
    // Drawing and tools
    setActiveDrawingTool,
    setActiveMeasurementTool,
    
    // Bookmarks
    addBookmark,
    navigateToBookmark,
    
    // Notifications
    showNotification,
    hideNotification,
    
    // Utility functions
    getLayerById: useCallback((id: string) => state.layers.find(l => l.id === id), [state.layers]),
    getVisibleLayers: useCallback(() => state.layers.filter(l => l.visible), [state.layers]),
    getFeatureCount: useCallback(() => state.layers.reduce((count, layer) => count + (layer.features?.length || 0), 0), [state.layers]),
    getFeatureById,
    getSelectedFeatures,
    
    // Performance
    performance: state.performance,
    
    // Options
    options: opts,
  };
}

export type GISMapHook = ReturnType<typeof useGISMap>;
