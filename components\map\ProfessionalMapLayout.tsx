import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Modal,
  TextInput,
  Dimensions,
  Platform,
  Alert,
  SafeAreaView,
  Animated,
  KeyboardAvoidingView,
} from 'react-native';
import { useTheme } from '@/hooks/useTheme';
import * as Location from 'expo-location';
import {
  MapPin,
  Layers,
  Ruler,
  Search,
  Settings,
  Map as MapIcon,
  Satellite,
  Plus,
  Minus,
  Target,
  Route,
  Pentagon,
  Activity,
  Thermometer,
  Shield,
  Edit3,
  Eye,
  EyeOff,
  Filter,
  BarChart3,
  Zap,
  X,
  Menu,
  CheckCircle,
  XCircle,
  Save,
  Trash2,
  Download,
  Upload,
  Grid,
  Compass,
  Navigation,
  Info,
  Crosshair,
  Move,
  Square,
  Circle,
  Hexagon,
} from 'lucide-react-native';

// Platform-specific map component import
const PlatformMap = Platform.OS === 'web' 
  ? require('./Map.web').default 
  : require('./Map.native').default;

interface Region {
  latitude: number;
  longitude: number;
  latitudeDelta: number;
  longitudeDelta: number;
}

interface DrawnFeature {
  id: string;
  type: 'point' | 'line' | 'polygon' | 'circle' | 'rectangle';
  coordinates: any;
  properties: {
    name: string;
    description?: string;
    color: string;
    created: number;
    measurements?: {
      area?: number;
      perimeter?: number;
      length?: number;
      radius?: number;
    };
  };
}

interface ProfessionalMapLayoutProps {
  initialRegion?: Region;
  onLocationSelect?: (location: { latitude: number; longitude: number }) => void;
  geoFeatures?: any[];
  showAnalysisTools?: boolean;
  enableDrawing?: boolean;
  enableMeasurement?: boolean;
  enableGeofencing?: boolean;
  enableRouting?: boolean;
  enableHeatmap?: boolean;
  enableClustering?: boolean;
  offlineMode?: boolean;
  onFeatureCreated?: (feature: DrawnFeature) => void;
  onFeatureDeleted?: (featureId: string) => void;
  onExportFeatures?: () => void;
  onClearAllFeatures?: () => void;
  featureStats?: {
    total: number;
    points: number;
    lines: number;
    polygons: number;
  };
  maxFeatures?: number;
}

type DrawingMode = 'none' | 'point' | 'line' | 'polygon' | 'circle' | 'rectangle' | 'freehand';
type MapLayer = 'standard' | 'satellite' | 'terrain' | 'hybrid';
type ActivePanel = 'none' | 'layers' | 'drawing' | 'analysis' | 'features' | 'settings';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

export default function ProfessionalMapLayout({
  initialRegion,
  onLocationSelect,
  geoFeatures = [],
  showAnalysisTools = true,
  enableDrawing = true,
  enableMeasurement = true,
  enableGeofencing = true,
  enableRouting = true,
  enableHeatmap = true,
  enableClustering = true,
  offlineMode = false,
  onFeatureCreated,
  onFeatureDeleted,
  onExportFeatures,
  onClearAllFeatures,
  featureStats,
  maxFeatures = 100,
}: ProfessionalMapLayoutProps) {
  const { theme } = useTheme();
  
  // State management
  const defaultRegion = {
    latitude: 37.78825,
    longitude: -122.4324,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  };
  const [region] = useState<Region>(initialRegion && typeof initialRegion.latitude === 'number' && typeof initialRegion.longitude === 'number' ? initialRegion : defaultRegion);
  
  const [mapType, setMapType] = useState<MapLayer>('standard');
  const [drawingMode, setDrawingMode] = useState<DrawingMode>('none');
  const [measurementMode, setMeasurementMode] = useState(false);
  const [showHeatmap, setShowHeatmap] = useState(false);
  const [showClustering, setShowClustering] = useState(true);
  const [userLocation, setUserLocation] = useState<Location.LocationObject | null>(null);
  const [drawnFeatures, setDrawnFeatures] = useState<DrawnFeature[]>([]);
  const [isDrawing, setIsDrawing] = useState(false);
  const [currentDrawingPoints, setCurrentDrawingPoints] = useState<any[]>([]);
  
  // UI state
  const [activePanel, setActivePanel] = useState<ActivePanel>('none');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFeature, setSelectedFeature] = useState<string | null>(null);
  const [showToolbar, setShowToolbar] = useState(false);
  
  // Animations
  const toolbarAnim = useRef(new Animated.Value(-200)).current;
  const panelAnim = useRef(new Animated.Value(SCREEN_HEIGHT)).current;

  useEffect(() => {
    requestLocationPermission();
  }, []);

  useEffect(() => {
    // Animate toolbar
    Animated.timing(toolbarAnim, {
      toValue: showToolbar ? 0 : -200,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, [showToolbar]);

  useEffect(() => {
    // Animate panel
    Animated.timing(panelAnim, {
      toValue: activePanel !== 'none' ? 0 : SCREEN_HEIGHT,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, [activePanel]);

  const requestLocationPermission = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status === 'granted') {
        const location = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.High,
        });
        setUserLocation(location);
      }
    } catch (error) {
      console.error('Error getting location:', error);
    }
  };

  const togglePanel = (panel: ActivePanel) => {
    if (activePanel === panel) {
      setActivePanel('none');
    } else {
      setActivePanel(panel);
    }
  };

  // Drawing functions
  const startDrawing = (mode: DrawingMode) => {
    setDrawingMode(mode);
    setIsDrawing(true);
    setCurrentDrawingPoints([]);
    setActivePanel('none');
  };

  const handleMapPress = (location: { latitude: number; longitude: number }) => {
    if (drawingMode !== 'none' && isDrawing) {
      setCurrentDrawingPoints(prev => [...prev, location]);
      
      if (drawingMode === 'point') {
        // Auto-complete point drawing
        const feature: DrawnFeature = {
          id: `point_${Date.now()}`,
          type: 'point',
          coordinates: location,
          properties: {
            name: `Point ${drawnFeatures.length + 1}`,
            color: theme.colors.primary,
            created: Date.now(),
          },
        };
        setDrawnFeatures(prev => [...prev, feature]);
        if (onFeatureCreated) onFeatureCreated(feature);
        setIsDrawing(false);
        setDrawingMode('none');
        Alert.alert('Success', 'Point created successfully!');
      } else if (drawingMode === 'line' && currentDrawingPoints.length >= 1) {
        Alert.alert(
          'Drawing Line',
          `${currentDrawingPoints.length + 1} points added. Tap to add more or finish.`,
          [
            { text: 'Add More', style: 'cancel' },
            {
              text: 'Finish',
              onPress: () => finishDrawing([...currentDrawingPoints, location]),
            },
          ]
        );
      } else if (drawingMode === 'polygon' && currentDrawingPoints.length >= 2) {
        Alert.alert(
          'Drawing Polygon',
          `${currentDrawingPoints.length + 1} points added. Tap to add more or finish.`,
          [
            { text: 'Add More', style: 'cancel' },
            {
              text: 'Finish',
              onPress: () => finishDrawing([...currentDrawingPoints, location]),
            },
          ]
        );
      }
    } else if (onLocationSelect) {
      onLocationSelect(location);
    }
  };

  const finishDrawing = (points?: any[]) => {
    const finalPoints = points || currentDrawingPoints;
    
    if (finalPoints.length === 0) return;

    let feature: DrawnFeature | null = null;

    switch (drawingMode) {
      case 'line':
        if (finalPoints.length >= 2) {
          feature = {
            id: `line_${Date.now()}`,
            type: 'line',
            coordinates: finalPoints,
            properties: {
              name: `Line ${drawnFeatures.length + 1}`,
              color: theme.colors.primary,
              created: Date.now(),
            },
          };
        }
        break;
        
      case 'polygon':
        if (finalPoints.length >= 3) {
          feature = {
            id: `polygon_${Date.now()}`,
            type: 'polygon',
            coordinates: [...finalPoints, finalPoints[0]], // Close polygon
            properties: {
              name: `Polygon ${drawnFeatures.length + 1}`,
              color: theme.colors.primary,
              created: Date.now(),
            },
          };
        }
        break;
    }

    if (feature) {
      setDrawnFeatures(prev => [...prev, feature!]);
      if (onFeatureCreated) onFeatureCreated(feature);
      Alert.alert('Success', `${drawingMode} created successfully!`);
    }

    setCurrentDrawingPoints([]);
    setIsDrawing(false);
    setDrawingMode('none');
  };

  const cancelDrawing = () => {
    setCurrentDrawingPoints([]);
    setIsDrawing(false);
    setDrawingMode('none');
  };

  // Fixed floating menu button
  const renderFloatingMenuButton = () => (
    <TouchableOpacity
      style={[styles.floatingMenuButton, { backgroundColor: theme.colors.primary }]}
      onPress={() => setShowToolbar(!showToolbar)}
    >
      <Menu size={24} color="white" />
    </TouchableOpacity>
  );

  // Fixed bottom toolbar
  const renderBottomToolbar = () => (
    <Animated.View
      style={[
        styles.bottomToolbar,
        {
          backgroundColor: theme.colors.card,
          transform: [{ translateY: toolbarAnim }],
          borderTopColor: theme.colors.border,
        },
      ]}
    >
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        <View style={styles.toolbarContent}>
          <TouchableOpacity
            style={[styles.toolButton, activePanel === 'layers' && styles.toolButtonActive]}
            onPress={() => togglePanel('layers')}
          >
            <Layers size={20} color={activePanel === 'layers' ? theme.colors.primary : theme.colors.text} />
            <Text style={[styles.toolButtonText, { color: theme.colors.text }]}>Layers</Text>
          </TouchableOpacity>

          {enableDrawing && (
            <TouchableOpacity
              style={[styles.toolButton, drawingMode !== 'none' && styles.toolButtonActive]}
              onPress={() => togglePanel('drawing')}
            >
              <Edit3 size={20} color={drawingMode !== 'none' ? theme.colors.primary : theme.colors.text} />
              <Text style={[styles.toolButtonText, { color: theme.colors.text }]}>Draw</Text>
            </TouchableOpacity>
          )}

          {enableMeasurement && (
            <TouchableOpacity
              style={[styles.toolButton, measurementMode && styles.toolButtonActive]}
              onPress={() => {
                setMeasurementMode(!measurementMode);
                setShowToolbar(false);
              }}
            >
              <Ruler size={20} color={measurementMode ? theme.colors.primary : theme.colors.text} />
              <Text style={[styles.toolButtonText, { color: theme.colors.text }]}>Measure</Text>
            </TouchableOpacity>
          )}

          {showAnalysisTools && (
            <TouchableOpacity
              style={[styles.toolButton, activePanel === 'analysis' && styles.toolButtonActive]}
              onPress={() => togglePanel('analysis')}
            >
              <BarChart3 size={20} color={activePanel === 'analysis' ? theme.colors.primary : theme.colors.text} />
              <Text style={[styles.toolButtonText, { color: theme.colors.text }]}>Analyze</Text>
            </TouchableOpacity>
          )}

          <TouchableOpacity
            style={[styles.toolButton, activePanel === 'features' && styles.toolButtonActive]}
            onPress={() => togglePanel('features')}
          >
            <Filter size={20} color={activePanel === 'features' ? theme.colors.primary : theme.colors.text} />
            <Text style={[styles.toolButtonText, { color: theme.colors.text }]}>Features</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </Animated.View>
  );

  // Fixed side panel
  const renderSidePanel = () => {
    if (activePanel === 'none') return null;

    return (
      <Animated.View
        style={[
          styles.sidePanel,
          {
            backgroundColor: theme.colors.card,
            transform: [{ translateY: panelAnim }],
          },
        ]}
      >
        <View style={styles.panelHeader}>
          <Text style={[styles.panelTitle, { color: theme.colors.text }]}>
            {activePanel === 'layers' && 'Map Layers'}
            {activePanel === 'drawing' && 'Drawing Tools'}
            {activePanel === 'analysis' && 'Spatial Analysis'}
            {activePanel === 'features' && 'Features'}
          </Text>
          <TouchableOpacity onPress={() => setActivePanel('none')}>
            <X size={24} color={theme.colors.text} />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.panelContent}>
          {activePanel === 'layers' && renderLayersContent()}
          {activePanel === 'drawing' && renderDrawingContent()}
          {activePanel === 'analysis' && renderAnalysisContent()}
          {activePanel === 'features' && renderFeaturesContent()}
        </ScrollView>
      </Animated.View>
    );
  };

  const renderLayersContent = () => (
    <View>
      {[
        { id: 'standard', icon: MapIcon, label: 'Standard' },
        { id: 'satellite', icon: Satellite, label: 'Satellite' },
        { id: 'terrain', icon: MapIcon, label: 'Terrain' },
        { id: 'hybrid', icon: Layers, label: 'Hybrid' },
      ].map((layer) => (
        <TouchableOpacity
          key={layer.id}
          style={[
            styles.optionCard,
            mapType === layer.id && styles.optionCardActive,
            { backgroundColor: mapType === layer.id ? theme.colors.primary + '10' : theme.colors.background }
          ]}
          onPress={() => {
            setMapType(layer.id as MapLayer);
            setActivePanel('none');
          }}
        >
          <layer.icon size={24} color={mapType === layer.id ? theme.colors.primary : theme.colors.text} />
          <Text style={[styles.optionText, { color: theme.colors.text }]}>{layer.label}</Text>
          {mapType === layer.id && <CheckCircle size={20} color={theme.colors.primary} />}
        </TouchableOpacity>
      ))}

      <View style={[styles.divider, { backgroundColor: theme.colors.border }]} />
      
      <TouchableOpacity
        style={[
          styles.optionCard,
          showHeatmap && styles.optionCardActive,
          { backgroundColor: showHeatmap ? theme.colors.warning + '10' : theme.colors.background }
        ]}
        onPress={() => setShowHeatmap(!showHeatmap)}
      >
        <Thermometer size={24} color={showHeatmap ? theme.colors.warning : theme.colors.text} />
        <Text style={[styles.optionText, { color: theme.colors.text }]}>Heat Map</Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={[
          styles.optionCard,
          showClustering && styles.optionCardActive,
          { backgroundColor: showClustering ? theme.colors.info + '10' : theme.colors.background }
        ]}
        onPress={() => setShowClustering(!showClustering)}
      >
        <Hexagon size={24} color={showClustering ? theme.colors.info : theme.colors.text} />
        <Text style={[styles.optionText, { color: theme.colors.text }]}>Clustering</Text>
      </TouchableOpacity>
    </View>
  );

  const renderDrawingContent = () => (
    <View>
      <View style={styles.drawingGrid}>
        {[
          { id: 'point', icon: MapPin, label: 'Point' },
          { id: 'line', icon: Route, label: 'Line' },
          { id: 'polygon', icon: Pentagon, label: 'Polygon' },
        ].map((tool) => (
          <TouchableOpacity
            key={tool.id}
            style={[
              styles.drawingTool,
              drawingMode === tool.id && styles.drawingToolActive,
              { 
                backgroundColor: drawingMode === tool.id ? theme.colors.primary : theme.colors.background,
                borderColor: drawingMode === tool.id ? theme.colors.primary : theme.colors.border,
              }
            ]}
            onPress={() => startDrawing(tool.id as DrawingMode)}
          >
            <tool.icon size={24} color={drawingMode === tool.id ? 'white' : theme.colors.text} />
            <Text style={[styles.drawingToolText, { color: drawingMode === tool.id ? 'white' : theme.colors.text }]}>
              {tool.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {drawingMode !== 'none' && (
        <View style={[styles.drawingStatus, { backgroundColor: theme.colors.info + '20' }]}>
          <Text style={[styles.drawingStatusText, { color: theme.colors.info }]}>
            {isDrawing ? `Drawing ${drawingMode}...` : `Tap map to start drawing ${drawingMode}`}
          </Text>
          <TouchableOpacity
            style={[styles.cancelButton, { backgroundColor: theme.colors.error }]}
            onPress={cancelDrawing}
          >
            <Text style={styles.cancelButtonText}>Cancel</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );

  const renderAnalysisContent = () => (
    <View>
      {enableGeofencing && (
        <TouchableOpacity
          style={[styles.analysisCard, { backgroundColor: theme.colors.background }]}
          onPress={() => {
            Alert.alert('Geofencing', 'Create and manage geofences');
            setActivePanel('none');
          }}
        >
          <Shield size={24} color={theme.colors.warning} />
          <View style={styles.analysisInfo}>
            <Text style={[styles.analysisTitle, { color: theme.colors.text }]}>Geofencing</Text>
            <Text style={[styles.analysisDescription, { color: theme.colors.muted }]}>
              Create virtual boundaries
            </Text>
          </View>
        </TouchableOpacity>
      )}

      {enableRouting && (
        <TouchableOpacity
          style={[styles.analysisCard, { backgroundColor: theme.colors.background }]}
          onPress={() => {
            Alert.alert('Route Analysis', 'Analyze routes');
            setActivePanel('none');
          }}
        >
          <Navigation size={24} color={theme.colors.info} />
          <View style={styles.analysisInfo}>
            <Text style={[styles.analysisTitle, { color: theme.colors.text }]}>Route Analysis</Text>
            <Text style={[styles.analysisDescription, { color: theme.colors.muted }]}>
              Calculate optimal paths
            </Text>
          </View>
        </TouchableOpacity>
      )}
    </View>
  );

  const renderFeaturesContent = () => (
    <View>
      {drawnFeatures.length === 0 ? (
        <View style={styles.emptyState}>
          <MapPin size={32} color={theme.colors.muted} />
          <Text style={[styles.emptyText, { color: theme.colors.muted }]}>
            No features yet. Start drawing!
          </Text>
        </View>
      ) : (
        drawnFeatures.map((feature) => (
          <View
            key={feature.id}
            style={[styles.featureItem, { backgroundColor: theme.colors.background }]}
          >
            <View style={[styles.featureIcon, { backgroundColor: feature.properties.color + '20' }]}>
              {feature.type === 'point' && <MapPin size={20} color={feature.properties.color} />}
              {feature.type === 'line' && <Route size={20} color={feature.properties.color} />}
              {feature.type === 'polygon' && <Pentagon size={20} color={feature.properties.color} />}
            </View>
            <View style={styles.featureInfo}>
              <Text style={[styles.featureName, { color: theme.colors.text }]}>
                {feature.properties.name}
              </Text>
              <Text style={[styles.featureType, { color: theme.colors.muted }]}>
                {feature.type}
              </Text>
            </View>
            <TouchableOpacity
              onPress={() => {
                if (onFeatureDeleted) {
                  onFeatureDeleted(feature.id);
                }
                setDrawnFeatures(prev => prev.filter(f => f.id !== feature.id));
              }}
            >
              <Trash2 size={18} color={theme.colors.error} />
            </TouchableOpacity>
          </View>
        ))
      )}
    </View>
  );

  // Fixed zoom controls
  const renderZoomControls = () => (
    <View style={[styles.zoomControls, { backgroundColor: theme.colors.card }]}>
      <TouchableOpacity
        style={styles.zoomButton}
        onPress={() => Alert.alert('Zoom', 'Zoom in')}
      >
        <Plus size={20} color={theme.colors.text} />
      </TouchableOpacity>
      <View style={[styles.zoomDivider, { backgroundColor: theme.colors.border }]} />
      <TouchableOpacity
        style={styles.zoomButton}
        onPress={() => Alert.alert('Zoom', 'Zoom out')}
      >
        <Minus size={20} color={theme.colors.text} />
      </TouchableOpacity>
    </View>
  );

  // Fixed location button
  const renderLocationButton = () => (
    <TouchableOpacity
      style={[styles.locationButton, { backgroundColor: theme.colors.card }]}
      onPress={() => {
        if (userLocation) {
          Alert.alert('Location', `Lat: ${userLocation.coords.latitude.toFixed(6)}, Lon: ${userLocation.coords.longitude.toFixed(6)}`);
        } else {
          requestLocationPermission();
        }
      }}
    >
      <Target size={22} color={theme.colors.primary} />
    </TouchableOpacity>
  );

  // Fixed status badges
  const renderStatusBadges = () => (
    <View style={styles.statusContainer}>
      {offlineMode && (
        <View style={[styles.statusBadge, { backgroundColor: theme.colors.info + '20' }]}>
          <Text style={[styles.statusText, { color: theme.colors.info }]}>Offline</Text>
        </View>
      )}
      {drawingMode !== 'none' && (
        <View style={[styles.statusBadge, { backgroundColor: theme.colors.primary + '20' }]}>
          <Text style={[styles.statusText, { color: theme.colors.primary }]}>Drawing: {drawingMode}</Text>
        </View>
      )}
      {measurementMode && (
        <View style={[styles.statusBadge, { backgroundColor: theme.colors.secondary + '20' }]}>
          <Text style={[styles.statusText, { color: theme.colors.secondary }]}>Measuring</Text>
        </View>
      )}
    </View>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}> 
      {/* Map Container with margin to avoid overlap */}
      <View style={[styles.mapContainer, { marginBottom: 80, marginTop: 60 }]}> 
        <PlatformMap
          initialRegion={region}
          mapType={mapType}
          drawingMode={drawingMode}
          measurementMode={measurementMode}
          showHeatmap={showHeatmap}
          showClustering={showClustering}
          userLocation={userLocation}
          geoFeatures={[...geoFeatures, ...drawnFeatures]}
          onLocationSelect={handleMapPress}
        />
      </View>

      {/* Fixed UI Elements with zIndex and spacing */}
      <View style={{ position: 'absolute', top: 16, left: 16, zIndex: 20 }}>{renderFloatingMenuButton()}</View>
      <View style={{ position: 'absolute', top: 16, right: 16, zIndex: 20 }}>{renderStatusBadges()}</View>
      <View style={{ position: 'absolute', bottom: 100, right: 16, zIndex: 20 }}>{renderZoomControls()}</View>
      <View style={{ position: 'absolute', bottom: 160, right: 16, zIndex: 20 }}>{renderLocationButton()}</View>
      
      {/* Sliding UI Elements with zIndex */}
      <View style={{ zIndex: 30 }}>{renderBottomToolbar()}</View>
      <View style={{ zIndex: 40 }}>{renderSidePanel()}</View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  mapContainer: {
    flex: 1,
  },
  
  // Floating Menu Button
  floatingMenuButton: {
    position: 'absolute',
    bottom: 100,
    left: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  
  // Bottom Toolbar
  bottomToolbar: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    borderTopWidth: 1,
    paddingBottom: Platform.OS === 'ios' ? 20 : 10,
  },
  toolbarContent: {
    flexDirection: 'row',
    paddingHorizontal: 10,
    paddingVertical: 10,
  },
  toolButton: {
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingVertical: 8,
    marginHorizontal: 5,
    borderRadius: 8,
  },
  toolButtonActive: {
    backgroundColor: 'rgba(0, 122, 255, 0.1)',
  },
  toolButtonText: {
    fontSize: 12,
    marginTop: 4,
    fontFamily: 'Inter-Regular',
  },
  
  // Side Panel
  sidePanel: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    maxHeight: '60%',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  panelHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  panelTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
  },
  panelContent: {
    padding: 20,
  },
  
  // Common Panel Elements
  optionCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 10,
  },
  optionCardActive: {
    borderWidth: 1,
    borderColor: 'rgba(0, 122, 255, 0.3)',
  },
  optionText: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
  },
  divider: {
    height: 1,
    marginVertical: 15,
  },
  
  // Drawing Tools
  drawingGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -5,
  },
  drawingTool: {
    width: (SCREEN_WIDTH - 60) / 3,
    height: 100,
    margin: 5,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 12,
    borderWidth: 2,
  },
  drawingToolActive: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  drawingToolText: {
    marginTop: 8,
    fontSize: 14,
    fontFamily: 'Inter-Medium',
  },
  drawingStatus: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    borderRadius: 8,
    marginTop: 15,
  },
  drawingStatusText: {
    flex: 1,
    fontSize: 14,
    fontFamily: 'Inter-Regular',
  },
  cancelButton: {
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 6,
  },
  cancelButtonText: {
    color: 'white',
    fontSize: 14,
    fontFamily: 'Inter-Medium',
  },
  
  // Analysis Cards
  analysisCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 10,
  },
  analysisInfo: {
    flex: 1,
    marginLeft: 12,
  },
  analysisTitle: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
  },
  analysisDescription: {
    fontSize: 14,
    marginTop: 2,
    fontFamily: 'Inter-Regular',
  },
  
  // Features
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    marginTop: 10,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  featureIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  featureInfo: {
    flex: 1,
    marginLeft: 12,
  },
  featureName: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
  },
  featureType: {
    fontSize: 12,
    marginTop: 2,
    fontFamily: 'Inter-Regular',
  },
  
  // Zoom Controls
  zoomControls: {
    position: 'absolute',
    right: 20,
    bottom: 180,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  zoomButton: {
    width: 44,
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
  },
  zoomDivider: {
    height: 1,
  },
  
  // Location Button
  locationButton: {
    position: 'absolute',
    right: 20,
    bottom: 250,
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  
  // Status Badges
  statusContainer: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 50 : 20,
    left: 20,
    right: 80,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 8,
  },
  statusText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },
});
