import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Text,
  Alert,
  Dimensions,
} from 'react-native';
import MapView, { 
  PROVIDER_GOOGLE, 
  Marker, 
  Polyline, 
  Polygon,
  Region,
  LatLng,
  MapPressEvent,
  MapType,
} from 'react-native-maps';
import * as Location from 'expo-location';
import { useTheme } from '@/hooks/useTheme';
import {
  MapPin,
  Layers,
  Plus,
  Minus,
  Target,
  Edit3,
  X,
} from 'lucide-react-native';

type MapLayerType = 'standard' | 'satellite' | 'terrain' | 'hybrid';

interface SimpleMapProps {
  initialRegion?: Region;
  mapType?: MapLayerType;
  userLocation?: Location.LocationObject | null;
  geoFeatures?: any[];
  onLocationSelect?: (location: { latitude: number; longitude: number }) => void;
}

const { width: SCREEN_WIDTH } = Dimensions.get('window');

export default function SimpleMap({
  initialRegion,
  mapType = 'standard',
  userLocation,
  geoFeatures = [],
  onLocationSelect,
}: SimpleMapProps) {
  const { theme } = useTheme();
  const mapRef = useRef<MapView>(null);
  
  const [region, setRegion] = useState<Region>(initialRegion || {
    latitude: 37.78825,
    longitude: -122.4324,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  });
  
  const [currentLocation, setCurrentLocation] = useState<Location.LocationObject | null>(userLocation || null);

  useEffect(() => {
    if (initialRegion) {
      setRegion(initialRegion);
    }
  }, [initialRegion]);

  useEffect(() => {
    if (userLocation) {
      setCurrentLocation(userLocation);
    }
  }, [userLocation]);

  // Convert mapType string to MapView MapType
  const getMapType = (): MapType => {
    switch (mapType) {
      case 'satellite':
        return 'satellite';
      case 'terrain':
        return 'terrain';
      case 'hybrid':
        return 'hybrid';
      default:
        return 'standard';
    }
  };

  const handleMapPress = (event: MapPressEvent) => {
    const coordinate = event.nativeEvent.coordinate;
    
    if (onLocationSelect) {
      onLocationSelect(coordinate);
    }
  };

  const centerOnUserLocation = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status === 'granted') {
        const location = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.High,
        });
        setCurrentLocation(location);
        
        const newRegion = {
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        };
        
        mapRef.current?.animateToRegion(newRegion, 1000);
      } else {
        Alert.alert('Permission Denied', 'Location access is needed to center on your location.');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to get current location');
    }
  };

  const zoomIn = () => {
    const newRegion = {
      ...region,
      latitudeDelta: region.latitudeDelta * 0.5,
      longitudeDelta: region.longitudeDelta * 0.5,
    };
    mapRef.current?.animateToRegion(newRegion, 300);
  };

  const zoomOut = () => {
    const newRegion = {
      ...region,
      latitudeDelta: region.latitudeDelta * 2,
      longitudeDelta: region.longitudeDelta * 2,
    };
    mapRef.current?.animateToRegion(newRegion, 300);
  };

  const renderGeoFeatures = () => {
    return geoFeatures.map((feature, index) => {
      // Handle different feature formats
      if (feature.geometry) {
        // GeoJSON format
        if (feature.type === 'point' && Array.isArray(feature.geometry.coordinates) && feature.geometry.coordinates.length === 2) {
          const [lng, lat] = feature.geometry.coordinates as [number, number];
          return (
            <Marker
              key={feature.id || `feature-${index}`}
              coordinate={{ latitude: lat, longitude: lng }}
              title={feature.properties?.name || `Point ${index + 1}`}
              description={feature.properties?.description || ''}
            />
          );
        } else if (feature.type === 'line' && Array.isArray(feature.geometry.coordinates) && feature.geometry.coordinates.length > 0) {
          const coordinates = (feature.geometry.coordinates as [number, number][]).map(
            ([lng, lat]) => ({ latitude: lat, longitude: lng })
          );
          return (
            <Polyline
              key={feature.id || `feature-${index}`}
              coordinates={coordinates}
              strokeColor={feature.properties?.color || theme.colors.primary}
              strokeWidth={3}
            />
          );
        } else if (feature.type === 'polygon' && Array.isArray(feature.geometry.coordinates) && 
                   Array.isArray(feature.geometry.coordinates[0]) && feature.geometry.coordinates[0].length > 0) {
          const coordinates = (feature.geometry.coordinates[0] as [number, number][]).map(
            ([lng, lat]) => ({ latitude: lat, longitude: lng })
          );
          return (
            <Polygon
              key={feature.id || `feature-${index}`}
              coordinates={coordinates}
              fillColor={(feature.properties?.color || theme.colors.primary) + '40'}
              strokeColor={feature.properties?.color || theme.colors.primary}
              strokeWidth={2}
            />
          );
        }
      } else if (feature.coordinates) {
        // Simple coordinate format
        if (feature.type === 'point') {
          return (
            <Marker
              key={feature.id || `feature-${index}`}
              coordinate={feature.coordinates}
              title={feature.properties?.name || `Point ${index + 1}`}
              description={feature.properties?.description || ''}
            />
          );
        } else if (feature.type === 'line' && Array.isArray(feature.coordinates)) {
          return (
            <Polyline
              key={feature.id || `feature-${index}`}
              coordinates={feature.coordinates}
              strokeColor={feature.properties?.color || theme.colors.primary}
              strokeWidth={3}
            />
          );
        } else if (feature.type === 'polygon' && Array.isArray(feature.coordinates)) {
          return (
            <Polygon
              key={feature.id || `feature-${index}`}
              coordinates={feature.coordinates}
              fillColor={(feature.properties?.color || theme.colors.primary) + '40'}
              strokeColor={feature.properties?.color || theme.colors.primary}
              strokeWidth={2}
            />
          );
        }
      }
      
      return null;
    });
  };

  return (
    <View style={styles.container}>
      <MapView
        ref={mapRef}
        style={styles.map}
        provider={PROVIDER_GOOGLE}
        showsUserLocation={!!currentLocation}
        showsMyLocationButton={false}
        region={region}
        mapType={getMapType()}
        onRegionChangeComplete={setRegion}
        onPress={handleMapPress}
      >
        {/* User Location Marker */}
        {currentLocation && (
          <Marker
            coordinate={{
              latitude: currentLocation.coords.latitude,
              longitude: currentLocation.coords.longitude,
            }}
            title="Your Location"
          >
            <View style={styles.userLocationMarker}>
              <View style={[styles.userLocationDot, { backgroundColor: theme.colors.primary }]} />
            </View>
          </Marker>
        )}

        {/* Render features */}
        {renderGeoFeatures()}
      </MapView>

      {/* Simple Controls */}
      <View style={[styles.controls, { backgroundColor: theme.colors.card }]}>
        <TouchableOpacity
          style={[styles.controlButton, { backgroundColor: theme.colors.primary }]}
          onPress={centerOnUserLocation}
        >
          <Target size={20} color="white" />
        </TouchableOpacity>
      </View>

      {/* Zoom Controls */}
      <View style={[styles.zoomControls, { backgroundColor: theme.colors.card }]}>
        <TouchableOpacity
          style={styles.zoomButton}
          onPress={zoomIn}
        >
          <Plus size={20} color={theme.colors.text} />
        </TouchableOpacity>
        
        <View style={[styles.zoomDivider, { backgroundColor: theme.colors.border }]} />
        
        <TouchableOpacity
          style={styles.zoomButton}
          onPress={zoomOut}
        >
          <Minus size={20} color={theme.colors.text} />
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  map: {
    flex: 1,
  },
  userLocationMarker: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(0, 122, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  userLocationDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  controls: {
    position: 'absolute',
    bottom: 40,
    left: 20,
    borderRadius: 25,
    padding: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  controlButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  zoomControls: {
    position: 'absolute',
    right: 20,
    top: '50%',
    transform: [{ translateY: -50 }],
    borderRadius: 25,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  zoomButton: {
    width: 44,
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
  },
  zoomDivider: {
    height: 1,
    marginHorizontal: 8,
  },
});
