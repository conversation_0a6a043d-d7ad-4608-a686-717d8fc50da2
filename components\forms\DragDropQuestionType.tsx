import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Modal,
  Dimensions,
} from 'react-native';
import { useTheme } from '@/hooks/useTheme';
import { QuestionType } from '@/types';
import {
  Type,
  Hash,
  Calendar,
  Clock,
  CalendarDays,
  CheckSquare,
  List,
  MapPin,
  Camera,
  Mic,
  Video,
  Edit3,
  Image,
  BarChart3,
  QrCode,
  Phone,
  X,
} from 'lucide-react-native';

interface DragDropQuestionTypeProps {
  onSelectType: (type: QuestionType) => void;
  onClose: () => void;
}

const questionTypes: Array<{
  type: QuestionType;
  label: string;
  description: string;
  icon: React.ReactNode;
  category: 'basic' | 'choice' | 'media' | 'location' | 'advanced';
  isNew?: boolean;
}> = [
  // Basic Types
  {
    type: 'text',
    label: 'Text Input',
    description: 'Short text field for names, titles, etc.',
    icon: <Type size={24} />,
    category: 'basic',
  },
  {
    type: 'number',
    label: 'Number',
    description: 'Numeric input with validation',
    icon: <Hash size={24} />,
    category: 'basic',
  },
  {
    type: 'phone',
    label: 'Phone Number',
    description: 'Phone number with format validation',
    icon: <Phone size={24} />,
    category: 'basic',
    isNew: true,
  },
  {
    type: 'date',
    label: 'Date Picker',
    description: 'Select a specific date',
    icon: <Calendar size={24} />,
    category: 'basic',
  },
  {
    type: 'time',
    label: 'Time Picker',
    description: 'Select a specific time',
    icon: <Clock size={24} />,
    category: 'basic',
  },
  {
    type: 'datetime',
    label: 'Date & Time',
    description: 'Combined date and time picker',
    icon: <CalendarDays size={24} />,
    category: 'basic',
  },
  
  // Choice Types
  {
    type: 'select',
    label: 'Single Choice',
    description: 'Radio buttons for single selection',
    icon: <CheckSquare size={24} />,
    category: 'choice',
  },
  {
    type: 'multiselect',
    label: 'Multiple Choice',
    description: 'Checkboxes for multiple selections',
    icon: <List size={24} />,
    category: 'choice',
  },
  
  // Location Types
  {
    type: 'location',
    label: 'GPS Location',
    description: 'Capture GPS coordinates',
    icon: <MapPin size={24} />,
    category: 'location',
  },
  
  // Media Types
  {
    type: 'photo',
    label: 'Take Photo',
    description: 'Camera capture for photos',
    icon: <Camera size={24} />,
    category: 'media',
  },
  {
    type: 'audio',
    label: 'Audio Record',
    description: 'Record voice or audio notes',
    icon: <Mic size={24} />,
    category: 'media',
    isNew: true,
  },
  {
    type: 'video',
    label: 'Video Record',
    description: 'Record video clips',
    icon: <Video size={24} />,
    category: 'media',
    isNew: true,
  },
  
  // Advanced Types
  {
    type: 'signature',
    label: 'Digital Signature',
    description: 'Capture handwritten signatures',
    icon: <Edit3 size={24} />,
    category: 'advanced',
    isNew: true,
  },
  {
    type: 'qr_scan',
    label: 'QR Code Scan',
    description: 'Scan QR codes or barcodes',
    icon: <QrCode size={24} />,
    category: 'advanced',
    isNew: true,
  },
  {
    type: 'drawing',
    label: 'Drawing/Sketch',
    description: 'Free-form drawing or sketching',
    icon: <Image size={24} />,
    category: 'advanced',
  },
  {
    type: 'barcode',
    label: 'Barcode Scan',
    description: 'Scan traditional barcodes',
    icon: <BarChart3 size={24} />,
    category: 'advanced',
  },
];

const categoryLabels = {
  basic: 'Basic Inputs',
  choice: 'Choice Questions',
  location: 'Location & GPS',
  media: 'Media Capture',
  advanced: 'Advanced Fields',
};

const categoryDescriptions = {
  basic: 'Standard form inputs for text, numbers, and dates',
  choice: 'Single and multiple choice questions',
  location: 'GPS and location-based fields',
  media: 'Photo, audio, and video capture',
  advanced: 'Specialized fields for signatures, codes, and drawings',
};

const screenHeight = Dimensions.get('window').height;

export default function DragDropQuestionType({ onSelectType, onClose }: DragDropQuestionTypeProps) {
  const { theme } = useTheme();

  const groupedTypes = questionTypes.reduce((acc, questionType) => {
    if (!acc[questionType.category]) {
      acc[questionType.category] = [];
    }
    acc[questionType.category].push(questionType);
    return acc;
  }, {} as Record<string, typeof questionTypes>);

  const renderQuestionType = (questionType: typeof questionTypes[0]) => (
    <TouchableOpacity
      key={questionType.type}
      style={[
        styles.questionTypeCard,
        { 
          backgroundColor: theme.colors.card, 
          borderColor: theme.colors.border,
          shadowColor: theme.colors.text,
        }
      ]}
      onPress={() => onSelectType(questionType.type)}
      activeOpacity={0.8}
    >
      {questionType.isNew && (
        <View style={[styles.newBadge, { backgroundColor: theme.colors.primary }]}>
          <Text style={styles.newBadgeText}>NEW</Text>
        </View>
      )}
      
      <View style={[styles.questionTypeIcon, { backgroundColor: theme.colors.primary + '15' }]}>
        {React.cloneElement(questionType.icon as React.ReactElement, {
          color: theme.colors.primary,
        })}
      </View>
      
      <View style={styles.questionTypeContent}>
        <Text style={[styles.questionTypeLabel, { color: theme.colors.text }]}>
          {questionType.label}
        </Text>
        <Text style={[styles.questionTypeDescription, { color: theme.colors.muted }]}>
          {questionType.description}
        </Text>
      </View>
      
      <View style={styles.dragHandle}>
        <Text style={[styles.dragText, { color: theme.colors.muted }]}>Tap to add</Text>
      </View>
    </TouchableOpacity>
  );

  const renderCategory = (category: keyof typeof categoryLabels) => (
    <View key={category} style={styles.category}>
      <View style={styles.categoryHeader}>
        <Text style={[styles.categoryTitle, { color: theme.colors.text }]}>
          {categoryLabels[category]}
        </Text>
        <Text style={[styles.categoryDescription, { color: theme.colors.muted }]}>
          {categoryDescriptions[category]}
        </Text>
      </View>
      
      <View style={styles.categoryGrid}>
        {groupedTypes[category]?.map(renderQuestionType)}
      </View>
    </View>
  );

  return (
    <Modal
      visible={true}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        {/* Header */}
        <View style={[styles.header, { borderBottomColor: theme.colors.border }]}>
          <View style={styles.headerContent}>
            <Text style={[styles.title, { color: theme.colors.text }]}>Choose Field Type</Text>
            <Text style={[styles.subtitle, { color: theme.colors.muted }]}>
              Select a field type to add to your section
            </Text>
          </View>
          <TouchableOpacity
            style={[styles.closeButton, { backgroundColor: theme.colors.card }]}
            onPress={onClose}
          >
            <X size={20} color={theme.colors.text} />
          </TouchableOpacity>
        </View>
        
        {/* Content */}
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {Object.keys(categoryLabels).map(category =>
            renderCategory(category as keyof typeof categoryLabels)
          )}
          
          {/* Help Section */}
          <View style={[styles.helpSection, { backgroundColor: theme.colors.card }]}>
            <Text style={[styles.helpTitle, { color: theme.colors.text }]}>
              💡 Field Type Guide
            </Text>
            <View style={styles.helpContent}>
              <Text style={[styles.helpItem, { color: theme.colors.muted }]}>
                • <Text style={{ fontWeight: '600' }}>Basic inputs</Text> for standard data collection
              </Text>
              <Text style={[styles.helpItem, { color: theme.colors.muted }]}>
                • <Text style={{ fontWeight: '600' }}>Choice fields</Text> for predefined options
              </Text>
              <Text style={[styles.helpItem, { color: theme.colors.muted }]}>
                • <Text style={{ fontWeight: '600' }}>Media fields</Text> for photos, audio, and video
              </Text>
              <Text style={[styles.helpItem, { color: theme.colors.muted }]}>
                • <Text style={{ fontWeight: '600' }}>Advanced fields</Text> for specialized data capture
              </Text>
            </View>
          </View>
        </ScrollView>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    borderBottomWidth: 1,
  },
  headerContent: {
    flex: 1,
  },
  title: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    lineHeight: 22,
  },
  closeButton: {
    padding: 12,
    borderRadius: 8,
    marginLeft: 16,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
  },
  category: {
    marginBottom: 32,
  },
  categoryHeader: {
    marginBottom: 16,
  },
  categoryTitle: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    marginBottom: 4,
  },
  categoryDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    lineHeight: 20,
  },
  categoryGrid: {
    gap: 12,
  },
  questionTypeCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    position: 'relative',
  },
  newBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    zIndex: 1,
  },
  newBadgeText: {
    color: 'white',
    fontSize: 10,
    fontFamily: 'Inter-Bold',
    letterSpacing: 0.5,
  },
  questionTypeIcon: {
    width: 56,
    height: 56,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  questionTypeContent: {
    flex: 1,
  },
  questionTypeLabel: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 4,
  },
  questionTypeDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    lineHeight: 20,
  },
  dragHandle: {
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  dragText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  helpSection: {
    padding: 20,
    borderRadius: 12,
    marginTop: 16,
  },
  helpTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 12,
  },
  helpContent: {
    gap: 8,
  },
  helpItem: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    lineHeight: 20,
  },
});
