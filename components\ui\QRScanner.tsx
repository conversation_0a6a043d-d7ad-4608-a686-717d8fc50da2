import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Modal,
  Alert,
  Platform,
} from 'react-native';
import { useTheme } from '@/hooks/useTheme';
import { 
  QrCode, 
  Camera, 
  Keyboard, 
  CheckCircle, 
  X, 
  Flashlight,
  RotateCcw 
} from 'lucide-react-native';

interface QRScannerProps {
  value?: string;
  onChange: (value: string) => void;
  placeholder?: string;
  required?: boolean;
  label?: string;
  allowManualEntry?: boolean;
  expectedFormat?: 'any' | 'qr' | 'ean' | 'code128' | 'datamatrix';
  manualPlaceholder?: string;
}

export default function QRScanner({
  value = '',
  onChange,
  placeholder = 'Scan or enter code',
  required,
  label,
  allowManualEntry = true,
  expectedFormat = 'any',
  manualPlaceholder = 'Enter code manually',
}: QRScannerProps) {
  const { theme } = useTheme();
  const [showScanner, setShowScanner] = useState(false);
  const [showManualEntry, setShowManualEntry] = useState(false);
  const [manualCode, setManualCode] = useState(value);
  const [isScanning, setIsScanning] = useState(false);
  const [flashEnabled, setFlashEnabled] = useState(false);

  useEffect(() => {
    setManualCode(value);
  }, [value]);

  const handleScanPress = () => {
    if (Platform.OS === 'web') {
      handleWebScanner();
    } else {
      setShowScanner(true);
    }
  };

  const handleWebScanner = () => {
    // For web, we'll simulate scanning or use a web-based QR scanner
    Alert.alert(
      'QR Scanner',
      'Web-based QR scanning would be implemented here using libraries like QuaggaJS or ZXing',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Simulate Scan', 
          onPress: () => {
            // Simulate a successful scan
            const simulatedCode = `QR_${Date.now()}`;
            onChange(simulatedCode);
            Alert.alert('Scan Successful', `Scanned: ${simulatedCode}`);
          }
        }
      ]
    );
  };

  const handleManualEntry = () => {
    setShowManualEntry(true);
  };

  const handleManualSubmit = () => {
    if (required && !manualCode.trim()) {
      Alert.alert('Error', 'Code is required');
      return;
    }

    onChange(manualCode.trim());
    setShowManualEntry(false);
  };

  const handleScanSuccess = (data: string) => {
    setIsScanning(false);
    setShowScanner(false);
    onChange(data);
  };

  const validateCode = (code: string) => {
    if (!code.trim()) return true; // Allow empty if not required

    // Basic validation based on expected format
    switch (expectedFormat) {
      case 'ean':
        return /^\d{8,13}$/.test(code);
      case 'code128':
        return code.length >= 1; // Code128 can encode any ASCII character
      case 'qr':
        return code.length >= 1; // QR codes can contain any data
      case 'datamatrix':
        return code.length >= 1; // DataMatrix can contain any data
      default:
        return true; // Any format allowed
    }
  };

  const getFormatDescription = () => {
    switch (expectedFormat) {
      case 'ean':
        return 'EAN barcode (8-13 digits)';
      case 'code128':
        return 'Code 128 barcode';
      case 'qr':
        return 'QR Code';
      case 'datamatrix':
        return 'DataMatrix code';
      default:
        return 'Any barcode or QR code';
    }
  };

  const renderScannerModal = () => (
    <Modal
      visible={showScanner}
      animationType="slide"
      onRequestClose={() => setShowScanner(false)}
    >
      <View style={[styles.scannerContainer, { backgroundColor: theme.colors.background }]}>
        {/* Scanner Header */}
        <View style={[styles.scannerHeader, { backgroundColor: theme.colors.card }]}>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={() => setShowScanner(false)}
          >
            <X size={24} color={theme.colors.text} />
          </TouchableOpacity>
          <Text style={[styles.scannerTitle, { color: theme.colors.text }]}>
            Scan {getFormatDescription()}
          </Text>
          <TouchableOpacity
            style={styles.flashButton}
            onPress={() => setFlashEnabled(!flashEnabled)}
          >
            <Flashlight 
              size={24} 
              color={flashEnabled ? theme.colors.primary : theme.colors.text} 
            />
          </TouchableOpacity>
        </View>

        {/* Scanner View Placeholder */}
        <View style={styles.scannerView}>
          <View style={[styles.scannerPlaceholder, { borderColor: theme.colors.primary }]}>
            <QrCode size={64} color={theme.colors.primary} />
            <Text style={[styles.scannerInstructions, { color: theme.colors.text }]}>
              Position the {expectedFormat === 'qr' ? 'QR code' : 'barcode'} within the frame
            </Text>
            {Platform.OS !== 'web' && (
              <Text style={[styles.scannerNote, { color: theme.colors.muted }]}>
                Camera integration requires expo-barcode-scanner or react-native-camera
              </Text>
            )}
          </View>
        </View>

        {/* Scanner Actions */}
        <View style={[styles.scannerActions, { backgroundColor: theme.colors.card }]}>
          {allowManualEntry && (
            <TouchableOpacity
              style={[styles.manualEntryButton, { backgroundColor: theme.colors.background }]}
              onPress={() => {
                setShowScanner(false);
                handleManualEntry();
              }}
            >
              <Keyboard size={20} color={theme.colors.text} />
              <Text style={[styles.manualEntryText, { color: theme.colors.text }]}>
                Enter Manually
              </Text>
            </TouchableOpacity>
          )}
          
          {/* Simulate scan for demo purposes */}
          <TouchableOpacity
            style={[styles.simulateScanButton, { backgroundColor: theme.colors.primary }]}
            onPress={() => handleScanSuccess(`${expectedFormat.toUpperCase()}_${Date.now()}`)}
          >
            <CheckCircle size={20} color="white" />
            <Text style={styles.simulateScanText}>Simulate Scan</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );

  const renderManualEntryModal = () => (
    <Modal
      visible={showManualEntry}
      transparent
      animationType="slide"
      onRequestClose={() => setShowManualEntry(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.modalContent, { backgroundColor: theme.colors.card }]}>
          <View style={styles.modalHeader}>
            <Text style={[styles.modalTitle, { color: theme.colors.text }]}>
              Enter Code Manually
            </Text>
            <TouchableOpacity onPress={() => setShowManualEntry(false)}>
              <X size={24} color={theme.colors.text} />
            </TouchableOpacity>
          </View>
          
          <View style={styles.modalBody}>
            <Text style={[styles.formatInfo, { color: theme.colors.muted }]}>
              Expected format: {getFormatDescription()}
            </Text>
            
            <TextInput
              style={[
                styles.manualInput,
                {
                  backgroundColor: theme.colors.background,
                  borderColor: theme.colors.border,
                  color: theme.colors.text,
                },
              ]}
              value={manualCode}
              onChangeText={setManualCode}
              placeholder={manualPlaceholder}
              placeholderTextColor={theme.colors.placeholder}
              autoFocus
              selectTextOnFocus
            />
            
            {!validateCode(manualCode) && (
              <Text style={[styles.errorText, { color: theme.colors.error }]}>
                Invalid format for {getFormatDescription()}
              </Text>
            )}
          </View>
          
          <View style={styles.modalActions}>
            <TouchableOpacity
              style={[styles.modalButton, { backgroundColor: theme.colors.background }]}
              onPress={() => setShowManualEntry(false)}
            >
              <Text style={[styles.modalButtonText, { color: theme.colors.text }]}>
                Cancel
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.modalButton,
                {
                  backgroundColor: validateCode(manualCode) 
                    ? theme.colors.primary 
                    : theme.colors.muted,
                },
              ]}
              onPress={handleManualSubmit}
              disabled={!validateCode(manualCode)}
            >
              <Text style={styles.modalButtonTextWhite}>Confirm</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );

  return (
    <View style={styles.container}>
      {label && (
        <Text style={[styles.label, { color: theme.colors.text }]}>
          {label}
          {required && <Text style={{ color: theme.colors.error }}> *</Text>}
        </Text>
      )}
      
      {value ? (
        <View style={[
          styles.resultContainer,
          {
            backgroundColor: theme.colors.background,
            borderColor: theme.colors.primary,
          },
        ]}>
          <CheckCircle size={20} color={theme.colors.primary} />
          <Text style={[styles.resultText, { color: theme.colors.text }]}>
            {value}
          </Text>
          <TouchableOpacity
            style={styles.clearButton}
            onPress={() => onChange('')}
          >
            <RotateCcw size={16} color={theme.colors.muted} />
          </TouchableOpacity>
        </View>
      ) : (
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={[
              styles.scanButton,
              {
                backgroundColor: theme.colors.primary,
              },
            ]}
            onPress={handleScanPress}
          >
            <Camera size={20} color="white" />
            <Text style={styles.scanButtonText}>Scan Code</Text>
          </TouchableOpacity>
          
          {allowManualEntry && (
            <TouchableOpacity
              style={[
                styles.manualButton,
                {
                  backgroundColor: theme.colors.background,
                  borderColor: theme.colors.border,
                },
              ]}
              onPress={handleManualEntry}
            >
              <Keyboard size={20} color={theme.colors.text} />
              <Text style={[styles.manualButtonText, { color: theme.colors.text }]}>
                Manual
              </Text>
            </TouchableOpacity>
          )}
        </View>
      )}
      
      {renderScannerModal()}
      {renderManualEntryModal()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
  },
  label: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    marginBottom: 6,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  scanButton: {
    flex: 2,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    borderRadius: 8,
    gap: 8,
  },
  scanButtonText: {
    color: 'white',
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
  },
  manualButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    borderRadius: 8,
    borderWidth: 1,
    gap: 6,
  },
  manualButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
  },
  resultContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 14,
    borderRadius: 8,
    borderWidth: 2,
    gap: 10,
  },
  resultText: {
    flex: 1,
    fontSize: 16,
    fontFamily: 'Inter-Medium',
  },
  clearButton: {
    padding: 4,
  },
  scannerContainer: {
    flex: 1,
  },
  scannerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    paddingTop: 50, // Account for status bar
  },
  closeButton: {
    padding: 8,
  },
  scannerTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
  },
  flashButton: {
    padding: 8,
  },
  scannerView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    margin: 20,
  },
  scannerPlaceholder: {
    width: 250,
    height: 250,
    borderWidth: 2,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    borderStyle: 'dashed',
  },
  scannerInstructions: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 8,
  },
  scannerNote: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
    fontStyle: 'italic',
  },
  scannerActions: {
    flexDirection: 'row',
    padding: 16,
    gap: 12,
  },
  manualEntryButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  manualEntryText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
  },
  simulateScanButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  simulateScanText: {
    color: 'white',
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 8,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  modalTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
  },
  modalBody: {
    padding: 20,
  },
  formatInfo: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    marginBottom: 12,
  },
  manualInput: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 14,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
  },
  errorText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    marginTop: 6,
  },
  modalActions: {
    flexDirection: 'row',
    padding: 20,
    gap: 12,
  },
  modalButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  modalButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
  },
  modalButtonTextWhite: {
    color: 'white',
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
  },
});
