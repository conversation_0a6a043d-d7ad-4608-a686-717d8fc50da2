import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Alert,
  Animated,
} from 'react-native';
import { useTheme } from '@/hooks/useTheme';
import { 
  Mic, 
  Square, 
  Play, 
  Pause, 
  RotateCcw, 
  Trash2,
  Upload,
  Volume2,
} from 'lucide-react-native';

interface AudioRecorderProps {
  value?: string; // Audio URI
  onChange: (uri: string) => void;
  placeholder?: string;
  required?: boolean;
  label?: string;
  maxDuration?: number; // in seconds
  compress?: boolean;
}

export default function AudioRecorder({
  value,
  onChange,
  placeholder = 'Record audio',
  required,
  label,
  maxDuration = 300, // 5 minutes default
  compress = true,
}: AudioRecorderProps) {
  const { theme } = useTheme();
  const [showRecorder, setShowRecorder] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [recordingDuration, setRecordingDuration] = useState(0);
  const [audioDuration, setAudioDuration] = useState(0);
  const [playbackPosition, setPlaybackPosition] = useState(0);
  
  const recordingTimer = useRef<NodeJS.Timeout | null>(null);
  const playbackTimer = useRef<NodeJS.Timeout | null>(null);
  const waveAnimation = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    return () => {
      if (recordingTimer.current) {
        clearInterval(recordingTimer.current);
      }
      if (playbackTimer.current) {
        clearInterval(playbackTimer.current);
      }
    };
  }, []);

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const startWaveAnimation = () => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(waveAnimation, {
          toValue: 1,
          duration: 500,
          useNativeDriver: false,
        }),
        Animated.timing(waveAnimation, {
          toValue: 0,
          duration: 500,
          useNativeDriver: false,
        }),
      ])
    ).start();
  };

  const stopWaveAnimation = () => {
    waveAnimation.stopAnimation();
    waveAnimation.setValue(0);
  };

  const startRecording = () => {
    setIsRecording(true);
    setRecordingDuration(0);
    startWaveAnimation();
    
    // Start timer
    recordingTimer.current = setInterval(() => {
      setRecordingDuration(prev => {
        const newDuration = prev + 1;
        if (newDuration >= maxDuration) {
          stopRecording();
        }
        return newDuration;
      });
    }, 1000);

    // In a real implementation, start audio recording here
    console.log('Starting audio recording...');
  };

  const stopRecording = () => {
    setIsRecording(false);
    stopWaveAnimation();
    
    if (recordingTimer.current) {
      clearInterval(recordingTimer.current);
      recordingTimer.current = null;
    }

    // Simulate successful recording
    const audioUri = `audio_${Date.now()}.m4a`;
    setAudioDuration(recordingDuration);
    onChange(audioUri);
    setShowRecorder(false);
    
    console.log('Audio recording stopped:', audioUri);
    Alert.alert('Recording Complete', `Audio recorded successfully (${formatDuration(recordingDuration)})`);
  };

  const cancelRecording = () => {
    if (isRecording) {
      setIsRecording(false);
      stopWaveAnimation();
      if (recordingTimer.current) {
        clearInterval(recordingTimer.current);
        recordingTimer.current = null;
      }
    }
    setRecordingDuration(0);
    setShowRecorder(false);
  };

  const playAudio = () => {
    if (isPlaying) {
      pauseAudio();
      return;
    }

    setIsPlaying(true);
    setPlaybackPosition(0);
    
    // Simulate audio playback
    playbackTimer.current = setInterval(() => {
      setPlaybackPosition(prev => {
        const newPosition = prev + 1;
        if (newPosition >= audioDuration) {
          pauseAudio();
          return 0;
        }
        return newPosition;
      });
    }, 1000);

    console.log('Playing audio:', value);
  };

  const pauseAudio = () => {
    setIsPlaying(false);
    if (playbackTimer.current) {
      clearInterval(playbackTimer.current);
      playbackTimer.current = null;
    }
  };

  const deleteAudio = () => {
    Alert.alert(
      'Delete Audio',
      'Are you sure you want to delete this audio recording?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            onChange('');
            setAudioDuration(0);
            setPlaybackPosition(0);
            pauseAudio();
          },
        },
      ]
    );
  };

  const handleFileUpload = () => {
    Alert.alert(
      'Upload Audio',
      'File picker would open here to select an audio file',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Simulate Upload',
          onPress: () => {
            const uploadedUri = `uploaded_audio_${Date.now()}.m4a`;
            setAudioDuration(120); // Simulate 2 minute audio
            onChange(uploadedUri);
            Alert.alert('Upload Complete', 'Audio uploaded successfully');
          },
        },
      ]
    );
  };

  const renderWaveform = () => {
    const bars = Array.from({ length: 5 }, (_, index) => {
      const animatedHeight = waveAnimation.interpolate({
        inputRange: [0, 1],
        outputRange: [8, 20 + (index % 3) * 8],
      });

      return (
        <Animated.View
          key={index}
          style={[
            styles.waveBar,
            {
              height: isRecording ? animatedHeight : 8,
              backgroundColor: theme.colors.primary,
            },
          ]}
        />
      );
    });

    return <View style={styles.waveform}>{bars}</View>;
  };

  const renderRecorderModal = () => (
    <Modal
      visible={showRecorder}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={cancelRecording}
    >
      <View style={[styles.recorderContainer, { backgroundColor: theme.colors.background }]}>
        {/* Header */}
        <View style={[styles.recorderHeader, { borderBottomColor: theme.colors.border }]}>
          <TouchableOpacity
            style={styles.cancelButton}
            onPress={cancelRecording}
          >
            <Text style={[styles.cancelButtonText, { color: theme.colors.text }]}>Cancel</Text>
          </TouchableOpacity>
          
          <Text style={[styles.recorderTitle, { color: theme.colors.text }]}>
            Audio Recording
          </Text>
          
          <View style={styles.headerSpacer} />
        </View>

        {/* Recording Interface */}
        <View style={styles.recordingInterface}>
          {/* Microphone Icon */}
          <View style={[
            styles.microphoneContainer,
            {
              backgroundColor: isRecording ? theme.colors.primary + '20' : theme.colors.card,
              borderColor: isRecording ? theme.colors.primary : theme.colors.border,
            },
          ]}>
            <Mic size={48} color={isRecording ? theme.colors.primary : theme.colors.muted} />
          </View>

          {/* Waveform */}
          {isRecording && renderWaveform()}

          {/* Duration Display */}
          <Text style={[styles.durationDisplay, { color: theme.colors.text }]}>
            {formatDuration(recordingDuration)}
          </Text>
          
          <Text style={[styles.maxDurationText, { color: theme.colors.muted }]}>
            Max: {formatDuration(maxDuration)}
          </Text>

          {/* Recording Instructions */}
          <Text style={[styles.instructions, { color: theme.colors.muted }]}>
            {isRecording ? 'Recording in progress...' : 'Tap the record button to start'}
          </Text>

          {/* Recording Button */}
          <TouchableOpacity
            style={[
              styles.recordButton,
              {
                backgroundColor: isRecording ? theme.colors.error : theme.colors.primary,
              },
            ]}
            onPress={isRecording ? stopRecording : startRecording}
          >
            {isRecording ? (
              <Square size={24} color="white" />
            ) : (
              <Mic size={24} color="white" />
            )}
          </TouchableOpacity>

          {/* Quality Info */}
          <View style={styles.qualityInfo}>
            <Text style={[styles.qualityText, { color: theme.colors.muted }]}>
              Quality: High • Format: M4A
            </Text>
            {compress && (
              <Text style={[styles.qualityText, { color: theme.colors.muted }]}>
                Compression: Enabled
              </Text>
            )}
          </View>
        </View>
      </View>
    </Modal>
  );

  const renderProgressBar = () => {
    if (!value || audioDuration === 0) return null;

    const progress = playbackPosition / audioDuration;

    return (
      <View style={[styles.progressContainer, { backgroundColor: theme.colors.border }]}>
        <View
          style={[
            styles.progressBar,
            {
              width: `${progress * 100}%`,
              backgroundColor: theme.colors.primary,
            },
          ]}
        />
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {label && (
        <Text style={[styles.label, { color: theme.colors.text }]}>
          {label}
          {required && <Text style={{ color: theme.colors.error }}> *</Text>}
        </Text>
      )}

      {value ? (
        <View style={[
          styles.audioContainer,
          {
            backgroundColor: theme.colors.background,
            borderColor: theme.colors.border,
          },
        ]}>
          <View style={styles.audioInfo}>
            <Volume2 size={24} color={theme.colors.primary} />
            <View style={styles.audioDetails}>
              <Text style={[styles.audioName, { color: theme.colors.text }]}>
                Audio recording
              </Text>
              <Text style={[styles.audioMeta, { color: theme.colors.muted }]}>
                Duration: {formatDuration(audioDuration)} • M4A
              </Text>
              {renderProgressBar()}
              {isPlaying && (
                <Text style={[styles.playbackTime, { color: theme.colors.muted }]}>
                  {formatDuration(playbackPosition)} / {formatDuration(audioDuration)}
                </Text>
              )}
            </View>
          </View>
          
          <View style={styles.audioActions}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={playAudio}
            >
              {isPlaying ? (
                <Pause size={20} color={theme.colors.primary} />
              ) : (
                <Play size={20} color={theme.colors.primary} />
              )}
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => setShowRecorder(true)}
            >
              <RotateCcw size={16} color={theme.colors.muted} />
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={deleteAudio}
            >
              <Trash2 size={16} color={theme.colors.error} />
            </TouchableOpacity>
          </View>
        </View>
      ) : (
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={[
              styles.recordActionButton,
              { backgroundColor: theme.colors.primary },
            ]}
            onPress={() => setShowRecorder(true)}
          >
            <Mic size={20} color="white" />
            <Text style={styles.recordActionText}>Record Audio</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.uploadButton,
              {
                backgroundColor: theme.colors.background,
                borderColor: theme.colors.border,
              },
            ]}
            onPress={handleFileUpload}
          >
            <Upload size={18} color={theme.colors.text} />
          </TouchableOpacity>
        </View>
      )}

      {renderRecorderModal()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
  },
  label: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    marginBottom: 6,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  recordActionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    borderRadius: 8,
    gap: 8,
  },
  recordActionText: {
    color: 'white',
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
  },
  uploadButton: {
    paddingVertical: 14,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  audioContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 14,
    borderRadius: 8,
    borderWidth: 1,
    gap: 12,
  },
  audioInfo: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  audioDetails: {
    flex: 1,
  },
  audioName: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 2,
  },
  audioMeta: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    marginBottom: 4,
  },
  progressContainer: {
    height: 3,
    borderRadius: 1.5,
    marginVertical: 4,
  },
  progressBar: {
    height: '100%',
    borderRadius: 1.5,
  },
  playbackTime: {
    fontSize: 10,
    fontFamily: 'Inter-Regular',
    marginTop: 2,
  },
  audioActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    padding: 8,
  },
  recorderContainer: {
    flex: 1,
  },
  recorderHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  cancelButton: {
    padding: 8,
  },
  cancelButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
  },
  recorderTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
  },
  headerSpacer: {
    width: 60,
  },
  recordingInterface: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  microphoneContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    borderWidth: 2,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 30,
  },
  waveform: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    marginBottom: 20,
  },
  waveBar: {
    width: 4,
    borderRadius: 2,
  },
  durationDisplay: {
    fontSize: 32,
    fontFamily: 'Inter-Bold',
    marginBottom: 4,
  },
  maxDurationText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    marginBottom: 30,
  },
  instructions: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
    marginBottom: 40,
  },
  recordButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 30,
  },
  qualityInfo: {
    alignItems: 'center',
    gap: 4,
  },
  qualityText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
  },
});
