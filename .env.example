# FieldSync Pro Environment Configuration

# ==============================================
# Supabase Configuration
# ==============================================

# Replace these with your actual Supabase project values
# You can find these in your Supabase project dashboard

# Supabase Project URL
EXPO_PUBLIC_SUPABASE_URL=https://dxfzgqjqmqnbycgiriam.supabase.co

# Supabase Anonymous Key (public key)
EXPO_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR4ZnpncWpxbXFuYnljZ2lyaWFtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgxOTY0NTcsImV4cCI6MjA2Mzc3MjQ1N30.pL-nM39Esfx8RFbM6jsX7Vr4WH0AynfnUwAq3RwMc_E

# Optional: Supabase Service Role Key (for admin operations)
# SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here

# ==============================================
# App Configuration
# ==============================================

# App Environment
EXPO_PUBLIC_APP_ENV=development

# API Base URL (if using custom backend)
# EXPO_PUBLIC_API_URL=https://api.fieldsyncpro.com

# ==============================================
# Feature Flags
# ==============================================

# Enable/disable real-time features
EXPO_PUBLIC_ENABLE_REALTIME=true

# Enable/disable offline support
EXPO_PUBLIC_ENABLE_OFFLINE=true

# Enable/disable analytics
EXPO_PUBLIC_ENABLE_ANALYTICS=true

# ==============================================
# Storage Configuration
# ==============================================

# Maximum file upload size (in MB)
EXPO_PUBLIC_MAX_FILE_SIZE=50

# Allowed file types for uploads
EXPO_PUBLIC_ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,video/mp4,audio/mp3,application/pdf

# ==============================================
# Map Configuration
# ==============================================

# Mapbox Access Token (optional, for advanced mapping)
# EXPO_PUBLIC_MAPBOX_ACCESS_TOKEN=your-mapbox-token

# Default map center coordinates
EXPO_PUBLIC_DEFAULT_MAP_CENTER_LAT=39.8283
EXPO_PUBLIC_DEFAULT_MAP_CENTER_LNG=-98.5795

# ==============================================
# Development Configuration
# ==============================================

# Enable development mode features
EXPO_PUBLIC_DEV_MODE=true

# Enable debug logging
EXPO_PUBLIC_DEBUG_LOGGING=true

# Mock data mode (when Supabase is not configured)
EXPO_PUBLIC_MOCK_DATA=false

# ==============================================
# Security Configuration
# ==============================================

# JWT Secret (for local development only)
# JWT_SECRET=your-jwt-secret-for-development

# Session timeout (in minutes)
EXPO_PUBLIC_SESSION_TIMEOUT=480

# ==============================================
# Performance Configuration
# ==============================================

# API request timeout (in milliseconds)
EXPO_PUBLIC_API_TIMEOUT=30000

# Default pagination size
EXPO_PUBLIC_DEFAULT_PAGE_SIZE=20

# Maximum pagination size
EXPO_PUBLIC_MAX_PAGE_SIZE=100

# ==============================================
# Notification Configuration
# ==============================================

# Push notification service
# EXPO_PUBLIC_PUSH_NOTIFICATIONS=true

# ==============================================
# Logging Configuration
# ==============================================

# Log level (error, warn, info, debug)
EXPO_PUBLIC_LOG_LEVEL=info

# Enable error reporting
EXPO_PUBLIC_ERROR_REPORTING=true

# ==============================================
# Analytics Configuration
# ==============================================

# Analytics service configuration
# EXPO_PUBLIC_ANALYTICS_ID=your-analytics-id
