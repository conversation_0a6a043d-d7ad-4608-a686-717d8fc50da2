import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Platform,
  Dimensions,
  SafeAreaView,
  ScrollView,
  Alert,
} from 'react-native';
import { useTheme } from '@/hooks/useTheme';
import * as Location from 'expo-location';
import {
  MapPin,
  Layers,
  Edit3,
  Ruler,
  BarChart3,
  Filter,
  Settings,
  Target,
  Plus, 
  Minus,
  Route,
  Pentagon,
  Square,
  Circle,
  Navigation,
  Eye,
  EyeOff,
  Save,
  Trash2,
  X,
  CheckCircle,
  Info,
  Menu,
  Maximize2,
  Download,
  FileText,
  Zap,
  GitMerge,
  Scissors,
} from 'lucide-react-native';

// Platform-specific map component import
const PlatformMap = Platform.OS === 'web' 
  ? require('./EnhancedLeafletMap.web').default 
  : require('./Map.native').default;

interface Region {
  latitude: number;
  longitude: number;
  latitudeDelta: number;
  longitudeDelta: number;
}

interface DrawnFeature {
  id: string;
  type: 'point' | 'line' | 'polygon' | 'circle' | 'rectangle';
  coordinates: any;
  properties: {
    name: string;
    description?: string;
    color: string;
    created: number;
    measurements?: {
      area?: number;
      perimeter?: number;
      length?: number;
      radius?: number;
    };
  };
}

interface ProfessionalMapUIProps {
  initialRegion?: Region;
  geoFeatures?: any[];
  onFeatureCreated?: (feature: DrawnFeature) => void;
  onFeatureDeleted?: (featureId: string) => void;
  onLocationSelect?: (location: { latitude: number; longitude: number }) => void;
  enableDrawing?: boolean;
  enableMeasurement?: boolean;
  enableAnalysis?: boolean;
  maxFeatures?: number;
}

type DrawingTool = 'none' | 'point' | 'line' | 'polygon' | 'rectangle' | 'circle';
type MapLayer = 'standard' | 'satellite' | 'terrain' | 'hybrid';
type ActiveTool = 'none' | 'layers' | 'drawing' | 'measurement' | 'analysis' | 'features';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');
const TOOLBAR_HEIGHT = 60;
const SIDEBAR_WIDTH = 320;

export default function ProfessionalMapUI({
  initialRegion,
  geoFeatures = [],
  onFeatureCreated,
  onFeatureDeleted,
  onLocationSelect,
  enableDrawing = true,
  enableMeasurement = true,
  enableAnalysis = true,
  maxFeatures = 100,
}: ProfessionalMapUIProps) {
  const { theme } = useTheme();
  
  // State management
  const [region] = useState<Region>(initialRegion || {
    latitude: 37.78825,
    longitude: -122.4324,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  });
  
  const [activeTool, setActiveTool] = useState<ActiveTool>('none');
  const [drawingTool, setDrawingTool] = useState<DrawingTool>('none');
  const [mapLayer, setMapLayer] = useState<MapLayer>('standard');
  const [userLocation, setUserLocation] = useState<Location.LocationObject | null>(null);
  const [drawnFeatures, setDrawnFeatures] = useState<DrawnFeature[]>([]);
  const [selectedFeature, setSelectedFeature] = useState<string | null>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [measurementMode, setMeasurementMode] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(true);
  const [showControls, setShowControls] = useState(true);
  const [exportInProgress, setExportInProgress] = useState(false);
  const [analysisInProgress, setAnalysisInProgress] = useState(false);
  const [analysisResults, setAnalysisResults] = useState<any[]>([]);

  // Refs
  const mapRef = useRef<any>(null);

  useEffect(() => {
    requestLocationPermission();
  }, []);

  const requestLocationPermission = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status === 'granted') {
        const location = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.High,
        });
        setUserLocation(location);
      }
    } catch (error) {
      console.error('Error getting location:', error);
    }
  };

  // Drawing tool handlers
  const selectDrawingTool = (tool: DrawingTool) => {
    setDrawingTool(tool);
    setActiveTool('drawing');
    setIsDrawing(tool !== 'none');
    
    if (tool !== 'none') {
      setSidebarCollapsed(true); // Hide sidebar when drawing
    }
  };

  const cancelDrawing = () => {
    setDrawingTool('none');
    setIsDrawing(false);
    setActiveTool('none');
  };

  const handleMapPress = (location: { latitude: number; longitude: number }) => {
    if (drawingTool !== 'none' && isDrawing) {
      // Handle drawing logic here
      handleDrawingComplete(location);
    } else if (onLocationSelect) {
      onLocationSelect(location);
    }
  };

  const handleDrawingComplete = (location: { latitude: number; longitude: number }) => {
    if (drawnFeatures.length >= maxFeatures) {
      Alert.alert(
        'Feature Limit Reached',
        `Maximum of ${maxFeatures} features allowed.`,
        [{ text: 'OK' }]
      );
      return;
    }

    const feature: DrawnFeature = {
      id: `${drawingTool}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: drawingTool as any,
      coordinates: drawingTool === 'point' ? location : [location], // Simplified for now
      properties: {
        name: `${drawingTool.charAt(0).toUpperCase() + drawingTool.slice(1)} ${drawnFeatures.length + 1}`,
        color: theme.colors.primary,
        created: Date.now(),
      },
    };

    setDrawnFeatures(prev => [...prev, feature]);
    if (onFeatureCreated) {
      onFeatureCreated(feature);
    }

    // Auto-complete point drawing, continue others
    if (drawingTool === 'point') {
      cancelDrawing();
      Alert.alert('Success', 'Point created successfully!');
    }
  };

  const deleteFeature = (featureId: string) => {
    Alert.alert(
      'Delete Feature',
      'Are you sure you want to delete this feature?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            setDrawnFeatures(prev => prev.filter(f => f.id !== featureId));
            if (onFeatureDeleted) {
              onFeatureDeleted(featureId);
            }
          },
        },
      ]
    );
  };

  // Enhanced zoom controls that actually work
  const handleZoomIn = () => {
    if (mapRef.current) {
      mapRef.current.zoomIn();
    }
  };

  const handleZoomOut = () => {
    if (mapRef.current) {
      mapRef.current.zoomOut();
    }
  };

  // Export functions
  const handleExportGeoJSON = async () => {
    if (!mapRef.current) return;
    
    setExportInProgress(true);
    try {
      const geoJsonData = mapRef.current.exportGeoJSON();
      
      // Create download link
      const dataStr = JSON.stringify(geoJsonData, null, 2);
      const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
      
      const exportFileDefaultName = `map_features_${new Date().toISOString().split('T')[0]}.geojson`;
      
      const linkElement = document.createElement('a');
      linkElement.setAttribute('href', dataUri);
      linkElement.setAttribute('download', exportFileDefaultName);
      linkElement.click();
      
      Alert.alert(
        'Export Success', 
        `Exported ${geoJsonData.features.length} features to GeoJSON format`
      );
    } catch (error) {
      console.error('Export error:', error);
      Alert.alert('Export Error', 'Failed to export GeoJSON');
    } finally {
      setExportInProgress(false);
    }
  };

  const handleExportShapefile = async () => {
    if (!mapRef.current) return;
    
    setExportInProgress(true);
    try {
      await mapRef.current.exportShapefile();
      Alert.alert('Export Success', 'Shapefile export initiated');
    } catch (error) {
      console.error('Shapefile export error:', error);
      Alert.alert('Export Error', 'Failed to export Shapefile');
    } finally {
      setExportInProgress(false);
    }
  };

  // Spatial Analysis Functions
  const runSpatialAnalysis = async (analysisType: string, options: any = {}) => {
    if (!mapRef.current) return;
    
    setAnalysisInProgress(true);
    try {
      const result = mapRef.current.runAnalysis(analysisType, options);
      if (result) {
        setAnalysisResults(prev => [...prev, {
          id: Date.now(),
          type: analysisType,
          result,
          timestamp: new Date().toISOString()
        }]);
        
        Alert.alert(
          'Analysis Complete',
          `${analysisType} analysis completed successfully`
        );
      }
    } catch (error) {
      console.error('Analysis error:', error);
      Alert.alert('Analysis Error', `Failed to run ${analysisType} analysis`);
    } finally {
      setAnalysisInProgress(false);
    }
  };

  const clearAnalysisResults = () => {
    setAnalysisResults([]);
    Alert.alert('Cleared', 'All analysis results cleared');
  };

  const toggleTool = (tool: ActiveTool) => {
    if (activeTool === tool) {
      setActiveTool('none');
      setSidebarCollapsed(true);
    } else {
      setActiveTool(tool);
      setSidebarCollapsed(false);
    }
  };

  const centerOnUserLocation = () => {
    if (userLocation && mapRef.current) {
      const newRegion = {
        latitude: userLocation.coords.latitude,
        longitude: userLocation.coords.longitude,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      };
      // Map centering logic would go here
      Alert.alert('Location', 'Centered on your location');
    } else {
      requestLocationPermission();
    }
  };

  // Render main toolbar (positioned outside map area)
  const renderMainToolbar = () => (
    <View style={[styles.mainToolbar, { backgroundColor: theme.colors.card, borderBottomColor: theme.colors.border }]}>
      <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.toolbarContent}>
        
        {/* Layer Control */}
        <TouchableOpacity
          style={[styles.toolbarButton, activeTool === 'layers' && styles.toolbarButtonActive]}
          onPress={() => toggleTool('layers')}
        >
          <Layers size={20} color={activeTool === 'layers' ? theme.colors.primary : theme.colors.text} />
          <Text style={[styles.toolbarButtonText, { color: theme.colors.text }]}>Layers</Text>
        </TouchableOpacity>

        {/* Drawing Tools */}
        {enableDrawing && (
          <TouchableOpacity
            style={[styles.toolbarButton, activeTool === 'drawing' && styles.toolbarButtonActive]}
            onPress={() => toggleTool('drawing')}
          >
            <Edit3 size={20} color={activeTool === 'drawing' ? theme.colors.primary : theme.colors.text} />
            <Text style={[styles.toolbarButtonText, { color: theme.colors.text }]}>Draw</Text>
          </TouchableOpacity>
        )}

        {/* Measurement */}
        {enableMeasurement && (
          <TouchableOpacity
            style={[styles.toolbarButton, measurementMode && styles.toolbarButtonActive]}
            onPress={() => {
              setMeasurementMode(!measurementMode);
              setActiveTool(measurementMode ? 'none' : 'measurement');
            }}
          >
            <Ruler size={20} color={measurementMode ? theme.colors.primary : theme.colors.text} />
            <Text style={[styles.toolbarButtonText, { color: theme.colors.text }]}>Measure</Text>
          </TouchableOpacity>
        )}

        {/* Analysis */}
        {enableAnalysis && (
          <TouchableOpacity
            style={[styles.toolbarButton, activeTool === 'analysis' && styles.toolbarButtonActive]}
            onPress={() => toggleTool('analysis')}
          >
            <BarChart3 size={20} color={activeTool === 'analysis' ? theme.colors.primary : theme.colors.text} />
            <Text style={[styles.toolbarButtonText, { color: theme.colors.text }]}>Analyze</Text>
          </TouchableOpacity>
        )}

        {/* Features */}
        <TouchableOpacity
          style={[styles.toolbarButton, activeTool === 'features' && styles.toolbarButtonActive]}
          onPress={() => toggleTool('features')}
        >
          <Filter size={20} color={activeTool === 'features' ? theme.colors.primary : theme.colors.text} />
          <Text style={[styles.toolbarButtonText, { color: theme.colors.text }]}>Features ({drawnFeatures.length})</Text>
        </TouchableOpacity>

      </ScrollView>

      {/* Controls Toggle */}
      <TouchableOpacity
        style={[styles.controlsToggle, { backgroundColor: theme.colors.background }]}
        onPress={() => setShowControls(!showControls)}
      >
        {showControls ? <EyeOff size={18} color={theme.colors.text} /> : <Eye size={18} color={theme.colors.text} />}
      </TouchableOpacity>
    </View>
  );

  // Render map controls (positioned outside map area)
  const renderMapControls = () => {
    if (!showControls) return null;

    return (
      <View style={[styles.mapControlsContainer, { backgroundColor: theme.colors.card }]}>
        {/* Zoom Controls */}
        <View style={styles.zoomControls}>
          <TouchableOpacity
            style={[styles.controlButton, { borderBottomWidth: 1, borderBottomColor: theme.colors.border }]}
            onPress={handleZoomIn}
          >
            <Plus size={18} color={theme.colors.text} />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.controlButton}
            onPress={handleZoomOut}
          >
            <Minus size={18} color={theme.colors.text} />
          </TouchableOpacity>
        </View>

        {/* Location Control */}
        <TouchableOpacity
          style={[styles.locationControl, { backgroundColor: theme.colors.primary }]}
          onPress={centerOnUserLocation}
        >
          <Target size={20} color="white" />
        </TouchableOpacity>

        {/* Map Controls */}
        <TouchableOpacity
          style={[styles.controlButton, { backgroundColor: theme.colors.background }]}
          onPress={() => setSidebarCollapsed(!sidebarCollapsed)}
        >
          <Menu size={18} color={theme.colors.text} />
        </TouchableOpacity>
      </View>
    );
  };

  // Render drawing status (when drawing is active)
  const renderDrawingStatus = () => {
    if (!isDrawing) return null;

    return (
      <View style={[styles.drawingStatus, { backgroundColor: theme.colors.primary + '15', borderColor: theme.colors.primary }]}>
        <View style={styles.drawingStatusContent}>
          <Text style={[styles.drawingStatusText, { color: theme.colors.primary }]}>
            Drawing: {drawingTool}
          </Text>
          <Text style={[styles.drawingStatusSubtext, { color: theme.colors.text }]}>
            Tap on the map to place {drawingTool}
          </Text>
        </View>
        <TouchableOpacity
          style={[styles.cancelDrawingButton, { backgroundColor: theme.colors.error }]}
          onPress={cancelDrawing}
        >
          <X size={16} color="white" />
          <Text style={styles.cancelButtonText}>Cancel</Text>
        </TouchableOpacity>
      </View>
    );
  };

  // Render sidebar panels
  const renderSidebar = () => {
    if (sidebarCollapsed || activeTool === 'none') return null;

    return (
      <View style={[styles.sidebar, { backgroundColor: theme.colors.card, borderLeftColor: theme.colors.border }]}>
        {/* Sidebar Header */}
        <View style={[styles.sidebarHeader, { borderBottomColor: theme.colors.border }]}>
          <Text style={[styles.sidebarTitle, { color: theme.colors.text }]}>
            {activeTool === 'layers' && 'Map Layers'}
            {activeTool === 'drawing' && 'Drawing Tools'}  
            {activeTool === 'measurement' && 'Measurement Tools'}
            {activeTool === 'analysis' && 'Spatial Analysis'}
            {activeTool === 'features' && 'Feature Manager'}
          </Text>
          <TouchableOpacity onPress={() => setSidebarCollapsed(true)}>
            <X size={20} color={theme.colors.text} />
          </TouchableOpacity>
        </View>

        {/* Sidebar Content */}
        <ScrollView style={styles.sidebarContent} showsVerticalScrollIndicator={false}>
          {activeTool === 'layers' && renderLayersPanel()}
          {activeTool === 'drawing' && renderDrawingPanel()}
          {activeTool === 'measurement' && renderMeasurementPanel()}
          {activeTool === 'analysis' && renderAnalysisPanel()}
          {activeTool === 'features' && renderFeaturesPanel()}
        </ScrollView>
      </View>
    );
  };

  const renderLayersPanel = () => (
    <View style={styles.panelContent}>
      <Text style={[styles.panelSectionTitle, { color: theme.colors.text }]}>Base Layers</Text>
      {[
        { id: 'standard', label: 'Street Map', icon: Layers },
        { id: 'satellite', label: 'Satellite', icon: Layers },
        { id: 'terrain', label: 'Terrain', icon: Layers },
        { id: 'hybrid', label: 'Hybrid', icon: Layers },
      ].map((layer) => (
        <TouchableOpacity
          key={layer.id}
          style={[
            styles.panelOption,
            mapLayer === layer.id && styles.panelOptionActive,
            { backgroundColor: mapLayer === layer.id ? theme.colors.primary + '15' : 'transparent' }
          ]}
          onPress={() => setMapLayer(layer.id as MapLayer)}
        >
          <layer.icon size={20} color={mapLayer === layer.id ? theme.colors.primary : theme.colors.text} />
          <Text style={[styles.panelOptionText, { color: theme.colors.text }]}>{layer.label}</Text>
          {mapLayer === layer.id && <CheckCircle size={16} color={theme.colors.primary} />}
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderDrawingPanel = () => (
    <View style={styles.panelContent}>
      <Text style={[styles.panelSectionTitle, { color: theme.colors.text }]}>Drawing Tools</Text>
      <View style={styles.drawingToolsGrid}>
        {[
          { id: 'point', label: 'Point', icon: MapPin },
          { id: 'line', label: 'Line', icon: Route },
          { id: 'polygon', label: 'Polygon', icon: Pentagon },
          { id: 'rectangle', label: 'Rectangle', icon: Square },
          { id: 'circle', label: 'Circle', icon: Circle },
        ].map((tool) => (
          <TouchableOpacity
            key={tool.id}
            style={[
              styles.drawingToolButton,
              drawingTool === tool.id && styles.drawingToolButtonActive,
              { 
                backgroundColor: drawingTool === tool.id ? theme.colors.primary : theme.colors.background,
                borderColor: theme.colors.border,
              }
            ]}
            onPress={() => selectDrawingTool(tool.id as DrawingTool)}
          >
            <tool.icon size={24} color={drawingTool === tool.id ? 'white' : theme.colors.text} />
            <Text style={[
              styles.drawingToolButtonText, 
              { color: drawingTool === tool.id ? 'white' : theme.colors.text }
            ]}>
              {tool.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {drawingTool !== 'none' && (
        <View style={[styles.drawingInstructions, { backgroundColor: theme.colors.info + '15' }]}>
          <Info size={16} color={theme.colors.info} />
          <Text style={[styles.instructionsText, { color: theme.colors.info }]}>
            Tap on the map to place your {drawingTool}. Use the cancel button to stop drawing.
          </Text>
        </View>
      )}
    </View>
  );

  const renderMeasurementPanel = () => (
    <View style={styles.panelContent}>
      <Text style={[styles.panelSectionTitle, { color: theme.colors.text }]}>Measurement Tools</Text>
      <Text style={[styles.panelDescription, { color: theme.colors.muted }]}>
        Click on the map to start measuring distances and areas.
      </Text>
      <TouchableOpacity
        style={[
          styles.measurementButton,
          { backgroundColor: measurementMode ? theme.colors.error : theme.colors.primary }
        ]}
        onPress={() => {
          setMeasurementMode(!measurementMode);
          if (measurementMode) {
            setActiveTool('none');
          }
        }}
      >
        <Text style={styles.measurementButtonText}>
          {measurementMode ? 'Stop Measuring' : 'Start Measuring'}
        </Text>
      </TouchableOpacity>
    </View>
  );

  const renderAnalysisPanel = () => (
    <View style={styles.panelContent}>
      <Text style={[styles.panelSectionTitle, { color: theme.colors.text }]}>Spatial Analysis</Text>
      <Text style={[styles.panelDescription, { color: theme.colors.muted }]}>
        Advanced spatial analysis tools powered by Turf.js
      </Text>
      
      {/* Analysis Results */}
      {analysisResults.length > 0 && (
        <View style={[styles.analysisResults, { backgroundColor: theme.colors.background }]}>
          <View style={styles.analysisResultsHeader}>
            <Text style={[styles.analysisResultsTitle, { color: theme.colors.text }]}>
              Results ({analysisResults.length})
            </Text>
            <TouchableOpacity onPress={clearAnalysisResults}>
              <X size={16} color={theme.colors.error} />
            </TouchableOpacity>
          </View>
          {analysisResults.slice(-3).map((result) => (
            <View key={result.id} style={[styles.analysisResultItem, { borderColor: theme.colors.border }]}>
              <Text style={[styles.analysisResultType, { color: theme.colors.primary }]}>
                {result.type.charAt(0).toUpperCase() + result.type.slice(1)}
              </Text>
              <Text style={[styles.analysisResultTime, { color: theme.colors.muted }]}>
                {new Date(result.timestamp).toLocaleTimeString()}
              </Text>
            </View>
          ))}
        </View>
      )}
      
      {/* Analysis Tools Grid */}
      <View style={styles.analysisToolsGrid}>
        <TouchableOpacity
          style={[styles.analysisToolButton, { backgroundColor: theme.colors.primary }]}
          onPress={() => runSpatialAnalysis('buffer', { distance: 100, units: 'meters' })}
          disabled={analysisInProgress}
        >
          <Zap size={20} color="white" />
          <Text style={styles.analysisToolButtonText}>Buffer</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.analysisToolButton, { backgroundColor: theme.colors.secondary }]}
          onPress={() => runSpatialAnalysis('intersect')}
          disabled={analysisInProgress}
        >
          <GitMerge size={20} color="white" />
          <Text style={styles.analysisToolButtonText}>Intersect</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.analysisToolButton, { backgroundColor: theme.colors.info }]}
          onPress={() => runSpatialAnalysis('union')}
          disabled={analysisInProgress}
        >
          <Plus size={20} color="white" />
          <Text style={styles.analysisToolButtonText}>Union</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.analysisToolButton, { backgroundColor: theme.colors.warning }]}
          onPress={() => runSpatialAnalysis('difference')}
          disabled={analysisInProgress}
        >
          <Scissors size={20} color="white" />
          <Text style={styles.analysisToolButtonText}>Difference</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.analysisToolButton, { backgroundColor: theme.colors.success }]}
          onPress={() => runSpatialAnalysis('centroid')}
          disabled={analysisInProgress}
        >
          <Target size={20} color="white" />
          <Text style={styles.analysisToolButtonText}>Centroid</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.analysisToolButton, { backgroundColor: theme.colors.error }]}
          onPress={() => runSpatialAnalysis('convexHull')}
          disabled={analysisInProgress}
        >
          <Pentagon size={20} color="white" />
          <Text style={styles.analysisToolButtonText}>Convex Hull</Text>
        </TouchableOpacity>
      </View>
      
      {analysisInProgress && (
        <Text style={[styles.analysisStatus, { color: theme.colors.primary }]}>
          Running analysis...
        </Text>
      )}
    </View>
  );

  const renderFeaturesPanel = () => (
    <View style={styles.panelContent}>
      <Text style={[styles.panelSectionTitle, { color: theme.colors.text }]}>
        Features ({drawnFeatures.length}/{maxFeatures})
      </Text>
      
      {/* Export Section */}
      {drawnFeatures.length > 0 && (
        <View style={[styles.exportSection, { backgroundColor: theme.colors.background }]}>
          <Text style={[styles.exportSectionTitle, { color: theme.colors.text }]}>Export Options</Text>
          <View style={styles.exportButtonsRow}>
            <TouchableOpacity
              style={[styles.exportButton, { backgroundColor: theme.colors.primary }]}
              onPress={handleExportGeoJSON}
              disabled={exportInProgress}
            >
              <FileText size={16} color="white" />
              <Text style={styles.exportButtonText}>GeoJSON</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.exportButton, { backgroundColor: theme.colors.secondary }]}
              onPress={handleExportShapefile}
              disabled={exportInProgress}
            >
              <Download size={16} color="white" />
              <Text style={styles.exportButtonText}>Shapefile</Text>
            </TouchableOpacity>
          </View>
          
          {exportInProgress && (
            <Text style={[styles.exportStatus, { color: theme.colors.primary }]}>
              Exporting...
            </Text>
          )}
        </View>
      )}
      
      {drawnFeatures.length === 0 ? (
        <View style={styles.emptyState}>
          <MapPin size={32} color={theme.colors.muted} />
          <Text style={[styles.emptyStateText, { color: theme.colors.muted }]}>
            No features yet. Start by drawing on the map!
          </Text>
        </View>
      ) : (
        drawnFeatures.map((feature) => (
          <View
            key={feature.id}
            style={[
              styles.featureItem,
              selectedFeature === feature.id && styles.featureItemSelected,
              { 
                backgroundColor: selectedFeature === feature.id ? theme.colors.primary + '15' : theme.colors.background,
                borderColor: theme.colors.border,
              }
            ]}
          >
            <View style={[styles.featureIcon, { backgroundColor: feature.properties.color + '25' }]}>
              {feature.type === 'point' && <MapPin size={16} color={feature.properties.color} />}
              {feature.type === 'line' && <Route size={16} color={feature.properties.color} />}
              {feature.type === 'polygon' && <Pentagon size={16} color={feature.properties.color} />}
              {feature.type === 'rectangle' && <Square size={16} color={feature.properties.color} />}
              {feature.type === 'circle' && <Circle size={16} color={feature.properties.color} />}
            </View>
            
            <View style={styles.featureInfo}>
              <Text style={[styles.featureName, { color: theme.colors.text }]}>
                {feature.properties.name}
              </Text>
              <Text style={[styles.featureType, { color: theme.colors.muted }]}>
                {feature.type.toUpperCase()}
              </Text>
              {/* Show measurements if available */}
              {feature.properties.measurements && (
                <Text style={[styles.featureMeasurements, { color: theme.colors.info }]}>
                  {feature.properties.measurements.area && `Area: ${feature.properties.measurements.area.toFixed(2)} m²`}
                  {feature.properties.measurements.length && `Length: ${feature.properties.measurements.length.toFixed(2)} m`}
                  {feature.properties.measurements.radius && `Radius: ${feature.properties.measurements.radius.toFixed(2)} m`}
                </Text>
              )}
            </View>

            <View style={styles.featureActions}>
              <TouchableOpacity
                style={[styles.featureActionButton, { backgroundColor: theme.colors.info }]}
                onPress={() => setSelectedFeature(selectedFeature === feature.id ? null : feature.id)}
              >
                <Navigation size={14} color="white" />
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.featureActionButton, { backgroundColor: theme.colors.error }]}
                onPress={() => deleteFeature(feature.id)}
              >
                <Trash2 size={14} color="white" />
              </TouchableOpacity>
            </View>
          </View>
        ))
      )}
    </View>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Main Toolbar - Fixed at top, outside map */}
      {renderMainToolbar()}

      {/* Drawing Status Bar - Shows when drawing */}
      {renderDrawingStatus()}

      {/* Main Content Area */}
      <View style={styles.contentArea}>
        {/* Map Container - Clean area without overlapping elements */}
        <View style={styles.mapArea}>
          <PlatformMap
            ref={mapRef}
            initialRegion={region}
            mapType={mapLayer}
            drawingMode={drawingTool}
            measurementMode={measurementMode}
            userLocation={userLocation}
            geoFeatures={[...geoFeatures, ...drawnFeatures]}
            onLocationSelect={handleMapPress}
            style={styles.map}
          />
        </View>

        {/* Map Controls - Positioned outside map area */}
        {renderMapControls()}

        {/* Sidebar - Slides from right, outside map */}
        {renderSidebar()}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  mainToolbar: {
    height: TOOLBAR_HEIGHT,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  toolbarContent: {
    flexGrow: 1,
    flexDirection: 'row',
    alignItems: 'center',
    paddingRight: 16,
  },
  toolbarButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginRight: 8,
    borderRadius: 8,
  },
  toolbarButtonActive: {
    backgroundColor: 'rgba(0, 122, 255, 0.1)',
  },
  toolbarButtonText: {
    marginLeft: 6,
    fontSize: 14,
    fontFamily: 'Inter-Medium',
  },
  controlsToggle: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  
  // Drawing Status
  drawingStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  drawingStatusContent: {
    flex: 1,
  },
  drawingStatusText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
  },
  drawingStatusSubtext: {
    fontSize: 12,
    marginTop: 2,
    fontFamily: 'Inter-Regular',
  },
  cancelDrawingButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  cancelButtonText: {
    color: 'white',
    marginLeft: 4,
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },

  // Content Area
  contentArea: {
    flex: 1,
    flexDirection: 'row',
  },
  mapArea: {
    flex: 1,
    position: 'relative',
  },
  map: {
    flex: 1,
  },

  // Map Controls - Positioned outside map area
  mapControlsContainer: {
    position: 'absolute',
    right: 16,
    top: 16,
    borderRadius: 8,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  zoomControls: {
    marginBottom: 8,
  },
  controlButton: {
    width: 44,
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
  },
  locationControl: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },

  // Sidebar
  sidebar: {
    width: SIDEBAR_WIDTH,
    borderLeftWidth: 1,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: -2, height: 0 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  sidebarHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
  },
  sidebarTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
  },
  sidebarContent: {
    flex: 1,
  },

  // Panel Content
  panelContent: {
    padding: 16,
  },
  panelSectionTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 12,
  },
  panelDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    marginBottom: 16,
    lineHeight: 20,
  },
  panelOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    marginBottom: 8,
    borderRadius: 8,
  },
  panelOptionActive: {
    borderWidth: 1,
  },
  panelOptionText: {
    flex: 1,
    marginLeft: 12,
    fontSize: 14,
    fontFamily: 'Inter-Regular',
  },

  // Drawing Tools
  drawingToolsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -4,
  },
  drawingToolButton: {
    width: (SIDEBAR_WIDTH - 48) / 2,
    height: 80,
    margin: 4,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
    borderWidth: 1,
  },
  drawingToolButtonActive: {
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  drawingToolButtonText: {
    marginTop: 8,
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },
  drawingInstructions: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: 12,
    borderRadius: 8,
    marginTop: 16,
  },
  instructionsText: {
    flex: 1,
    marginLeft: 8,
    fontSize: 12,
    lineHeight: 16,
    fontFamily: 'Inter-Regular',
  },

  // Buttons
  measurementButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
  },
  measurementButtonText: {
    color: 'white',
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
  },
  analysisButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
  },
  analysisButtonText: {
    color: 'white',
    marginLeft: 8,
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
  },

  // Features
  emptyState: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  emptyStateText: {
    marginTop: 12,
    fontSize: 14,
    textAlign: 'center',
    fontFamily: 'Inter-Regular',
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    marginBottom: 8,
    borderRadius: 8,
    borderWidth: 1,
  },
  featureItemSelected: {
    borderWidth: 2,
  },
  featureIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  featureInfo: {
    flex: 1,
    marginLeft: 12,
  },
  featureName: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
  },
  featureType: {
    fontSize: 11,
    marginTop: 2,
    fontFamily: 'Inter-Regular',
  },
  featureActions: {
    flexDirection: 'row',
    gap: 6,
  },
  featureActionButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Analysis Panel Styles
  analysisResults: {
    marginBottom: 16,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
  },
  analysisResultsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  analysisResultsTitle: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
  },
  analysisResultItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 8,
    marginBottom: 4,
    borderRadius: 4,
    borderWidth: 1,
  },
  analysisResultType: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },
  analysisResultTime: {
    fontSize: 10,
    fontFamily: 'Inter-Regular',
  },
  analysisToolsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -4,
    marginBottom: 12,
  },
  analysisToolButton: {
    width: (SIDEBAR_WIDTH - 48) / 3,
    height: 60,
    margin: 4,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
  },
  analysisToolButtonText: {
    color: 'white',
    fontSize: 10,
    fontFamily: 'Inter-Medium',
    marginTop: 4,
  },
  analysisStatus: {
    textAlign: 'center',
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    fontStyle: 'italic',
  },

  // Export Section Styles
  exportSection: {
    marginBottom: 16,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
  },
  exportSectionTitle: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 8,
  },
  exportButtonsRow: {
    flexDirection: 'row',
    gap: 8,
  },
  exportButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    borderRadius: 6,
  },
  exportButtonText: {
    color: 'white',
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    marginLeft: 6,
  },
  exportStatus: {
    textAlign: 'center',
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    marginTop: 8,
    fontStyle: 'italic',
  },
  featureMeasurements: {
    fontSize: 10,
    fontFamily: 'Inter-Regular',
    marginTop: 2,
  },
});
