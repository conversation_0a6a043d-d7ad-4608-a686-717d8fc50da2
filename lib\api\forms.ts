import { supabase, TABLES } from '../supabase';
import { BaseApi } from './base';
import { 
  Form, 
  FormInsert, 
  FormUpdate,
  ApiResponse,
  PaginatedResponse
} from '@/types/database';

export interface FormFilters {
  projectId?: string;
  status?: 'draft' | 'published' | 'archived';
  createdBy?: string;
  search?: string;
}

export interface FormField {
  id: string;
  type: 'text' | 'number' | 'date' | 'time' | 'select' | 'multiselect' | 'boolean' | 'file' | 'image' | 'audio' | 'video' | 'location' | 'signature' | 'barcode' | 'rating';
  label: string;
  description?: string;
  required: boolean;
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    message?: string;
  };
  options?: Array<{ label: string; value: string; }>;
  conditional?: {
    field: string;
    operator: 'equals' | 'not_equals' | 'contains' | 'greater_than' | 'less_than';
    value: any;
  };
  metadata?: Record<string, any>;
}

export interface FormSchema {
  fields: FormField[];
  sections?: Array<{
    id: string;
    title: string;
    description?: string;
    fields: string[]; // field IDs
  }>;
  settings?: {
    allowSaveAsDraft?: boolean;
    requireLocation?: boolean;
    allowOfflineSubmission?: boolean;
    maxSubmissions?: number;
    validityPeriod?: {
      start?: string;
      end?: string;
    };
  };
}

/**
 * Forms API Service
 * Handles form creation, management, and publishing
 */
export class FormsApi extends BaseApi {
  constructor() {
    super(TABLES.FORMS);
  }

  /**
   * Create a new form
   */
  async createForm(formData: {
    projectId: string;
    name: string;
    description?: string;
    schema: FormSchema;
    settings?: any;
  }): Promise<ApiResponse<Form>> {
    const userId = await this.getCurrentUserId();
    if (!userId) {
      return {
        data: null,
        error: 'User not authenticated'
      };
    }

    const newForm: FormInsert = {
      project_id: formData.projectId,
      name: formData.name,
      description: formData.description,
      schema: formData.schema as any,
      settings: formData.settings || {},
      created_by: userId,
      version: 1,
      status: 'draft'
    };

    return this.handleResponse<Form>(
      supabase
        .from(TABLES.FORMS)
        .insert(newForm)
        .select(`
          *,
          user_profiles:created_by (
            id,
            full_name,
            email
          ),
          projects (
            id,
            name,
            status
          )
        `)
        .single(),
      'create form'
    );
  }

  /**
   * Get forms with pagination and filters
   */
  async getForms(
    page: number = 1,
    pageSize: number = 20,
    filters: FormFilters = {}
  ): Promise<PaginatedResponse<Form>> {
    const from = (page - 1) * pageSize;
    const to = from + pageSize - 1;

    let query = supabase
      .from(TABLES.FORMS)
      .select(`
        *,
        user_profiles:created_by (
          id,
          full_name,
          email
        ),
        projects (
          id,
          name,
          status
        )
      `, { count: 'exact' })
      .range(from, to)
      .order('updated_at', { ascending: false });

    // Apply filters
    if (filters.projectId) {
      query = query.eq('project_id', filters.projectId);
    }
    if (filters.status) {
      query = query.eq('status', filters.status);
    }
    if (filters.createdBy) {
      query = query.eq('created_by', filters.createdBy);
    }
    if (filters.search) {
      query = query.or(`name.ilike.%${filters.search}%,description.ilike.%${filters.search}%`);
    }

    return this.handlePaginatedResponse<Form>(
      query,
      'get forms',
      page,
      pageSize
    );
  }

  /**
   * Get form by ID
   */
  async getFormById(formId: string): Promise<ApiResponse<Form>> {
    return this.handleResponse<Form>(
      supabase
        .from(TABLES.FORMS)
        .select(`
          *,
          user_profiles:created_by (
            id,
            full_name,
            email
          ),
          projects (
            id,
            name,
            status,
            description
          )
        `)
        .eq('id', formId)
        .single(),
      'get form by id'
    );
  }

  /**
   * Update form
   */
  async updateForm(
    formId: string,
    updates: {
      name?: string;
      description?: string;
      schema?: FormSchema;
      settings?: any;
      status?: 'draft' | 'published' | 'archived';
    }
  ): Promise<ApiResponse<Form>> {
    // If updating schema or critical fields, increment version
    const shouldIncrementVersion = updates.schema !== undefined;
    
    let formUpdate: FormUpdate = {
      ...updates,
      schema: updates.schema as any,
      updated_at: new Date().toISOString()
    };

    if (shouldIncrementVersion) {
      // Get current version
      const { data: currentForm } = await supabase
        .from(TABLES.FORMS)
        .select('version')
        .eq('id', formId)
        .single();
      
      if (currentForm) {
        formUpdate.version = (currentForm.version || 1) + 1;
      }
    }

    return this.handleResponse<Form>(
      supabase
        .from(TABLES.FORMS)
        .update(formUpdate)
        .eq('id', formId)
        .select(`
          *,
          user_profiles:created_by (
            id,
            full_name,
            email
          ),
          projects (
            id,
            name,
            status
          )
        `)
        .single(),
      'update form'
    );
  }

  /**
   * Publish form
   */
  async publishForm(formId: string): Promise<ApiResponse<Form>> {
    const formUpdate: FormUpdate = {
      status: 'published',
      published_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    return this.handleResponse<Form>(
      supabase
        .from(TABLES.FORMS)
        .update(formUpdate)
        .eq('id', formId)
        .select(`
          *,
          user_profiles:created_by (
            id,
            full_name,
            email
          ),
          projects (
            id,
            name,
            status
          )
        `)
        .single(),
      'publish form'
    );
  }

  /**
   * Archive form
   */
  async archiveForm(formId: string): Promise<ApiResponse<Form>> {
    return this.updateForm(formId, { status: 'archived' });
  }

  /**
   * Delete form
   */
  async deleteForm(formId: string): Promise<ApiResponse<null>> {
    return this.handleResponse<null>(
      supabase
        .from(TABLES.FORMS)
        .delete()
        .eq('id', formId),
      'delete form'
    );
  }

  /**
   * Duplicate form
   */
  async duplicateForm(
    formId: string,
    newName: string,
    targetProjectId?: string
  ): Promise<ApiResponse<Form>> {
    try {
      // Get original form
      const originalResponse = await this.getFormById(formId);
      if (!originalResponse.data) {
        return {
          data: null,
          error: 'Original form not found'
        };
      }

      const original = originalResponse.data;

      // Create new form
      const newFormData = {
        projectId: targetProjectId || original.project_id,
        name: newName,
        description: `Copy of ${original.name}`,
        schema: original.schema as FormSchema,
        settings: {
          ...original.settings,
          duplicated_from: formId,
          duplicated_at: new Date().toISOString()
        }
      };

      return this.createForm(newFormData);
    } catch (error) {
      console.error('Duplicate form failed:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to duplicate form'
      };
    }
  }

  /**
   * Get form versions
   */
  async getFormVersions(formId: string): Promise<ApiResponse<Form[]>> {
    // Get the form name to find all versions
    const { data: currentForm } = await supabase
      .from(TABLES.FORMS)
      .select('name, project_id')
      .eq('id', formId)
      .single();

    if (!currentForm) {
      return {
        data: null,
        error: 'Form not found'
      };
    }

    return this.handleResponse<Form[]>(
      supabase
        .from(TABLES.FORMS)
        .select(`
          *,
          user_profiles:created_by (
            id,
            full_name,
            email
          )
        `)
        .eq('project_id', currentForm.project_id)
        .eq('name', currentForm.name)
        .order('version', { ascending: false }),
      'get form versions'
    );
  }

  /**
   * Validate form schema
   */
  validateFormSchema(schema: FormSchema): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check required properties
    if (!schema.fields || !Array.isArray(schema.fields)) {
      errors.push('Form must have fields array');
      return { isValid: false, errors };
    }

    if (schema.fields.length === 0) {
      errors.push('Form must have at least one field');
    }

    // Validate each field
    schema.fields.forEach((field, index) => {
      if (!field.id) {
        errors.push(`Field ${index + 1}: ID is required`);
      }
      if (!field.type) {
        errors.push(`Field ${index + 1}: Type is required`);
      }
      if (!field.label) {
        errors.push(`Field ${index + 1}: Label is required`);
      }

      // Validate field type
      const validTypes = ['text', 'number', 'date', 'time', 'select', 'multiselect', 'boolean', 'file', 'image', 'audio', 'video', 'location', 'signature', 'barcode', 'rating'];
      if (!validTypes.includes(field.type)) {
        errors.push(`Field ${index + 1}: Invalid field type '${field.type}'`);
      }

      // Validate select/multiselect options
      if ((field.type === 'select' || field.type === 'multiselect') && (!field.options || field.options.length === 0)) {
        errors.push(`Field ${index + 1}: Select fields must have options`);
      }

      // Check for duplicate field IDs
      const duplicateIds = schema.fields.filter(f => f.id === field.id);
      if (duplicateIds.length > 1) {
        errors.push(`Field ${index + 1}: Duplicate field ID '${field.id}'`);
      }
    });

    // Validate sections if present
    if (schema.sections) {
      schema.sections.forEach((section, index) => {
        if (!section.id) {
          errors.push(`Section ${index + 1}: ID is required`);
        }
        if (!section.title) {
          errors.push(`Section ${index + 1}: Title is required`);
        }
        if (!section.fields || section.fields.length === 0) {
          errors.push(`Section ${index + 1}: Must have fields`);
        }

        // Check that section fields exist in form fields
        section.fields.forEach(fieldId => {
          if (!schema.fields.find(f => f.id === fieldId)) {
            errors.push(`Section ${index + 1}: Field '${fieldId}' not found in form fields`);
          }
        });
      });
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Get form templates
   */
  async getFormTemplates(): Promise<ApiResponse<Form[]>> {
    // In a real implementation, this would query a templates table
    // For now, we'll return some predefined templates
    const templates: Partial<Form>[] = [
      {
        id: 'template-1',
        name: 'Basic Survey Template',
        description: 'Simple survey with text, number, and select fields',
        schema: {
          fields: [
            {
              id: 'respondent_name',
              type: 'text',
              label: 'Respondent Name',
              required: true
            },
            {
              id: 'age',
              type: 'number',
              label: 'Age',
              required: true,
              validation: { min: 1, max: 120 }
            },
            {
              id: 'satisfaction',
              type: 'select',
              label: 'Satisfaction Level',
              required: true,
              options: [
                { label: 'Very Satisfied', value: 'very_satisfied' },
                { label: 'Satisfied', value: 'satisfied' },
                { label: 'Neutral', value: 'neutral' },
                { label: 'Dissatisfied', value: 'dissatisfied' },
                { label: 'Very Dissatisfied', value: 'very_dissatisfied' }
              ]
            }
          ]
        } as FormSchema
      },
      {
        id: 'template-2',
        name: 'Field Data Collection Template',
        description: 'Template for field research with location and media capture',
        schema: {
          fields: [
            {
              id: 'site_id',
              type: 'text',
              label: 'Site ID',
              required: true
            },
            {
              id: 'coordinates',
              type: 'location',
              label: 'GPS Coordinates',
              required: true
            },
            {
              id: 'photos',
              type: 'image',
              label: 'Site Photos',
              required: false
            },
            {
              id: 'observations',
              type: 'text',
              label: 'Observations',
              required: true
            },
            {
              id: 'weather',
              type: 'select',
              label: 'Weather Conditions',
              required: true,
              options: [
                { label: 'Sunny', value: 'sunny' },
                { label: 'Cloudy', value: 'cloudy' },
                { label: 'Rainy', value: 'rainy' },
                { label: 'Stormy', value: 'stormy' }
              ]
            }
          ]
        } as FormSchema
      }
    ];

    return {
      data: templates as Form[],
      error: null
    };
  }

  /**
   * Export form definition
   */
  async exportForm(formId: string, format: 'json' | 'xlsx' = 'json'): Promise<ApiResponse<any>> {
    try {
      const formResponse = await this.getFormById(formId);
      if (!formResponse.data) {
        return {
          data: null,
          error: 'Form not found'
        };
      }

      const form = formResponse.data;

      if (format === 'json') {
        const exportData = {
          form: {
            name: form.name,
            description: form.description,
            schema: form.schema,
            settings: form.settings,
            version: form.version
          },
          exported_at: new Date().toISOString(),
          exported_by: await this.getCurrentUserId()
        };

        return {
          data: exportData,
          error: null
        };
      }

      // TODO: Implement XLSX export
      return {
        data: null,
        error: 'XLSX export not yet implemented'
      };
    } catch (error) {
      console.error('Export form failed:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to export form'
      };
    }
  }

  /**
   * Import form definition
   */
  async importForm(
    projectId: string,
    formData: any,
    name?: string
  ): Promise<ApiResponse<Form>> {
    try {
      // Validate import data
      if (!formData.form || !formData.form.schema) {
        return {
          data: null,
          error: 'Invalid form data format'
        };
      }

      const schema = formData.form.schema as FormSchema;
      const validation = this.validateFormSchema(schema);
      
      if (!validation.isValid) {
        return {
          data: null,
          error: `Form validation failed: ${validation.errors.join(', ')}`
        };
      }

      // Create form from imported data
      const newFormData = {
        projectId,
        name: name || `${formData.form.name} (Imported)`,
        description: formData.form.description,
        schema,
        settings: {
          ...formData.form.settings,
          imported_at: new Date().toISOString(),
          imported_from: formData.exported_by
        }
      };

      return this.createForm(newFormData);
    } catch (error) {
      console.error('Import form failed:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to import form'
      };
    }
  }

  /**
   * Subscribe to form changes
   */
  subscribeToFormChanges(projectId: string, callback: (payload: any) => void) {
    return this.subscribeToChanges(callback, `project_id=eq.${projectId}`);
  }

  /**
   * Subscribe to specific form changes
   */
  subscribeToFormById(formId: string, callback: (payload: any) => void) {
    return this.subscribeToChanges(callback, `id=eq.${formId}`);
  }
}

// Export singleton instance
export const formsApi = new FormsApi();
export default formsApi;
