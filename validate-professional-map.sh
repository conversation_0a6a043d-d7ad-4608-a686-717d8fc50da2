#!/bin/bash

# Final Validation Script for Professional Map UI Implementation
# Comprehensive testing and status report

echo "🎯 Professional Map UI - Final Validation"
echo "=========================================="

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# Counters
total_checks=0
passed_checks=0
warnings=0

# Function to print status with counters
print_check() {
    total_checks=$((total_checks + 1))
    if [ "$2" = "PASS" ]; then
        echo -e "${GREEN}✅ $1${NC}"
        passed_checks=$((passed_checks + 1))
    elif [ "$2" = "WARN" ]; then
        echo -e "${YELLOW}⚠️  $1${NC}"
        warnings=$((warnings + 1))
    else
        echo -e "${RED}❌ $1${NC}"
    fi
}

# Function to check file exists and has content
check_file_content() {
    if [ -f "$1" ] && [ -s "$1" ]; then
        print_check "$1 exists and has content" "PASS"
        return 0
    else
        print_check "$1 missing or empty" "FAIL"
        return 1
    fi
}

# Function to check for specific content in file
check_content() {
    if [ -f "$2" ] && grep -q "$1" "$2" 2>/dev/null; then
        print_check "$3" "PASS"
        return 0
    else
        print_check "$3" "FAIL" 
        return 1
    fi
}

echo -e "${BLUE}🔍 Phase 1: File Structure Validation${NC}"
echo "======================================"

# Check core component files
check_file_content "components/map/ProfessionalMapUI.tsx"
check_file_content "components/map/OptimizedLeafletMap.web.tsx"  
check_file_content "components/map/EnhancedMapIntegration.tsx"
check_file_content "components/map/MapScreen.tsx"
check_file_content "components/map/index.ts"

# Check documentation files
check_file_content "PROFESSIONAL_MAP_IMPLEMENTATION_GUIDE.md"
check_file_content "QUICK_START_GUIDE.md"

# Check style files
check_file_content "components/map/professionalMapStyles.css"

# Check test scripts
check_file_content "test-professional-map-implementation.sh"
check_file_content "test-professional-map-implementation.bat"

echo ""
echo -e "${BLUE}🔧 Phase 2: Component Integration Analysis${NC}"
echo "=========================================="

# Check MapScreen integration
check_content "EnhancedMapIntegration" "components/map/MapScreen.tsx" "MapScreen uses EnhancedMapIntegration"
check_content "import.*EnhancedMapIntegration" "components/map/MapScreen.tsx" "EnhancedMapIntegration properly imported"

# Check component exports
check_content "export.*ProfessionalMapUI" "components/map/index.ts" "ProfessionalMapUI exported in index"
check_content "export.*EnhancedMapIntegration" "components/map/index.ts" "EnhancedMapIntegration exported in index"
check_content "export.*OptimizedLeafletMap" "components/map/index.ts" "OptimizedLeafletMap exported in index"

echo ""
echo -e "${BLUE}🎨 Phase 3: UI Architecture Validation${NC}"
echo "====================================="

# Check ProfessionalMapUI architecture
check_content "TOOLBAR_HEIGHT.*=.*60" "components/map/ProfessionalMapUI.tsx" "Proper toolbar height constant"
check_content "SIDEBAR_WIDTH.*=.*320" "components/map/ProfessionalMapUI.tsx" "Proper sidebar width constant"
check_content "renderMainToolbar" "components/map/ProfessionalMapUI.tsx" "Main toolbar rendering method"
check_content "renderSidebar" "components/map/ProfessionalMapUI.tsx" "Sidebar rendering method"
check_content "renderMapControls" "components/map/ProfessionalMapUI.tsx" "Map controls rendering method"
check_content "contentArea.*flex.*1" "components/map/ProfessionalMapUI.tsx" "Proper layout flex structure"

# Check drawing tool implementation
check_content "DrawingTool.*=.*'none'.*'point'.*'line'.*'polygon'" "components/map/ProfessionalMapUI.tsx" "Drawing tool types defined"
check_content "selectDrawingTool" "components/map/ProfessionalMapUI.tsx" "Drawing tool selection method"
check_content "cancelDrawing" "components/map/ProfessionalMapUI.tsx" "Drawing cancellation method"
check_content "handleMapPress" "components/map/ProfessionalMapUI.tsx" "Map press handler"

echo ""
echo -e "${BLUE}🌐 Phase 4: Leaflet Integration Validation${NC}"
echo "========================================"

# Check OptimizedLeafletMap implementation
check_content "loadLeafletLibraries" "components/map/OptimizedLeafletMap.web.tsx" "Leaflet library loading"
check_content "initializeMap" "components/map/OptimizedLeafletMap.web.tsx" "Map initialization method"
check_content "createBaseLayers" "components/map/OptimizedLeafletMap.web.tsx" "Base layers creation"
check_content "updateDrawingMode" "components/map/OptimizedLeafletMap.web.tsx" "Drawing mode updates"
check_content "handleDrawCreated" "components/map/OptimizedLeafletMap.web.tsx" "Draw creation handler"
check_content "convertLayerToFeature" "components/map/OptimizedLeafletMap.web.tsx" "Layer to feature conversion"

# Check Leaflet CDN integration
check_content "unpkg.com/leaflet" "components/map/OptimizedLeafletMap.web.tsx" "Leaflet CDN loading"
check_content "leaflet.draw" "components/map/OptimizedLeafletMap.web.tsx" "Leaflet Draw plugin"

echo ""
echo -e "${BLUE}💾 Phase 5: Data Management Validation${NC}"
echo "====================================="

# Check EnhancedMapIntegration storage
check_content "AsyncStorage" "components/map/EnhancedMapIntegration.tsx" "AsyncStorage integration"
check_content "loadFeatures" "components/map/EnhancedMapIntegration.tsx" "Feature loading method"
check_content "saveFeatures" "components/map/EnhancedMapIntegration.tsx" "Feature saving method"
check_content "handleFeatureCreated" "components/map/EnhancedMapIntegration.tsx" "Feature creation handler"
check_content "handleFeatureDeleted" "components/map/EnhancedMapIntegration.tsx" "Feature deletion handler"
check_content "STORAGE_KEY.*=.*'fieldsyncpro_enhanced_features'" "components/map/EnhancedMapIntegration.tsx" "Storage key definition"

# Check feature validation
check_content "maxFeatures.*>=.*features.length" "components/map/EnhancedMapIntegration.tsx" "Feature limit validation"
check_content "Alert.alert.*Feature Limit" "components/map/EnhancedMapIntegration.tsx" "Feature limit user feedback"

echo ""
echo -e "${BLUE}🎯 Phase 6: TypeScript & Code Quality${NC}"
echo "=================================="

# Check TypeScript interfaces
check_content "interface.*Region" "components/map/index.ts" "Region interface defined"
check_content "interface.*DrawnFeature" "components/map/index.ts" "DrawnFeature interface defined"
check_content "interface.*StoredFeature" "components/map/index.ts" "StoredFeature interface defined"
check_content "type.*DrawingTool" "components/map/index.ts" "DrawingTool type defined"
check_content "type.*MapLayer" "components/map/index.ts" "MapLayer type defined"

# Check utility functions
check_content "createFeatureId" "components/map/index.ts" "Feature ID creation utility"
check_content "validateFeature" "components/map/index.ts" "Feature validation utility"
check_content "convertFeatureToGeoJSON" "components/map/index.ts" "GeoJSON conversion utility"

echo ""
echo -e "${BLUE}🎨 Phase 7: Styling & CSS Validation${NC}"
echo "================================="

# Check CSS file structure
check_content "Professional Map UI Styles" "components/map/professionalMapStyles.css" "CSS header comment"
check_content "leaflet-container" "components/map/professionalMapStyles.css" "Leaflet container styles"
check_content "leaflet-draw-toolbar" "components/map/professionalMapStyles.css" "Drawing toolbar styles"
check_content "leaflet-popup-content-wrapper" "components/map/professionalMapStyles.css" "Popup styling"
check_content "@media.*max-width.*768px" "components/map/professionalMapStyles.css" "Mobile responsive styles"
check_content "prefers-color-scheme.*dark" "components/map/professionalMapStyles.css" "Dark mode support"

echo ""
echo -e "${BLUE}📋 Phase 8: Documentation Validation${NC}"
echo "=================================="

# Check implementation guide
check_content "Professional Map UI Implementation" "PROFESSIONAL_MAP_IMPLEMENTATION_GUIDE.md" "Implementation guide title"
check_content "Key Problems Solved" "PROFESSIONAL_MAP_IMPLEMENTATION_GUIDE.md" "Problems solved section"
check_content "Architecture Overview" "PROFESSIONAL_MAP_IMPLEMENTATION_GUIDE.md" "Architecture documentation"
check_content "Testing Strategy" "PROFESSIONAL_MAP_IMPLEMENTATION_GUIDE.md" "Testing documentation"

# Check quick start guide
check_content "Quick Start Guide" "QUICK_START_GUIDE.md" "Quick start guide title"
check_content "Getting Started" "QUICK_START_GUIDE.md" "Getting started section"
check_content "Using the Map Interface" "QUICK_START_GUIDE.md" "Usage instructions"
check_content "Troubleshooting" "QUICK_START_GUIDE.md" "Troubleshooting section"

echo ""
echo -e "${BLUE}🔄 Phase 9: Integration Points Check${NC}"
echo "================================="

# Check proper component usage
check_content "ProfessionalMapUI" "components/map/EnhancedMapIntegration.tsx" "Integration uses ProfessionalMapUI"
check_content "OptimizedLeafletMap" "components/map/ProfessionalMapUI.tsx" "UI uses OptimizedLeafletMap"

# Check theme integration
check_content "useTheme" "components/map/ProfessionalMapUI.tsx" "Theme hook usage in UI"
check_content "useTheme" "components/map/OptimizedLeafletMap.web.tsx" "Theme hook usage in Leaflet"
check_content "useTheme" "components/map/EnhancedMapIntegration.tsx" "Theme hook usage in integration"

# Check prop drilling and data flow
check_content "onFeatureCreated.*onFeatureDeleted" "components/map/ProfessionalMapUI.tsx" "Proper prop drilling"
check_content "handleFeatureCreated.*handleFeatureDeleted" "components/map/EnhancedMapIntegration.tsx" "Handler implementation"

echo ""
echo -e "${BLUE}🧪 Phase 10: Error Handling & Safety${NC}"
echo "=================================="

# Check error boundaries and handling
check_content "try.*catch" "components/map/OptimizedLeafletMap.web.tsx" "Error handling in Leaflet integration"
check_content "try.*catch" "components/map/EnhancedMapIntegration.tsx" "Error handling in data layer"
check_content "Alert.alert.*Error" "components/map/EnhancedMapIntegration.tsx" "User error feedback"

# Check cleanup and memory management
check_content "cleanup.*remove" "components/map/OptimizedLeafletMap.web.tsx" "Proper cleanup implementation"
check_content "useEffect.*return.*=>" "components/map/OptimizedLeafletMap.web.tsx" "Effect cleanup"

echo ""
echo -e "${PURPLE}📊 VALIDATION SUMMARY${NC}"
echo "===================="

# Calculate success rate
success_rate=$((passed_checks * 100 / total_checks))

echo -e "Total Checks: ${CYAN}$total_checks${NC}"
echo -e "Passed: ${GREEN}$passed_checks${NC}"
echo -e "Warnings: ${YELLOW}$warnings${NC}"
echo -e "Failed: ${RED}$((total_checks - passed_checks - warnings))${NC}"
echo -e "Success Rate: ${CYAN}$success_rate%${NC}"

echo ""
if [ $success_rate -ge 90 ]; then
    echo -e "${GREEN}🎉 EXCELLENT! Implementation is ready for production${NC}"
    echo -e "${GREEN}✨ Your professional map UI is fully functional${NC}"
elif [ $success_rate -ge 80 ]; then
    echo -e "${YELLOW}⚡ GOOD! Implementation is mostly complete${NC}"
    echo -e "${YELLOW}🔧 Minor fixes may be needed${NC}"
elif [ $success_rate -ge 70 ]; then
    echo -e "${YELLOW}⚠️  FAIR! Implementation needs some attention${NC}"
    echo -e "${YELLOW}🛠️  Review failed checks above${NC}"
else
    echo -e "${RED}❌ NEEDS WORK! Implementation has significant issues${NC}"
    echo -e "${RED}🚨 Review and fix failed components${NC}"
fi

echo ""
echo -e "${BLUE}🚀 NEXT STEPS${NC}"
echo "============"

if [ $success_rate -ge 90 ]; then
    echo "1. Start development server: npm run web"
    echo "2. Test drawing tools in browser"
    echo "3. Verify feature storage functionality"
    echo "4. Test on mobile devices"
    echo "5. Deploy to staging environment"
elif [ $success_rate -ge 80 ]; then
    echo "1. Review warnings and failed checks above"
    echo "2. Fix any missing components or functions"
    echo "3. Re-run this validation script"
    echo "4. Test core functionality"
elif [ $success_rate -ge 70 ]; then
    echo "1. Address failed checks systematically"
    echo "2. Check TypeScript compilation"
    echo "3. Verify all imports and exports"
    echo "4. Re-run validation after fixes"
else
    echo "1. Review implementation guide carefully"
    echo "2. Check if all files were created correctly"
    echo "3. Verify component structure and imports"
    echo "4. Consider regenerating components"
fi

echo ""
echo -e "${CYAN}📚 DOCUMENTATION AVAILABLE${NC}"
echo "========================="
echo "• PROFESSIONAL_MAP_IMPLEMENTATION_GUIDE.md - Complete technical guide"
echo "• QUICK_START_GUIDE.md - User-friendly getting started guide"
echo "• components/map/professionalMapStyles.css - Professional styling"
echo "• components/map/index.ts - Type definitions and utilities"

echo ""
echo -e "${PURPLE}🔧 DEVELOPMENT COMMANDS${NC}"
echo "===================="
echo "npm run web      # Start web development server"
echo "npm run android  # Test on Android device/emulator"
echo "npm run ios      # Test on iOS device/simulator"
echo "npm start        # Standard React Native development"

echo ""
echo -e "${GREEN}🎯 Professional Map UI Implementation Validation Complete!${NC}"

# Return appropriate exit code
if [ $success_rate -ge 90 ]; then
    exit 0
elif [ $success_rate -ge 70 ]; then
    exit 1
else
    exit 2
fi
