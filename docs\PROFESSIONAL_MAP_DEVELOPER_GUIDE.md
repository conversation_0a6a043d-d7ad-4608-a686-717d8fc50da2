# Professional Map Layout - Developer Quick Reference

## Component Import

```typescript
import { ProfessionalMapLayout } from '@/components/map';
```

## Basic Usage

```tsx
<ProfessionalMapLayout
  initialRegion={{
    latitude: 37.78825,
    longitude: -122.4324,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  }}
  onLocationSelect={(location) => console.log(location)}
  enableDrawing={true}
  showAnalysisTools={true}
/>
```

## Props Reference

### Required Props
None - all props are optional with sensible defaults

### Optional Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `initialRegion` | `Region` | San Francisco | Initial map viewport |
| `onLocationSelect` | `Function` | - | Callback when location is selected |
| `geoFeatures` | `Array` | `[]` | Array of features to display |
| `showAnalysisTools` | `boolean` | `true` | Show spatial analysis tools |
| `enableDrawing` | `boolean` | `true` | Enable drawing tools |
| `enableMeasurement` | `boolean` | `true` | Enable measurement tools |
| `enableGeofencing` | `boolean` | `true` | Enable geofencing tools |
| `enableRouting` | `boolean` | `true` | Enable route analysis |
| `enableHeatmap` | `boolean` | `true` | Enable heatmap layer |
| `enableClustering` | `boolean` | `true` | Enable point clustering |
| `offlineMode` | `boolean` | `false` | Enable offline mode |
| `onFeatureCreated` | `Function` | - | Callback when feature is created |
| `onFeatureDeleted` | `Function` | - | Callback when feature is deleted |
| `onExportFeatures` | `Function` | - | Callback for export action |
| `onClearAllFeatures` | `Function` | - | Callback for clear all action |
| `featureStats` | `Object` | - | Feature statistics to display |
| `maxFeatures` | `number` | `100` | Maximum features allowed |

## Feature Types

### Drawing Modes
- `point` - Single point markers
- `line` - Connected line segments
- `polygon` - Closed polygon shapes
- `circle` - Circle shapes (coming soon)
- `rectangle` - Rectangle shapes (coming soon)
- `freehand` - Freehand drawing (coming soon)

### Map Layers
- `standard` - Standard street map
- `satellite` - Satellite imagery
- `terrain` - Terrain/topographic
- `hybrid` - Satellite + labels

## Events

### Location Selection
```tsx
onLocationSelect={(location) => {
  console.log('Selected:', location.latitude, location.longitude);
}}
```

### Feature Creation
```tsx
onFeatureCreated={(feature) => {
  console.log('Created:', feature.type, feature.id);
  // Save to database, sync with server, etc.
}}
```

### Feature Deletion
```tsx
onFeatureDeleted={(featureId) => {
  console.log('Deleted:', featureId);
  // Remove from database, sync deletion, etc.
}}
```

## UI Panels

The component includes these panels (no overlapping):

1. **Layers Panel** - Map type selection and data layers
2. **Drawing Tools** - Feature creation tools
3. **Spatial Analysis** - Geofencing, routing, buffer analysis
4. **Feature Manager** - View/edit/delete features
5. **Settings** - Map preferences and data management

## Status Indicators

The map shows these status badges:
- Offline mode indicator
- Active drawing mode
- Measurement mode
- GPS status

## Best Practices

1. **Performance**
   - Limit features to reasonable amounts (use `maxFeatures`)
   - Enable clustering for many points
   - Use offline mode when appropriate

2. **User Experience**
   - Provide feedback via callbacks
   - Show loading states
   - Handle errors gracefully

3. **Data Management**
   - Persist features to storage
   - Implement proper sync logic
   - Validate feature data

## Example: Complete Implementation

```tsx
import React, { useState } from 'react';
import { View, Alert } from 'react-native';
import { ProfessionalMapLayout } from '@/components/map';
import AsyncStorage from '@react-native-async-storage/async-storage';

export function MyMapScreen() {
  const [features, setFeatures] = useState([]);

  const handleFeatureCreated = async (feature) => {
    // Add to state
    const updatedFeatures = [...features, feature];
    setFeatures(updatedFeatures);
    
    // Persist to storage
    await AsyncStorage.setItem('map_features', JSON.stringify(updatedFeatures));
    
    // Sync with server
    await syncFeatureToServer(feature);
  };

  const handleExport = async () => {
    const geoJSON = {
      type: 'FeatureCollection',
      features: features.map(f => ({
        type: 'Feature',
        geometry: {
          type: f.type === 'point' ? 'Point' : 
                f.type === 'line' ? 'LineString' : 'Polygon',
          coordinates: f.coordinates,
        },
        properties: f.properties,
      })),
    };
    
    // Export logic here
    await shareGeoJSON(geoJSON);
  };

  return (
    <View style={{ flex: 1 }}>
      <ProfessionalMapLayout
        geoFeatures={features}
        onFeatureCreated={handleFeatureCreated}
        onFeatureDeleted={handleFeatureDeleted}
        onExportFeatures={handleExport}
        enableDrawing={true}
        showAnalysisTools={true}
        maxFeatures={500}
        featureStats={{
          total: features.length,
          points: features.filter(f => f.type === 'point').length,
          lines: features.filter(f => f.type === 'line').length,
          polygons: features.filter(f => f.type === 'polygon').length,
        }}
      />
    </View>
  );
}
```

## Troubleshooting

### UI Elements Overlapping
This should not happen with ProfessionalMapLayout. If it does:
1. Check z-index values haven't been overridden
2. Ensure using latest version
3. Clear caches and rebuild

### Performance Issues
1. Reduce number of features
2. Enable clustering
3. Use offline mode
4. Optimize feature rendering

### Drawing Not Working
1. Ensure `enableDrawing={true}`
2. Check drawing mode is selected
3. Verify touch events are working

---

For more details, see the component source: `components/map/ProfessionalMapLayout.tsx`
