@echo off
echo 🚀 Starting FieldSync Pro Development Environment
echo ================================================

:: Check if we're in the right directory
if not exist "package.json" (
    echo ❌ Error: package.json not found. Please run this from the project root directory.
    pause
    exit /b 1
)

:: Check if project name matches
findstr /C:"fieldsync-pro" package.json >nul
if errorlevel 1 (
    echo ⚠️  Warning: This doesn't appear to be the FieldSync Pro project.
    set /p continue="Continue anyway? (y/N): "
    if /i not "%continue%"=="y" (
        exit /b 1
    )
)

echo ✅ Project validation passed

:: Clean up any existing processes
echo 🧹 Cleaning up existing processes...
taskkill /f /im "node.exe" 2>nul
taskkill /f /im "expo.exe" 2>nul

:: Install dependencies if node_modules doesn't exist
if not exist "node_modules" (
    echo 📦 Installing dependencies...
    npm install
)

:: Clear Metro cache and start fresh
echo 🔄 Starting with cleared cache...
npx expo start --clear

echo.
echo 🎉 Development server should be starting!
echo.
echo 📱 Scan the QR code with Expo Go app (mobile)
echo 🌐 Press 'w' to open in web browser  
echo 💡 Press 'r' to reload
echo 🔧 Press 'd' to open developer menu
echo.
pause
