/**
 * Advanced Spatial Analysis Algorithms
 * Implementation of GIS spatial operations for the Advanced Map feature
 */

import type { Coordinate, GISFeature, AnalysisResult } from '@/types/gis';

// Turf.js-inspired spatial operations implemented for React Native

export interface BufferOptions {
  distance: number;
  unit: 'meters' | 'kilometers' | 'feet' | 'miles';
  steps?: number;
  dissolve?: boolean;
}

export interface ClipOptions {
  preserveAttributes?: boolean;
}

export interface DissolveOptions {
  field?: string;
  aggregateFields?: string[];
  multipart?: boolean;
}

export interface SpatialJoinOptions {
  operation: 'intersects' | 'contains' | 'within' | 'touches' | 'crosses';
  joinType: 'one_to_one' | 'one_to_many';
  keepAllTargets?: boolean;
}

/**
 * Creates buffer zones around point, line, or polygon features
 */
export const createBuffer = async (
  features: GISFeature[],
  options: BufferOptions
): Promise<AnalysisResult> => {
  const startTime = Date.now();
  
  try {
    const { distance, unit, steps = 16, dissolve = false } = options;
    
    // Convert distance to meters
    const distanceInMeters = convertToMeters(distance, unit);
    
    const bufferedFeatures: GISFeature[] = [];
    
    for (const feature of features) {
      const bufferedCoordinates = generateBufferCoordinates(
        feature.coordinates,
        feature.type,
        distanceInMeters,
        steps
      );
      
      const bufferedFeature: GISFeature = {
        id: `${feature.id}_buffer`,
        type: 'polygon',
        coordinates: bufferedCoordinates,
        properties: {
          ...feature.properties,
          originalId: feature.id,
          bufferDistance: distance,
          bufferUnit: unit,
        },
        style: {
          fillColor: '#3B82F620',
          strokeColor: '#3B82F6',
          strokeWidth: 2,
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      
      bufferedFeatures.push(bufferedFeature);
    }
    
    // Apply dissolve if requested
    const finalFeatures = dissolve 
      ? await dissolveOverlappingBuffers(bufferedFeatures)
      : bufferedFeatures;
    
    const endTime = Date.now();
    
    return {
      id: `buffer_${Date.now()}`,
      type: 'buffer',
      name: `Buffer Analysis (${distance} ${unit})`,
      status: 'completed',
      progress: 100,
      parameters: options,
      result: {
        features: finalFeatures,
        inputCount: features.length,
        outputCount: finalFeatures.length,
      },
      startTime: new Date(startTime).toISOString(),
      endTime: new Date(endTime).toISOString(),
      executionTime: endTime - startTime,
      outputLayers: [`buffer_layer_${Date.now()}`],
    };
  } catch (error) {
    return {
      id: `buffer_${Date.now()}`,
      type: 'buffer',
      name: 'Buffer Analysis',
      status: 'failed',
      progress: 0,
      parameters: options,
      error: error instanceof Error ? error.message : 'Unknown error',
      startTime: new Date(startTime).toISOString(),
    };
  }
};

/**
 * Clips features to a boundary polygon
 */
export const clipFeatures = async (
  inputFeatures: GISFeature[],
  clipBoundary: GISFeature,
  options: ClipOptions = {}
): Promise<AnalysisResult> => {
  const startTime = Date.now();
  
  try {
    const { preserveAttributes = true } = options;
    const clippedFeatures: GISFeature[] = [];
    
    for (const feature of inputFeatures) {
      const intersection = calculateIntersection(feature, clipBoundary);
      
      if (intersection && intersection.coordinates.length > 0) {
        const clippedFeature: GISFeature = {
          id: `${feature.id}_clipped`,
          type: feature.type,
          coordinates: intersection.coordinates,
          properties: preserveAttributes 
            ? { ...feature.properties, clippedFrom: feature.id }
            : { clippedFrom: feature.id },
          style: feature.style,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        
        clippedFeatures.push(clippedFeature);
      }
    }
    
    const endTime = Date.now();
    
    return {
      id: `clip_${Date.now()}`,
      type: 'clip',
      name: 'Clip Analysis',
      status: 'completed',
      progress: 100,
      parameters: options,
      result: {
        features: clippedFeatures,
        inputCount: inputFeatures.length,
        outputCount: clippedFeatures.length,
      },
      startTime: new Date(startTime).toISOString(),
      endTime: new Date(endTime).toISOString(),
      executionTime: endTime - startTime,
      outputLayers: [`clipped_layer_${Date.now()}`],
    };
  } catch (error) {
    return {
      id: `clip_${Date.now()}`,
      type: 'clip',
      name: 'Clip Analysis',
      status: 'failed',
      progress: 0,
      parameters: options,
      error: error instanceof Error ? error.message : 'Unknown error',
      startTime: new Date(startTime).toISOString(),
    };
  }
};

// Helper functions

function convertToMeters(distance: number, unit: string): number {
  switch (unit) {
    case 'kilometers':
      return distance * 1000;
    case 'feet':
      return distance * 0.3048;
    case 'miles':
      return distance * 1609.34;
    default:
      return distance;
  }
}

function generateBufferCoordinates(
  coordinates: Coordinate[],
  geometryType: string,
  distance: number,
  steps: number
): Coordinate[] {
  // Simplified buffer generation
  // In a real implementation, this would use more sophisticated algorithms
  
  if (geometryType === 'point' && coordinates.length > 0) {
    const center = coordinates[0];
    const bufferCoords: Coordinate[] = [];
    
    for (let i = 0; i <= steps; i++) {
      const angle = (i / steps) * 2 * Math.PI;
      const lat = center.latitude + (distance / 111320) * Math.cos(angle);
      const lng = center.longitude + (distance / (111320 * Math.cos(center.latitude * Math.PI / 180))) * Math.sin(angle);
      
      bufferCoords.push({ latitude: lat, longitude: lng });
    }
    
    return bufferCoords;
  }
  
  // For lines and polygons, implement more complex buffering
  return coordinates;
}

function calculateDistance(point1: Coordinate, point2: Coordinate): number {
  const R = 6371000; // Earth's radius in meters
  const lat1Rad = (point1.latitude * Math.PI) / 180;
  const lat2Rad = (point2.latitude * Math.PI) / 180;
  const deltaLatRad = ((point2.latitude - point1.latitude) * Math.PI) / 180;
  const deltaLngRad = ((point2.longitude - point1.longitude) * Math.PI) / 180;

  const a = 
    Math.sin(deltaLatRad / 2) * Math.sin(deltaLatRad / 2) +
    Math.cos(lat1Rad) * Math.cos(lat2Rad) *
    Math.sin(deltaLngRad / 2) * Math.sin(deltaLngRad / 2);
  
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
}

function calculateIntersection(feature1: GISFeature, feature2: GISFeature): { coordinates: Coordinate[] } | null {
  // Simplified intersection calculation
  // In a real implementation, this would use robust geometric algorithms
  
  // For demo purposes, return the first feature if they potentially overlap
  const centroid1 = getCentroid(feature1);
  const centroid2 = getCentroid(feature2);
  const distance = calculateDistance(centroid1, centroid2);
  
  if (distance < 1000) { // Within 1km - simplified check
    return { coordinates: feature1.coordinates };
  }
  
  return null;
}

function getCentroid(feature: GISFeature): Coordinate {
  if (feature.coordinates.length === 0) {
    return { latitude: 0, longitude: 0 };
  }
  
  if (feature.type === 'point') {
    return feature.coordinates[0];
  }
  
  // Calculate centroid for lines and polygons
  let latSum = 0;
  let lngSum = 0;
  
  for (const coord of feature.coordinates) {
    latSum += coord.latitude;
    lngSum += coord.longitude;
  }
  
  return {
    latitude: latSum / feature.coordinates.length,
    longitude: lngSum / feature.coordinates.length,
  };
}

async function dissolveOverlappingBuffers(features: GISFeature[]): Promise<GISFeature[]> {
  // Simplified dissolve operation
  // In a real implementation, this would use proper geometric union
  
  if (features.length <= 1) return features;
  
  // For demo purposes, just return the first feature with combined properties
  const dissolvedFeature: GISFeature = {
    ...features[0],
    id: `dissolved_buffer_${Date.now()}`,
    properties: {
      ...features[0].properties,
      dissolvedCount: features.length,
      dissolvedIds: features.map(f => f.id),
    },
  };
  
  return [dissolvedFeature];
}

/**
 * Dissolves features based on a common attribute or spatial adjacency
 */
export const dissolveFeatures = async (
  features: GISFeature[],
  options: DissolveOptions = {}
): Promise<AnalysisResult> => {
  const startTime = Date.now();
  
  try {
    const { field, aggregateFields = [], multipart = false } = options;
    
    // Group features by dissolve field if specified
    const groups = field 
      ? groupFeaturesByField(features, field)
      : [features]; // Single group if no field specified
    
    const dissolvedFeatures: GISFeature[] = [];
    
    for (const group of groups) {
      if (group.length === 0) continue;
      
      // Calculate union of geometries in the group
      const unionGeometry = calculateUnion(group);
      
      // Aggregate properties
      const aggregatedProperties = aggregateProperties(group, aggregateFields);
      
      const dissolvedFeature: GISFeature = {
        id: `dissolved_${Date.now()}_${dissolvedFeatures.length}`,
        type: unionGeometry.type,
        coordinates: unionGeometry.coordinates,
        properties: {
          ...aggregatedProperties,
          dissolveField: field || 'all',
          originalCount: group.length,
          originalIds: group.map(f => f.id),
        },
        style: {
          fillColor: '#10B98160',
          strokeColor: '#10B981',
          strokeWidth: 2,
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      
      dissolvedFeatures.push(dissolvedFeature);
    }
    
    const endTime = Date.now();
    
    return {
      id: `dissolve_${Date.now()}`,
      type: 'dissolve',
      name: `Dissolve Analysis${field ? ` by ${field}` : ''}`,
      status: 'completed',
      progress: 100,
      parameters: options,
      result: {
        features: dissolvedFeatures,
        inputCount: features.length,
        outputCount: dissolvedFeatures.length,
        groups: groups.length,
      },
      startTime: new Date(startTime).toISOString(),
      endTime: new Date(endTime).toISOString(),
      executionTime: endTime - startTime,
      outputLayers: [`dissolved_layer_${Date.now()}`],
    };
  } catch (error) {
    return {
      id: `dissolve_${Date.now()}`,
      type: 'dissolve',
      name: 'Dissolve Analysis',
      status: 'failed',
      progress: 0,
      parameters: options,
      error: error instanceof Error ? error.message : 'Unknown error',
      startTime: new Date(startTime).toISOString(),
    };
  }
};

/**
 * Performs spatial join between target and join features
 */
export const spatialJoin = async (
  targetFeatures: GISFeature[],
  joinFeatures: GISFeature[],
  options: SpatialJoinOptions
): Promise<AnalysisResult> => {
  const startTime = Date.now();
  
  try {
    const { operation, joinType, keepAllTargets = true } = options;
    const joinedFeatures: GISFeature[] = [];
    
    for (const targetFeature of targetFeatures) {
      const matchingJoinFeatures = findSpatialMatches(
        targetFeature,
        joinFeatures,
        operation
      );
      
      if (matchingJoinFeatures.length > 0) {
        if (joinType === 'one_to_one') {
          // Take the first match
          const joinProperties = aggregateJoinProperties([matchingJoinFeatures[0]]);
          
          const joinedFeature: GISFeature = {
            ...targetFeature,
            id: `${targetFeature.id}_joined`,
            properties: {
              ...targetFeature.properties,
              ...joinProperties,
              joinCount: 1,
              joinOperation: operation,
            },
            updatedAt: new Date().toISOString(),
          };
          
          joinedFeatures.push(joinedFeature);
        } else {
          // one_to_many: create feature for each match
          for (const [index, joinFeature] of matchingJoinFeatures.entries()) {
            const joinedFeature: GISFeature = {
              ...targetFeature,
              id: `${targetFeature.id}_joined_${index}`,
              properties: {
                ...targetFeature.properties,
                ...joinFeature.properties,
                joinIndex: index,
                joinOperation: operation,
              },
              updatedAt: new Date().toISOString(),
            };
            
            joinedFeatures.push(joinedFeature);
          }
        }
      } else if (keepAllTargets) {
        // Keep target feature even without matches
        const joinedFeature: GISFeature = {
          ...targetFeature,
          id: `${targetFeature.id}_no_join`,
          properties: {
            ...targetFeature.properties,
            joinCount: 0,
            joinOperation: operation,
          },
          updatedAt: new Date().toISOString(),
        };
        
        joinedFeatures.push(joinedFeature);
      }
    }
    
    const endTime = Date.now();
    
    return {
      id: `spatial_join_${Date.now()}`,
      type: 'spatial_join',
      name: `Spatial Join (${operation})`,
      status: 'completed',
      progress: 100,
      parameters: options,
      result: {
        features: joinedFeatures,
        inputCount: targetFeatures.length,
        outputCount: joinedFeatures.length,
        joinFeatureCount: joinFeatures.length,
      },
      startTime: new Date(startTime).toISOString(),
      endTime: new Date(endTime).toISOString(),
      executionTime: endTime - startTime,
      outputLayers: [`spatial_join_layer_${Date.now()}`],
    };
  } catch (error) {
    return {
      id: `spatial_join_${Date.now()}`,
      type: 'spatial_join',
      name: 'Spatial Join',
      status: 'failed',
      progress: 0,
      parameters: options,
      error: error instanceof Error ? error.message : 'Unknown error',
      startTime: new Date(startTime).toISOString(),
    };
  }
};

/**
 * Analyzes proximity relationships between features
 */
export const proximityAnalysis = async (
  sourceFeatures: GISFeature[],
  targetFeatures: GISFeature[],
  maxDistance: number,
  unit: 'meters' | 'kilometers' | 'feet' | 'miles' = 'meters'
): Promise<AnalysisResult> => {
  const startTime = Date.now();
  
  try {
    const maxDistanceInMeters = convertToMeters(maxDistance, unit);
    const proximityResults: Array<{
      sourceFeature: GISFeature;
      nearestTarget: GISFeature;
      distance: number;
      bearing: number;
    }> = [];
    
    for (const sourceFeature of sourceFeatures) {
      const sourceCentroid = getCentroid(sourceFeature);
      let nearestTarget: GISFeature | null = null;
      let minDistance = Infinity;
      let bearing = 0;
      
      for (const targetFeature of targetFeatures) {
        const targetCentroid = getCentroid(targetFeature);
        const distance = calculateDistance(sourceCentroid, targetCentroid);
        
        if (distance <= maxDistanceInMeters && distance < minDistance) {
          minDistance = distance;
          nearestTarget = targetFeature;
          bearing = calculateBearing(sourceCentroid, targetCentroid);
        }
      }
      
      if (nearestTarget) {
        proximityResults.push({
          sourceFeature,
          nearestTarget,
          distance: minDistance,
          bearing,
        });
      }
    }
    
    // Create result features showing proximity relationships
    const resultFeatures: GISFeature[] = proximityResults.map((result, index) => ({
      id: `proximity_${Date.now()}_${index}`,
      type: 'line',
      coordinates: [
        getCentroid(result.sourceFeature),
        getCentroid(result.nearestTarget),
      ],
      properties: {
        sourceId: result.sourceFeature.id,
        targetId: result.nearestTarget.id,
        distance: result.distance,
        bearing: result.bearing,
        distanceFormatted: formatDistance(result.distance),
        bearingFormatted: formatBearing(result.bearing),
      },
      style: {
        strokeColor: '#F59E0B',
        strokeWidth: 2,
        strokeDashArray: [5, 5],
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }));
    
    const endTime = Date.now();
    
    return {
      id: `proximity_${Date.now()}`,
      type: 'proximity',
      name: `Proximity Analysis (${maxDistance} ${unit})`,
      status: 'completed',
      progress: 100,
      parameters: { maxDistance, unit },
      result: {
        features: resultFeatures,
        inputCount: sourceFeatures.length,
        outputCount: resultFeatures.length,
        proximityPairs: proximityResults.length,
        statistics: {
          averageDistance: proximityResults.reduce((sum, r) => sum + r.distance, 0) / proximityResults.length,
          minDistance: Math.min(...proximityResults.map(r => r.distance)),
          maxDistance: Math.max(...proximityResults.map(r => r.distance)),
        },
      },
      startTime: new Date(startTime).toISOString(),
      endTime: new Date(endTime).toISOString(),
      executionTime: endTime - startTime,
      outputLayers: [`proximity_layer_${Date.now()}`],
    };
  } catch (error) {
    return {
      id: `proximity_${Date.now()}`,
      type: 'proximity',
      name: 'Proximity Analysis',
      status: 'failed',
      progress: 0,
      parameters: { maxDistance, unit },
      error: error instanceof Error ? error.message : 'Unknown error',
      startTime: new Date(startTime).toISOString(),
    };
  }
};

/**
 * Calculates the bearing (direction) from one point to another
 */
export const calculateBearing = (from: Coordinate, to: Coordinate): number => {
  const lat1Rad = (from.latitude * Math.PI) / 180;
  const lat2Rad = (to.latitude * Math.PI) / 180;
  const deltaLngRad = ((to.longitude - from.longitude) * Math.PI) / 180;
  
  const y = Math.sin(deltaLngRad) * Math.cos(lat2Rad);
  const x = Math.cos(lat1Rad) * Math.sin(lat2Rad) - 
           Math.sin(lat1Rad) * Math.cos(lat2Rad) * Math.cos(deltaLngRad);
  
  const bearingRad = Math.atan2(y, x);
  const bearingDeg = (bearingRad * 180) / Math.PI;
  
  // Normalize to 0-360 degrees
  return (bearingDeg + 360) % 360;
};

// Additional helper functions

function groupFeaturesByField(features: GISFeature[], field: string): GISFeature[][] {
  const groups = new Map<string, GISFeature[]>();
  
  for (const feature of features) {
    const value = feature.properties[field]?.toString() || 'null';
    if (!groups.has(value)) {
      groups.set(value, []);
    }
    groups.get(value)!.push(feature);
  }
  
  return Array.from(groups.values());
}

function calculateUnion(features: GISFeature[]): { type: string; coordinates: Coordinate[] } {
  // Simplified union - in real implementation would use robust geometric algorithms
  if (features.length === 0) return { type: 'polygon', coordinates: [] };
  if (features.length === 1) return { type: features[0].type, coordinates: features[0].coordinates };
  
  // For demo, combine all coordinates
  const allCoordinates = features.flatMap(f => f.coordinates);
  
  return {
    type: features[0].type,
    coordinates: allCoordinates,
  };
}

function aggregateProperties(features: GISFeature[], aggregateFields: string[]): Record<string, any> {
  const aggregated: Record<string, any> = {};
  
  for (const field of aggregateFields) {
    const values = features.map(f => f.properties[field]).filter(v => v != null);
    
    if (values.length > 0) {
      // Simple aggregation strategies
      if (typeof values[0] === 'number') {
        aggregated[`${field}_sum`] = values.reduce((sum, val) => sum + val, 0);
        aggregated[`${field}_avg`] = aggregated[`${field}_sum`] / values.length;
        aggregated[`${field}_min`] = Math.min(...values);
        aggregated[`${field}_max`] = Math.max(...values);
      } else {
        aggregated[`${field}_concat`] = values.join(', ');
        aggregated[`${field}_unique`] = [...new Set(values)].join(', ');
      }
    }
  }
  
  return aggregated;
}

function findSpatialMatches(
  targetFeature: GISFeature,
  joinFeatures: GISFeature[],
  operation: string
): GISFeature[] {
  const matches: GISFeature[] = [];
  
  for (const joinFeature of joinFeatures) {
    let isMatch = false;
    
    switch (operation) {
      case 'intersects':
        isMatch = featuresIntersect(targetFeature, joinFeature);
        break;
      case 'contains':
        isMatch = featureContains(targetFeature, joinFeature);
        break;
      case 'within':
        isMatch = featureContains(joinFeature, targetFeature);
        break;
      case 'touches':
        isMatch = featuresTouch(targetFeature, joinFeature);
        break;
      case 'crosses':
        isMatch = featuresCross(targetFeature, joinFeature);
        break;
      default:
        isMatch = featuresIntersect(targetFeature, joinFeature);
    }
    
    if (isMatch) {
      matches.push(joinFeature);
    }
  }
  
  return matches;
}

function aggregateJoinProperties(joinFeatures: GISFeature[]): Record<string, any> {
  // Simple aggregation of join properties
  const aggregated: Record<string, any> = {};
  
  if (joinFeatures.length === 1) {
    return { ...joinFeatures[0].properties };
  }
  
  // Combine properties from multiple join features
  for (const feature of joinFeatures) {
    for (const [key, value] of Object.entries(feature.properties)) {
      if (key in aggregated) {
        // Append to existing value
        aggregated[key] = `${aggregated[key]}, ${value}`;
      } else {
        aggregated[key] = value;
      }
    }
  }
  
  return aggregated;
}

// Simplified spatial relationship functions
function featuresIntersect(feature1: GISFeature, feature2: GISFeature): boolean {
  // Simplified intersection test based on bounding boxes
  const bbox1 = getFeatureBounds(feature1);
  const bbox2 = getFeatureBounds(feature2);
  
  return !(bbox1.maxLat < bbox2.minLat || 
           bbox1.minLat > bbox2.maxLat || 
           bbox1.maxLng < bbox2.minLng || 
           bbox1.minLng > bbox2.maxLng);
}

function featureContains(container: GISFeature, contained: GISFeature): boolean {
  // Simplified containment test
  const containerBounds = getFeatureBounds(container);
  const containedBounds = getFeatureBounds(contained);
  
  return containerBounds.minLat <= containedBounds.minLat &&
         containerBounds.maxLat >= containedBounds.maxLat &&
         containerBounds.minLng <= containedBounds.minLng &&
         containerBounds.maxLng >= containedBounds.maxLng;
}

function featuresTouch(feature1: GISFeature, feature2: GISFeature): boolean {
  // Simplified touch test - features share boundary but don't overlap
  const centroid1 = getCentroid(feature1);
  const centroid2 = getCentroid(feature2);
  const distance = calculateDistance(centroid1, centroid2);
  
  return distance < 100 && distance > 50; // Simplified logic
}

function featuresCross(feature1: GISFeature, feature2: GISFeature): boolean {
  // Simplified cross test - mainly for line features
  return feature1.type === 'line' || feature2.type === 'line';
}

function getFeatureBounds(feature: GISFeature): {
  minLat: number;
  maxLat: number;
  minLng: number;
  maxLng: number;
} {
  if (feature.coordinates.length === 0) {
    return { minLat: 0, maxLat: 0, minLng: 0, maxLng: 0 };
  }
  
  let minLat = feature.coordinates[0].latitude;
  let maxLat = feature.coordinates[0].latitude;
  let minLng = feature.coordinates[0].longitude;
  let maxLng = feature.coordinates[0].longitude;
  
  for (const coord of feature.coordinates) {
    minLat = Math.min(minLat, coord.latitude);
    maxLat = Math.max(maxLat, coord.latitude);
    minLng = Math.min(minLng, coord.longitude);
    maxLng = Math.max(maxLng, coord.longitude);
  }
  
  return { minLat, maxLat, minLng, maxLng };
}

function formatDistance(meters: number): string {
  if (meters < 1000) {
    return `${meters.toFixed(1)} m`;
  } else {
    return `${(meters / 1000).toFixed(2)} km`;
  }
}

function formatBearing(degrees: number): string {
  const directions = [
    'N', 'NNE', 'NE', 'ENE', 'E', 'ESE', 'SE', 'SSE',
    'S', 'SSW', 'SW', 'WSW', 'W', 'WNW', 'NW', 'NNW'
  ];
  
  const index = Math.round(degrees / 22.5) % 16;
  return `${degrees.toFixed(1)}° ${directions[index]}`;
}

export {
  calculateDistance,
  getCentroid,
};
