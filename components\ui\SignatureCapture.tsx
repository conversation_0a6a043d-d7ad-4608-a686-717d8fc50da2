import React from 'react';
import { Platform } from 'react-native';

// Platform-specific imports
const SignatureCaptureNative = React.lazy(() => import('./SignatureCapture.native'));
const SignatureCaptureWeb = React.lazy(() => import('./SignatureCapture.web'));

interface SignatureCaptureProps {
  value?: string;
  onChange: (signature: string | null) => void;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
}

export default function SignatureCapture(props: SignatureCaptureProps) {
  const SignatureCaptureComponent = Platform.OS === 'web' ? SignatureCaptureWeb : SignatureCaptureNative;
  
  return (
    <React.Suspense fallback={null}>
      <SignatureCaptureComponent {...props} />
    </React.Suspense>
  );
}
