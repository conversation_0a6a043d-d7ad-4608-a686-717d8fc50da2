import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Modal,
  TextInput,
  Animated,
  Dimensions,
  Platform,
  Alert,
} from 'react-native';
import { useTheme } from '@/hooks/useTheme';
import * as Location from 'expo-location';
import {
  MapPin,
  Layers,
  Ruler,
  Search,
  Navigation,
  Download,
  Upload,
  Settings,
  Map as MapIcon,
  Satellite,
  Plus,
  Minus,
  Target,
  Route,
  CircleDot,
  Square,
  Pentagon,
  Activity,
  Thermometer,
  Shield,
  Save,
  Trash2,
  Edit3,
  Eye,
  EyeOff,
  Filter,
  BarChart3,
  Zap,
  AlertTriangle,
} from 'lucide-react-native';

// Platform-specific map component import
const PlatformMap = Platform.OS === 'web' 
  ? require('./Map.web').default 
  : require('./Map.native').default;

interface Region {
  latitude: number;
  longitude: number;
  latitudeDelta: number;
  longitudeDelta: number;
}

interface EnhancedMapProps {
  initialRegion?: Region;
  onLocationSelect?: (location: { latitude: number; longitude: number }) => void;
  geoFeatures?: any[];
  showAnalysisTools?: boolean;
  enableDrawing?: boolean;
  enableMeasurement?: boolean;
  enableGeofencing?: boolean;
  enableRouting?: boolean;
  enableHeatmap?: boolean;
  enableClustering?: boolean;
  offlineMode?: boolean;
}

type DrawingMode = 'none' | 'point' | 'line' | 'polygon' | 'circle' | 'rectangle';
type MapLayer = 'standard' | 'satellite' | 'terrain' | 'hybrid';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

export default function EnhancedMap({
  initialRegion,
  onLocationSelect,
  geoFeatures = [],
  showAnalysisTools = true,
  enableDrawing = true,
  enableMeasurement = true,
  enableGeofencing = true,
  enableRouting = true,
  enableHeatmap = true,
  enableClustering = true,
  offlineMode = false,
}: EnhancedMapProps) {
  const { theme } = useTheme();
  
  // State management
  const [region] = useState<Region>(initialRegion || {
    latitude: 37.78825,
    longitude: -122.4324,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  });
  
  const [mapType, setMapType] = useState<MapLayer>('standard');
  const [drawingMode, setDrawingMode] = useState<DrawingMode>('none');
  const [measurementMode, setMeasurementMode] = useState(false);
  const [showHeatmap, setShowHeatmap] = useState(false);
  const [userLocation, setUserLocation] = useState<Location.LocationObject | null>(null);
  
  // UI state
  const [showLayersPanel, setShowLayersPanel] = useState(false);
  const [showDrawingTools, setShowDrawingTools] = useState(false);
  const [showAnalysisPanel, setShowAnalysisPanel] = useState(false);
  const [showFeaturesList, setShowFeaturesList] = useState(false);
  const [showSearch, setShowSearch] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    requestLocationPermission();
  }, []);

  const requestLocationPermission = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status === 'granted') {
        const location = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.High,
        });
        setUserLocation(location);
      }
    } catch (error) {
      console.error('Error getting location:', error);
    }
  };

  const generateHeatmapData = () => {
    Alert.alert('Heatmap', 'Heatmap data generation demo');
    setShowHeatmap(true);
  };

  const renderMapLayers = () => (
    <Modal
      visible={showLayersPanel}
      transparent
      animationType="slide"
      onRequestClose={() => setShowLayersPanel(false)}
    >
      <TouchableOpacity 
        style={styles.modalOverlay}
        activeOpacity={1}
        onPress={() => setShowLayersPanel(false)}
      >
        <View style={[styles.layersPanel, { backgroundColor: theme.colors.card }]}>
          <Text style={[styles.panelTitle, { color: theme.colors.text }]}>
            Map Layers
          </Text>
          
          <TouchableOpacity
            style={[styles.layerOption, mapType === 'standard' && styles.layerOptionActive]}
            onPress={() => setMapType('standard')}
          >
            <MapIcon size={24} color={mapType === 'standard' ? theme.colors.primary : theme.colors.text} />
            <Text style={[styles.layerText, { color: theme.colors.text }]}>Standard</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.layerOption, mapType === 'satellite' && styles.layerOptionActive]}
            onPress={() => setMapType('satellite')}
          >
            <Satellite size={24} color={mapType === 'satellite' ? theme.colors.primary : theme.colors.text} />
            <Text style={[styles.layerText, { color: theme.colors.text }]}>Satellite</Text>
          </TouchableOpacity>
          
          <View style={styles.panelDivider} />
          
          <Text style={[styles.panelSubtitle, { color: theme.colors.text }]}>
            Data Layers
          </Text>
          
          <TouchableOpacity
            style={[styles.layerOption, showHeatmap && styles.layerOptionActive]}
            onPress={() => setShowHeatmap(!showHeatmap)}
          >
            <Thermometer size={24} color={showHeatmap ? theme.colors.primary : theme.colors.text} />
            <Text style={[styles.layerText, { color: theme.colors.text }]}>Heatmap</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.layerOption}
            onPress={generateHeatmapData}
          >
            <Activity size={24} color={theme.colors.text} />
            <Text style={[styles.layerText, { color: theme.colors.text }]}>Generate Heatmap</Text>
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    </Modal>
  );

  const renderDrawingTools = () => (
    <View style={[styles.drawingToolbar, { backgroundColor: theme.colors.card }]}>
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        <TouchableOpacity
          style={[
            styles.drawingTool,
            drawingMode === 'point' && { backgroundColor: theme.colors.primary }
          ]}
          onPress={() => setDrawingMode(drawingMode === 'point' ? 'none' : 'point')}
        >
          <MapPin size={20} color={drawingMode === 'point' ? 'white' : theme.colors.text} />
          <Text style={[styles.toolText, { color: drawingMode === 'point' ? 'white' : theme.colors.text }]}>
            Point
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.drawingTool,
            drawingMode === 'line' && { backgroundColor: theme.colors.primary }
          ]}
          onPress={() => setDrawingMode(drawingMode === 'line' ? 'none' : 'line')}
        >
          <Route size={20} color={drawingMode === 'line' ? 'white' : theme.colors.text} />
          <Text style={[styles.toolText, { color: drawingMode === 'line' ? 'white' : theme.colors.text }]}>
            Line
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.drawingTool,
            drawingMode === 'polygon' && { backgroundColor: theme.colors.primary }
          ]}
          onPress={() => setDrawingMode(drawingMode === 'polygon' ? 'none' : 'polygon')}
        >
          <Pentagon size={20} color={drawingMode === 'polygon' ? 'white' : theme.colors.text} />
          <Text style={[styles.toolText, { color: drawingMode === 'polygon' ? 'white' : theme.colors.text }]}>
            Polygon
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.drawingTool,
            measurementMode && { backgroundColor: theme.colors.primary }
          ]}
          onPress={() => setMeasurementMode(!measurementMode)}
        >
          <Ruler size={20} color={measurementMode ? 'white' : theme.colors.text} />
          <Text style={[styles.toolText, { color: measurementMode ? 'white' : theme.colors.text }]}>
            Measure
          </Text>
        </TouchableOpacity>
      </ScrollView>
    </View>
  );

  const renderAnalysisTools = () => (
    <Modal
      visible={showAnalysisPanel}
      transparent
      animationType="slide"
      onRequestClose={() => setShowAnalysisPanel(false)}
    >
      <TouchableOpacity 
        style={styles.modalOverlay}
        activeOpacity={1}
        onPress={() => setShowAnalysisPanel(false)}
      >
        <View style={[styles.analysisPanel, { backgroundColor: theme.colors.card }]}>
          <Text style={[styles.panelTitle, { color: theme.colors.text }]}>
            Spatial Analysis Tools
          </Text>
          
          <TouchableOpacity
            style={styles.analysisTool}
            onPress={() => Alert.alert('Buffer Analysis', 'Create buffer zones around features')}
          >
            <Shield size={24} color={theme.colors.text} />
            <View style={styles.toolInfo}>
              <Text style={[styles.toolName, { color: theme.colors.text }]}>Buffer Analysis</Text>
              <Text style={[styles.toolDescription, { color: theme.colors.muted }]}>
                Create buffer zones around features
              </Text>
            </View>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.analysisTool}
            onPress={() => Alert.alert('Centroid Analysis', 'Find center points of polygons')}
          >
            <Target size={24} color={theme.colors.text} />
            <View style={styles.toolInfo}>
              <Text style={[styles.toolName, { color: theme.colors.text }]}>Centroid Analysis</Text>
              <Text style={[styles.toolDescription, { color: theme.colors.muted }]}>
                Find center point of polygons
              </Text>
            </View>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.analysisTool}
            onPress={() => Alert.alert('Proximity Analysis', 'Find nearby features within radius')}
          >
            <Zap size={24} color={theme.colors.text} />
            <View style={styles.toolInfo}>
              <Text style={[styles.toolName, { color: theme.colors.text }]}>Proximity Analysis</Text>
              <Text style={[styles.toolDescription, { color: theme.colors.muted }]}>
                Find nearby features within radius
              </Text>
            </View>
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    </Modal>
  );

  const renderFeaturesList = () => (
    <Modal
      visible={showFeaturesList}
      transparent
      animationType="slide"
      onRequestClose={() => setShowFeaturesList(false)}
    >
      <TouchableOpacity 
        style={styles.modalOverlay}
        activeOpacity={1}
        onPress={() => setShowFeaturesList(false)}
      >
        <View style={[styles.featuresPanel, { backgroundColor: theme.colors.card }]}>
          <Text style={[styles.panelTitle, { color: theme.colors.text }]}>
            Features ({geoFeatures.length})
          </Text>
          
          <ScrollView>
            {geoFeatures.length === 0 ? (
              <View style={styles.emptyFeatures}>
                <Text style={[styles.emptyText, { color: theme.colors.muted }]}>
                  No features created yet
                </Text>
                <Text style={[styles.emptySubtext, { color: theme.colors.muted }]}>
                  Use drawing tools to create features
                </Text>
              </View>
            ) : (
              geoFeatures.map((feature, index) => (
                <View key={index} style={styles.featureItem}>
                  <View style={styles.featureIcon}>
                    <MapPin size={20} color={theme.colors.primary} />
                  </View>
                  
                  <View style={styles.featureInfo}>
                    <Text style={[styles.featureName, { color: theme.colors.text }]}>
                      Feature {index + 1}
                    </Text>
                    <Text style={[styles.featureDetails, { color: theme.colors.muted }]}>
                      Type: {feature.type}
                    </Text>
                  </View>
                  
                  <TouchableOpacity>
                    <Eye size={20} color={theme.colors.text} />
                  </TouchableOpacity>
                </View>
              ))
            )}
          </ScrollView>
        </View>
      </TouchableOpacity>
    </Modal>
  );

  return (
    <View style={styles.container}>
      {/* Platform-specific Map Component */}
      <PlatformMap
        initialRegion={region}
        mapType={mapType}
        drawingMode={drawingMode}
        measurementMode={measurementMode}
        showHeatmap={showHeatmap}
        userLocation={userLocation}
        geoFeatures={geoFeatures}
        onLocationSelect={onLocationSelect}
      />
      
      {/* Main Toolbar */}
      <View style={[styles.toolbar, { backgroundColor: theme.colors.card }]}>
        <TouchableOpacity
          style={styles.toolbarButton}
          onPress={() => setShowLayersPanel(true)}
        >
          <Layers size={24} color={theme.colors.text} />
        </TouchableOpacity>
        
        {enableDrawing && (
          <TouchableOpacity
            style={styles.toolbarButton}
            onPress={() => setShowDrawingTools(!showDrawingTools)}
          >
            <Edit3 size={24} color={theme.colors.text} />
          </TouchableOpacity>
        )}
        
        {showAnalysisTools && (
          <TouchableOpacity
            style={styles.toolbarButton}
            onPress={() => setShowAnalysisPanel(true)}
          >
            <BarChart3 size={24} color={theme.colors.text} />
          </TouchableOpacity>
        )}
        
        <TouchableOpacity
          style={styles.toolbarButton}
          onPress={() => setShowFeaturesList(true)}
        >
          <Filter size={24} color={theme.colors.text} />
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.toolbarButton}
          onPress={() => setShowSearch(!showSearch)}
        >
          <Search size={24} color={theme.colors.text} />
        </TouchableOpacity>
      </View>
      
      {/* Zoom Controls */}
      <View style={[styles.zoomControls, { backgroundColor: theme.colors.card }]}>
        <TouchableOpacity
          style={styles.zoomButton}
          onPress={() => Alert.alert('Zoom In', 'Zoom in functionality')}
        >
          <Plus size={20} color={theme.colors.text} />
        </TouchableOpacity>
        
        <View style={styles.zoomDivider} />
        
        <TouchableOpacity
          style={styles.zoomButton}
          onPress={() => Alert.alert('Zoom Out', 'Zoom out functionality')}
        >
          <Minus size={20} color={theme.colors.text} />
        </TouchableOpacity>
      </View>
      
      {/* Location Button */}
      <TouchableOpacity
        style={[styles.locationButton, { backgroundColor: theme.colors.card }]}
        onPress={() => {
          if (userLocation) {
            Alert.alert('Location', `Current: ${userLocation.coords.latitude.toFixed(4)}, ${userLocation.coords.longitude.toFixed(4)}`);
          } else {
            requestLocationPermission();
          }
        }}
      >
        <Target size={24} color={theme.colors.primary} />
      </TouchableOpacity>
      
      {/* Drawing Tools */}
      {showDrawingTools && renderDrawingTools()}
      
      {/* Search Bar */}
      {showSearch && (
        <View style={[styles.searchBar, { backgroundColor: theme.colors.card }]}>
          <Search size={20} color={theme.colors.muted} />
          <TextInput
            style={[styles.searchInput, { color: theme.colors.text }]}
            placeholder="Search location..."
            placeholderTextColor={theme.colors.placeholder}
            value={searchQuery}
            onChangeText={setSearchQuery}
            onSubmitEditing={() => {
              Alert.alert('Search', `Searching for: ${searchQuery}`);
            }}
          />
        </View>
      )}
      
      {/* Status Banner - Only show offline mode if needed */}
      {offlineMode && (
        <View style={[styles.offlineStatusBanner, { backgroundColor: theme.colors.info + '20' }]}>
          <Text style={[styles.statusText, { color: theme.colors.info }]}>
            📱 Offline Mode - Using cached data
          </Text>
        </View>
      )}
      
      {/* Modals */}
      {renderMapLayers()}
      {renderAnalysisTools()}
      {renderFeaturesList()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  toolbar: {
    position: 'absolute',
    top: 50,
    left: 16,
    right: 16,
    flexDirection: 'row',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
    gap: 8,
    zIndex: 1000,
  },
  toolbarButton: {
    width: 40,
    height: 40,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  zoomControls: {
    position: 'absolute',
    right: 16,
    bottom: 120,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
    zIndex: 998,
  },
  zoomButton: {
    width: 44,
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
  },
  zoomDivider: {
    height: 1,
    backgroundColor: '#E0E0E0',
  },
  locationButton: {
    position: 'absolute',
    right: 16,
    bottom: 200,
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
    zIndex: 997,
  },
  drawingToolbar: {
    position: 'absolute',
    bottom: 20,
    left: 16,
    right: 16,
    borderRadius: 12,
    paddingVertical: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
    zIndex: 996,
  },
  drawingTool: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    marginHorizontal: 4,
    gap: 8,
  },
  toolText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
  },
  searchBar: {
    position: 'absolute',
    top: 120,
    left: 16,
    right: 16,
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
    gap: 12,
    zIndex: 999,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
  },
  statusText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
  },
  offlineStatusBanner: {
    position: 'absolute',
    top: 180,
    left: 16,
    right: 16,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    alignItems: 'center',
    zIndex: 995,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  layersPanel: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 20,
    paddingHorizontal: 16,
    paddingBottom: 32,
  },
  panelTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 20,
    textAlign: 'center',
  },
  panelSubtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    marginVertical: 12,
  },
  layerOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 8,
    gap: 12,
  },
  layerOptionActive: {
    backgroundColor: 'rgba(0, 122, 255, 0.1)',
  },
  layerText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
  },
  panelDivider: {
    height: 1,
    backgroundColor: '#E0E0E0',
    marginVertical: 16,
  },
  analysisPanel: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 20,
    paddingHorizontal: 16,
    paddingBottom: 32,
    maxHeight: '80%',
  },
  analysisTool: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 12,
    gap: 16,
  },
  toolInfo: {
    flex: 1,
  },
  toolName: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 4,
  },
  toolDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
  },
  featuresPanel: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 20,
    paddingHorizontal: 16,
    paddingBottom: 32,
    maxHeight: '60%',
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 8,
    gap: 12,
  },
  featureIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 122, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  featureInfo: {
    flex: 1,
  },
  featureName: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    marginBottom: 2,
  },
  featureDetails: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
  },
  emptyFeatures: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  emptyText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    marginBottom: 4,
  },
  emptySubtext: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
  },
});
