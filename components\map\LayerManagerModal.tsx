import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Switch,
  Alert,
  Platform,
} from 'react-native';
import { useTheme } from '@/hooks/useTheme';
import {
  <PERSON>,
  <PERSON>,
  <PERSON>Off,
  Trash2,
  Settings,
  Upload,
  Download,
  Info,
  Palette,
  Layers,
  Plus,
  Search,
  Filter,
  Star,
  Globe,
  Database,
  MapPin,
  BarChart3,
  Table,
} from 'lucide-react-native';
import { AttributeTableModal } from './AttributeTableModal';

interface LayerStyle {
  strokeColor?: string;
  strokeWidth?: number;
  fillColor?: string;
  fillOpacity?: number;
  iconType?: string;
  iconSize?: number;
  labelField?: string;
  labelSize?: number;
  labelColor?: string;
}

interface MapLayer {
  id: string;
  name: string;
  type: 'vector' | 'raster' | 'tile' | 'wms' | 'wmts' | 'geojson' | 'kml' | 'shapefile';
  source: 'catalog' | 'upload' | 'external';
  visible: boolean;
  opacity: number;
  minZoom?: number;
  maxZoom?: number;
  style?: LayerStyle;
  metadata?: {
    description: string;
    source: string;
    lastUpdated: string;
    properties: string[];
    geometryType: 'Point' | 'LineString' | 'Polygon' | 'MultiPoint' | 'MultiLineString' | 'MultiPolygon';
    featureCount: number;
    extent: {
      minX: number;
      minY: number;
      maxX: number;
      maxY: number;
    };
  };
  data?: any;
  url?: string;
}

interface LayerManagerModalProps {
  visible: boolean;
  onClose: () => void;
  layers: MapLayer[];
  onLayerUpdate: (layerId: string, updates: Partial<MapLayer>) => void;
  onLayerRemove: (layerId: string) => void;
  onLayerAdd: (layer: MapLayer) => void;
  onLayerImport: () => void;
  onLayerExport: (layerId: string) => void;
  onOpenAttributeTable?: (layer: MapLayer) => void;
}

const PREDEFINED_STYLES = {
  // Point styles
  points: {
    default: { iconType: 'circle', iconSize: 8, fillColor: '#3B82F6', strokeColor: '#1E40AF', strokeWidth: 1 },
    highlighted: { iconType: 'star', iconSize: 12, fillColor: '#F59E0B', strokeColor: '#D97706', strokeWidth: 2 },
    critical: { iconType: 'triangle', iconSize: 10, fillColor: '#EF4444', strokeColor: '#DC2626', strokeWidth: 2 },
  },
  // Line styles
  lines: {
    default: { strokeColor: '#3B82F6', strokeWidth: 2 },
    highway: { strokeColor: '#1F2937', strokeWidth: 4 },
    river: { strokeColor: '#06B6D4', strokeWidth: 3 },
    boundary: { strokeColor: '#6B7280', strokeWidth: 1, dashPattern: [5, 5] },
  },
  // Polygon styles
  polygons: {
    default: { strokeColor: '#3B82F6', strokeWidth: 1, fillColor: '#3B82F620', fillOpacity: 0.3 },
    forest: { strokeColor: '#16A34A', strokeWidth: 1, fillColor: '#16A34A40', fillOpacity: 0.4 },
    urban: { strokeColor: '#6B7280', strokeWidth: 1, fillColor: '#6B728040', fillOpacity: 0.4 },
    water: { strokeColor: '#06B6D4', strokeWidth: 1, fillColor: '#06B6D440', fillOpacity: 0.5 },
  },
};

export const LayerManagerModal: React.FC<LayerManagerModalProps> = ({
  visible,
  onClose,
  layers,
  onLayerUpdate,
  onLayerRemove,
  onLayerAdd,
  onLayerImport,
  onLayerExport,
  onOpenAttributeTable,
}) => {
  const { theme } = useTheme();
  const [activeTab, setActiveTab] = useState<'layers' | 'styles' | 'metadata'>('layers');
  const [selectedLayerId, setSelectedLayerId] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState<'all' | 'vector' | 'raster'>('all');

  const selectedLayer = selectedLayerId ? layers.find(l => l.id === selectedLayerId) : null;

  const filteredLayers = layers.filter(layer => {
    const matchesSearch = layer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         layer.metadata?.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesFilter = filterType === 'all' || layer.type === filterType;
    return matchesSearch && matchesFilter;
  });

  const handleLayerVisibilityToggle = useCallback((layerId: string) => {
    const layer = layers.find(l => l.id === layerId);
    if (layer) {
      onLayerUpdate(layerId, { visible: !layer.visible });
    }
  }, [layers, onLayerUpdate]);

  const handleOpacityChange = useCallback((layerId: string, opacity: number) => {
    onLayerUpdate(layerId, { opacity });
  }, [onLayerUpdate]);

  const handleStyleUpdate = useCallback((layerId: string, style: LayerStyle) => {
    onLayerUpdate(layerId, { style });
  }, [onLayerUpdate]);

  const handleLayerReorder = useCallback((fromIndex: number, toIndex: number) => {
    // Layer reordering logic would be implemented here
    console.log('Reorder layer from', fromIndex, 'to', toIndex);
  }, []);

  const renderLayersTab = () => (
    <View style={styles.tabContent}>
      {/* Search and Filter */}
      <View style={styles.searchSection}>
        <View style={[styles.searchContainer, { backgroundColor: theme.colors.background }]}>
          <Search size={16} color={theme.colors.muted} />
          <TextInput
            style={[styles.searchInput, { color: theme.colors.text }]}
            placeholder="Search layers..."
            placeholderTextColor={theme.colors.muted}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>
        
        <View style={styles.filterRow}>
          {['all', 'vector', 'raster'].map(type => (
            <TouchableOpacity
              key={type}
              style={[
                styles.filterButton,
                {
                  backgroundColor: filterType === type ? theme.colors.primary : theme.colors.background,
                }
              ]}
              onPress={() => setFilterType(type as any)}
            >
              <Text style={[
                styles.filterButtonText,
                { color: filterType === type ? 'white' : theme.colors.text }
              ]}>
                {type.charAt(0).toUpperCase() + type.slice(1)}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Layer List */}
      <ScrollView style={styles.layersList}>
        {filteredLayers.map((layer, index) => (
          <View key={layer.id} style={[styles.layerItem, { borderBottomColor: theme.colors.border }]}>
            <TouchableOpacity
              style={styles.layerItemContent}
              onPress={() => setSelectedLayerId(selectedLayerId === layer.id ? null : layer.id)}
            >
              <View style={styles.layerHeader}>
                <View style={styles.layerInfo}>
                  <TouchableOpacity onPress={() => handleLayerVisibilityToggle(layer.id)}>
                    {layer.visible ? (
                      <Eye size={18} color={theme.colors.primary} />
                    ) : (
                      <EyeOff size={18} color={theme.colors.muted} />
                    )}
                  </TouchableOpacity>
                  
                  <View style={styles.layerDetails}>
                    <Text style={[styles.layerName, { color: theme.colors.text }]}>
                      {layer.name}
                    </Text>
                    <Text style={[styles.layerType, { color: theme.colors.muted }]}>
                      {layer.type} • {layer.source} • {layer.metadata?.featureCount || 0} features
                    </Text>
                  </View>
                </View>
                
                <View style={styles.layerActions}>
                  {/* Attribute Table button - only show for layers with features */}
                  {(layer.source === 'upload' || layer.metadata?.featureCount) && onOpenAttributeTable && (
                    <TouchableOpacity
                      onPress={() => onOpenAttributeTable(layer)}
                      style={styles.actionButton}
                    >
                      <Table size={16} color={theme.colors.primary} />
                    </TouchableOpacity>
                  )}

                  <TouchableOpacity
                    onPress={() => Alert.alert(
                      'Layer Info',
                      `${layer.metadata?.description || 'No description available'}\n\nSource: ${layer.metadata?.source || 'Unknown'}\nLast Updated: ${layer.metadata?.lastUpdated || 'Unknown'}`
                    )}
                    style={styles.actionButton}
                  >
                    <Info size={16} color={theme.colors.muted} />
                  </TouchableOpacity>

                  <TouchableOpacity
                    onPress={() => onLayerExport(layer.id)}
                    style={styles.actionButton}
                  >
                    <Download size={16} color={theme.colors.muted} />
                  </TouchableOpacity>

                  <TouchableOpacity
                    onPress={() => onLayerRemove(layer.id)}
                    style={styles.actionButton}
                  >
                    <Trash2 size={16} color={theme.colors.destructive} />
                  </TouchableOpacity>
                </View>
              </View>
              
              {/* Opacity Slider */}
              <View style={styles.opacitySection}>
                <Text style={[styles.opacityLabel, { color: theme.colors.muted }]}>
                  Opacity: {Math.round(layer.opacity * 100)}%
                </Text>
                <View style={styles.opacitySlider}>
                  {/* In a real implementation, you'd use a proper slider component */}
                  <TouchableOpacity
                    style={[styles.opacityButton, { backgroundColor: theme.colors.background }]}
                    onPress={() => handleOpacityChange(layer.id, Math.max(0, layer.opacity - 0.1))}
                  >
                    <Text style={[styles.opacityButtonText, { color: theme.colors.text }]}>-</Text>
                  </TouchableOpacity>
                  
                  <View style={[styles.opacityTrack, { backgroundColor: theme.colors.border }]}>
                    <View
                      style={[
                        styles.opacityThumb,
                        {
                          backgroundColor: theme.colors.primary,
                          left: `${layer.opacity * 100}%`,
                        }
                      ]}
                    />
                  </View>
                  
                  <TouchableOpacity
                    style={[styles.opacityButton, { backgroundColor: theme.colors.background }]}
                    onPress={() => handleOpacityChange(layer.id, Math.min(1, layer.opacity + 0.1))}
                  >
                    <Text style={[styles.opacityButtonText, { color: theme.colors.text }]}>+</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </TouchableOpacity>
          </View>
        ))}
      </ScrollView>

      {/* Add Layer Button */}
      <View style={styles.addLayerSection}>
        <TouchableOpacity
          style={[styles.addButton, { backgroundColor: theme.colors.primary }]}
          onPress={onLayerImport}
        >
          <Upload size={18} color="white" />
          <Text style={[styles.addButtonText, { color: 'white' }]}>Import Layer</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderStylesTab = () => (
    <View style={styles.tabContent}>
      {selectedLayer ? (
        <ScrollView>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Style: {selectedLayer.name}
          </Text>
          
          <View style={styles.styleSection}>
            <Text style={[styles.styleLabel, { color: theme.colors.text }]}>Predefined Styles</Text>
            
            {selectedLayer.metadata?.geometryType === 'Point' && (
              <View style={styles.styleOptions}>
                {Object.entries(PREDEFINED_STYLES.points).map(([key, style]) => (
                  <TouchableOpacity
                    key={key}
                    style={[styles.styleOption, { backgroundColor: theme.colors.background }]}
                    onPress={() => handleStyleUpdate(selectedLayer.id, style)}
                  >
                    <Text style={[styles.styleOptionText, { color: theme.colors.text }]}>
                      {key.charAt(0).toUpperCase() + key.slice(1)}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            )}
            
            {selectedLayer.metadata?.geometryType === 'LineString' && (
              <View style={styles.styleOptions}>
                {Object.entries(PREDEFINED_STYLES.lines).map(([key, style]) => (
                  <TouchableOpacity
                    key={key}
                    style={[styles.styleOption, { backgroundColor: theme.colors.background }]}
                    onPress={() => handleStyleUpdate(selectedLayer.id, style)}
                  >
                    <Text style={[styles.styleOptionText, { color: theme.colors.text }]}>
                      {key.charAt(0).toUpperCase() + key.slice(1)}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            )}
            
            {selectedLayer.metadata?.geometryType === 'Polygon' && (
              <View style={styles.styleOptions}>
                {Object.entries(PREDEFINED_STYLES.polygons).map(([key, style]) => (
                  <TouchableOpacity
                    key={key}
                    style={[styles.styleOption, { backgroundColor: theme.colors.background }]}
                    onPress={() => handleStyleUpdate(selectedLayer.id, style)}
                  >
                    <Text style={[styles.styleOptionText, { color: theme.colors.text }]}>
                      {key.charAt(0).toUpperCase() + key.slice(1)}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            )}
          </View>
        </ScrollView>
      ) : (
        <View style={styles.noSelectionContainer}>
          <Palette size={48} color={theme.colors.muted} />
          <Text style={[styles.noSelectionText, { color: theme.colors.muted }]}>
            Select a layer to edit its style
          </Text>
        </View>
      )}
    </View>
  );

  const renderMetadataTab = () => (
    <View style={styles.tabContent}>
      {selectedLayer ? (
        <ScrollView>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Metadata: {selectedLayer.name}
          </Text>
          
          <View style={styles.metadataSection}>
            <View style={styles.metadataItem}>
              <Text style={[styles.metadataLabel, { color: theme.colors.muted }]}>Type</Text>
              <Text style={[styles.metadataValue, { color: theme.colors.text }]}>
                {selectedLayer.type}
              </Text>
            </View>
            
            <View style={styles.metadataItem}>
              <Text style={[styles.metadataLabel, { color: theme.colors.muted }]}>Source</Text>
              <Text style={[styles.metadataValue, { color: theme.colors.text }]}>
                {selectedLayer.metadata?.source || 'Unknown'}
              </Text>
            </View>
            
            <View style={styles.metadataItem}>
              <Text style={[styles.metadataLabel, { color: theme.colors.muted }]}>Description</Text>
              <Text style={[styles.metadataValue, { color: theme.colors.text }]}>
                {selectedLayer.metadata?.description || 'No description available'}
              </Text>
            </View>
            
            <View style={styles.metadataItem}>
              <Text style={[styles.metadataLabel, { color: theme.colors.muted }]}>Geometry Type</Text>
              <Text style={[styles.metadataValue, { color: theme.colors.text }]}>
                {selectedLayer.metadata?.geometryType || 'Unknown'}
              </Text>
            </View>
            
            <View style={styles.metadataItem}>
              <Text style={[styles.metadataLabel, { color: theme.colors.muted }]}>Feature Count</Text>
              <Text style={[styles.metadataValue, { color: theme.colors.text }]}>
                {selectedLayer.metadata?.featureCount?.toLocaleString() || '0'}
              </Text>
            </View>
            
            <View style={styles.metadataItem}>
              <Text style={[styles.metadataLabel, { color: theme.colors.muted }]}>Last Updated</Text>
              <Text style={[styles.metadataValue, { color: theme.colors.text }]}>
                {selectedLayer.metadata?.lastUpdated ? 
                  new Date(selectedLayer.metadata.lastUpdated).toLocaleDateString() : 
                  'Unknown'
                }
              </Text>
            </View>
            
            {selectedLayer.metadata?.properties && (
              <View style={styles.metadataItem}>
                <Text style={[styles.metadataLabel, { color: theme.colors.muted }]}>Properties</Text>
                <View style={styles.propertiesList}>
                  {selectedLayer.metadata.properties.map(prop => (
                    <View key={prop} style={[styles.propertyTag, { backgroundColor: theme.colors.primary + '20' }]}>
                      <Text style={[styles.propertyText, { color: theme.colors.primary }]}>
                        {prop}
                      </Text>
                    </View>
                  ))}
                </View>
              </View>
            )}
          </View>
        </ScrollView>
      ) : (
        <View style={styles.noSelectionContainer}>
          <Database size={48} color={theme.colors.muted} />
          <Text style={[styles.noSelectionText, { color: theme.colors.muted }]}>
            Select a layer to view its metadata
          </Text>
        </View>
      )}
    </View>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={[styles.modalContainer, { backgroundColor: theme.colors.background }]}>
        {/* Header */}
        <View style={[styles.modalHeader, { backgroundColor: theme.colors.card, borderBottomColor: theme.colors.border }]}>
          <Text style={[styles.modalTitle, { color: theme.colors.text }]}>Layer Manager</Text>
          <TouchableOpacity onPress={onClose}>
            <X size={24} color={theme.colors.text} />
          </TouchableOpacity>
        </View>

        {/* Tabs */}
        <View style={[styles.tabsContainer, { backgroundColor: theme.colors.card, borderBottomColor: theme.colors.border }]}>
          {[
            { key: 'layers', icon: Layers, label: 'Layers' },
            { key: 'styles', icon: Palette, label: 'Styles' },
            { key: 'metadata', icon: Info, label: 'Metadata' },
          ].map(tab => (
            <TouchableOpacity
              key={tab.key}
              style={[
                styles.tab,
                { backgroundColor: activeTab === tab.key ? theme.colors.primary : 'transparent' }
              ]}
              onPress={() => setActiveTab(tab.key as any)}
            >
              <tab.icon
                size={18}
                color={activeTab === tab.key ? 'white' : theme.colors.text}
              />
              <Text style={[
                styles.tabText,
                { color: activeTab === tab.key ? 'white' : theme.colors.text }
              ]}>
                {tab.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Content */}
        {activeTab === 'layers' && renderLayersTab()}
        {activeTab === 'styles' && renderStylesTab()}
        {activeTab === 'metadata' && renderMetadataTab()}
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
  },
  tabsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    borderBottomWidth: 1,
  },
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginRight: 8,
    borderRadius: 8,
    gap: 6,
  },
  tabText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
  },
  tabContent: {
    flex: 1,
  },
  searchSection: {
    padding: 16,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    marginBottom: 12,
    gap: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 14,
    fontFamily: 'Inter-Regular',
  },
  filterRow: {
    flexDirection: 'row',
    gap: 8,
  },
  filterButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  filterButtonText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },
  layersList: {
    flex: 1,
    paddingHorizontal: 16,
  },
  layerItem: {
    borderBottomWidth: 1,
    paddingVertical: 12,
  },
  layerItemContent: {
    flex: 1,
  },
  layerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  layerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    flex: 1,
  },
  layerDetails: {
    flex: 1,
  },
  layerName: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 2,
  },
  layerType: {
    fontSize: 11,
    fontFamily: 'Inter-Regular',
  },
  layerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  actionButton: {
    padding: 4,
  },
  opacitySection: {
    marginTop: 8,
  },
  opacityLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    marginBottom: 6,
  },
  opacitySlider: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  opacityButton: {
    width: 30,
    height: 30,
    borderRadius: 15,
    alignItems: 'center',
    justifyContent: 'center',
  },
  opacityButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
  },
  opacityTrack: {
    flex: 1,
    height: 4,
    borderRadius: 2,
    position: 'relative',
  },
  opacityThumb: {
    position: 'absolute',
    width: 12,
    height: 12,
    borderRadius: 6,
    top: -4,
    marginLeft: -6,
  },
  addLayerSection: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  addButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    padding: 16,
  },
  styleSection: {
    padding: 16,
  },
  styleLabel: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 12,
  },
  styleOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  styleOption: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
  },
  styleOptionText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },
  metadataSection: {
    padding: 16,
  },
  metadataItem: {
    marginBottom: 16,
  },
  metadataLabel: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    textTransform: 'uppercase',
    marginBottom: 4,
  },
  metadataValue: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    lineHeight: 20,
  },
  propertiesList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 6,
    marginTop: 6,
  },
  propertyTag: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  propertyText: {
    fontSize: 11,
    fontFamily: 'Inter-Medium',
  },
  noSelectionContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    gap: 16,
  },
  noSelectionText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
  },
});
