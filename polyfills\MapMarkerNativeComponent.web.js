import { View } from 'react-native';
import React from 'react';

// Mock the native component for web
const MapMarkerNativeComponent = React.forwardRef((props, ref) => {
  return (
    <View ref={ref} {...props} />
  );
});

// This matches the structure that react-native-maps expects
export const MapMarkerNativeComponentImpl = MapMarkerNativeComponent;
export default MapMarkerNativeComponent;

// Mock commands that might be used
export const Commands = {
  showCallout: () => {},
  hideCallout: () => {},
  animateMarkerToCoordinate: () => {},
};

// Mock any additional exports that might be needed
export const NativeProps = {};
export const Symbols = {};
