import { useEffect } from 'react';
import { useSettings } from '@/providers/SettingsProvider';
import { useTheme } from '@/providers/ThemeProvider';

/**
 * Hook to integrate settings changes with other parts of the application
 * This ensures that settings changes are propagated throughout the app
 */
export function useSettingsIntegration() {
  const { settings, unitUtils } = useSettings();
  const { setTheme } = useTheme();

  // Apply theme changes
  useEffect(() => {
    setTheme(settings.theme);
  }, [settings.theme, setTheme]);

  // Create derived values for easy consumption by other components
  const derivedSettings = {
    // Map settings
    map: {
      coordinateSystem: settings.coordinateSystem,
      baseMap: settings.baseMap,
      showScale: settings.showScale,
      showCoordinates: settings.showCoordinates,
    },
    
    // Unit settings with conversion utilities
    units: {
      system: settings.units,
      analysis: settings.analysisUnits,
      distance: unitUtils.getDistanceUnit(settings.units),
      distanceShort: unitUtils.getDistanceUnit(settings.units, true),
      area: unitUtils.getAreaUnit(settings.units),
      areaShort: unitUtils.getAreaUnit(settings.units, true),
      largeDistance: unitUtils.getLargeDistanceUnit(settings.units),
      largeDistanceShort: unitUtils.getLargeDistanceUnit(settings.units, true),
      largeArea: unitUtils.getLargeAreaUnit(settings.units),
      largeAreaShort: unitUtils.getLargeAreaUnit(settings.units, true),
      convert: {
        distance: (value: number, to: 'metric' | 'imperial') => 
          unitUtils.convertDistance(value, settings.units, to),
        area: (value: number, to: 'metric' | 'imperial') => 
          unitUtils.convertArea(value, settings.units, to),
      }
    },
    
    // Analysis settings
    analysis: {
      defaultBuffer: settings.defaultBuffer,
      units: settings.analysisUnits,
      showHistory: settings.showAnalysisHistory,
      autoSave: settings.autoSaveResults,
    },
    
    // Data settings
    data: {
      autoSync: settings.autoSync,
      syncInterval: settings.syncInterval,
      offlineStorage: settings.offlineStorage,
      cloudBackup: settings.cloudBackup,
      dataRetention: settings.dataRetention,
    },
    
    // UI settings
    ui: {
      theme: settings.theme,
      language: settings.language,
      fontSize: settings.fontSize,
      highContrast: settings.highContrast,
      reducedMotion: settings.reducedMotion,
    },
    
    // Notification settings
    notifications: {
      push: settings.pushNotifications,
      email: settings.emailNotifications,
      sound: settings.soundEnabled,
      vibration: settings.vibrationEnabled,
    },
    
    // Security settings
    security: {
      biometric: settings.biometricAuth,
      sessionTimeout: settings.sessionTimeout,
      encryption: settings.dataEncryption,
      auditLogging: settings.auditLogging,
    }
  };

  return {
    settings: derivedSettings,
    rawSettings: settings,
    unitUtils,
  };
}

/**
 * Hook specifically for map components to get map-related settings
 */
export function useMapSettings() {
  const { settings } = useSettingsIntegration();
  return settings.map;
}

/**
 * Hook specifically for analysis components to get analysis-related settings
 */
export function useAnalysisSettings() {
  const { settings } = useSettingsIntegration();
  return settings.analysis;
}

/**
 * Hook specifically for unit conversions and formatting
 */
export function useUnitSettings() {
  const { settings } = useSettingsIntegration();
  return settings.units;
}

/**
 * Hook for data synchronization settings
 */
export function useDataSettings() {
  const { settings } = useSettingsIntegration();
  return settings.data;
}

/**
 * Hook for notification settings
 */
export function useNotificationSettings() {
  const { settings } = useSettingsIntegration();
  return settings.notifications;
}

/**
 * Hook for security settings
 */
export function useSecuritySettings() {
  const { settings } = useSettingsIntegration();
  return settings.security;
}

/**
 * Hook for UI/accessibility settings
 */
export function useUISettings() {
  const { settings } = useSettingsIntegration();
  return settings.ui;
}
