import { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  ScrollView
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { Project } from '@/types';
import { useTheme } from '@/hooks/useTheme';
import { useProjects } from '@/hooks/useProjects';
import { useConnection } from '@/hooks/useConnection';
import {
  Plus,
  Filter,
  Clock,
  CheckCircle,
  AlertCircle,
  Archive,
  ClipboardList,
} from 'lucide-react-native';

import ProjectCard from '@/components/projects/ProjectCard';
import CreateProjectModal from '@/components/projects/CreateProjectModal';
import EmptyState from '@/components/ui/EmptyState';
import SearchBar from '@/components/ui/SearchBar';

export default function ProjectsScreen() {
  const { theme } = useTheme();
  const { isConnected } = useConnection();
  const { projects, loading, error, fetchProjects } = useProjects();
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredProjects, setFilteredProjects] = useState<Project[]>([]);
  const [refreshing, setRefreshing] = useState(false);
  const [statusFilter, setStatusFilter] = useState<Project['status'] | 'all'>('all');
  const [showCreateModal, setShowCreateModal] = useState(false);

  useEffect(() => {
    let result = projects;

    if (searchQuery) {
      result = result.filter(project =>
        project.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (project.description && project.description.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    if (statusFilter !== 'all') {
      result = result.filter(project => project.status === statusFilter);
    }

    setFilteredProjects(result);
  }, [projects, searchQuery, statusFilter]);

  const onRefresh = async () => {
    setRefreshing(true);
    await fetchProjects();
    setRefreshing(false);
  };

  const renderStatusFilterButton = (status: Project['status'] | 'all', label: string, icon: JSX.Element) => (
    <TouchableOpacity
      style={[
        styles.filterButton,
        { backgroundColor: statusFilter === status ? theme.colors.primary : theme.colors.card }
      ]}
      onPress={() => setStatusFilter(status)}
    >
      {icon}
      <Text
        style={[
          styles.filterButtonText,
          { color: statusFilter === status ? 'white' : theme.colors.text }
        ]}
      >
        {label}
      </Text>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]} edges={['bottom']}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: theme.colors.text }]}>Projects</Text>
        <Text style={[styles.subtitle, { color: theme.colors.muted }]}>
          📋 Manage all your field research projects
        </Text>
      </View>

      <View style={styles.searchContainer}>
        <SearchBar
          placeholder="Search projects..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          onClear={() => setSearchQuery('')}
        />
      </View>

      <View style={styles.filterContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.filtersScrollContent}>
          {renderStatusFilterButton('all', 'All', <Filter size={16} color={statusFilter === 'all' ? 'white' : theme.colors.text} />)}
          {renderStatusFilterButton('active', 'Active', <CheckCircle size={16} color={statusFilter === 'active' ? 'white' : theme.colors.success} />)}
          {renderStatusFilterButton('draft', 'Drafts', <Clock size={16} color={statusFilter === 'draft' ? 'white' : theme.colors.warning} />)}
          {renderStatusFilterButton('completed', 'Completed', <CheckCircle size={16} color={statusFilter === 'completed' ? 'white' : theme.colors.success} />)}
          {renderStatusFilterButton('archived', 'Archived', <Archive size={16} color={statusFilter === 'archived' ? 'white' : theme.colors.muted} />)}
        </ScrollView>
      </View>

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={[styles.loadingText, { color: theme.colors.text }]}>Loading projects...</Text>
        </View>
      ) : error ? (
        <View style={styles.errorContainer}>
          <AlertCircle size={48} color={theme.colors.error} />
          <Text style={[styles.errorText, { color: theme.colors.error }]}>Failed to load projects</Text>
          <TouchableOpacity
            style={[styles.retryButton, { backgroundColor: theme.colors.primary }]}
            onPress={() => fetchProjects()}
          >
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <>
          <FlatList
            data={filteredProjects}
            keyExtractor={(item) => item.id}
            renderItem={({ item }) => (
              <ProjectCard
                project={item}
                onPress={() => router.push(`/projects/${item.id}`)}
              />
            )}
            contentContainerStyle={styles.listContent}
            showsVerticalScrollIndicator={false}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={onRefresh}
                colors={[theme.colors.primary]}
                tintColor={theme.colors.primary}
              />
            }
            ListEmptyComponent={
              <EmptyState
                icon={<ClipboardList size={64} color={theme.colors.muted} />}
                title="No projects found"
                description={
                  searchQuery
                    ? "No projects match your search criteria"
                    : "Tap the + button to create your first project"
                }
              />
            }
          />

          <TouchableOpacity
            style={[styles.fab, { backgroundColor: theme.colors.primary }]}
            onPress={() => setShowCreateModal(true)}
          >
            <Plus size={24} color="white" />
          </TouchableOpacity>
        </>
      )}

      {/* Connection Status */}
      <View style={styles.connectionStatus}>
        <View 
          style={[
            styles.connectionIndicator, 
            { backgroundColor: isConnected ? theme.colors.success : theme.colors.warning }
          ]} 
        />
        <Text style={[styles.connectionText, { color: 'white' }]}>
          {isConnected ? 'Online' : 'Offline'}
        </Text>
      </View>

      {/* Create Project Modal */}
      <CreateProjectModal
        visible={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSuccess={(project) => {
          console.log('Project created:', project);
          fetchProjects(); // Refresh the projects list
        }}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 16,
    paddingTop: 8,
    paddingBottom: 12,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  subtitle: {
    fontSize: 14,
    marginTop: 4,
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  filterContainer: {
    paddingVertical: 8,
  },
  filtersScrollContent: {
    paddingHorizontal: 16,
    gap: 8,
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    gap: 6,
  },
  filterButtonText: {
    fontWeight: '500',
    fontSize: 14,
  },
  listContent: {
    padding: 16,
    paddingBottom: 100,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    fontWeight: '500',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  errorText: {
    marginTop: 16,
    marginBottom: 24,
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
  retryButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  fab: {
    position: 'absolute',
    right: 24,
    bottom: 100,
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  connectionStatus: {
    position: 'absolute',
    bottom: 24,
    left: 24,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
  },
  connectionIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  connectionText: {
    fontSize: 12,
    fontWeight: '500',
  },
});
