import { useState, useEffect } from 'react';
import { Team, User } from '@/types';
import { v4 as uuidv4 } from 'uuid';

// Sample data for demo purposes
const SAMPLE_TEAMS: Team[] = [
  {
    id: 'team1',
    name: 'Field Research Alpha',
    description: 'Primary field data collection team for the Pacific Northwest region',
    members: ['user1', 'user2', 'user3'],
  },
  {
    id: 'team2',
    name: 'Urban Survey Beta',
    description: 'Urban water quality assessment team',
    members: ['user4', 'user5'],
  },
  {
    id: 'team3',
    name: 'Agricultural Monitoring',
    description: 'Soil analysis and crop monitoring specialists',
    members: ['user1', 'user6', 'user7'],
  },
];

const SAMPLE_TEAM_MEMBERS: User[] = [
  {
    id: 'user1',
    name: 'Dr. <PERSON>',
    email: '<EMAIL>',
    role: 'manager',
    teamId: 'team1',
    lastSyncTimestamp: Date.now() - 120000, // 2 minutes ago
  },
  {
    id: 'user2',
    name: '<PERSON>',
    email: 'mike.<PERSON><PERSON><PERSON><PERSON>@fieldsync.com',
    role: 'collector',
    teamId: 'team1',
    lastSyncTimestamp: Date.now() - 60000, // 1 minute ago
  },
  {
    id: 'user3',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'collector',
    teamId: 'team1',
    lastSyncTimestamp: Date.now() - 3600000, // 1 hour ago
  },
  {
    id: 'user4',
    name: 'David Kim',
    email: '<EMAIL>',
    role: 'collector',
    teamId: 'team2',
    lastSyncTimestamp: Date.now() - 300000, // 5 minutes ago
  },
  {
    id: 'user5',
    name: 'Lisa Thompson',
    email: '<EMAIL>',
    role: 'collector',
    teamId: 'team2',
    lastSyncTimestamp: Date.now() - 180000, // 3 minutes ago
  },
  {
    id: 'user6',
    name: 'James Wilson',
    email: '<EMAIL>',
    role: 'collector',
    teamId: 'team3',
    lastSyncTimestamp: Date.now() - 1800000, // 30 minutes ago
  },
  {
    id: 'user7',
    name: 'Maria Garcia',
    email: '<EMAIL>',
    role: 'collector',
    teamId: 'team3',
    lastSyncTimestamp: Date.now() - 240000, // 4 minutes ago
  },
];

export function useTeams() {
  const [teams, setTeams] = useState<Team[]>([]);
  const [teamMembers, setTeamMembers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  
  const fetchTeams = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // In a real app, this would be an API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setTeams(SAMPLE_TEAMS);
      setTeamMembers(SAMPLE_TEAM_MEMBERS);
    } catch (err) {
      console.error('Error fetching teams:', err);
      setError(err instanceof Error ? err : new Error('Unknown error occurred'));
    } finally {
      setLoading(false);
    }
  };
  
  const createTeam = async (team: Omit<Team, 'id'>) => {
    try {
      const newTeam: Team = {
        ...team,
        id: uuidv4(),
      };
      
      setTeams(prev => [...prev, newTeam]);
      return newTeam;
    } catch (err) {
      console.error('Error creating team:', err);
      throw err;
    }
  };
  
  const updateTeam = async (id: string, updates: Partial<Omit<Team, 'id'>>) => {
    try {
      setTeams(prev => 
        prev.map(team => 
          team.id === id ? { ...team, ...updates } : team
        )
      );
    } catch (err) {
      console.error('Error updating team:', err);
      throw err;
    }
  };
  
  const deleteTeam = async (id: string) => {
    try {
      setTeams(prev => prev.filter(team => team.id !== id));
      // Also remove team members
      setTeamMembers(prev => prev.filter(member => member.teamId !== id));
    } catch (err) {
      console.error('Error deleting team:', err);
      throw err;
    }
  };
  
  const addTeamMember = async (teamId: string, memberEmail: string) => {
    try {
      // In a real app, this would look up the user by email
      const newMember: User = {
        id: uuidv4(),
        name: memberEmail.split('@')[0], // Simple name extraction
        email: memberEmail,
        role: 'collector',
        teamId,
        lastSyncTimestamp: Date.now(),
      };
      
      // Add member to team
      setTeams(prev =>
        prev.map(team =>
          team.id === teamId
            ? { ...team, members: [...team.members, newMember.id] }
            : team
        )
      );
      
      // Add member to members list
      setTeamMembers(prev => [...prev, newMember]);
      
      return newMember;
    } catch (err) {
      console.error('Error adding team member:', err);
      throw err;
    }
  };
  
  const removeTeamMember = async (teamId: string, memberId: string) => {
    try {
      // Remove member from team
      setTeams(prev =>
        prev.map(team =>
          team.id === teamId
            ? { ...team, members: team.members.filter(id => id !== memberId) }
            : team
        )
      );
      
      // Remove member from members list
      setTeamMembers(prev => prev.filter(member => member.id !== memberId));
    } catch (err) {
      console.error('Error removing team member:', err);
      throw err;
    }
  };
  
  const getTeamById = (id: string) => {
    return teams.find(team => team.id === id) || null;
  };
  
  const getTeamMembers = (teamId: string) => {
    const team = teams.find(t => t.id === teamId);
    if (!team) return [];
    
    return teamMembers.filter(member => team.members.includes(member.id));
  };
  
  const updateMemberLocation = async (memberId: string, location: { latitude: number; longitude: number }) => {
    try {
      setTeamMembers(prev =>
        prev.map(member =>
          member.id === memberId
            ? { ...member, lastSyncTimestamp: Date.now() }
            : member
        )
      );
    } catch (err) {
      console.error('Error updating member location:', err);
      throw err;
    }
  };
  
  // Load teams on mount
  useEffect(() => {
    fetchTeams();
  }, []);
  
  return {
    teams,
    teamMembers,
    loading,
    error,
    fetchTeams,
    createTeam,
    updateTeam,
    deleteTeam,
    addTeamMember,
    removeTeamMember,
    getTeamById,
    getTeamMembers,
    updateMemberLocation,
  };
}
