import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Switch,
  Alert,
  Platform,
  Image,
  Dimensions,
} from 'react-native';
import { useTheme } from '@/hooks/useTheme';
import {
  X,
  Plus,
  Play,
  Pause,
  Skip<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Edit3,
  Trash2,
  Image as ImageIcon,
  Video,
  Type,
  Map,
  Eye,
  EyeOff,
  Move,
  Copy,
  Share,
  Download,
  Settings,
  ChevronLeft,
  ChevronRight,
  BookOpen,
  Layers,
  Clock,
  User,
  Calendar,
  Globe,
} from 'lucide-react-native';

interface StorySlide {
  id: string;
  title: string;
  description: string;
  content: string;
  duration: number; // seconds
  extent?: {
    latitude: number;
    longitude: number;
    latitudeDelta: number;
    longitudeDelta: number;
  };
  visibleLayers: string[];
  baseMap: string;
  media?: {
    type: 'image' | 'video';
    url: string;
    caption?: string;
    position: 'top' | 'bottom' | 'left' | 'right' | 'overlay';
    size: 'small' | 'medium' | 'large';
  };
  annotations?: Array<{
    id: string;
    type: 'text' | 'arrow' | 'highlight';
    position: { x: number; y: number };
    content: string;
    style: any;
  }>;
  transition?: {
    type: 'fade' | 'slide' | 'zoom' | 'none';
    duration: number;
  };
  interactive: boolean;
  autoAdvance: boolean;
}

interface MapStory {
  id: string;
  title: string;
  description: string;
  author: string;
  createdAt: string;
  updatedAt: string;
  tags: string[];
  slides: StorySlide[];
  settings: {
    showProgress: boolean;
    showControls: boolean;
    allowSkipping: boolean;
    loop: boolean;
    theme: 'light' | 'dark' | 'auto';
    layout: 'sidebar' | 'overlay' | 'bottom';
  };
  sharing: {
    isPublic: boolean;
    allowComments: boolean;
    allowEmbedding: boolean;
    password?: string;
  };
}

interface StoryBuilderModalProps {
  visible: boolean;
  onClose: () => void;
  story: MapStory;
  onStoryUpdate: (story: MapStory) => void;
  availableLayers: Array<{ id: string; name: string; type: string }>;
  currentMapExtent: any;
  onPlayStory: (story: MapStory) => void;
  onExportStory: (story: MapStory) => void;
  onShareStory: (story: MapStory) => void;
}

const { width: screenWidth } = Dimensions.get('window');

export const StoryBuilderModal: React.FC<StoryBuilderModalProps> = ({
  visible,
  onClose,
  story,
  onStoryUpdate,
  availableLayers,
  currentMapExtent,
  onPlayStory,
  onExportStory,
  onShareStory,
}) => {
  const { theme } = useTheme();
  const [activeTab, setActiveTab] = useState<'slides' | 'settings' | 'preview'>('slides');
  const [selectedSlideIndex, setSelectedSlideIndex] = useState(0);
  const [isEditing, setIsEditing] = useState(false);
  const [tempStory, setTempStory] = useState<MapStory>(story);

  const selectedSlide = tempStory.slides[selectedSlideIndex];

  const handleStoryUpdate = useCallback((updates: Partial<MapStory>) => {
    setTempStory(prev => ({ ...prev, ...updates }));
  }, []);

  const handleSlideUpdate = useCallback((slideIndex: number, updates: Partial<StorySlide>) => {
    setTempStory(prev => ({
      ...prev,
      slides: prev.slides.map((slide, index) =>
        index === slideIndex ? { ...slide, ...updates } : slide
      ),
    }));
  }, []);

  const handleAddSlide = useCallback(() => {
    const newSlide: StorySlide = {
      id: `slide-${Date.now()}`,
      title: `Slide ${tempStory.slides.length + 1}`,
      description: '',
      content: '',
      duration: 30,
      extent: currentMapExtent || {
        latitude: 37.7749,
        longitude: -122.4194,
        latitudeDelta: 0.05,
        longitudeDelta: 0.05,
      },
      visibleLayers: availableLayers.filter(layer => Math.random() > 0.5).map(l => l.id),
      baseMap: 'satellite',
      interactive: true,
      autoAdvance: false,
      transition: {
        type: 'fade',
        duration: 1,
      },
    };

    setTempStory(prev => ({
      ...prev,
      slides: [...prev.slides, newSlide],
    }));

    setSelectedSlideIndex(tempStory.slides.length);
  }, [tempStory.slides.length, currentMapExtent, availableLayers]);

  const handleDeleteSlide = useCallback((slideIndex: number) => {
    Alert.alert(
      'Delete Slide',
      'Are you sure you want to delete this slide?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            setTempStory(prev => ({
              ...prev,
              slides: prev.slides.filter((_, index) => index !== slideIndex),
            }));
            
            if (selectedSlideIndex >= slideIndex && selectedSlideIndex > 0) {
              setSelectedSlideIndex(selectedSlideIndex - 1);
            }
          },
        },
      ]
    );
  }, [selectedSlideIndex]);

  const handleDuplicateSlide = useCallback((slideIndex: number) => {
    const slideToClone = tempStory.slides[slideIndex];
    const duplicatedSlide: StorySlide = {
      ...slideToClone,
      id: `slide-${Date.now()}`,
      title: `${slideToClone.title} (Copy)`,
    };

    setTempStory(prev => ({
      ...prev,
      slides: [
        ...prev.slides.slice(0, slideIndex + 1),
        duplicatedSlide,
        ...prev.slides.slice(slideIndex + 1),
      ],
    }));
  }, [tempStory.slides]);

  const handleMoveSlide = useCallback((fromIndex: number, toIndex: number) => {
    if (fromIndex === toIndex) return;

    const slides = [...tempStory.slides];
    const [movedSlide] = slides.splice(fromIndex, 1);
    slides.splice(toIndex, 0, movedSlide);

    setTempStory(prev => ({ ...prev, slides }));
    setSelectedSlideIndex(toIndex);
  }, [tempStory.slides]);

  const handleSaveStory = useCallback(() => {
    onStoryUpdate(tempStory);
    Alert.alert('Story Saved', 'Your map story has been saved successfully.');
  }, [tempStory, onStoryUpdate]);

  const renderSlidesTab = () => (
    <View style={styles.tabContent}>
      {/* Slide List */}
      <View style={styles.slideListContainer}>
        <View style={styles.slideListHeader}>
          <Text style={[styles.slideListTitle, { color: theme.colors.text }]}>
            Slides ({tempStory.slides.length})
          </Text>
          <TouchableOpacity
            style={[styles.addSlideButton, { backgroundColor: theme.colors.primary }]}
            onPress={handleAddSlide}
          >
            <Plus size={16} color="white" />
            <Text style={[styles.addSlideText, { color: 'white' }]}>Add Slide</Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.slideList}>
          {tempStory.slides.map((slide, index) => (
            <TouchableOpacity
              key={slide.id}
              style={[
                styles.slideItem,
                {
                  backgroundColor: selectedSlideIndex === index ? theme.colors.primary : theme.colors.background,
                  borderColor: theme.colors.border,
                }
              ]}
              onPress={() => setSelectedSlideIndex(index)}
            >
              <View style={styles.slideItemHeader}>
                <Text style={[
                  styles.slideNumber,
                  { color: selectedSlideIndex === index ? 'white' : theme.colors.muted }
                ]}>
                  {index + 1}
                </Text>
                <View style={styles.slideInfo}>
                  <Text style={[
                    styles.slideTitle,
                    { color: selectedSlideIndex === index ? 'white' : theme.colors.text }
                  ]}>
                    {slide.title}
                  </Text>
                  <Text style={[
                    styles.slideDuration,
                    { color: selectedSlideIndex === index ? 'rgba(255,255,255,0.8)' : theme.colors.muted }
                  ]}>
                    {slide.duration}s • {slide.visibleLayers.length} layers
                  </Text>
                </View>
                
                <View style={styles.slideActions}>
                  <TouchableOpacity onPress={() => handleDuplicateSlide(index)}>
                    <Copy size={14} color={selectedSlideIndex === index ? 'white' : theme.colors.muted} />
                  </TouchableOpacity>
                  <TouchableOpacity onPress={() => handleDeleteSlide(index)}>
                    <Trash2 size={14} color={selectedSlideIndex === index ? 'white' : theme.colors.destructive} />
                  </TouchableOpacity>
                </View>
              </View>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Slide Editor */}
      {selectedSlide && (
        <View style={styles.slideEditor}>
          <ScrollView>
            <View style={styles.editorSection}>
              <Text style={[styles.editorSectionTitle, { color: theme.colors.text }]}>Slide Content</Text>
              
              <View style={styles.inputGroup}>
                <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Title</Text>
                <TextInput
                  style={[styles.textInput, { backgroundColor: theme.colors.background, color: theme.colors.text }]}
                  value={selectedSlide.title}
                  onChangeText={(text) => handleSlideUpdate(selectedSlideIndex, { title: text })}
                  placeholder="Enter slide title..."
                  placeholderTextColor={theme.colors.muted}
                />
              </View>

              <View style={styles.inputGroup}>
                <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Description</Text>
                <TextInput
                  style={[styles.textAreaInput, { backgroundColor: theme.colors.background, color: theme.colors.text }]}
                  value={selectedSlide.description}
                  onChangeText={(text) => handleSlideUpdate(selectedSlideIndex, { description: text })}
                  placeholder="Enter slide description..."
                  placeholderTextColor={theme.colors.muted}
                  multiline
                  numberOfLines={3}
                />
              </View>

              <View style={styles.inputGroup}>
                <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Content</Text>
                <TextInput
                  style={[styles.contentInput, { backgroundColor: theme.colors.background, color: theme.colors.text }]}
                  value={selectedSlide.content}
                  onChangeText={(text) => handleSlideUpdate(selectedSlideIndex, { content: text })}
                  placeholder="Enter detailed content for this slide..."
                  placeholderTextColor={theme.colors.muted}
                  multiline
                  numberOfLines={6}
                />
              </View>
            </View>

            <View style={styles.editorSection}>
              <Text style={[styles.editorSectionTitle, { color: theme.colors.text }]}>Map View</Text>
              
              <TouchableOpacity
                style={[styles.captureExtentButton, { backgroundColor: theme.colors.primary }]}
                onPress={() => {
                  const extent = currentMapExtent || {
                    latitude: 37.7749,
                    longitude: -122.4194,
                    latitudeDelta: 0.05,
                    longitudeDelta: 0.05,
                  };
                  handleSlideUpdate(selectedSlideIndex, { extent });
                  Alert.alert('Extent Captured', 'Current map view has been saved for this slide.');
                }}
              >
                <Globe size={16} color="white" />
                <Text style={[styles.captureExtentText, { color: 'white' }]}>
                  Capture Current View
                </Text>
              </TouchableOpacity>

              <View style={styles.inputGroup}>
                <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Base Map</Text>
                <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.baseMapScroll}>
                  {['satellite', 'street', 'terrain', 'dark', 'light'].map(baseMap => (
                    <TouchableOpacity
                      key={baseMap}
                      style={[
                        styles.baseMapOption,
                        {
                          backgroundColor: selectedSlide.baseMap === baseMap ? theme.colors.primary : theme.colors.background,
                          borderColor: theme.colors.border,
                        }
                      ]}
                      onPress={() => handleSlideUpdate(selectedSlideIndex, { baseMap })}
                    >
                      <Text style={[
                        styles.baseMapText,
                        { color: selectedSlide.baseMap === baseMap ? 'white' : theme.colors.text }
                      ]}>
                        {baseMap.charAt(0).toUpperCase() + baseMap.slice(1)}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              </View>

              <View style={styles.inputGroup}>
                <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Visible Layers</Text>
                <ScrollView style={styles.layersList}>
                  {availableLayers.map(layer => (
                    <TouchableOpacity
                      key={layer.id}
                      style={styles.layerItem}
                      onPress={() => {
                        const isVisible = selectedSlide.visibleLayers.includes(layer.id);
                        const updatedLayers = isVisible
                          ? selectedSlide.visibleLayers.filter(id => id !== layer.id)
                          : [...selectedSlide.visibleLayers, layer.id];
                        handleSlideUpdate(selectedSlideIndex, { visibleLayers: updatedLayers });
                      }}
                    >
                      {selectedSlide.visibleLayers.includes(layer.id) ? (
                        <Eye size={16} color={theme.colors.primary} />
                      ) : (
                        <EyeOff size={16} color={theme.colors.muted} />
                      )}
                      <Text style={[styles.layerName, { color: theme.colors.text }]}>
                        {layer.name}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              </View>
            </View>

            <View style={styles.editorSection}>
              <Text style={[styles.editorSectionTitle, { color: theme.colors.text }]}>Timing & Behavior</Text>
              
              <View style={styles.rowGroup}>
                <View style={styles.halfWidth}>
                  <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Duration (seconds)</Text>
                  <TextInput
                    style={[styles.numberInput, { backgroundColor: theme.colors.background, color: theme.colors.text }]}
                    value={selectedSlide.duration.toString()}
                    onChangeText={(text) => handleSlideUpdate(selectedSlideIndex, { duration: parseInt(text) || 30 })}
                    keyboardType="numeric"
                    placeholder="30"
                    placeholderTextColor={theme.colors.muted}
                  />
                </View>
              </View>

              <View style={styles.switchRow}>
                <Text style={[styles.switchLabel, { color: theme.colors.text }]}>Interactive</Text>
                <Switch
                  value={selectedSlide.interactive}
                  onValueChange={(value) => handleSlideUpdate(selectedSlideIndex, { interactive: value })}
                  trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
                  thumbColor={Platform.OS === 'ios' ? undefined : 'white'}
                />
              </View>

              <View style={styles.switchRow}>
                <Text style={[styles.switchLabel, { color: theme.colors.text }]}>Auto Advance</Text>
                <Switch
                  value={selectedSlide.autoAdvance}
                  onValueChange={(value) => handleSlideUpdate(selectedSlideIndex, { autoAdvance: value })}
                  trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
                  thumbColor={Platform.OS === 'ios' ? undefined : 'white'}
                />
              </View>
            </View>

            <View style={styles.editorSection}>
              <Text style={[styles.editorSectionTitle, { color: theme.colors.text }]}>Media</Text>
              
              <TouchableOpacity
                style={[styles.addMediaButton, { backgroundColor: theme.colors.background, borderColor: theme.colors.border }]}
                onPress={() => {
                  Alert.alert(
                    'Add Media',
                    'Choose media type:',
                    [
                      { text: 'Cancel', style: 'cancel' },
                      {
                        text: 'Image',
                        onPress: () => {
                          // In a real app, this would open image picker
                          handleSlideUpdate(selectedSlideIndex, {
                            media: {
                              type: 'image',
                              url: 'https://example.com/image.jpg',
                              position: 'right',
                              size: 'medium',
                            },
                          });
                        },
                      },
                      {
                        text: 'Video',
                        onPress: () => {
                          // In a real app, this would open video picker
                          handleSlideUpdate(selectedSlideIndex, {
                            media: {
                              type: 'video',
                              url: 'https://example.com/video.mp4',
                              position: 'bottom',
                              size: 'large',
                            },
                          });
                        },
                      },
                    ]
                  );
                }}
              >
                <ImageIcon size={20} color={theme.colors.muted} />
                <Text style={[styles.addMediaText, { color: theme.colors.muted }]}>
                  {selectedSlide.media ? 'Change Media' : 'Add Media'}
                </Text>
              </TouchableOpacity>

              {selectedSlide.media && (
                <View style={[styles.mediaPreview, { backgroundColor: theme.colors.background, borderColor: theme.colors.border }]}>
                  <View style={styles.mediaInfo}>
                    {selectedSlide.media.type === 'image' ? (
                      <ImageIcon size={16} color={theme.colors.primary} />
                    ) : (
                      <Video size={16} color={theme.colors.primary} />
                    )}
                    <Text style={[styles.mediaType, { color: theme.colors.text }]}>
                      {selectedSlide.media.type.charAt(0).toUpperCase() + selectedSlide.media.type.slice(1)}
                    </Text>
                    <Text style={[styles.mediaPosition, { color: theme.colors.muted }]}>
                      {selectedSlide.media.position} • {selectedSlide.media.size}
                    </Text>
                  </View>
                  <TouchableOpacity
                    onPress={() => handleSlideUpdate(selectedSlideIndex, { media: undefined })}
                  >
                    <Trash2 size={16} color={theme.colors.destructive} />
                  </TouchableOpacity>
                </View>
              )}
            </View>
          </ScrollView>
        </View>
      )}
    </View>
  );

  const renderSettingsTab = () => (
    <ScrollView style={styles.tabContent}>
      <View style={styles.settingsSection}>
        <Text style={[styles.settingsSectionTitle, { color: theme.colors.text }]}>Story Information</Text>
        
        <View style={styles.inputGroup}>
          <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Title</Text>
          <TextInput
            style={[styles.textInput, { backgroundColor: theme.colors.background, color: theme.colors.text }]}
            value={tempStory.title}
            onChangeText={(text) => handleStoryUpdate({ title: text })}
            placeholder="Enter story title..."
            placeholderTextColor={theme.colors.muted}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Description</Text>
          <TextInput
            style={[styles.textAreaInput, { backgroundColor: theme.colors.background, color: theme.colors.text }]}
            value={tempStory.description}
            onChangeText={(text) => handleStoryUpdate({ description: text })}
            placeholder="Enter story description..."
            placeholderTextColor={theme.colors.muted}
            multiline
            numberOfLines={3}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Author</Text>
          <TextInput
            style={[styles.textInput, { backgroundColor: theme.colors.background, color: theme.colors.text }]}
            value={tempStory.author}
            onChangeText={(text) => handleStoryUpdate({ author: text })}
            placeholder="Author name..."
            placeholderTextColor={theme.colors.muted}
          />
        </View>
      </View>

      <View style={styles.settingsSection}>
        <Text style={[styles.settingsSectionTitle, { color: theme.colors.text }]}>Playback Settings</Text>
        
        {[
          { key: 'showProgress', label: 'Show Progress Bar' },
          { key: 'showControls', label: 'Show Navigation Controls' },
          { key: 'allowSkipping', label: 'Allow Slide Skipping' },
          { key: 'loop', label: 'Loop Story' },
        ].map(setting => (
          <View key={setting.key} style={styles.switchRow}>
            <Text style={[styles.switchLabel, { color: theme.colors.text }]}>{setting.label}</Text>
            <Switch
              value={tempStory.settings[setting.key as keyof typeof tempStory.settings] as boolean}
              onValueChange={(value) => {
                handleStoryUpdate({
                  settings: { ...tempStory.settings, [setting.key]: value },
                });
              }}
              trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
              thumbColor={Platform.OS === 'ios' ? undefined : 'white'}
            />
          </View>
        ))}
      </View>

      <View style={styles.settingsSection}>
        <Text style={[styles.settingsSectionTitle, { color: theme.colors.text }]}>Sharing & Privacy</Text>
        
        {[
          { key: 'isPublic', label: 'Make Story Public' },
          { key: 'allowComments', label: 'Allow Comments' },
          { key: 'allowEmbedding', label: 'Allow Embedding' },
        ].map(setting => (
          <View key={setting.key} style={styles.switchRow}>
            <Text style={[styles.switchLabel, { color: theme.colors.text }]}>{setting.label}</Text>
            <Switch
              value={tempStory.sharing[setting.key as keyof typeof tempStory.sharing] as boolean}
              onValueChange={(value) => {
                handleStoryUpdate({
                  sharing: { ...tempStory.sharing, [setting.key]: value },
                });
              }}
              trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
              thumbColor={Platform.OS === 'ios' ? undefined : 'white'}
            />
          </View>
        ))}
      </View>
    </ScrollView>
  );

  const renderPreviewTab = () => (
    <View style={styles.tabContent}>
      <View style={styles.previewContainer}>
        <View style={styles.previewHeader}>
          <Text style={[styles.previewTitle, { color: theme.colors.text }]}>
            {tempStory.title}
          </Text>
          <Text style={[styles.previewSubtitle, { color: theme.colors.muted }]}>
            {tempStory.slides.length} slides • {tempStory.author}
          </Text>
        </View>

        <View style={styles.previewControls}>
          <TouchableOpacity
            style={[styles.previewControlButton, { backgroundColor: theme.colors.primary }]}
            onPress={() => onPlayStory(tempStory)}
          >
            <Play size={20} color="white" />
            <Text style={[styles.previewControlText, { color: 'white' }]}>Play Story</Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.previewSlidesList}>
          {tempStory.slides.map((slide, index) => (
            <View key={slide.id} style={[styles.previewSlideItem, { backgroundColor: theme.colors.background, borderColor: theme.colors.border }]}>
              <View style={styles.previewSlideHeader}>
                <Text style={[styles.previewSlideNumber, { color: theme.colors.primary }]}>
                  {index + 1}
                </Text>
                <View style={styles.previewSlideInfo}>
                  <Text style={[styles.previewSlideTitle, { color: theme.colors.text }]}>
                    {slide.title}
                  </Text>
                  <Text style={[styles.previewSlideDesc, { color: theme.colors.muted }]}>
                    {slide.description}
                  </Text>
                </View>
                <Text style={[styles.previewSlideDuration, { color: theme.colors.muted }]}>
                  {slide.duration}s
                </Text>
              </View>
              {slide.content && (
                <Text style={[styles.previewSlideContent, { color: theme.colors.text }]} numberOfLines={3}>
                  {slide.content}
                </Text>
              )}
            </View>
          ))}
        </ScrollView>
      </View>
    </View>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="fullScreen"
      onRequestClose={onClose}
    >
      <View style={[styles.modalContainer, { backgroundColor: theme.colors.background }]}>
        {/* Header */}
        <View style={[styles.modalHeader, { backgroundColor: theme.colors.card, borderBottomColor: theme.colors.border }]}>
          <Text style={[styles.modalTitle, { color: theme.colors.text }]}>Story Builder</Text>
          <View style={styles.headerActions}>
            <TouchableOpacity
              style={[styles.headerButton, { backgroundColor: theme.colors.muted }]}
              onPress={() => onExportStory(tempStory)}
            >
              <Download size={18} color="white" />
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.headerButton, { backgroundColor: theme.colors.info }]}
              onPress={() => onShareStory(tempStory)}
            >
              <Share size={18} color="white" />
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.headerButton, { backgroundColor: theme.colors.primary }]}
              onPress={handleSaveStory}
            >
              <Text style={[styles.saveButtonText, { color: 'white' }]}>Save</Text>
            </TouchableOpacity>
            <TouchableOpacity onPress={onClose}>
              <X size={24} color={theme.colors.text} />
            </TouchableOpacity>
          </View>
        </View>

        {/* Tabs */}
        <View style={[styles.tabsContainer, { backgroundColor: theme.colors.card, borderBottomColor: theme.colors.border }]}>
          {[
            { key: 'slides', icon: BookOpen, label: 'Slides' },
            { key: 'settings', icon: Settings, label: 'Settings' },
            { key: 'preview', icon: Play, label: 'Preview' },
          ].map(tab => (
            <TouchableOpacity
              key={tab.key}
              style={[
                styles.tab,
                { backgroundColor: activeTab === tab.key ? theme.colors.primary : 'transparent' }
              ]}
              onPress={() => setActiveTab(tab.key as any)}
            >
              <tab.icon
                size={18}
                color={activeTab === tab.key ? 'white' : theme.colors.text}
              />
              <Text style={[
                styles.tabText,
                { color: activeTab === tab.key ? 'white' : theme.colors.text }
              ]}>
                {tab.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Content */}
        {activeTab === 'slides' && renderSlidesTab()}
        {activeTab === 'settings' && renderSettingsTab()}
        {activeTab === 'preview' && renderPreviewTab()}
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  headerButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
  },
  saveButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
  },
  tabsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    borderBottomWidth: 1,
  },
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginRight: 8,
    borderRadius: 8,
    gap: 6,
  },
  tabText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
  },
  tabContent: {
    flex: 1,
  },
  slideListContainer: {
    width: 300,
    borderRightWidth: 1,
    borderRightColor: '#E5E7EB',
  },
  slideListHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  slideListTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
  },
  addSlideButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    gap: 4,
  },
  addSlideText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },
  slideList: {
    flex: 1,
  },
  slideItem: {
    margin: 8,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
  },
  slideItemHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  slideNumber: {
    fontSize: 12,
    fontFamily: 'Inter-Bold',
    width: 20,
  },
  slideInfo: {
    flex: 1,
  },
  slideTitle: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 2,
  },
  slideDuration: {
    fontSize: 11,
    fontFamily: 'Inter-Regular',
  },
  slideActions: {
    flexDirection: 'row',
    gap: 8,
  },
  slideEditor: {
    flex: 1,
    padding: 16,
  },
  editorSection: {
    marginBottom: 24,
  },
  editorSectionTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 12,
  },
  inputGroup: {
    marginBottom: 12,
  },
  inputLabel: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 6,
  },
  textInput: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  textAreaInput: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    textAlignVertical: 'top',
  },
  contentInput: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    textAlignVertical: 'top',
    minHeight: 120,
  },
  numberInput: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  rowGroup: {
    flexDirection: 'row',
    gap: 12,
  },
  halfWidth: {
    flex: 1,
  },
  switchRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 8,
  },
  switchLabel: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
  },
  captureExtentButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    borderRadius: 6,
    marginBottom: 12,
    gap: 6,
  },
  captureExtentText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
  },
  baseMapScroll: {
    flexDirection: 'row',
  },
  baseMapOption: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
    borderWidth: 1,
    marginRight: 8,
  },
  baseMapText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },
  layersList: {
    maxHeight: 120,
  },
  layerItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    gap: 8,
  },
  layerName: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
  },
  addMediaButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 8,
    borderWidth: 2,
    borderStyle: 'dashed',
    gap: 8,
  },
  addMediaText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
  },
  mediaPreview: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 12,
    borderRadius: 6,
    borderWidth: 1,
    marginTop: 8,
  },
  mediaInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  mediaType: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
  },
  mediaPosition: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
  },
  settingsSection: {
    padding: 16,
  },
  settingsSectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 16,
  },
  previewContainer: {
    flex: 1,
    padding: 16,
  },
  previewHeader: {
    marginBottom: 16,
  },
  previewTitle: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    marginBottom: 4,
  },
  previewSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
  },
  previewControls: {
    marginBottom: 24,
  },
  previewControlButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  previewControlText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
  },
  previewSlidesList: {
    flex: 1,
  },
  previewSlideItem: {
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 12,
  },
  previewSlideHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  previewSlideNumber: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    width: 32,
  },
  previewSlideInfo: {
    flex: 1,
    marginRight: 8,
  },
  previewSlideTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 4,
  },
  previewSlideDesc: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
  },
  previewSlideDuration: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },
  previewSlideContent: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    lineHeight: 20,
  },
});
