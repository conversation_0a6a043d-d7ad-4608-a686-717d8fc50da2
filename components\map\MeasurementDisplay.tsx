import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Modal,
  Alert,
} from 'react-native';
import { useTheme } from '@/hooks/useTheme';
import {
  Ruler,
  Square,
  Compass,
  Mountain,
  Trash2,
  Copy,
  Share,
  Download,
  MoreHorizontal,
  Clock,
  MapPin,
  Maximize2,
  Eye,
  EyeOff,
} from 'lucide-react-native';
import type { MeasurementResult } from '@/types/gis';

interface MeasurementDisplayProps {
  measurements: MeasurementResult[];
  onMeasurementDelete?: (measurementId: string) => void;
  onMeasurementShow?: (measurement: MeasurementResult) => void;
  onMeasurementHide?: (measurementId: string) => void;
  onClearAll?: () => void;
  onExport?: (measurements: MeasurementResult[]) => void;
  compact?: boolean;
  maxVisible?: number;
}

interface MeasurementItemProps {
  measurement: MeasurementResult;
  onDelete?: (measurementId: string) => void;
  onShow?: (measurement: MeasurementResult) => void;
  onHide?: (measurementId: string) => void;
  isVisible?: boolean;
  compact?: boolean;
}

const formatMeasurementValue = (value: number, unit: string, type: string): string => {
  let formattedValue: string;
  
  if (type === 'area') {
    if (unit === 'km²') {
      formattedValue = value.toFixed(2);
    } else if (unit === 'm²') {
      if (value >= 10000) {
        formattedValue = (value / 10000).toFixed(2);
        unit = 'hectares';
      } else {
        formattedValue = value.toFixed(0);
      }
    } else {
      formattedValue = value.toFixed(2);
    }
  } else if (type === 'distance') {
    if (unit === 'km') {
      formattedValue = value.toFixed(3);
    } else if (unit === 'm') {
      if (value >= 1000) {
        formattedValue = (value / 1000).toFixed(2);
        unit = 'km';
      } else {
        formattedValue = value.toFixed(1);
      }
    } else {
      formattedValue = value.toFixed(2);
    }
  } else if (type === 'bearing') {
    formattedValue = value.toFixed(1);
  } else if (type === 'elevation') {
    formattedValue = value.toFixed(1);
  } else {
    formattedValue = value.toFixed(2);
  }
  
  return `${formattedValue} ${unit}`;
};

const getMeasurementIcon = (type: string) => {
  switch (type) {
    case 'distance':
      return Ruler;
    case 'area':
      return Square;
    case 'bearing':
      return Compass;
    case 'elevation':
      return Mountain;
    default:
      return Ruler;
  }
};

const getMeasurementColor = (type: string) => {
  switch (type) {
    case 'distance':
      return '#3B82F6';
    case 'area':
      return '#10B981';
    case 'bearing':
      return '#F59E0B';
    case 'elevation':
      return '#8B5CF6';
    default:
      return '#6B7280';
  }
};

const MeasurementItem: React.FC<MeasurementItemProps> = ({
  measurement,
  onDelete,
  onShow,
  onHide,
  isVisible = true,
  compact = false,
}) => {
  const { theme } = useTheme();
  const [showActions, setShowActions] = useState(false);
  
  const Icon = getMeasurementIcon(measurement.type);
  const color = getMeasurementColor(measurement.type);
  const formattedValue = formatMeasurementValue(measurement.value, measurement.unit, measurement.type);

  const handleCopy = async () => {
    const text = `${measurement.type.toUpperCase()}: ${formattedValue}`;
    try {
      // In React Native, use Clipboard.setString(text)
      console.log('Copied to clipboard:', text);
      setShowActions(false);
    } catch (error) {
      console.error('Failed to copy measurement:', error);
    }
  };

  const handleShare = async () => {
    const text = `Measurement Result\n${measurement.type.toUpperCase()}: ${formattedValue}\nTimestamp: ${new Date(measurement.timestamp).toLocaleString()}`;
    try {
      // In React Native, use Share.share({ message: text })
      console.log('Sharing measurement:', text);
      setShowActions(false);
    } catch (error) {
      console.error('Failed to share measurement:', error);
    }
  };

  const handleDelete = () => {
    Alert.alert(
      'Delete Measurement',
      'Are you sure you want to delete this measurement?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            onDelete?.(measurement.id);
            setShowActions(false);
          },
        },
      ]
    );
  };

  if (compact) {
    return (
      <View style={[styles.compactItem, { backgroundColor: theme.colors.background }]}>
        <Icon size={14} color={color} />
        <Text style={[styles.compactValue, { color: theme.colors.text }]}>
          {formattedValue}
        </Text>
        <TouchableOpacity
          style={styles.compactAction}
          onPress={() => onDelete?.(measurement.id)}
        >
          <Trash2 size={12} color={theme.colors.destructive} />
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <>
      <View style={[styles.measurementItem, { backgroundColor: theme.colors.background, borderColor: theme.colors.border }]}>
        <TouchableOpacity
          style={styles.measurementContent}
          onPress={() => isVisible ? onHide?.(measurement.id) : onShow?.(measurement)}
        >
          <View style={styles.measurementHeader}>
            <View style={styles.measurementInfo}>
              <Icon size={20} color={color} />
              <View style={styles.measurementDetails}>
                <Text style={[styles.measurementType, { color: theme.colors.text }]}>
                  {measurement.type.charAt(0).toUpperCase() + measurement.type.slice(1)}
                </Text>
                <Text style={[styles.measurementValue, { color }]}>
                  {formattedValue}
                </Text>
              </View>
            </View>
            
            <View style={styles.measurementActions}>
              <TouchableOpacity
                style={styles.visibilityButton}
                onPress={() => isVisible ? onHide?.(measurement.id) : onShow?.(measurement)}
              >
                {isVisible ? (
                  <Eye size={16} color={theme.colors.primary} />
                ) : (
                  <EyeOff size={16} color={theme.colors.muted} />
                )}
              </TouchableOpacity>
              
              <TouchableOpacity
                style={styles.moreButton}
                onPress={() => setShowActions(true)}
              >
                <MoreHorizontal size={16} color={theme.colors.muted} />
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.measurementMeta}>
            <View style={styles.metaItem}>
              <Clock size={12} color={theme.colors.muted} />
              <Text style={[styles.metaText, { color: theme.colors.muted }]}>
                {new Date(measurement.timestamp).toLocaleTimeString()}
              </Text>
            </View>
            
            <View style={styles.metaItem}>
              <MapPin size={12} color={theme.colors.muted} />
              <Text style={[styles.metaText, { color: theme.colors.muted }]}>
                {measurement.coordinates.length} point{measurement.coordinates.length !== 1 ? 's' : ''}
              </Text>
            </View>
          </View>
        </TouchableOpacity>
      </View>

      <Modal
        visible={showActions}
        transparent
        animationType="fade"
        onRequestClose={() => setShowActions(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setShowActions(false)}
        >
          <View style={[styles.actionsMenu, { backgroundColor: theme.colors.card }]}>
            <TouchableOpacity style={styles.actionItem} onPress={handleCopy}>
              <Copy size={16} color={theme.colors.text} />
              <Text style={[styles.actionText, { color: theme.colors.text }]}>Copy</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.actionItem} onPress={handleShare}>
              <Share size={16} color={theme.colors.text} />
              <Text style={[styles.actionText, { color: theme.colors.text }]}>Share</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[styles.actionItem, styles.actionItemDestructive]} 
              onPress={handleDelete}
            >
              <Trash2 size={16} color={theme.colors.destructive} />
              <Text style={[styles.actionText, { color: theme.colors.destructive }]}>Delete</Text>
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </Modal>
    </>
  );
};

export const MeasurementDisplay: React.FC<MeasurementDisplayProps> = ({
  measurements,
  onMeasurementDelete,
  onMeasurementShow,
  onMeasurementHide,
  onClearAll,
  onExport,
  compact = false,
  maxVisible = 5,
}) => {
  const { theme } = useTheme();
  const [showAll, setShowAll] = useState(false);
  const [visibleMeasurements, setVisibleMeasurements] = useState<Set<string>>(
    new Set(measurements.map(m => m.id))
  );

  const displayedMeasurements = showAll ? measurements : measurements.slice(0, maxVisible);
  const hasMore = measurements.length > maxVisible;

  const handleToggleVisibility = useCallback((measurementId: string) => {
    setVisibleMeasurements(prev => {
      const newSet = new Set(prev);
      if (newSet.has(measurementId)) {
        newSet.delete(measurementId);
        onMeasurementHide?.(measurementId);
      } else {
        newSet.add(measurementId);
        const measurement = measurements.find(m => m.id === measurementId);
        if (measurement) {
          onMeasurementShow?.(measurement);
        }
      }
      return newSet;
    });
  }, [measurements, onMeasurementShow, onMeasurementHide]);

  const handleClearAll = () => {
    Alert.alert(
      'Clear All Measurements',
      'Are you sure you want to delete all measurements?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear All',
          style: 'destructive',
          onPress: () => {
            onClearAll?.();
            setVisibleMeasurements(new Set());
          },
        },
      ]
    );
  };

  const handleExport = () => {
    onExport?.(measurements);
  };

  if (measurements.length === 0) {
    return (
      <View style={[styles.emptyState, { backgroundColor: theme.colors.background }]}>
        <Ruler size={32} color={theme.colors.muted} />
        <Text style={[styles.emptyStateText, { color: theme.colors.muted }]}>
          No measurements yet
        </Text>
        <Text style={[styles.emptyStateSubtext, { color: theme.colors.muted }]}>
          Use the measurement tools to start measuring distances, areas, and more
        </Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.card }]}>
      <View style={[styles.header, { borderBottomColor: theme.colors.border }]}>
        <View style={styles.headerLeft}>
          <Text style={[styles.title, { color: theme.colors.text }]}>
            Measurements ({measurements.length})
          </Text>
        </View>
        
        <View style={styles.headerActions}>
          {measurements.length > 0 && (
            <>
              <TouchableOpacity
                style={[styles.headerButton, { backgroundColor: theme.colors.info }]}
                onPress={handleExport}
              >
                <Download size={14} color="white" />
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.headerButton, { backgroundColor: theme.colors.destructive }]}
                onPress={handleClearAll}
              >
                <Trash2 size={14} color="white" />
              </TouchableOpacity>
            </>
          )}
        </View>
      </View>

      <ScrollView style={styles.measurementsList} showsVerticalScrollIndicator={false}>
        {displayedMeasurements.map(measurement => (
          <MeasurementItem
            key={measurement.id}
            measurement={measurement}
            onDelete={onMeasurementDelete}
            onShow={onMeasurementShow}
            onHide={(id) => handleToggleVisibility(id)}
            isVisible={visibleMeasurements.has(measurement.id)}
            compact={compact}
          />
        ))}
      </ScrollView>

      {hasMore && !showAll && (
        <TouchableOpacity
          style={[styles.showMoreButton, { backgroundColor: theme.colors.background, borderTopColor: theme.colors.border }]}
          onPress={() => setShowAll(true)}
        >
          <Maximize2 size={14} color={theme.colors.primary} />
          <Text style={[styles.showMoreText, { color: theme.colors.primary }]}>
            Show {measurements.length - maxVisible} more
          </Text>
        </TouchableOpacity>
      )}

      {showAll && hasMore && (
        <TouchableOpacity
          style={[styles.showMoreButton, { backgroundColor: theme.colors.background, borderTopColor: theme.colors.border }]}
          onPress={() => setShowAll(false)}
        >
          <Text style={[styles.showMoreText, { color: theme.colors.primary }]}>
            Show less
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 8,
    overflow: 'hidden',
    maxHeight: 400,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  headerLeft: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8,
  },
  headerButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    alignItems: 'center',
    justifyContent: 'center',
  },
  measurementsList: {
    flex: 1,
    maxHeight: 300,
  },
  measurementItem: {
    borderBottomWidth: 1,
  },
  measurementContent: {
    padding: 12,
  },
  measurementHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  measurementInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    gap: 12,
  },
  measurementDetails: {
    flex: 1,
  },
  measurementType: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 2,
  },
  measurementValue: {
    fontSize: 16,
    fontFamily: 'Inter-Bold',
  },
  measurementActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  visibilityButton: {
    padding: 4,
  },
  moreButton: {
    padding: 4,
  },
  measurementMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  metaText: {
    fontSize: 11,
    fontFamily: 'Inter-Regular',
  },
  compactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    gap: 6,
    marginBottom: 4,
  },
  compactValue: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    flex: 1,
  },
  compactAction: {
    padding: 2,
  },
  showMoreButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderTopWidth: 1,
    gap: 6,
  },
  showMoreText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 32,
    borderRadius: 8,
    gap: 8,
  },
  emptyStateText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    textAlign: 'center',
  },
  emptyStateSubtext: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
    lineHeight: 16,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionsMenu: {
    borderRadius: 8,
    padding: 8,
    minWidth: 150,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 8,
  },
  actionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderRadius: 4,
    gap: 8,
  },
  actionItemDestructive: {
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    marginTop: 4,
    paddingTop: 8,
  },
  actionText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
  },
});
