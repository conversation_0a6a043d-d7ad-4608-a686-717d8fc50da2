import { supabase, TABLES } from '../supabase';
import { BaseApi } from './base';
import { 
  Team, 
  TeamInsert, 
  TeamUpdate,
  TeamMember,
  TeamMemberInsert,
  TeamMemberUpdate,
  TeamWithMembers,
  UserProfile,
  ApiResponse,
  PaginatedResponse
} from '@/types/database';

export interface TeamFilters {
  organization?: string;
  isActive?: boolean;
  search?: string;
  memberId?: string;
}

export interface TeamMemberFilters {
  teamId?: string;
  role?: 'lead' | 'member';
  isActive?: boolean;
}

export interface TeamInvitation {
  email: string;
  role: 'lead' | 'member';
  teamId: string;
}

/**
 * Teams API Service
 * Handles team management, member operations, and team-related analytics
 */
export class TeamsApi extends BaseApi {
  constructor() {
    super(TABLES.TEAMS);
  }

  /**
   * Create a new team
   */
  async createTeam(teamData: Omit<TeamInsert, 'id' | 'created_at' | 'updated_at'>): Promise<ApiResponse<Team>> {
    const userId = await this.getCurrentUserId();
    if (!userId) {
      return {
        data: null,
        error: 'User not authenticated'
      };
    }

    const newTeam: TeamInsert = {
      ...teamData,
      created_by: userId,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const response = await this.handleResponse<Team>(
      supabase
        .from(TABLES.TEAMS)
        .insert(newTeam)
        .select(`
          *,
          user_profiles:created_by (
            id,
            full_name,
            email,
            role
          )
        `)
        .single(),
      'create team'
    );

    // Automatically add creator as team lead
    if (response.data) {
      await this.addTeamMember(response.data.id, userId, 'lead');
    }

    return response;
  }

  /**
   * Get teams with pagination and filters
   */
  async getTeams(
    page: number = 1,
    pageSize: number = 20,
    filters: TeamFilters = {}
  ): Promise<PaginatedResponse<TeamWithMembers>> {
    const from = (page - 1) * pageSize;
    const to = from + pageSize - 1;

    let query = supabase
      .from(TABLES.TEAMS)
      .select(`
        *,
        user_profiles:created_by (
          id,
          full_name,
          email,
          role
        ),
        team_members!inner (
          id,
          user_id,
          role,
          joined_at,
          is_active,
          user_profiles (
            id,
            full_name,
            email,
            role,
            avatar_url,
            last_seen
          )
        )
      `, { count: 'exact' })
      .range(from, to)
      .order('updated_at', { ascending: false });

    // Apply filters
    if (filters.organization) {
      query = query.eq('organization', filters.organization);
    }
    if (filters.isActive !== undefined) {
      query = query.eq('is_active', filters.isActive);
    }
    if (filters.search) {
      query = query.or(`name.ilike.%${filters.search}%,description.ilike.%${filters.search}%`);
    }
    if (filters.memberId) {
      query = query.eq('team_members.user_id', filters.memberId);
      query = query.eq('team_members.is_active', true);
    }

    return this.handlePaginatedResponse<TeamWithMembers>(
      query,
      'get teams',
      page,
      pageSize
    );
  }

  /**
   * Get team by ID with full details
   */
  async getTeamById(teamId: string): Promise<ApiResponse<TeamWithMembers>> {
    return this.handleResponse<TeamWithMembers>(
      supabase
        .from(TABLES.TEAMS)
        .select(`
          *,
          user_profiles:created_by (
            id,
            full_name,
            email,
            role
          ),
          team_members (
            id,
            user_id,
            role,
            joined_at,
            is_active,
            user_profiles (
              id,
              full_name,
              email,
              role,
              avatar_url,
              last_seen,
              is_active
            )
          )
        `)
        .eq('id', teamId)
        .single(),
      'get team by id'
    );
  }

  /**
   * Update team
   */
  async updateTeam(
    teamId: string,
    updates: Omit<TeamUpdate, 'updated_at'>
  ): Promise<ApiResponse<Team>> {
    const teamUpdate: TeamUpdate = {
      ...updates,
      updated_at: new Date().toISOString()
    };

    return this.handleResponse<Team>(
      supabase
        .from(TABLES.TEAMS)
        .update(teamUpdate)
        .eq('id', teamId)
        .select(`
          *,
          user_profiles:created_by (
            id,
            full_name,
            email,
            role
          )
        `)
        .single(),
      'update team'
    );
  }

  /**
   * Delete team (soft delete)
   */
  async deleteTeam(teamId: string): Promise<ApiResponse<Team>> {
    // First, deactivate all team members
    await supabase
      .from(TABLES.TEAM_MEMBERS)
      .update({ is_active: false })
      .eq('team_id', teamId);

    // Then deactivate the team
    return this.updateTeam(teamId, { is_active: false });
  }

  /**
   * Add member to team
   */
  async addTeamMember(
    teamId: string,
    userId: string,
    role: 'lead' | 'member' = 'member'
  ): Promise<ApiResponse<TeamMember>> {
    const memberData: TeamMemberInsert = {
      team_id: teamId,
      user_id: userId,
      role,
      joined_at: new Date().toISOString()
    };

    return this.handleResponse<TeamMember>(
      supabase
        .from(TABLES.TEAM_MEMBERS)
        .insert(memberData)
        .select(`
          *,
          user_profiles (
            id,
            full_name,
            email,
            role,
            avatar_url
          ),
          teams (
            id,
            name,
            description
          )
        `)
        .single(),
      'add team member'
    );
  }

  /**
   * Add member to team by email
   */
  async addTeamMemberByEmail(
    teamId: string,
    email: string,
    role: 'lead' | 'member' = 'member'
  ): Promise<ApiResponse<TeamMember>> {
    try {
      // First, find user by email
      const { data: user, error: userError } = await supabase
        .from(TABLES.USER_PROFILES)
        .select('id')
        .eq('email', email)
        .eq('is_active', true)
        .single();

      if (userError || !user) {
        return {
          data: null,
          error: `User with email ${email} not found or inactive`
        };
      }

      // Check if user is already a member
      const { data: existingMember } = await supabase
        .from(TABLES.TEAM_MEMBERS)
        .select('id')
        .eq('team_id', teamId)
        .eq('user_id', user.id)
        .eq('is_active', true)
        .single();

      if (existingMember) {
        return {
          data: null,
          error: 'User is already a member of this team'
        };
      }

      // Add member
      return this.addTeamMember(teamId, user.id, role);
    } catch (error) {
      console.error('Add team member by email failed:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to add team member'
      };
    }
  }

  /**
   * Remove member from team
   */
  async removeTeamMember(teamId: string, userId: string): Promise<ApiResponse<null>> {
    return this.handleResponse<null>(
      supabase
        .from(TABLES.TEAM_MEMBERS)
        .update({ is_active: false })
        .eq('team_id', teamId)
        .eq('user_id', userId),
      'remove team member'
    );
  }

  /**
   * Update team member role
   */
  async updateTeamMemberRole(
    teamId: string,
    userId: string,
    role: 'lead' | 'member'
  ): Promise<ApiResponse<TeamMember>> {
    return this.handleResponse<TeamMember>(
      supabase
        .from(TABLES.TEAM_MEMBERS)
        .update({ role })
        .eq('team_id', teamId)
        .eq('user_id', userId)
        .select(`
          *,
          user_profiles (
            id,
            full_name,
            email,
            role,
            avatar_url
          )
        `)
        .single(),
      'update team member role'
    );
  }

  /**
   * Get team members
   */
  async getTeamMembers(
    teamId: string,
    filters: TeamMemberFilters = {}
  ): Promise<ApiResponse<Array<TeamMember & { user_profiles: UserProfile }>>> {
    let query = supabase
      .from(TABLES.TEAM_MEMBERS)
      .select(`
        *,
        user_profiles (
          id,
          full_name,
          email,
          role,
          avatar_url,
          last_seen,
          is_active
        )
      `)
      .eq('team_id', teamId)
      .order('joined_at', { ascending: false });

    // Apply filters
    if (filters.role) {
      query = query.eq('role', filters.role);
    }
    if (filters.isActive !== undefined) {
      query = query.eq('is_active', filters.isActive);
    }

    return this.handleResponse(
      query,
      'get team members'
    );
  }

  /**
   * Get user's teams
   */
  async getUserTeams(userId?: string): Promise<ApiResponse<TeamWithMembers[]>> {
    const currentUserId = userId || await this.getCurrentUserId();
    if (!currentUserId) {
      return {
        data: null,
        error: 'User not authenticated'
      };
    }

    return this.handleResponse<TeamWithMembers[]>(
      supabase
        .from(TABLES.TEAMS)
        .select(`
          *,
          user_profiles:created_by (
            id,
            full_name,
            email
          ),
          team_members!inner (
            id,
            role,
            joined_at,
            is_active
          )
        `)
        .eq('team_members.user_id', currentUserId)
        .eq('team_members.is_active', true)
        .eq('is_active', true)
        .order('updated_at', { ascending: false }),
      'get user teams'
    );
  }

  /**
   * Search teams
   */
  async searchTeams(
    searchTerm: string,
    page: number = 1,
    pageSize: number = 20,
    filters: Omit<TeamFilters, 'search'> = {}
  ): Promise<PaginatedResponse<TeamWithMembers>> {
    return this.search<TeamWithMembers>(
      searchTerm,
      ['name', 'description'],
      page,
      pageSize,
      filters
    );
  }

  /**
   * Get team performance stats
   */
  async getTeamPerformance(teamId?: string): Promise<ApiResponse<any>> {
    let query = supabase
      .from('team_performance')
      .select('*');

    if (teamId) {
      query = query.eq('team_id', teamId);
    }

    return this.handleResponse(
      query,
      'get team performance'
    );
  }

  /**
   * Get team activity summary
   */
  async getTeamActivity(
    teamId: string,
    dateFrom?: string,
    dateTo?: string
  ): Promise<ApiResponse<any>> {
    try {
      // Get team submissions in date range
      let query = supabase
        .from(TABLES.SUBMISSIONS)
        .select(`
          id,
          status,
          started_at,
          completed_at,
          user_profiles (
            id,
            full_name,
            email
          ),
          forms (
            id,
            name,
            project_id,
            projects (
              id,
              name
            )
          )
        `)
        .in('user_id', 
          supabase
            .from(TABLES.TEAM_MEMBERS)
            .select('user_id')
            .eq('team_id', teamId)
            .eq('is_active', true)
        )
        .order('started_at', { ascending: false });

      if (dateFrom) {
        query = query.gte('started_at', dateFrom);
      }
      if (dateTo) {
        query = query.lte('started_at', dateTo);
      }

      const { data, error } = await query;

      if (error) {
        throw error;
      }

      // Process activity data
      const activitySummary = {
        totalSubmissions: data?.length || 0,
        completedSubmissions: data?.filter(s => s.status === 'completed' || s.status === 'synced').length || 0,
        activeMembers: new Set(data?.map(s => s.user_profiles?.id)).size,
        recentActivity: data?.slice(0, 10) || []
      };

      return {
        data: activitySummary,
        error: null
      };
    } catch (error) {
      console.error('Get team activity failed:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to get team activity'
      };
    }
  }

  /**
   * Invite user to team (placeholder for email invitation system)
   */
  async inviteUserToTeam(invitation: TeamInvitation): Promise<ApiResponse<any>> {
    try {
      // In a real implementation, this would send an email invitation
      // For now, we'll just check if the user exists and add them directly
      const result = await this.addTeamMemberByEmail(
        invitation.teamId,
        invitation.email,
        invitation.role
      );

      if (result.error) {
        return result;
      }

      // TODO: Send email invitation
      console.log(`Invitation sent to ${invitation.email} for team ${invitation.teamId}`);

      return {
        data: { message: 'Invitation sent successfully' },
        error: null
      };
    } catch (error) {
      console.error('Invite user to team failed:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to send invitation'
      };
    }
  }

  /**
   * Get team projects
   */
  async getTeamProjects(teamId: string): Promise<ApiResponse<any[]>> {
    return this.handleResponse(
      supabase
        .from(TABLES.PROJECT_TEAMS)
        .select(`
          *,
          projects (
            id,
            name,
            description,
            status,
            created_at,
            updated_at
          )
        `)
        .eq('team_id', teamId)
        .eq('is_active', true)
        .order('assigned_at', { ascending: false }),
      'get team projects'
    );
  }

  /**
   * Check if user can manage team
   */
  async canManageTeam(teamId: string, userId?: string): Promise<boolean> {
    const currentUserId = userId || await this.getCurrentUserId();
    if (!currentUserId) return false;

    // Check if user is admin/manager
    const isAdmin = await this.checkUserRole(['admin', 'manager']);
    if (isAdmin) return true;

    // Check if user is team lead or creator
    const { data: team } = await supabase
      .from(TABLES.TEAMS)
      .select(`
        created_by,
        team_members!inner (
          role
        )
      `)
      .eq('id', teamId)
      .eq('team_members.user_id', currentUserId)
      .eq('team_members.is_active', true)
      .single();

    return team?.created_by === currentUserId || 
           team?.team_members?.some(m => m.role === 'lead');
  }

  /**
   * Subscribe to team changes
   */
  subscribeToTeamChanges(callback: (payload: any) => void) {
    return this.subscribeToChanges(callback);
  }

  /**
   * Subscribe to team member changes
   * Returns null on mobile platforms where real-time is disabled
   */
  subscribeToTeamMemberChanges(teamId: string, callback: (payload: any) => void) {
    try {
      return supabase
        .channel(`team-members-${teamId}`)
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: TABLES.TEAM_MEMBERS,
            filter: `team_id=eq.${teamId}`
          },
          callback
        )
        .subscribe();
    } catch (error) {
      console.warn(`Real-time subscriptions not available for team members:`, error);
      return null;
    }
  }
}

// Export singleton instance
export const teamsApi = new TeamsApi();
export default teamsApi;
