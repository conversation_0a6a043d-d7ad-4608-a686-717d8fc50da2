import React, { useState, useEffect, useRef, useCallback, useImperativeHandle, forwardRef } from 'react';
import { View, StyleSheet, Alert } from 'react-native';
import { useTheme } from '@/hooks/useTheme';

// Leaflet type definitions for web environment
declare global {
  interface Window {
    L: any;
    turf: any;
    shp: any;
  }
}

type DrawingMode = 'none' | 'point' | 'line' | 'polygon' | 'rectangle' | 'circle';
type MapType = 'standard' | 'satellite' | 'terrain' | 'hybrid';

interface Region {
  latitude: number;
  longitude: number;
  latitudeDelta: number;
  longitudeDelta: number;
}

interface EnhancedLeafletMapProps {
  initialRegion?: Region;
  mapType?: MapType;
  drawingMode?: DrawingMode;
  measurementMode?: boolean;
  userLocation?: any;
  geoFeatures?: any[];
  onLocationSelect?: (location: { latitude: number; longitude: number }) => void;
  onFeatureCreated?: (feature: any) => void;
  onFeatureUpdated?: (feature: any) => void;
  onFeatureDeleted?: (featureId: string) => void;
  onZoomIn?: () => void;
  onZoomOut?: () => void;
  onExportGeoJSON?: (data: any) => void;
  onExportShapefile?: (data: any) => void;
  onAnalysisResult?: (result: any) => void;
  style?: any;
}

// Export interface for map methods
export interface MapRef {
  zoomIn: () => void;
  zoomOut: () => void;
  centerOnLocation: (lat: number, lng: number) => void;
  fitBounds: (bounds: any) => void;
  getZoom: () => number;
  getCenter: () => { lat: number; lng: number };
  exportGeoJSON: () => any;
  exportShapefile: () => Promise<void>;
  runAnalysis: (type: string, options?: any) => any;
  getAllFeatures: () => any[];
}

const EnhancedLeafletMap = forwardRef<MapRef, EnhancedLeafletMapProps>((props, ref) => {
  const {
    initialRegion,
    mapType = 'standard',
    drawingMode = 'none',
    measurementMode = false,
    userLocation,
    geoFeatures = [],
    onLocationSelect,
    onFeatureCreated,
    onFeatureUpdated,
    onFeatureDeleted,
    onZoomIn,
    onZoomOut,
    onExportGeoJSON,
    onExportShapefile,
    onAnalysisResult,
    style,
  } = props;

  const { theme } = useTheme();
  
  // Map references and state
  const mapRef = useRef<any>(null);
  const drawnItemsRef = useRef<any>(null);
  const currentDrawControlRef = useRef<any>(null);
  const measureControlRef = useRef<any>(null);
  
  const [isMapLoaded, setIsMapLoaded] = useState(false);
  const [isDrawing, setIsDrawing] = useState(false);
  const [leafletLoaded, setLeafletLoaded] = useState(false);
  const [turfLoaded, setTurfLoaded] = useState(false);
  const [shapefileLoaded, setShapefileLoaded] = useState(false);
  const [allFeatures, setAllFeatures] = useState<any[]>([]);

  // Expose enhanced map methods to parent component
  useImperativeHandle(ref, () => ({
    zoomIn: () => {
      if (mapRef.current) {
        mapRef.current.zoomIn();
        if (onZoomIn) onZoomIn();
      }
    },
    zoomOut: () => {
      if (mapRef.current) {
        mapRef.current.zoomOut();
        if (onZoomOut) onZoomOut();
      }
    },
    centerOnLocation: (lat: number, lng: number) => {
      if (mapRef.current) {
        mapRef.current.setView([lat, lng], mapRef.current.getZoom());
      }
    },
    fitBounds: (bounds: any) => {
      if (mapRef.current) {
        mapRef.current.fitBounds(bounds);
      }
    },
    getZoom: () => {
      return mapRef.current ? mapRef.current.getZoom() : 12;
    },
    getCenter: () => {
      if (mapRef.current) {
        const center = mapRef.current.getCenter();
        return { lat: center.lat, lng: center.lng };
      }
      return { lat: 0, lng: 0 };
    },
    exportGeoJSON: () => {
      return exportToGeoJSON();
    },
    exportShapefile: async () => {
      await exportToShapefile();
    },
    runAnalysis: (type: string, options?: any) => {
      return performSpatialAnalysis(type, options);
    },
    getAllFeatures: () => {
      return allFeatures;
    },
  }));

  // Load all required libraries
  useEffect(() => {
    loadAllLibraries();
    return () => {
      cleanup();
    };
  }, []);

  // Update drawing mode
  useEffect(() => {
    if (isMapLoaded && leafletLoaded) {
      updateDrawingMode();
    }
  }, [drawingMode, isMapLoaded, leafletLoaded]);

  // Update measurement mode
  useEffect(() => {
    if (isMapLoaded && leafletLoaded) {
      updateMeasurementMode();
    }
  }, [measurementMode, isMapLoaded, leafletLoaded]);

  // Update map type
  useEffect(() => {
    if (isMapLoaded && leafletLoaded) {
      updateMapType();
    }
  }, [mapType, isMapLoaded, leafletLoaded]);

  // Update features
  useEffect(() => {
    if (isMapLoaded && leafletLoaded) {
      updateMapFeatures();
    }
  }, [geoFeatures, isMapLoaded, leafletLoaded]);

  const cleanup = () => {
    if (mapRef.current) {
      try {
        mapRef.current.remove();
        mapRef.current = null;
      } catch (error) {
        console.error('Error cleaning up map:', error);
      }
    }
  };

  const loadAllLibraries = async () => {
    try {
      await loadLeafletLibraries();
      await loadTurfLibrary();
      await loadShapefileLibrary();
    } catch (error) {
      console.error('Error loading libraries:', error);
      Alert.alert('Map Error', 'Failed to load required libraries');
    }
  };

  const loadLeafletLibraries = async () => {
    return new Promise<void>((resolve) => {
      // Check if Leaflet is already loaded
      if (typeof window !== 'undefined' && window.L) {
        setLeafletLoaded(true);
        initializeMap();
        resolve();
        return;
      }

      // Load Leaflet CSS
      const cssLink = document.createElement('link');
      cssLink.rel = 'stylesheet';
      cssLink.href = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css';
      cssLink.integrity = 'sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=';
      cssLink.crossOrigin = '';
      document.head.appendChild(cssLink);

      // Load Leaflet JavaScript
      const leafletScript = document.createElement('script');
      leafletScript.src = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.js';
      leafletScript.integrity = 'sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=';
      leafletScript.crossOrigin = '';
      
      leafletScript.onload = () => {
        loadDrawPlugin(() => {
          setLeafletLoaded(true);
          initializeMap();
          resolve();
        });
      };
      
      document.head.appendChild(leafletScript);
    });
  };

  const loadDrawPlugin = (callback: () => void) => {
    // Load Leaflet Draw CSS
    const drawCss = document.createElement('link');
    drawCss.rel = 'stylesheet';
    drawCss.href = 'https://cdnjs.cloudflare.com/ajax/libs/leaflet.draw/1.0.4/leaflet.draw.css';
    document.head.appendChild(drawCss);

    // Load Leaflet Draw JS
    const drawScript = document.createElement('script');
    drawScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/leaflet.draw/1.0.4/leaflet.draw.js';
    drawScript.onload = callback;
    document.head.appendChild(drawScript);
  };

  const loadTurfLibrary = async () => {
    return new Promise<void>((resolve) => {
      if (typeof window !== 'undefined' && window.turf) {
        setTurfLoaded(true);
        resolve();
        return;
      }

      const turfScript = document.createElement('script');
      turfScript.src = 'https://unpkg.com/@turf/turf@6.5.0/turf.min.js';
      turfScript.onload = () => {
        setTurfLoaded(true);
        console.log('Turf.js loaded successfully');
        resolve();
      };
      turfScript.onerror = () => {
        console.error('Failed to load Turf.js');
        resolve(); // Continue even if turf fails to load
      };
      document.head.appendChild(turfScript);
    });
  };

  const loadShapefileLibrary = async () => {
    return new Promise<void>((resolve) => {
      if (typeof window !== 'undefined' && window.shp) {
        setShapefileLoaded(true);
        resolve();
        return;
      }

      const shpScript = document.createElement('script');
      shpScript.src = 'https://unpkg.com/shpjs@4.0.4/dist/shp.min.js';
      shpScript.onload = () => {
        setShapefileLoaded(true);
        console.log('Shapefile library loaded successfully');
        resolve();
      };
      shpScript.onerror = () => {
        console.error('Failed to load Shapefile library');
        resolve(); // Continue even if shapefile fails to load
      };
      document.head.appendChild(shpScript);
    });
  };

  const initializeMap = useCallback(() => {
    if (!window.L || !document.getElementById('enhanced-leaflet-map')) {
      setTimeout(initializeMap, 100);
      return;
    }

    try {
      // Initialize map with better defaults
      const map = window.L.map('enhanced-leaflet-map', {
        center: [
          initialRegion?.latitude || 37.78825,
          initialRegion?.longitude || -122.4324
        ],
        zoom: getZoomFromRegion(),
        zoomControl: false, // We'll add our own controls
        attributionControl: true,
        preferCanvas: true, // Better performance for many features
        maxZoom: 20,
        minZoom: 2,
      });

      // Add base layers
      const baseLayers = createBaseLayers();
      baseLayers[getLayerKey(mapType)].addTo(map);

      // Create feature group for drawn items
      const drawnItems = new window.L.FeatureGroup();
      map.addLayer(drawnItems);

      // Map event handlers
      map.on('click', handleMapClick);
      map.on('draw:created', handleDrawCreated);
      map.on('draw:edited', handleDrawEdited);
      map.on('draw:deleted', handleDrawDeleted);
      map.on('draw:drawstart', () => setIsDrawing(true));
      map.on('draw:drawstop', () => setIsDrawing(false));

      // Store references
      mapRef.current = map;
      drawnItemsRef.current = drawnItems;
      setIsMapLoaded(true);

      // Load existing features
      setTimeout(() => updateMapFeatures(), 100);

      console.log('Enhanced map initialized successfully');

    } catch (error) {
      console.error('Error initializing map:', error);
      Alert.alert('Map Error', 'Failed to initialize map');
    }
  }, [initialRegion, mapType]);

  const createBaseLayers = () => {
    return {
      standard: window.L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors',
        maxZoom: 19,
      }),
      satellite: window.L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
        attribution: '© Esri',
        maxZoom: 18,
      }),
      terrain: window.L.tileLayer('https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenTopoMap contributors',  
        maxZoom: 16,
      }),
      hybrid: window.L.layerGroup([
        window.L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
          attribution: '© Esri',
          maxZoom: 18,
        }),
        window.L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/Reference/World_Boundaries_and_Places/MapServer/tile/{z}/{y}/{x}', {
          attribution: '© Esri',
          maxZoom: 18,
        })
      ])
    };
  };

  const getLayerKey = (type: MapType): string => {
    switch (type) {
      case 'satellite': return 'satellite';
      case 'terrain': return 'terrain';
      case 'hybrid': return 'hybrid';
      default: return 'standard';
    }
  };

  const getZoomFromRegion = (): number => {
    if (!initialRegion) return 12;
    const delta = initialRegion.latitudeDelta;
    if (delta <= 0.005) return 17;
    if (delta <= 0.01) return 16;
    if (delta <= 0.05) return 14;
    if (delta <= 0.1) return 12;
    if (delta <= 0.5) return 10;
    return 8;
  };

  const updateDrawingMode = () => {
    if (!mapRef.current || !drawnItemsRef.current || !window.L.Control.Draw) return;

    // Remove existing draw control
    if (currentDrawControlRef.current) {
      mapRef.current.removeControl(currentDrawControlRef.current);
      currentDrawControlRef.current = null;
    }

    // Add new draw control if drawing mode is active
    if (drawingMode !== 'none') {
      const drawOptions = getDrawOptions();
      const drawControl = new window.L.Control.Draw({
        position: 'topleft',
        draw: drawOptions,
        edit: {
          featureGroup: drawnItemsRef.current,
          remove: true,
        }
      });

      mapRef.current.addControl(drawControl);
      currentDrawControlRef.current = drawControl;
    }
  };

  const getDrawOptions = () => {
    const baseOptions = {
      shapeOptions: {
        color: theme.colors.primary,
        weight: 2,
        fillOpacity: 0.2,
      }
    };

    const options: any = {
      polygon: false,
      polyline: false,
      rectangle: false,
      circle: false,
      marker: false,
      circlemarker: false,
    };

    switch (drawingMode) {
      case 'point':
        options.marker = {
          icon: createCustomMarkerIcon(),
        };
        break;
      case 'line':
        options.polyline = {
          shapeOptions: {
            color: theme.colors.primary,
            weight: 3,
          }
        };
        break;
      case 'polygon':
        options.polygon = {
          allowIntersection: false,
          drawError: {
            color: theme.colors.error,
            message: 'Shape edges cannot cross!'
          },
          ...baseOptions,
        };
        break;
      case 'rectangle':
        options.rectangle = baseOptions;
        break;
      case 'circle':
        options.circle = baseOptions;
        break;
    }

    return options;
  };

  const createCustomMarkerIcon = () => {
    return window.L.divIcon({
      className: 'custom-map-marker',
      html: `
        <div style="
          background-color: ${theme.colors.primary};
          width: 16px;
          height: 16px;
          border-radius: 50%;
          border: 3px solid white;
          box-shadow: 0 2px 6px rgba(0,0,0,0.3);
        "></div>
      `,
      iconSize: [22, 22],
      iconAnchor: [11, 11],
    });
  };

  const updateMeasurementMode = () => {
    if (!mapRef.current) return;

    // Remove existing measurement control
    if (measureControlRef.current) {
      mapRef.current.removeControl(measureControlRef.current);
      measureControlRef.current = null;
    }

    // Add measurement functionality
    if (measurementMode && turfLoaded) {
      console.log('Measurement mode activated with Turf.js');
      // Add click handlers for measurement
      mapRef.current.on('click', handleMeasurementClick);
    } else {
      mapRef.current.off('click', handleMeasurementClick);
    }
  };

  const handleMeasurementClick = (e: any) => {
    // Measurement logic would go here
    // For now, just log the coordinates
    console.log('Measurement click at:', e.latlng);
  };

  const updateMapType = () => {
    if (!mapRef.current) return;

    const baseLayers = createBaseLayers();
    
    // Remove all existing base layers
    mapRef.current.eachLayer((layer: any) => {
      if (layer.options && layer.options.attribution) {
        mapRef.current.removeLayer(layer);
      }
    });

    // Add the selected base layer
    baseLayers[getLayerKey(mapType)].addTo(mapRef.current);
  };

  const updateMapFeatures = () => {
    if (!drawnItemsRef.current) return;

    // Clear existing features but preserve drawn features
    drawnItemsRef.current.eachLayer((layer: any) => {
      if (!layer.isDrawnFeature) {
        drawnItemsRef.current.removeLayer(layer);
      }
    });

    // Add new features from props
    geoFeatures.forEach(feature => {
      try {
        const layer = createFeatureLayer(feature);
        if (layer) {
          layer.featureId = feature.id || `feature_${Date.now()}`;
          layer.bindPopup(createFeaturePopup(feature));
          drawnItemsRef.current.addLayer(layer);
        }
      } catch (error) {
        console.error('Error adding feature to map:', error);
      }
    });

    // Update all features array
    updateAllFeaturesArray();
  };

  const createFeatureLayer = (feature: any) => {
    if (!feature.coordinates && !feature.geometry) return null;

    const coords = feature.coordinates || feature.geometry.coordinates;
    const type = feature.type || (feature.geometry && feature.geometry.type?.toLowerCase());

    switch (type) {
      case 'point':
        if (Array.isArray(coords) && coords.length >= 2) {
          const [lng, lat] = coords;
          return window.L.marker([lat, lng], {
            icon: createCustomMarkerIcon()
          });
        }
        break;

      case 'line':
      case 'linestring':
        if (Array.isArray(coords) && coords.length >= 2) {
          const latlngs = coords.map(([lng, lat]: [number, number]) => [lat, lng]);
          return window.L.polyline(latlngs, {
            color: feature.properties?.color || theme.colors.primary,
            weight: 3,
            opacity: 0.8,
          });
        }
        break;

      case 'polygon':
        if (Array.isArray(coords) && coords.length > 0) {
          const outerRing = coords[0] || coords;
          const latlngs = outerRing.map(([lng, lat]: [number, number]) => [lat, lng]);
          return window.L.polygon(latlngs, {
            color: feature.properties?.color || theme.colors.primary,
            weight: 2,
            fillColor: feature.properties?.color || theme.colors.primary,
            fillOpacity: 0.2,
          });
        }
        break;

      case 'circle':
        if (Array.isArray(coords) && coords.length >= 2) {
          const [lng, lat] = coords;
          const radius = feature.properties?.radius || 100;
          return window.L.circle([lat, lng], {
            color: feature.properties?.color || theme.colors.primary,
            fillColor: feature.properties?.color || theme.colors.primary,
            fillOpacity: 0.2,
            radius: radius,
          });
        }
        break;

      case 'rectangle':
        if (Array.isArray(coords) && coords.length >= 2) {
          const [[lng1, lat1], [lng2, lat2]] = coords;
          return window.L.rectangle([[lat1, lng1], [lat2, lng2]], {
            color: feature.properties?.color || theme.colors.primary,
            fillColor: feature.properties?.color || theme.colors.primary,
            fillOpacity: 0.2,
          });
        }
        break;
    }

    return null;
  };

  const createFeaturePopup = (feature: any) => {
    const name = feature.properties?.name || feature.name || 'Unnamed Feature';
    const type = feature.type || 'Unknown';
    const description = feature.properties?.description || feature.description || '';
    const created = feature.properties?.created || feature.created || feature.createdAt;
    
    // Add measurements if available
    const measurements = feature.properties?.measurements;
    let measurementText = '';
    if (measurements) {
      if (measurements.area) measurementText += `<br>Area: ${measurements.area.toFixed(2)} m²`;
      if (measurements.length) measurementText += `<br>Length: ${measurements.length.toFixed(2)} m`;
      if (measurements.perimeter) measurementText += `<br>Perimeter: ${measurements.perimeter.toFixed(2)} m`;
      if (measurements.radius) measurementText += `<br>Radius: ${measurements.radius.toFixed(2)} m`;
    }

    return `
      <div style="min-width: 200px; font-family: 'Inter', sans-serif;">
        <h3 style="margin: 0 0 8px 0; color: ${theme.colors.text}; font-size: 16px;">
          ${name}
        </h3>
        <p style="margin: 0 0 4px 0; color: ${theme.colors.muted}; font-size: 12px; text-transform: uppercase;">
          ${type}
        </p>
        ${description ? `
          <p style="margin: 4px 0; color: ${theme.colors.text}; font-size: 13px; line-height: 1.4;">
            ${description}
          </p>
        ` : ''}
        ${measurementText ? `
          <div style="margin: 8px 0; padding: 8px; background: ${theme.colors.background}; border-radius: 4px;">
            <strong style="color: ${theme.colors.text}; font-size: 12px;">Measurements:</strong>
            <p style="margin: 4px 0 0 0; color: ${theme.colors.text}; font-size: 11px;">${measurementText}</p>
          </div>
        ` : ''}
        ${created ? `
          <p style="margin: 4px 0 0 0; color: ${theme.colors.muted}; font-size: 11px;">
            Created: ${new Date(created).toLocaleDateString()}
          </p>
        ` : ''}
      </div>
    `;
  };

  const handleMapClick = (e: any) => {
    if (!isDrawing && onLocationSelect) {
      onLocationSelect({
        latitude: e.latlng.lat,
        longitude: e.latlng.lng,
      });
    }
  };

  const handleDrawCreated = (e: any) => {
    const layer = e.layer;
    const type = e.layerType;
    
    try {
      // Mark as drawn feature to prevent removal
      layer.isDrawnFeature = true;
      
      // Add to drawnItems group immediately to ensure persistence
      drawnItemsRef.current.addLayer(layer);
      
      const feature = convertLayerToFeature(layer, type);
      if (feature) {
        layer.featureId = feature.id;
        
        // Calculate measurements using Turf.js if available
        if (turfLoaded && window.turf) {
          feature.properties.measurements = calculateMeasurements(feature);
        }
        
        // Add popup
        layer.bindPopup(createFeaturePopup(feature));
        
        // Update features array
        setAllFeatures(prev => [...prev, feature]);
        
        // Notify parent
        if (onFeatureCreated) {
          onFeatureCreated(feature);
        }
        
        console.log('Feature created and persisted:', feature.id);
      }
    } catch (error) {
      console.error('Error creating feature:', error);
      Alert.alert('Error', 'Failed to create feature');
    }
  };

  const handleDrawEdited = (e: any) => {
    const layers = e.layers;
    
    layers.eachLayer((layer: any) => {
      if (layer.featureId && onFeatureUpdated) {
        try {
          const feature = convertLayerToFeature(layer, getLayerType(layer));
          if (feature) {
            feature.id = layer.featureId;
            
            // Recalculate measurements
            if (turfLoaded && window.turf) {
              feature.properties.measurements = calculateMeasurements(feature);
            }
            
            // Update popup
            layer.bindPopup(createFeaturePopup(feature));
            
            // Update features array  
            setAllFeatures(prev => prev.map(f => f.id === feature.id ? feature : f));
            
            onFeatureUpdated(feature);
          }
        } catch (error) {
          console.error('Error updating feature:', error);
        }
      }
    });
  };

  const handleDrawDeleted = (e: any) => {
    const layers = e.layers;
    
    layers.eachLayer((layer: any) => {
      if (layer.featureId) {
        // Remove from features array
        setAllFeatures(prev => prev.filter(f => f.id !== layer.featureId));
        
        if (onFeatureDeleted) {
          onFeatureDeleted(layer.featureId);
        }
      }
    });
  };

  const calculateMeasurements = (feature: any) => {
    if (!window.turf) return {};
    
    try {
      const measurements: any = {};
      const geoJsonFeature = convertToGeoJSON(feature);
      
      switch (feature.type) {
        case 'line':
          measurements.length = window.turf.length(geoJsonFeature, { units: 'meters' });
          break;
        case 'polygon':
          measurements.area = window.turf.area(geoJsonFeature);
          measurements.perimeter = window.turf.length(window.turf.polygonToLine(geoJsonFeature), { units: 'meters' });
          break;
        case 'circle':
          measurements.radius = feature.properties?.radius || 0;
          measurements.area = Math.PI * Math.pow(measurements.radius, 2);
          measurements.perimeter = 2 * Math.PI * measurements.radius;
          break;
        case 'rectangle':
          measurements.area = window.turf.area(geoJsonFeature);
          measurements.perimeter = window.turf.length(window.turf.polygonToLine(geoJsonFeature), { units: 'meters' });
          break;
      }
      
      return measurements;
    } catch (error) {
      console.error('Error calculating measurements:', error);
      return {};
    }
  };

  const convertToGeoJSON = (feature: any) => {
    return {
      type: 'Feature',
      geometry: feature.geometry,
      properties: feature.properties
    };
  };

  const convertLayerToFeature = (layer: any, type: string) => {
    let geometryType: string;
    let coordinates: any;

    switch (type) {
      case 'marker':
        geometryType = 'point';
        const latlng = layer.getLatLng();
        coordinates = [latlng.lng, latlng.lat];
        break;
        
      case 'polyline':
        geometryType = 'line';
        coordinates = layer.getLatLngs().map((ll: any) => [ll.lng, ll.lat]);
        break;
        
      case 'polygon':
        geometryType = 'polygon';
        const latlngs = layer.getLatLngs()[0];
        coordinates = [latlngs.map((ll: any) => [ll.lng, ll.lat])];
        // Ensure polygon is closed
        if (coordinates[0].length > 0) {
          const first = coordinates[0][0];
          const last = coordinates[0][coordinates[0].length - 1];
          if (first[0] !== last[0] || first[1] !== last[1]) {
            coordinates[0].push(first);
          }
        }
        break;
        
      case 'rectangle':
        geometryType = 'rectangle';
        const bounds = layer.getBounds();
        coordinates = [[
          [bounds.getWest(), bounds.getSouth()],
          [bounds.getEast(), bounds.getSouth()],
          [bounds.getEast(), bounds.getNorth()],
          [bounds.getWest(), bounds.getNorth()],
          [bounds.getWest(), bounds.getSouth()]
        ]];
        break;
        
      case 'circle':
        geometryType = 'circle';
        const center = layer.getLatLng();
        coordinates = [center.lng, center.lat];
        break;
        
      default:
        throw new Error(`Unknown layer type: ${type}`);
    }

    return {
      id: `${geometryType}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: geometryType,
      coordinates,
      properties: {
        name: `${geometryType.charAt(0).toUpperCase() + geometryType.slice(1)} ${Date.now()}`,
        color: theme.colors.primary,
        created: Date.now(),
        ...(type === 'circle' ? { radius: layer.getRadius() } : {}),
      },
      geometry: {
        type: geometryType === 'point' ? 'Point' : 
              geometryType === 'line' ? 'LineString' : 
              geometryType === 'polygon' ? 'Polygon' : 
              geometryType === 'rectangle' ? 'Polygon' : geometryType,
        coordinates: geometryType === 'rectangle' ? [coordinates] : coordinates,
      }
    };
  };

  const getLayerType = (layer: any): string => {
    if (layer instanceof window.L.Marker) return 'marker';
    if (layer instanceof window.L.Polyline && !(layer instanceof window.L.Polygon)) return 'polyline';
    if (layer instanceof window.L.Polygon && !(layer instanceof window.L.Rectangle)) return 'polygon';
    if (layer instanceof window.L.Rectangle) return 'rectangle';
    if (layer instanceof window.L.Circle) return 'circle';
    return 'unknown';
  };

  const updateAllFeaturesArray = () => {
    const features: any[] = [];
    
    if (drawnItemsRef.current) {
      drawnItemsRef.current.eachLayer((layer: any) => {
        if (layer.featureId) {
          try {
            const feature = convertLayerToFeature(layer, getLayerType(layer));
            feature.id = layer.featureId;
            features.push(feature);
          } catch (error) {
            console.error('Error converting layer to feature:', error);
          }
        }
      });
    }
    
    setAllFeatures(features);
  };

  // Export functions
  const exportToGeoJSON = () => {
    const featureCollection = {
      type: 'FeatureCollection',
      features: allFeatures.map(feature => convertToGeoJSON(feature)),
      metadata: {
        exportedAt: new Date().toISOString(),
        totalFeatures: allFeatures.length,
        version: '1.0'
      }
    };
    
    if (onExportGeoJSON) {
      onExportGeoJSON(featureCollection);
    }
    
    return featureCollection;
  };

  const exportToShapefile = async () => {
    if (!shapefileLoaded || !window.shp) {
      Alert.alert('Export Error', 'Shapefile library not loaded');
      return;
    }
    
    try {
      const geoJson = exportToGeoJSON();
      
      // Convert GeoJSON to Shapefile format
      // Note: This is a simplified conversion. Real shapefile export would need more work
      const shapefileData = {
        type: 'shapefile',
        data: geoJson,
        filename: `features_${Date.now()}.zip`
      };
      
      if (onExportShapefile) {
        onExportShapefile(shapefileData);
      }
      
      console.log('Shapefile export prepared:', shapefileData);
      Alert.alert('Export Success', 'Shapefile export data prepared. Check console for details.');
      
    } catch (error) {
      console.error('Error exporting to shapefile:', error);
      Alert.alert('Export Error', 'Failed to export shapefile');
    }
  };

  // Spatial Analysis Functions
  const performSpatialAnalysis = (analysisType: string, options: any = {}) => {
    if (!turfLoaded || !window.turf) {
      Alert.alert('Analysis Error', 'Turf.js library not loaded');
      return null;
    }
    
    try {
      let result = null;
      const features = allFeatures.map(f => convertToGeoJSON(f));
      
      switch (analysisType) {
        case 'buffer':
          if (features.length > 0 && options.distance) {
            result = features.map(f => window.turf.buffer(f, options.distance, options));
          }
          break;
          
        case 'intersect':
          if (features.length >= 2) {
            result = window.turf.intersect(features[0], features[1]);
          }
          break;
          
        case 'union':
          if (features.length >= 2) {
            result = window.turf.union(features[0], features[1]);
          }
          break;
          
        case 'difference':
          if (features.length >= 2) {
            result = window.turf.difference(features[0], features[1]);
          }
          break;
          
        case 'centroid':
          result = features.map(f => window.turf.centroid(f));
          break;
          
        case 'bbox':
          result = features.map(f => window.turf.bbox(f));
          break;
          
        case 'convexHull':
          if (features.length > 0) {
            const points = features.filter(f => f.geometry.type === 'Point');
            if (points.length >= 3) {
              result = window.turf.convex(window.turf.featureCollection(points));
            }
          }
          break;
          
        case 'nearestPoint':
          if (features.length > 0 && options.targetPoint) {
            const points = features.filter(f => f.geometry.type === 'Point');
            if (points.length > 0) {
              result = window.turf.nearestPoint(options.targetPoint, window.turf.featureCollection(points));
            }
          }
          break;
          
        default:
          Alert.alert('Analysis Error', `Unknown analysis type: ${analysisType}`);
          return null;
      }
      
      if (result && onAnalysisResult) {
        onAnalysisResult({
          type: analysisType,
          result,
          inputFeatures: features.length,
          timestamp: Date.now()
        });
      }
      
      return result;
      
    } catch (error) {
      console.error('Error performing spatial analysis:', error);
      Alert.alert('Analysis Error', `Failed to perform ${analysisType} analysis`);
      return null;
    }
  };

  return (
    <View style={[styles.container, style]}>
      <div
        id="enhanced-leaflet-map"
        style={{
          width: '100%',
          height: '100%',
          backgroundColor: theme.colors.background,
          borderRadius: 0,
        }}
      />
      
      {/* Custom CSS for better styling */}
      <style jsx>{`
        .custom-map-marker {
          background: transparent !important;
          border: none !important;
        }
        
        .leaflet-control-container .leaflet-top.leaflet-left {
          top: 10px;
          left: 10px;
        }
        
        .leaflet-draw-toolbar {
          margin-top: 10px;
        }
        
        .leaflet-draw-toolbar a {
          background-color: ${theme.colors.card};
          border: 1px solid ${theme.colors.border};
          color: ${theme.colors.text};
        }
        
        .leaflet-draw-toolbar a:hover {
          background-color: ${theme.colors.primary};
          color: white;
        }
        
        .leaflet-popup-content-wrapper {
          background-color: ${theme.colors.card};
          color: ${theme.colors.text};
          border-radius: 8px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        .leaflet-popup-tip {
          background-color: ${theme.colors.card};
        }
        
        .leaflet-control-zoom a {
          background-color: ${theme.colors.card};
          border: 1px solid ${theme.colors.border};
          color: ${theme.colors.text};
        }
        
        .leaflet-control-zoom a:hover {
          background-color: ${theme.colors.primary};
          color: white;
        }
      `}</style>
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },
});

export default EnhancedLeafletMap;
